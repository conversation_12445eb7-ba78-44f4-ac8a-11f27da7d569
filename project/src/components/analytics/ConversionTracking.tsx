import React, { useEffect, useCallback } from 'react';
import { useAnalytics } from '../../hooks/useAnalytics';
import { ConversionEvent, ConversionType } from '../../types/analytics';
import { createConversionEvent } from '../../utils/analyticsUtils';

interface ConversionTrackingProps {
  children: React.ReactNode;
  enablePurchaseTracking?: boolean;
  enableSignupTracking?: boolean;
  enableNewsletterTracking?: boolean;
  enableContactTracking?: boolean;
  enableCustomConversions?: boolean;
}

interface ConversionFunnelStep {
  step: number;
  name: string;
  url?: string;
  required?: boolean;
}

interface ConversionFunnel {
  name: string;
  steps: ConversionFunnelStep[];
  currentStep?: number;
}

const ConversionTracking: React.FC<ConversionTrackingProps> = ({
  children,
  enablePurchaseTracking = true,
  enableSignupTracking = true,
  enableNewsletterTracking = true,
  enableContactTracking = true,
  enableCustomConversions = true
}) => {
  const { trackConversion, trackEvent } = useAnalytics();

  // Define conversion funnels
  const conversionFunnels: Record<string, ConversionFunnel> = {
    purchase: {
      name: 'خرید محصول',
      steps: [
        { step: 1, name: 'مشاهده محصول', url: '/product/' },
        { step: 2, name: 'افزودن به سبد', required: true },
        { step: 3, name: 'مشاهده سبد خرید', url: '/cart' },
        { step: 4, name: 'شروع پرداخت', url: '/checkout', required: true },
        { step: 5, name: 'تکمیل خرید', required: true }
      ]
    },
    signup: {
      name: 'ثبت نام کاربر',
      steps: [
        { step: 1, name: 'مشاهده صفحه ثبت نام', url: '/auth' },
        { step: 2, name: 'شروع فرم ثبت نام', required: true },
        { step: 3, name: 'تکمیل ثبت نام', required: true }
      ]
    },
    newsletter: {
      name: 'عضویت در خبرنامه',
      steps: [
        { step: 1, name: 'مشاهده فرم خبرنامه' },
        { step: 2, name: 'تکمیل عضویت', required: true }
      ]
    },
    contact: {
      name: 'تماس با ما',
      steps: [
        { step: 1, name: 'مشاهده صفحه تماس', url: '/contact' },
        { step: 2, name: 'شروع فرم تماس', required: true },
        { step: 3, name: 'ارسال پیام', required: true }
      ]
    }
  };

  // Track conversion event
  const trackConversionEvent = useCallback((
    type: ConversionType,
    value?: number,
    conversionId?: string,
    additionalData?: Record<string, any>
  ) => {
    const event = createConversionEvent(
      `conversion_${type}`,
      value,
      conversionId,
      additionalData?.funnelStep,
      additionalData?.funnelName
    );

    trackConversion(event);

    // Also track as a custom event for more detailed analysis
    trackEvent('conversion', {
      conversion_type: type,
      conversion_value: value,
      conversion_id: conversionId,
      funnel_step: additionalData?.funnelStep,
      funnel_name: additionalData?.funnelName,
      ...additionalData
    });
  }, [trackConversion, trackEvent]);

  // Track funnel step
  const trackFunnelStep = useCallback((
    funnelName: string,
    stepNumber: number,
    stepName: string,
    additionalData?: Record<string, any>
  ) => {
    trackEvent('funnel_step', {
      funnel_name: funnelName,
      step_number: stepNumber,
      step_name: stepName,
      page_location: window.location.href,
      ...additionalData
    });
  }, [trackEvent]);

  // Purchase conversion tracking
  useEffect(() => {
    if (!enablePurchaseTracking) return;

    const handlePurchaseConversion = (event: CustomEvent) => {
      const { transactionId, value, items, paymentMethod } = event.detail;
      
      trackConversionEvent('purchase', value, transactionId, {
        funnelName: 'purchase',
        funnelStep: 5,
        payment_method: paymentMethod,
        item_count: items?.length || 0,
        currency: 'IRR'
      });

      // Track individual items
      if (items && Array.isArray(items)) {
        items.forEach((item: any, index: number) => {
          trackEvent('purchase_item', {
            transaction_id: transactionId,
            item_id: item.id,
            item_name: item.name,
            item_category: item.category,
            item_brand: item.brand,
            item_price: item.price,
            item_quantity: item.quantity,
            item_position: index + 1
          });
        });
      }
    };

    window.addEventListener('purchase_completed', handlePurchaseConversion as EventListener);
    return () => window.removeEventListener('purchase_completed', handlePurchaseConversion as EventListener);
  }, [enablePurchaseTracking, trackConversionEvent, trackEvent]);

  // Signup conversion tracking
  useEffect(() => {
    if (!enableSignupTracking) return;

    const handleSignupConversion = (event: CustomEvent) => {
      const { userId, userType, registrationMethod } = event.detail;
      
      trackConversionEvent('signup', undefined, userId, {
        funnelName: 'signup',
        funnelStep: 3,
        user_type: userType,
        registration_method: registrationMethod
      });
    };

    window.addEventListener('user_registered', handleSignupConversion as EventListener);
    return () => window.removeEventListener('user_registered', handleSignupConversion as EventListener);
  }, [enableSignupTracking, trackConversionEvent]);

  // Newsletter conversion tracking
  useEffect(() => {
    if (!enableNewsletterTracking) return;

    const handleNewsletterConversion = (event: CustomEvent) => {
      const { email, source, variant } = event.detail;
      
      trackConversionEvent('newsletter', undefined, email, {
        funnelName: 'newsletter',
        funnelStep: 2,
        subscription_source: source,
        subscription_variant: variant
      });
    };

    window.addEventListener('newsletter_subscribed', handleNewsletterConversion as EventListener);
    return () => window.removeEventListener('newsletter_subscribed', handleNewsletterConversion as EventListener);
  }, [enableNewsletterTracking, trackConversionEvent]);

  // Contact form conversion tracking
  useEffect(() => {
    if (!enableContactTracking) return;

    const handleContactConversion = (event: CustomEvent) => {
      const { contactId, subject, category } = event.detail;
      
      trackConversionEvent('contact', undefined, contactId, {
        funnelName: 'contact',
        funnelStep: 3,
        contact_subject: subject,
        contact_category: category
      });
    };

    window.addEventListener('contact_form_submitted', handleContactConversion as EventListener);
    return () => window.removeEventListener('contact_form_submitted', handleContactConversion as EventListener);
  }, [enableContactTracking, trackConversionEvent]);

  // Custom conversion tracking
  useEffect(() => {
    if (!enableCustomConversions) return;

    const handleCustomConversion = (event: CustomEvent) => {
      const { type, value, conversionId, additionalData } = event.detail;
      
      trackConversionEvent(type, value, conversionId, additionalData);
    };

    window.addEventListener('custom_conversion', handleCustomConversion as EventListener);
    return () => window.removeEventListener('custom_conversion', handleCustomConversion as EventListener);
  }, [enableCustomConversions, trackConversionEvent]);

  // Automatic funnel step tracking based on URL
  useEffect(() => {
    const currentUrl = window.location.pathname;
    
    // Check each funnel for matching steps
    Object.entries(conversionFunnels).forEach(([funnelKey, funnel]) => {
      funnel.steps.forEach(step => {
        if (step.url && currentUrl.includes(step.url)) {
          trackFunnelStep(funnel.name, step.step, step.name, {
            funnel_key: funnelKey,
            auto_tracked: true
          });
        }
      });
    });
  }, [trackFunnelStep]);

  // Micro-conversion tracking (smaller actions that lead to conversions)
  useEffect(() => {
    const trackMicroConversion = (event: Event) => {
      const target = event.target as HTMLElement;
      
      // Track product page interactions
      if (window.location.pathname.includes('/product/')) {
        if (target.closest('[data-action="add-to-cart"]')) {
          trackFunnelStep('خرید محصول', 2, 'افزودن به سبد', {
            product_id: window.location.pathname.split('/').pop()
          });
        }
        
        if (target.closest('[data-action="add-to-wishlist"]')) {
          trackEvent('micro_conversion', {
            action: 'add_to_wishlist',
            product_id: window.location.pathname.split('/').pop(),
            funnel_context: 'purchase'
          });
        }
      }
      
      // Track checkout interactions
      if (window.location.pathname.includes('/checkout')) {
        if (target.closest('[data-step="payment"]')) {
          trackFunnelStep('خرید محصول', 4, 'شروع پرداخت');
        }
      }
      
      // Track auth interactions
      if (window.location.pathname.includes('/auth')) {
        if (target.closest('form[data-form="register"]')) {
          trackFunnelStep('ثبت نام کاربر', 2, 'شروع فرم ثبت نام');
        }
      }
    };

    document.addEventListener('click', trackMicroConversion);
    return () => document.removeEventListener('click', trackMicroConversion);
  }, [trackFunnelStep, trackEvent]);

  // Conversion attribution tracking
  useEffect(() => {
    // Track the source of conversions
    const trackConversionAttribution = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const referrer = document.referrer;
      
      const attribution = {
        utm_source: urlParams.get('utm_source'),
        utm_medium: urlParams.get('utm_medium'),
        utm_campaign: urlParams.get('utm_campaign'),
        utm_content: urlParams.get('utm_content'),
        utm_term: urlParams.get('utm_term'),
        referrer: referrer,
        landing_page: window.location.href,
        timestamp: Date.now()
      };
      
      // Store attribution data for later conversion tracking
      sessionStorage.setItem('conversion_attribution', JSON.stringify(attribution));
    };

    // Track attribution on first page load
    if (!sessionStorage.getItem('conversion_attribution')) {
      trackConversionAttribution();
    }
  }, []);

  // Expose conversion tracking functions globally for easy access
  useEffect(() => {
    (window as any).conversionTracking = {
      trackConversion: trackConversionEvent,
      trackFunnelStep,
      trackMicroConversion: (action: string, value?: number, data?: Record<string, any>) => {
        trackEvent('micro_conversion', {
          action,
          value,
          ...data
        });
      }
    };
  }, [trackConversionEvent, trackFunnelStep, trackEvent]);

  return <>{children}</>;
};

export default ConversionTracking;
