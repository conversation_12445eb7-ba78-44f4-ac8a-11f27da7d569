import { useEffect, useRef, useState, useCallback } from 'react';

export interface UseIntersectionObserverOptions extends IntersectionObserverInit {
  freezeOnceVisible?: boolean;
  initialIsIntersecting?: boolean;
}

export interface UseIntersectionObserverReturn {
  isIntersecting: boolean;
  entry: IntersectionObserverEntry | null;
  ref: React.RefObject<HTMLElement>;
}

/**
 * Hook for intersection observer functionality
 */
export const useIntersectionObserver = (
  options: UseIntersectionObserverOptions = {}
): UseIntersectionObserverReturn => {
  const {
    threshold = 0.1,
    root = null,
    rootMargin = '0px',
    freezeOnceVisible = false,
    initialIsIntersecting = false
  } = options;

  const ref = useRef<HTMLElement>(null);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const [isIntersecting, setIsIntersecting] = useState(initialIsIntersecting);

  const frozen = freezeOnceVisible && isIntersecting;

  const updateEntry = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      setEntry(entry);
      setIsIntersecting(entry.isIntersecting);
    },
    []
  );

  useEffect(() => {
    const node = ref.current;
    const hasIOSupport = !!window.IntersectionObserver;

    if (!hasIOSupport || frozen || !node) return;

    const observer = new IntersectionObserver(updateEntry, {
      threshold,
      root,
      rootMargin
    });

    observer.observe(node);

    return () => observer.disconnect();
  }, [threshold, root, rootMargin, frozen, updateEntry]);

  return { isIntersecting, entry, ref };
};

/**
 * Hook for lazy loading with intersection observer
 */
export const useLazyLoad = (
  options: UseIntersectionObserverOptions = {}
) => {
  const { isIntersecting, ref } = useIntersectionObserver({
    freezeOnceVisible: true,
    ...options
  });

  return { isVisible: isIntersecting, ref };
};

/**
 * Hook for infinite scrolling
 */
export const useInfiniteScroll = (
  callback: () => void,
  options: UseIntersectionObserverOptions = {}
) => {
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold: 1.0,
    ...options
  });

  useEffect(() => {
    if (isIntersecting) {
      callback();
    }
  }, [isIntersecting, callback]);

  return { ref, isLoading: isIntersecting };
};

/**
 * Hook for tracking element visibility
 */
export const useVisibilityTracker = (
  onVisible?: () => void,
  onHidden?: () => void,
  options: UseIntersectionObserverOptions = {}
) => {
  const { isIntersecting, entry, ref } = useIntersectionObserver(options);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  useEffect(() => {
    if (isIntersecting && !hasBeenVisible) {
      setHasBeenVisible(true);
      onVisible?.();
    } else if (!isIntersecting && hasBeenVisible) {
      onHidden?.();
    }
  }, [isIntersecting, hasBeenVisible, onVisible, onHidden]);

  return {
    isVisible: isIntersecting,
    hasBeenVisible,
    entry,
    ref
  };
};

/**
 * Hook for measuring element visibility percentage
 */
export const useVisibilityPercentage = (
  options: UseIntersectionObserverOptions = {}
) => {
  const [visibilityPercentage, setVisibilityPercentage] = useState(0);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        const percentage = entry.intersectionRatio * 100;
        setVisibilityPercentage(percentage);
      },
      {
        threshold: Array.from({ length: 101 }, (_, i) => i / 100),
        ...options
      }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [options]);

  return { visibilityPercentage, ref };
};

/**
 * Hook for viewport-based animations
 */
export const useViewportAnimation = (
  animationClass: string = 'animate-in',
  options: UseIntersectionObserverOptions = {}
) => {
  const { isIntersecting, ref } = useIntersectionObserver({
    freezeOnceVisible: true,
    threshold: 0.1,
    ...options
  });

  const [shouldAnimate, setShouldAnimate] = useState(false);

  useEffect(() => {
    if (isIntersecting) {
      setShouldAnimate(true);
    }
  }, [isIntersecting]);

  return {
    ref,
    shouldAnimate,
    className: shouldAnimate ? animationClass : ''
  };
};

/**
 * Hook for progressive image loading
 */
export const useProgressiveImage = (
  lowQualitySrc: string,
  highQualitySrc: string,
  options: UseIntersectionObserverOptions = {}
) => {
  const { isVisible, ref } = useLazyLoad(options);
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (isVisible && !isLoaded) {
      setIsLoading(true);
      
      const img = new Image();
      img.onload = () => {
        setCurrentSrc(highQualitySrc);
        setIsLoading(false);
        setIsLoaded(true);
      };
      img.onerror = () => {
        setIsLoading(false);
      };
      img.src = highQualitySrc;
    }
  }, [isVisible, highQualitySrc, isLoaded]);

  return {
    ref,
    src: currentSrc,
    isLoading,
    isLoaded,
    blur: !isLoaded
  };
};

/**
 * Hook for scroll-triggered effects
 */
export const useScrollTrigger = (
  callback: (entry: IntersectionObserverEntry) => void,
  options: UseIntersectionObserverOptions = {}
) => {
  const { entry, ref } = useIntersectionObserver(options);

  useEffect(() => {
    if (entry) {
      callback(entry);
    }
  }, [entry, callback]);

  return { ref };
};

/**
 * Hook for parallax effects
 */
export const useParallax = (
  speed: number = 0.5,
  options: UseIntersectionObserverOptions = {}
) => {
  const [offset, setOffset] = useState(0);
  const { isIntersecting, ref } = useIntersectionObserver(options);

  useEffect(() => {
    if (!isIntersecting) return;

    const handleScroll = () => {
      const element = ref.current;
      if (!element) return;

      const rect = element.getBoundingClientRect();
      const scrolled = window.pageYOffset;
      const rate = scrolled * -speed;
      
      setOffset(rate);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isIntersecting, speed, ref]);

  return {
    ref,
    style: {
      transform: `translateY(${offset}px)`
    }
  };
};

export default useIntersectionObserver;
