import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Gift, Spark<PERSON>, Mail } from 'lucide-react';
import NewsletterSignup from './NewsletterSignup';
import { useNewsletterPopup } from '../../hooks/useNewsletter';
import { PERSIAN_NEWSLETTER_MESSAGES } from '../../types/newsletter';

interface NewsletterModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  showAutomatically?: boolean;
}

const NewsletterModal: React.FC<NewsletterModalProps> = ({
  isOpen: controlledIsOpen,
  onClose: controlledOnClose,
  showAutomatically = false
}) => {
  const { shouldShow, markAsShown, dismiss } = useNewsletterPopup();
  
  // Use controlled state if provided, otherwise use automatic popup logic
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : (showAutomatically && shouldShow);
  const onClose = controlledOnClose || dismiss;

  const handleSuccess = () => {
    if (showAutomatically) {
      markAsShown();
    }
    onClose();
  };

  const handleClose = () => {
    if (showAutomatically) {
      markAsShown();
    }
    onClose();
  };

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="fixed inset-0 bg-black bg-opacity-50"
          />

          {/* Modal */}
          <div className="flex min-h-full items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="relative w-full max-w-lg bg-white rounded-xl shadow-2xl overflow-hidden"
            >
              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-4 left-4 z-10 p-2 text-gray-400 hover:text-gray-600 bg-white rounded-full shadow-lg transition-colors"
                aria-label="بستن"
              >
                <X className="w-5 h-5" />
              </button>

              {/* Header with decorative elements */}
              <div className="relative bg-gradient-to-br from-primary-500 to-secondary-500 text-white p-6 pb-8">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full translate-y-12 -translate-x-12"></div>
                
                <div className="relative text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="bg-white bg-opacity-20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4"
                  >
                    <Mail className="w-10 h-10 text-white" />
                  </motion.div>
                  
                  <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-2xl font-bold mb-2"
                  >
                    {PERSIAN_NEWSLETTER_MESSAGES.content.title}
                  </motion.h2>
                  
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-white text-opacity-90"
                  >
                    {PERSIAN_NEWSLETTER_MESSAGES.content.subtitle}
                  </motion.p>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Special offer banner */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 mb-6"
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-yellow-100 rounded-full w-12 h-12 flex items-center justify-center">
                      <Gift className="w-6 h-6 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-yellow-900">
                        🎉 پیشنهاد ویژه برای اعضای جدید!
                      </h3>
                      <p className="text-sm text-yellow-700">
                        ۱۰٪ تخفیف برای اولین خرید + ارسال رایگان
                      </p>
                    </div>
                  </div>
                </motion.div>

                {/* Benefits */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="mb-6"
                >
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-primary-600" />
                    چرا عضو شوید؟
                  </h4>
                  <div className="grid grid-cols-1 gap-3">
                    {PERSIAN_NEWSLETTER_MESSAGES.content.benefits.slice(0, 3).map((benefit, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7 + index * 0.1 }}
                        className="flex items-center gap-3 text-sm text-gray-700"
                      >
                        <div className="bg-green-100 rounded-full w-6 h-6 flex items-center justify-center">
                          <span className="text-green-600 text-xs">✓</span>
                        </div>
                        {benefit}
                      </motion.div>
                    ))}
                  </div>
                </motion.div>

                {/* Newsletter Signup Form */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <NewsletterSignup
                    source="popup"
                    variant="compact"
                    showBenefits={false}
                    onSuccess={handleSuccess}
                  />
                </motion.div>

                {/* Footer note */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                  className="mt-4 text-center"
                >
                  <p className="text-xs text-gray-500">
                    {PERSIAN_NEWSLETTER_MESSAGES.content.unsubscribeNote}
                  </p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

// Auto-popup component that shows automatically
export const AutoNewsletterModal: React.FC = () => {
  return <NewsletterModal showAutomatically={true} />;
};

export default NewsletterModal;
