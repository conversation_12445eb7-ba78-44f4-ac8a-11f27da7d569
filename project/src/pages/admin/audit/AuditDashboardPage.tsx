import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  Clock,
  User,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Download,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  Globe,
  Monitor,
  Smartphone,
  Eye,
  Settings,
  Database,
  X
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import AdminModal from '../../../components/admin/common/AdminModal';
import { useAdminNotifications } from '../../../hooks/useAdminNotifications';
import { AuditLogEntry, AUDIT_ACTIONS, AUDIT_RESOURCES } from '../../../types/adminNotifications';
import { formatPersianDate, formatNumber } from '../../../utils/formatters';
import toast from 'react-hot-toast';

const AuditDashboardPage: React.FC = () => {
  const {
    auditLogs,
    loading,
    error,
    auditFilters,
    setAuditFilters,
    refetch
  } = useAdminNotifications();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [selectedResource, setSelectedResource] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [dateRange, setDateRange] = useState('last7days');
  const [selectedLog, setSelectedLog] = useState<AuditLogEntry | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const handleExport = async () => {
    try {
      toast.loading('در حال تهیه فایل خروجی...', { id: 'export' });

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create CSV content
      const headers = ['زمان', 'کاربر', 'نقش', 'عملیات', 'منبع', 'وضعیت', 'IP آدرس'];
      const csvContent = [
        headers.join(','),
        ...filteredLogs.map(log => [
          new Date(log.timestamp).toLocaleString('fa-IR'),
          log.userEmail,
          log.userRole,
          AUDIT_ACTIONS[log.action] || log.action,
          AUDIT_RESOURCES[log.resource] || log.resource,
          log.success ? 'موفق' : 'ناموفق',
          log.ipAddress
        ].join(','))
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `audit-logs-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('فایل خروجی با موفقیت دانلود شد', { id: 'export' });
    } catch (error) {
      toast.error('خطا در تهیه فایل خروجی', { id: 'export' });
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleViewDetails = (log: AuditLogEntry) => {
    setSelectedLog(log);
    setShowDetailsModal(true);
  };

  // Filter audit logs based on search and filters
  const filteredLogs = auditLogs.filter(log => {
    if (searchTerm && !log.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !log.action.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !log.resource.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    if (selectedAction && log.action !== selectedAction) return false;
    if (selectedResource && log.resource !== selectedResource) return false;
    if (selectedStatus === 'success' && !log.success) return false;
    if (selectedStatus === 'failed' && log.success) return false;
    return true;
  });

  // Calculate statistics
  const totalLogs = auditLogs.length;
  const successfulActions = auditLogs.filter(log => log.success).length;
  const failedActions = auditLogs.filter(log => !log.success).length;
  const uniqueUsers = new Set(auditLogs.map(log => log.userId)).size;
  const uniqueIPs = new Set(auditLogs.map(log => log.ipAddress)).size;

  // Get recent activity summary
  const recentActions = auditLogs.slice(0, 10);
  const actionCounts = auditLogs.reduce((acc, log) => {
    acc[log.action] = (acc[log.action] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topActions = Object.entries(actionCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const auditColumns: AdminTableColumn<AuditLogEntry>[] = [
    {
      key: 'timestamp',
      title: 'زمان',
      sortable: true,
      render: (log) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {formatPersianDate(log.timestamp)}
          </div>
          <div className="text-gray-500">
            {new Date(log.timestamp).toLocaleTimeString('fa-IR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>
      )
    },
    {
      key: 'user',
      title: 'کاربر',
      render: (log) => (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-400" />
          <div>
            <div className="font-medium text-gray-900">{log.userEmail}</div>
            <div className="text-sm text-gray-500">{log.userRole}</div>
          </div>
        </div>
      )
    },
    {
      key: 'action',
      title: 'عملیات',
      render: (log) => (
        <div>
          <div className="font-medium text-gray-900">
            {AUDIT_ACTIONS[log.action] || log.action}
          </div>
          <div className="text-sm text-gray-500">
            {AUDIT_RESOURCES[log.resource] || log.resource}
            {log.resourceId && ` #${log.resourceId}`}
          </div>
        </div>
      )
    },
    {
      key: 'success',
      title: 'وضعیت',
      render: (log) => (
        <div className="flex items-center gap-2">
          {log.success ? (
            <>
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-600">موفق</span>
            </>
          ) : (
            <>
              <XCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-600">ناموفق</span>
            </>
          )}
        </div>
      )
    },
    {
      key: 'ipAddress',
      title: 'IP آدرس',
      render: (log) => (
        <div className="flex items-center gap-2">
          <Globe className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-mono text-gray-900">{log.ipAddress}</span>
        </div>
      )
    },
    {
      key: 'details',
      title: 'جزئیات',
      render: (log) => (
        <AdminButton
          variant="ghost"
          size="sm"
          icon={Eye}
          onClick={() => handleViewDetails(log)}
        >
          مشاهده
        </AdminButton>
      )
    }
  ];

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <AdminButton onClick={handleRefresh} className="mt-4">
            تلاش مجدد
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">گزارش عملکرد</h1>
            <p className="text-gray-600 mt-1">نظارت و بررسی فعالیت‌های سیستم</p>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={handleRefresh}
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی Excel
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminStatsCard
            title="کل رویدادها"
            value={formatNumber(totalLogs)}
            change="+12.5%"
            changeType="increase"
            icon={Activity}
            color="bg-blue-500"
          />
          
          <AdminStatsCard
            title="عملیات موفق"
            value={formatNumber(successfulActions)}
            change={`${((successfulActions / totalLogs) * 100).toFixed(1)}%`}
            changeType="neutral"
            icon={CheckCircle}
            color="bg-green-500"
          />
          
          <AdminStatsCard
            title="عملیات ناموفق"
            value={formatNumber(failedActions)}
            change={`${((failedActions / totalLogs) * 100).toFixed(1)}%`}
            changeType="neutral"
            icon={AlertTriangle}
            color="bg-red-500"
          />
          
          <AdminStatsCard
            title="کاربران فعال"
            value={formatNumber(uniqueUsers)}
            change="+8.3%"
            changeType="increase"
            icon={User}
            color="bg-purple-500"
          />
        </div>



        {/* Activity Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Actions */}
          <AdminCard title="عملیات پرتکرار" icon={Activity}>
            <div className="space-y-4">
              {topActions.map(([action, count], index) => (
                <motion.div
                  key={action}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-admin-600">
                        {formatNumber(index + 1)}
                      </span>
                    </div>
                    <span className="font-medium text-gray-900">
                      {AUDIT_ACTIONS[action] || action}
                    </span>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-gray-900">
                      {formatNumber(count)}
                    </div>
                    <div className="text-xs text-gray-500">
                      رویداد
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </AdminCard>

          {/* Security Overview */}
          <AdminCard title="امنیت سیستم" icon={Shield}>
            <div className="space-y-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">عملیات موفق</span>
                </div>
                <div className="text-2xl font-bold text-green-900 mb-1">
                  {formatNumber(successfulActions)}
                </div>
                <div className="text-sm text-green-600">
                  {((successfulActions / totalLogs) * 100).toFixed(1)}% از کل عملیات
                </div>
              </div>

              <div className="p-4 bg-red-50 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <span className="font-medium text-red-900">عملیات ناموفق</span>
                </div>
                <div className="text-2xl font-bold text-red-900 mb-1">
                  {formatNumber(failedActions)}
                </div>
                <div className="text-sm text-red-600">
                  {((failedActions / totalLogs) * 100).toFixed(1)}% از کل عملیات
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <Globe className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">IP آدرس‌های منحصر</span>
                </div>
                <div className="text-2xl font-bold text-blue-900 mb-1">
                  {formatNumber(uniqueIPs)}
                </div>
                <div className="text-sm text-blue-600">
                  آدرس‌های مختلف
                </div>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Recent Activity */}
        <AdminCard title="فعالیت‌های اخیر" icon={Clock}>
          <div className="space-y-3">
            {recentActions.map((log, index) => (
              <motion.div
                key={log.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${log.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">
                      {AUDIT_ACTIONS[log.action] || log.action}
                    </div>
                    <div className="text-xs text-gray-500">
                      {log.userEmail} • {AUDIT_RESOURCES[log.resource] || log.resource}
                      {log.resourceId && ` #${log.resourceId}`}
                    </div>
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-sm text-gray-900">
                    {formatPersianDate(log.timestamp)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(log.timestamp).toLocaleTimeString('fa-IR', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </AdminCard>

        {/* Audit Logs Table */}
        <AdminCard title="گزارش کامل عملکرد" icon={Database}>
          {/* Filters */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  جستجو
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="جستجو در کاربر، عملیات..."
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع عملیات
                </label>
                <select
                  value={selectedAction}
                  onChange={(e) => setSelectedAction(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
                >
                  <option value="">همه عملیات</option>
                  {Object.entries(AUDIT_ACTIONS).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  منبع
                </label>
                <select
                  value={selectedResource}
                  onChange={(e) => setSelectedResource(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
                >
                  <option value="">همه منابع</option>
                  {Object.entries(AUDIT_RESOURCES).map(([key, label]) => (
                    <option key={key} value={key}>{label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  وضعیت
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
                >
                  <option value="">همه وضعیت‌ها</option>
                  <option value="success">موفق</option>
                  <option value="failed">ناموفق</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  بازه زمانی
                </label>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
                >
                  <option value="last24h">۲۴ ساعت گذشته</option>
                  <option value="last7days">۷ روز گذشته</option>
                  <option value="last30days">۳۰ روز گذشته</option>
                  <option value="last90days">۹۰ روز گذشته</option>
                </select>
              </div>
            </div>
          </div>

          <AdminTable
            columns={auditColumns}
            data={filteredLogs}
            loading={loading}
            emptyMessage="هیچ گزارش عملکردی یافت نشد"
            hoverable
            striped
          />
        </AdminCard>

        {/* Details Modal */}
        <AdminModal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title="جزئیات رویداد"
          size="lg"
        >
          {selectedLog && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    زمان رویداد
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium text-gray-900">
                      {formatPersianDate(selectedLog.timestamp)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(selectedLog.timestamp).toLocaleTimeString('fa-IR')}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وضعیت
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {selectedLog.success ? (
                        <>
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <span className="font-medium text-green-600">موفق</span>
                        </>
                      ) : (
                        <>
                          <XCircle className="w-5 h-5 text-red-500" />
                          <span className="font-medium text-red-600">ناموفق</span>
                        </>
                      )}
                    </div>
                    {selectedLog.errorMessage && (
                      <div className="text-sm text-red-600 mt-1">
                        {selectedLog.errorMessage}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* User Info */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اطلاعات کاربر
                </label>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">ایمیل:</span>
                      <div className="font-medium text-gray-900">{selectedLog.userEmail}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">نقش:</span>
                      <div className="font-medium text-gray-900">{selectedLog.userRole}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">IP آدرس:</span>
                      <div className="font-medium text-gray-900 font-mono">{selectedLog.ipAddress}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Info */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اطلاعات عملیات
                </label>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">عملیات:</span>
                      <div className="font-medium text-gray-900">
                        {AUDIT_ACTIONS[selectedLog.action] || selectedLog.action}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">منبع:</span>
                      <div className="font-medium text-gray-900">
                        {AUDIT_RESOURCES[selectedLog.resource] || selectedLog.resource}
                      </div>
                    </div>
                    {selectedLog.resourceId && (
                      <div>
                        <span className="text-sm text-gray-500">شناسه منبع:</span>
                        <div className="font-medium text-gray-900">#{selectedLog.resourceId}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Technical Details */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  جزئیات فنی
                </label>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="mb-3">
                    <span className="text-sm text-gray-500">User Agent:</span>
                    <div className="font-mono text-sm text-gray-900 break-all">
                      {selectedLog.userAgent}
                    </div>
                  </div>
                  {selectedLog.details && (
                    <div>
                      <span className="text-sm text-gray-500">جزئیات اضافی:</span>
                      <pre className="mt-1 p-3 bg-white rounded border text-sm text-gray-900 overflow-auto">
                        {JSON.stringify(selectedLog.details, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </AdminModal>
      </div>
    </AdminLayout>
  );
};

export default AuditDashboardPage;
