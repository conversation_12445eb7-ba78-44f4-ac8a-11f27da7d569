import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingBag,
  Calendar,
  Crown,
  AlertTriangle,
  BarChart3,
  PieChart,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { formatPersianNumber, formatPersianCurrency, formatPersianDate } from '../../../utils/customerUtils';

const CustomerAnalyticsPage: React.FC = () => {
  const { customers, getAnalytics } = useAdminCustomers();
  const [dateRange, setDateRange] = useState('30'); // days
  const [refreshing, setRefreshing] = useState(false);

  const analytics = getAnalytics();

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleExport = () => {
    // Export analytics data
    console.log('Exporting analytics data');
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    subtitle?: string;
  }> = ({ title, value, change, icon: Icon, color, subtitle }) => (
    <AdminCard>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
          {change !== undefined && (
            <div className={`flex items-center mt-1 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? (
                <TrendingUp className="w-4 h-4 ml-1" />
              ) : (
                <TrendingDown className="w-4 h-4 ml-1" />
              )}
              <span className="text-sm font-medium">
                {Math.abs(change)}% نسبت به ماه قبل
              </span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </AdminCard>
  );

  return (
    <AdminLayout
      title="تحلیل‌های مشتریان"
      subtitle="گزارش‌های تفصیلی و تحلیل رفتار مشتریان"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="7">7 روز گذشته</option>
              <option value="30">30 روز گذشته</option>
              <option value="90">90 روز گذشته</option>
              <option value="365">یک سال گذشته</option>
            </select>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              loading={refreshing}
              onClick={handleRefresh}
            >
              به‌روزرسانی
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی گزارش
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="کل مشتریان"
            value={formatPersianNumber(analytics.totalCustomers)}
            change={12}
            icon={Users}
            color="bg-blue-500"
          />
          
          <StatCard
            title="مشتریان فعال"
            value={formatPersianNumber(analytics.activeCustomers)}
            change={8}
            icon={TrendingUp}
            color="bg-green-500"
            subtitle={`${((analytics.activeCustomers / analytics.totalCustomers) * 100).toFixed(1)}% از کل`}
          />
          
          <StatCard
            title="میانگین ارزش مشتری"
            value={formatPersianCurrency(analytics.averageLifetimeValue)}
            change={-3}
            icon={DollarSign}
            color="bg-purple-500"
          />
          
          <StatCard
            title="نرخ نگهداری"
            value={`${analytics.customerRetentionRate.toFixed(1)}%`}
            change={5}
            icon={Crown}
            color="bg-yellow-500"
          />
        </div>

        {/* Customer Segments Distribution */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdminCard title="توزیع بخش‌های مشتریان">
            <div className="space-y-4">
              {Object.entries(analytics.segmentDistribution).map(([segment, count]) => {
                const percentage = (count / analytics.totalCustomers) * 100;
                const segmentNames: Record<string, string> = {
                  new: 'جدید',
                  active: 'فعال',
                  loyal: 'وفادار',
                  at_risk: 'در معرض خطر',
                  lost: 'از دست رفته'
                };
                
                return (
                  <div key={segment} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-900">
                        {segmentNames[segment] || segment}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 bg-primary-500 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 min-w-0">
                        {formatPersianNumber(count)} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </AdminCard>

          <AdminCard title="وضعیت مشتریان">
            <div className="space-y-4">
              {Object.entries(analytics.statusDistribution).map(([status, count]) => {
                const percentage = (count / analytics.totalCustomers) * 100;
                const statusNames: Record<string, string> = {
                  active: 'فعال',
                  inactive: 'غیرفعال',
                  blocked: 'مسدود',
                  pending: 'در انتظار'
                };
                
                const colors: Record<string, string> = {
                  active: 'bg-green-500',
                  inactive: 'bg-gray-500',
                  blocked: 'bg-red-500',
                  pending: 'bg-yellow-500'
                };
                
                return (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 ${colors[status]} rounded-full`}></div>
                      <span className="text-sm font-medium text-gray-900">
                        {statusNames[status] || status}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 ${colors[status]} rounded-full`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 min-w-0">
                        {formatPersianNumber(count)} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </AdminCard>
        </div>

        {/* Top Customers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdminCard title="برترین مشتریان (بر اساس خرید)">
            <div className="space-y-4">
              {analytics.topSpenders.slice(0, 5).map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {customer.firstName} {customer.lastName}
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatPersianNumber(customer.analytics.totalOrders)} سفارش
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-gray-900">
                      {formatPersianCurrency(customer.analytics.totalSpent)}
                    </div>
                    {customer.isVip && (
                      <Crown className="w-4 h-4 text-yellow-500 mr-auto" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </AdminCard>

          <AdminCard title="مشتریان در معرض خطر">
            <div className="space-y-4">
              {analytics.atRiskCustomers.slice(0, 5).map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-orange-500" />
                    <div>
                      <div className="font-medium text-gray-900">
                        {customer.firstName} {customer.lastName}
                      </div>
                      <div className="text-sm text-gray-600">
                        آخرین خرید: {customer.analytics.lastOrderDate ? 
                          formatPersianDate(customer.analytics.lastOrderDate) : 'هرگز'
                        }
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="text-sm font-medium text-orange-600">
                      امتیاز خطر: {customer.riskScore}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </AdminCard>
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AdminCard title="میانگین سفارش">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {formatPersianCurrency(analytics.averageOrderValue)}
              </div>
              <p className="text-sm text-gray-600">میانگین ارزش هر سفارش</p>
            </div>
          </AdminCard>

          <AdminCard title="تعداد سفارش در ماه">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {analytics.averageOrderFrequency.toFixed(1)}
              </div>
              <p className="text-sm text-gray-600">میانگین سفارش در ماه</p>
            </div>
          </AdminCard>

          <AdminCard title="نرخ ترک">
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">
                {analytics.churnRate.toFixed(1)}%
              </div>
              <p className="text-sm text-gray-600">مشتریان از دست رفته</p>
            </div>
          </AdminCard>
        </div>
      </div>
    </AdminLayout>
  );
};

export default CustomerAnalyticsPage;
