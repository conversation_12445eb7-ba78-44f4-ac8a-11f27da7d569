import {
  AdminUser,
  AdminRole,
  AdminResource,
  AdminAction,
  ADMIN_ROLE_PERMISSIONS,
  PERSIAN_ADMIN_MESSAGES
} from '../types/admin';

/**
 * Role hierarchy utilities
 */
export class RoleHierarchy {
  private static readonly ROLE_LEVELS: Record<AdminRole, number> = {
    viewer: 1,
    moderator: 2,
    admin: 3,
    super_admin: 4
  };

  static getRoleLevel(role: AdminRole): number {
    return this.ROLE_LEVELS[role];
  }

  static isHigherRole(role1: AdminRole, role2: AdminRole): boolean {
    return this.getRoleLevel(role1) > this.getRoleLevel(role2);
  }

  static isEqualOrHigherRole(role1: AdminRole, role2: AdminRole): boolean {
    return this.getRoleLevel(role1) >= this.getRoleLevel(role2);
  }

  static canManageRole(managerRole: AdminRole, targetRole: AdminRole): boolean {
    // Super admin can manage all roles
    if (managerRole === 'super_admin') return true;
    
    // Admin can manage moderator and viewer
    if (managerRole === 'admin') {
      return ['moderator', 'viewer'].includes(targetRole);
    }
    
    // Moderator can only manage viewer
    if (managerRole === 'moderator') {
      return targetRole === 'viewer';
    }
    
    // Viewer cannot manage anyone
    return false;
  }

  static getManageableRoles(role: AdminRole): AdminRole[] {
    switch (role) {
      case 'super_admin':
        return ['super_admin', 'admin', 'moderator', 'viewer'];
      case 'admin':
        return ['moderator', 'viewer'];
      case 'moderator':
        return ['viewer'];
      case 'viewer':
        return [];
      default:
        return [];
    }
  }
}

/**
 * Permission validation utilities
 */
export class PermissionValidator {
  static validateResourceAccess(
    user: AdminUser | null,
    resource: AdminResource,
    action: AdminAction
  ): { allowed: boolean; reason?: string } {
    if (!user) {
      return {
        allowed: false,
        reason: PERSIAN_ADMIN_MESSAGES.auth.accessDenied
      };
    }

    if (!user.isActive) {
      return {
        allowed: false,
        reason: 'حساب کاربری غیرفعال است'
      };
    }

    // Super admin has all permissions
    if (user.role === 'super_admin') {
      return { allowed: true };
    }

    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    const resourcePermission = rolePermissions.find(p => p.resource === resource);

    if (!resourcePermission) {
      return {
        allowed: false,
        reason: `دسترسی به ${PERSIAN_ADMIN_MESSAGES.permissions[resource]} ندارید`
      };
    }

    if (!resourcePermission.actions.includes(action)) {
      return {
        allowed: false,
        reason: `مجوز ${PERSIAN_ADMIN_MESSAGES.actions[action]} در ${PERSIAN_ADMIN_MESSAGES.permissions[resource]} ندارید`
      };
    }

    return { allowed: true };
  }

  static getPermissionSummary(user: AdminUser | null): {
    role: string;
    resources: Array<{
      resource: string;
      resourceName: string;
      actions: Array<{
        action: string;
        actionName: string;
      }>;
    }>;
  } {
    if (!user) {
      return {
        role: 'هیچ نقشی',
        resources: []
      };
    }

    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    
    return {
      role: PERSIAN_ADMIN_MESSAGES.roles[user.role],
      resources: rolePermissions.map(permission => ({
        resource: permission.resource,
        resourceName: PERSIAN_ADMIN_MESSAGES.permissions[permission.resource],
        actions: permission.actions.map(action => ({
          action,
          actionName: PERSIAN_ADMIN_MESSAGES.actions[action]
        }))
      }))
    };
  }
}

/**
 * Navigation menu utilities based on permissions
 */
export class NavigationPermissions {
  static getAccessibleMenuItems(user: AdminUser | null): Array<{
    key: string;
    label: string;
    resource: AdminResource;
    requiredAction: AdminAction;
    icon?: string;
    children?: Array<{
      key: string;
      label: string;
      resource: AdminResource;
      requiredAction: AdminAction;
    }>;
  }> {
    const menuItems = [
      {
        key: 'dashboard',
        label: PERSIAN_ADMIN_MESSAGES.navigation.dashboard,
        resource: 'analytics' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'dashboard'
      },
      {
        key: 'products',
        label: PERSIAN_ADMIN_MESSAGES.navigation.products,
        resource: 'products' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'products',
        children: [
          {
            key: 'products-list',
            label: 'فهرست محصولات',
            resource: 'products' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'products-create',
            label: 'افزودن محصول',
            resource: 'products' as AdminResource,
            requiredAction: 'create' as AdminAction
          },
          {
            key: 'categories',
            label: 'دسته‌بندی‌ها',
            resource: 'products' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'orders',
        label: PERSIAN_ADMIN_MESSAGES.navigation.orders,
        resource: 'orders' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'orders',
        children: [
          {
            key: 'orders-list',
            label: 'فهرست سفارشات',
            resource: 'orders' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'orders-tracking',
            label: 'پیگیری سفارشات',
            resource: 'orders' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'customers',
        label: PERSIAN_ADMIN_MESSAGES.navigation.customers,
        resource: 'customers' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'customers',
        children: [
          {
            key: 'customers-list',
            label: 'فهرست مشتریان',
            resource: 'customers' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'customers-support',
            label: 'پشتیبانی مشتریان',
            resource: 'customers' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'reviews',
        label: PERSIAN_ADMIN_MESSAGES.navigation.reviews,
        resource: 'reviews' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'reviews',
        children: [
          {
            key: 'reviews-moderation',
            label: 'نظارت بر نظرات',
            resource: 'reviews' as AdminResource,
            requiredAction: 'moderate' as AdminAction
          },
          {
            key: 'reviews-analytics',
            label: 'آمار نظرات',
            resource: 'reviews' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'loyalty',
        label: PERSIAN_ADMIN_MESSAGES.navigation.loyalty,
        resource: 'loyalty' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'loyalty',
        children: [
          {
            key: 'loyalty-dashboard',
            label: 'داشبورد باشگاه',
            resource: 'loyalty' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'loyalty-tiers',
            label: 'مدیریت سطوح',
            resource: 'loyalty' as AdminResource,
            requiredAction: 'configure' as AdminAction
          },
          {
            key: 'loyalty-rewards',
            label: 'مدیریت جوایز',
            resource: 'loyalty' as AdminResource,
            requiredAction: 'update' as AdminAction
          }
        ]
      },
      {
        key: 'content',
        label: PERSIAN_ADMIN_MESSAGES.navigation.content,
        resource: 'content' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'content',
        children: [
          {
            key: 'content-banners',
            label: 'مدیریت بنرها',
            resource: 'content' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'content-newsletter',
            label: 'مدیریت خبرنامه',
            resource: 'content' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'analytics',
        label: PERSIAN_ADMIN_MESSAGES.navigation.analytics,
        resource: 'analytics' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'analytics',
        children: [
          {
            key: 'analytics-sales',
            label: 'گزارش فروش',
            resource: 'analytics' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'analytics-customers',
            label: 'آمار مشتریان',
            resource: 'analytics' as AdminResource,
            requiredAction: 'read' as AdminAction
          }
        ]
      },
      {
        key: 'settings',
        label: PERSIAN_ADMIN_MESSAGES.navigation.settings,
        resource: 'settings' as AdminResource,
        requiredAction: 'read' as AdminAction,
        icon: 'settings',
        children: [
          {
            key: 'settings-general',
            label: 'تنظیمات عمومی',
            resource: 'settings' as AdminResource,
            requiredAction: 'read' as AdminAction
          },
          {
            key: 'settings-payment',
            label: 'تنظیمات پرداخت',
            resource: 'settings' as AdminResource,
            requiredAction: 'configure' as AdminAction
          }
        ]
      }
    ];

    if (!user) return [];

    // Filter menu items based on user permissions
    return menuItems.filter(item => {
      const hasAccess = PermissionValidator.validateResourceAccess(
        user,
        item.resource,
        item.requiredAction
      ).allowed;

      if (hasAccess && item.children) {
        // Filter children based on permissions
        item.children = item.children.filter(child => 
          PermissionValidator.validateResourceAccess(
            user,
            child.resource,
            child.requiredAction
          ).allowed
        );
      }

      return hasAccess;
    });
  }

  static canAccessRoute(user: AdminUser | null, route: string): boolean {
    if (!user) return false;

    // Route to resource mapping
    const routePermissions: Record<string, { resource: AdminResource; action: AdminAction }> = {
      '/admin': { resource: 'analytics', action: 'read' },
      '/admin/dashboard': { resource: 'analytics', action: 'read' },
      '/admin/products': { resource: 'products', action: 'read' },
      '/admin/products/create': { resource: 'products', action: 'create' },
      '/admin/products/categories': { resource: 'products', action: 'read' },
      '/admin/products/brands': { resource: 'products', action: 'read' },
      '/admin/products/inventory': { resource: 'products', action: 'read' },
      '/admin/orders': { resource: 'orders', action: 'read' },
      '/admin/orders/pending': { resource: 'orders', action: 'read' },
      '/admin/orders/processing': { resource: 'orders', action: 'read' },
      '/admin/orders/shipped': { resource: 'orders', action: 'read' },
      '/admin/orders/returns': { resource: 'orders', action: 'read' },
      '/admin/orders/tracking': { resource: 'orders', action: 'read' },
      '/admin/customers': { resource: 'customers', action: 'read' },
      '/admin/customers/segments': { resource: 'customers', action: 'read' },
      '/admin/customers/analytics': { resource: 'analytics', action: 'read' },
      '/admin/reviews': { resource: 'reviews', action: 'read' },
      '/admin/reviews/moderation': { resource: 'reviews', action: 'moderate' },
      '/admin/reviews/analytics': { resource: 'analytics', action: 'read' },
      '/admin/loyalty': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/dashboard': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/tiers': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/rewards': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/transactions': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/points': { resource: 'loyalty', action: 'read' },
      '/admin/loyalty/programs': { resource: 'loyalty', action: 'read' },
      '/admin/content': { resource: 'content', action: 'read' },
      '/admin/content/dashboard': { resource: 'content', action: 'read' },
      '/admin/content/homepage': { resource: 'content', action: 'read' },
      '/admin/content/banners': { resource: 'content', action: 'read' },
      '/admin/content/promotions': { resource: 'content', action: 'read' },
      '/admin/content/newsletter': { resource: 'content', action: 'read' },
      '/admin/content/pages': { resource: 'content', action: 'read' },
      '/admin/content/media': { resource: 'content', action: 'read' },
      '/admin/analytics': { resource: 'analytics', action: 'read' },
      '/admin/analytics/sales': { resource: 'analytics', action: 'read' },
      '/admin/analytics/customers': { resource: 'analytics', action: 'read' },
      '/admin/analytics/products': { resource: 'analytics', action: 'read' },
      '/admin/analytics/traffic': { resource: 'analytics', action: 'read' },
      '/admin/settings': { resource: 'settings', action: 'read' },
      '/admin/users': { resource: 'users', action: 'read' },
      '/admin/users/admins': { resource: 'users', action: 'read' },
      '/admin/users/roles': { resource: 'users', action: 'read' },
      '/admin/audit': { resource: 'audit', action: 'read' },
      '/admin/notifications': { resource: 'notifications', action: 'read' }
    };

    // Check exact route match first
    const permission = routePermissions[route];
    if (permission) {
      return PermissionValidator.validateResourceAccess(
        user,
        permission.resource,
        permission.action
      ).allowed;
    }

    // Handle dynamic routes with patterns
    const dynamicRoutePatterns = [
      // Product routes (numeric IDs)
      {
        pattern: /^\/admin\/products\/\d+$/,
        resource: 'products' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/products\/\d+\/edit$/,
        resource: 'products' as AdminResource,
        action: 'update' as AdminAction
      },
      // Order routes (CUID format: cmbkhzdt00...)
      {
        pattern: /^\/admin\/orders\/[a-z0-9]+$/,
        resource: 'orders' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/orders\/[a-z0-9]+\/edit$/,
        resource: 'orders' as AdminResource,
        action: 'update' as AdminAction
      },
      // Customer routes (prefixed IDs: customer_123)
      {
        pattern: /^\/admin\/customers\/customer_\d+$/,
        resource: 'customers' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/customers\/customer_\d+\/edit$/,
        resource: 'customers' as AdminResource,
        action: 'update' as AdminAction
      },
      {
        pattern: /^\/admin\/customers\/customer_\d+\/communication$/,
        resource: 'customers' as AdminResource,
        action: 'read' as AdminAction
      },
      // Review routes (prefixed IDs: review_123)
      {
        pattern: /^\/admin\/reviews\/review_\d+$/,
        resource: 'reviews' as AdminResource,
        action: 'read' as AdminAction
      },
      // User routes (numeric IDs)
      {
        pattern: /^\/admin\/users\/\d+$/,
        resource: 'users' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/users\/\d+\/edit$/,
        resource: 'users' as AdminResource,
        action: 'update' as AdminAction
      },
      // Content management routes
      {
        pattern: /^\/admin\/content\/pages\/create$/,
        resource: 'content' as AdminResource,
        action: 'create' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/pages\/[^\/]+\/edit$/,
        resource: 'content' as AdminResource,
        action: 'update' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/banners\/create$/,
        resource: 'content' as AdminResource,
        action: 'create' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/banners\/[^\/]+\/edit$/,
        resource: 'content' as AdminResource,
        action: 'update' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/banners\/[^\/]+$/,
        resource: 'content' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/promotions\/create$/,
        resource: 'content' as AdminResource,
        action: 'create' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/promotions\/[^\/]+\/edit$/,
        resource: 'content' as AdminResource,
        action: 'update' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/promotions\/[^\/]+$/,
        resource: 'content' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/newsletter\/create$/,
        resource: 'content' as AdminResource,
        action: 'create' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/newsletter\/[^\/]+\/edit$/,
        resource: 'content' as AdminResource,
        action: 'update' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/newsletter\/[^\/]+$/,
        resource: 'content' as AdminResource,
        action: 'read' as AdminAction
      },
      {
        pattern: /^\/admin\/content\/media$/,
        resource: 'content' as AdminResource,
        action: 'read' as AdminAction
      }
    ];

    // Check dynamic route patterns
    for (const routePattern of dynamicRoutePatterns) {
      if (routePattern.pattern.test(route)) {
        return PermissionValidator.validateResourceAccess(
          user,
          routePattern.resource,
          routePattern.action
        ).allowed;
      }
    }

    // If no pattern matches, deny access
    return false;
  }
}
