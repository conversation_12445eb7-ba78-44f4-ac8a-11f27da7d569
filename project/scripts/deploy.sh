#!/bin/bash
# GlowRoya Deployment Script - Updated for Remote VPS Database
# Automated deployment script for production environment

set -e  # Exit on any error

# Configuration
PROJECT_NAME="glowroya"
BACKUP_BEFORE_DEPLOY=true
RUN_MIGRATIONS=true
RESTART_SERVICES=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log "✅ Docker is running"
}

# Check if required files exist
check_requirements() {
    local required_files=(
        "docker-compose.yml"
        "backend/package.json"
        "package.json"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            error "Required file not found: $file"
            exit 1
        fi
    done
    log "✅ All required files found"
}

# Create backup before deployment
create_backup() {
    if [ "$BACKUP_BEFORE_DEPLOY" = true ]; then
        log "Creating backup before deployment..."
        if [ -f "./scripts/backup.sh" ]; then
            ./scripts/backup.sh
            log "✅ Backup completed"
        else
            warning "⚠️ Backup script not found, skipping backup"
        fi
    fi
}

# Test remote database connection
test_database_connection() {
    log "Testing remote database connection..."
    
    # Check if we can connect to the remote database
    if command -v pg_isready >/dev/null 2>&1; then
        if pg_isready -h ************* -p 5432 -U remote_admin -d glowroya >/dev/null 2>&1; then
            log "✅ Remote database connection successful"
        else
            error "❌ Cannot connect to remote database"
            exit 1
        fi
    else
        warning "⚠️ pg_isready not found, skipping database connection test"
    fi
}

# Run database migrations
run_migrations() {
    if [ "$RUN_MIGRATIONS" = true ]; then
        log "Running database migrations..."
        
        # Navigate to backend directory and run migrations
        cd backend
        
        # Install dependencies if needed
        if [ ! -d "node_modules" ]; then
            log "Installing backend dependencies..."
            npm ci
        fi
        
        # Generate Prisma client
        log "Generating Prisma client..."
        npx prisma generate
        
        # Run migrations
        log "Applying database migrations..."
        npx prisma migrate deploy
        
        # Go back to project root
        cd ..
        
        log "✅ Database migrations completed"
    fi
}

# Build and deploy services
deploy_services() {
    log "Building and deploying services..."
    
    # Stop existing services
    log "Stopping existing services..."
    docker-compose down --remove-orphans
    
    # Build frontend
    log "Building frontend..."
    npm ci
    npm run build
    
    # Build and start services
    log "Building and starting Docker services..."
    docker-compose build --no-cache
    docker-compose up -d
    
    log "✅ Services deployed successfully"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3001/health >/dev/null 2>&1; then
            log "✅ Backend service is healthy"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "❌ Backend service failed to start within timeout"
            exit 1
        fi
        
        info "Waiting for backend service... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    # Check frontend
    if curl -f http://localhost >/dev/null 2>&1; then
        log "✅ Frontend service is healthy"
    else
        warning "⚠️ Frontend service may not be ready yet"
    fi
}

# Display deployment summary
show_summary() {
    log "🎉 Deployment completed successfully!"
    log "📊 Deployment Summary:"
    log "   - Project: $PROJECT_NAME"
    log "   - Date: $(date)"
    log "   - Backend API: http://localhost:3001"
    log "   - Frontend: http://localhost"
    log "   - Health Check: http://localhost:3001/health"
    log "   - API Docs: http://localhost:3001/api/v1/docs"
    
    # Show running containers
    log "🐳 Running containers:"
    docker-compose ps
}

# Main deployment process
main() {
    log "🚀 Starting GlowRoya deployment process..."
    
    check_docker
    check_requirements
    create_backup
    test_database_connection
    run_migrations
    deploy_services
    wait_for_services
    show_summary
    
    log "✅ Deployment process completed successfully!"
}

# Handle script interruption
trap 'error "❌ Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
