import React, { ReactNode } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';
import { ChevronUp, ChevronDown, MoreHorizontal, Search } from 'lucide-react';
import AdminButton, { AdminIconButton } from './AdminButton';

export interface AdminTableColumn<T = any> {
  key: string;
  title: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (row: T, index: number, value?: any) => ReactNode;
}

export interface AdminTableProps<T = any> {
  columns: AdminTableColumn<T>[];
  data: T[];
  loading?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (key: string) => void;
  onRowClick?: (row: T, index: number) => void;
  rowActions?: (row: T, index: number) => ReactNode;
  emptyMessage?: string;
  className?: string;
  striped?: boolean;
  hoverable?: boolean;
  compact?: boolean;
  searchable?: boolean;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
}

const AdminTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  sortBy,
  sortOrder,
  onSort,
  onRowClick,
  rowActions,
  emptyMessage = 'هیچ داده‌ای یافت نشد',
  className = '',
  striped = true,
  hoverable = true,
  compact = false,
  searchable = false,
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'جستجو...'
}: AdminTableProps<T>) => {
  const handleSort = (key: string) => {
    if (onSort && columns.find(col => col.key === key)?.sortable) {
      onSort(key);
    }
  };

  const getSortIcon = (key: string) => {
    if (sortBy !== key) return null;
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4" /> : 
      <ChevronDown className="w-4 h-4" />;
  };

  const getCellValue = (row: T, column: AdminTableColumn<T>, index: number) => {
    const value = row[column.key];
    if (column.render) {
      return column.render(row, index, value);
    }
    return value;
  };

  const getAlignmentClass = (align?: string) => {
    switch (align) {
      case 'center': return 'text-center';
      case 'right': return 'text-right';
      default: return 'text-right'; // Default to right for Persian
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${className}`}>
      {/* Search Bar */}
      {searchable && (
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Header */}
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`
                    px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider
                    ${getAlignmentClass(column.align)}
                    ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}
                    ${compact ? 'px-4 py-2' : ''}
                  `}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column.key)}
                >
                  <div className="flex items-center gap-1 justify-end">
                    <span>{column.title}</span>
                    {column.sortable && getSortIcon(column.key)}
                  </div>
                </th>
              ))}
              {rowActions && (
                <th className={`px-6 py-3 text-center ${compact ? 'px-4 py-2' : ''}`}>
                  <span className="sr-only">عملیات</span>
                </th>
              )}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={columns.length + (rowActions ? 1 : 0)} className="px-6 py-12 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
                    <span className="mr-3 text-gray-500">در حال بارگذاری...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length + (rowActions ? 1 : 0)} className="px-6 py-12 text-center text-gray-500">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <motion.tr
                  key={index}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.05 }}
                  className={`
                    ${striped && index % 2 === 1 ? 'bg-gray-50' : ''}
                    ${hoverable ? 'hover:bg-gray-100' : ''}
                    ${onRowClick ? 'cursor-pointer' : ''}
                    transition-colors duration-150
                  `}
                  onClick={() => onRowClick?.(row, index)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`
                        px-6 py-4 whitespace-nowrap text-sm text-gray-900
                        ${getAlignmentClass(column.align)}
                        ${compact ? 'px-4 py-2' : ''}
                      `}
                    >
                      {getCellValue(row, column, index)}
                    </td>
                  ))}
                  {rowActions && (
                    <td className={`px-6 py-4 whitespace-nowrap text-center ${compact ? 'px-4 py-2' : ''}`}>
                      {rowActions(row, index)}
                    </td>
                  )}
                </motion.tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Specialized table components
export const AdminActionMenu: React.FC<{
  actions: Array<{
    label: string;
    onClick: () => void;
    variant?: 'default' | 'danger';
    disabled?: boolean;
  }>;
}> = ({ actions }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [buttonPosition, setButtonPosition] = React.useState({ top: 0, left: 0 });
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const menuWidth = 192; // w-48 = 12rem = 192px
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate horizontal position
      let left = rect.left + window.scrollX - menuWidth + rect.width;

      // Ensure menu doesn't go off the left edge
      if (left < 10) {
        left = rect.right + window.scrollX + 8; // Position to the right
      }

      // Ensure menu doesn't go off the right edge
      if (left + menuWidth > viewportWidth - 10) {
        left = rect.left + window.scrollX - menuWidth - 8; // Position to the left
      }

      // Calculate vertical position
      let top = rect.bottom + window.scrollY + 4;

      // Ensure menu doesn't go off the bottom edge
      const menuHeight = actions.length * 40 + 16; // Approximate height
      if (top + menuHeight > viewportHeight + window.scrollY - 10) {
        top = rect.top + window.scrollY - menuHeight - 4; // Position above
      }

      setButtonPosition({ top, left });
    }

    setIsOpen(!isOpen);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        title="عملیات"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>

      {isOpen && createPortal(
        <>
          <div
            className="fixed inset-0 z-[9999]"
            onClick={handleClose}
          />
          <div
            className="fixed w-48 bg-white rounded-md shadow-xl border border-gray-200 z-[9999]"
            style={{
              top: `${buttonPosition.top + 4}px`,
              left: `${buttonPosition.left}px`
            }}
          >
            <div className="py-1">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick();
                    setIsOpen(false);
                  }}
                  disabled={action.disabled}
                  className={`
                    block w-full text-right px-4 py-2 text-sm transition-colors
                    ${action.variant === 'danger'
                      ? 'text-red-700 hover:bg-red-50'
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                    ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  `}
                >
                  {action.label}
                </button>
              ))}
            </div>
          </div>
        </>,
        document.body
      )}
    </>
  );
};

export const AdminTableBadge: React.FC<{
  children: ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'sm' | 'md';
}> = ({ children, variant = 'default', size = 'sm' }) => {
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800'
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm'
  };

  return (
    <span className={`
      inline-flex items-center rounded-full font-medium
      ${variantClasses[variant]}
      ${sizeClasses[size]}
    `}>
      {children}
    </span>
  );
};

export default AdminTable;
