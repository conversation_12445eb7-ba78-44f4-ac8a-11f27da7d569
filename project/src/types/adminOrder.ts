import { Order, OrderItem, OrderSummary } from './checkout';

export interface AdminOrder extends Order {
  // Enhanced admin fields
  customerInfo: {
    id?: string;
    name: string;
    email: string;
    phone: string;
    totalOrders: number;
    loyaltyTier?: string;
    isVip?: boolean;
  };
  fulfillment: {
    warehouse?: string;
    picker?: string;
    packer?: string;
    shippingCarrier?: string;
    shippingService?: string;
    labelPrinted?: boolean;
    packageWeight?: number;
    packageDimensions?: {
      length: number;
      width: number;
      height: number;
    };
  };
  timeline: OrderTimelineEvent[];
  metrics: {
    processingTime?: number; // minutes
    shippingTime?: number; // days
    customerSatisfaction?: number; // 1-5
    profitMargin?: number;
    costOfGoods?: number;
  };
  flags: {
    isRush: boolean;
    isGift: boolean;
    requiresSignature: boolean;
    isInternational: boolean;
    hasCustoms: boolean;
    isFragile: boolean;
  };
}

export interface OrderTimelineEvent {
  id: string;
  type: 'status_change' | 'note_added' | 'payment' | 'shipping' | 'refund' | 'communication';
  title: string;
  description?: string;
  timestamp: string;
  userId?: string;
  userName?: string;
  metadata?: Record<string, any>;
  isSystemGenerated: boolean;
}

export interface OrderStatusTransition {
  from: Order['status'];
  to: Order['status'];
  label: string;
  description: string;
  requiresConfirmation: boolean;
  allowedRoles: string[];
  autoActions?: string[];
}

export interface OrderFilters {
  status?: Order['status'][];
  priority?: Order['priority'][];
  dateRange?: {
    start: string;
    end: string;
  };
  customerSearch?: string;
  orderNumber?: string;
  minAmount?: number;
  maxAmount?: number;
  paymentMethod?: string[];
  shippingMethod?: string[];
  tags?: string[];
  hasRefund?: boolean;
  isRush?: boolean;
  isGift?: boolean;
}

export interface OrderBulkAction {
  id: string;
  label: string;
  description: string;
  icon: string;
  requiresConfirmation: boolean;
  allowedStatuses: Order['status'][];
  action: (orderIds: string[]) => Promise<void>;
}

export interface OrderExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: string[];
  filters?: OrderFilters;
  dateRange?: {
    start: string;
    end: string;
  };
  includeItems?: boolean;
  includeCustomerInfo?: boolean;
  includeTimeline?: boolean;
}

export interface OrderAnalytics {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: Record<Order['status'], number>;
  ordersByPriority: Record<Order['priority'], number>;
  topProducts: Array<{
    productId: number;
    productName: string;
    quantity: number;
    revenue: number;
  }>;
  customerMetrics: {
    newCustomers: number;
    returningCustomers: number;
    vipCustomers: number;
  };
  fulfillmentMetrics: {
    averageProcessingTime: number;
    averageShippingTime: number;
    onTimeDeliveryRate: number;
  };
  revenueByDay: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
}

export interface ShippingLabel {
  id: string;
  orderId: string;
  trackingNumber: string;
  carrier: string;
  service: string;
  labelUrl: string;
  cost: number;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  createdAt: string;
  createdBy: string;
}

export interface RefundRequest {
  id: string;
  orderId: string;
  amount: number;
  reason: string;
  status: 'pending' | 'approved' | 'processed' | 'rejected';
  requestedBy: string;
  requestedAt: string;
  processedBy?: string;
  processedAt?: string;
  notes?: string;
  refundMethod: 'original_payment' | 'store_credit' | 'bank_transfer';
  items: Array<{
    orderItemId: string;
    quantity: number;
    reason: string;
  }>;
}

// Persian messages for order management
export const PERSIAN_ORDER_MESSAGES = {
  status: {
    pending: 'در انتظار تأیید',
    confirmed: 'تأیید شده',
    processing: 'در حال پردازش',
    shipped: 'ارسال شده',
    delivered: 'تحویل داده شده',
    cancelled: 'لغو شده'
  },
  priority: {
    low: 'کم',
    normal: 'عادی',
    high: 'بالا',
    urgent: 'فوری'
  },
  refundStatus: {
    none: 'بدون مرجوعی',
    requested: 'درخواست مرجوعی',
    approved: 'تأیید شده',
    processed: 'پردازش شده',
    rejected: 'رد شده'
  },
  actions: {
    viewDetails: 'مشاهده جزئیات',
    editOrder: 'ویرایش سفارش',
    updateStatus: 'بروزرسانی وضعیت',
    addNote: 'افزودن یادداشت',
    printLabel: 'چاپ برچسب',
    processRefund: 'پردازش مرجوعی',
    sendEmail: 'ارسال ایمیل',
    exportOrders: 'خروجی سفارشات',
    bulkUpdate: 'بروزرسانی گروهی'
  },
  filters: {
    allOrders: 'همه سفارشات',
    pendingOrders: 'سفارشات در انتظار',
    processingOrders: 'در حال پردازش',
    shippedOrders: 'ارسال شده',
    deliveredOrders: 'تحویل داده شده',
    cancelledOrders: 'لغو شده',
    rushOrders: 'سفارشات فوری',
    giftOrders: 'سفارشات هدیه',
    refundRequests: 'درخواست‌های مرجوعی'
  },
  timeline: {
    orderCreated: 'سفارش ایجاد شد',
    statusChanged: 'وضعیت تغییر کرد',
    noteAdded: 'یادداشت افزوده شد',
    paymentProcessed: 'پرداخت انجام شد',
    labelPrinted: 'برچسب چاپ شد',
    shipped: 'سفارش ارسال شد',
    delivered: 'سفارش تحویل داده شد',
    refundProcessed: 'مرجوعی پردازش شد'
  },
  errors: {
    orderNotFound: 'سفارش یافت نشد',
    invalidStatus: 'وضعیت نامعتبر',
    updateFailed: 'بروزرسانی ناموفق',
    exportFailed: 'خروجی ناموفق',
    refundFailed: 'پردازش مرجوعی ناموفق',
    insufficientPermissions: 'دسترسی کافی ندارید'
  },
  success: {
    orderUpdated: 'سفارش بروزرسانی شد',
    statusChanged: 'وضعیت تغییر کرد',
    noteAdded: 'یادداشت افزوده شد',
    labelPrinted: 'برچسب چاپ شد',
    refundProcessed: 'مرجوعی پردازش شد',
    emailSent: 'ایمیل ارسال شد',
    exportCompleted: 'خروجی تکمیل شد'
  }
};

// Order status workflow configuration
export const ORDER_STATUS_WORKFLOW: OrderStatusTransition[] = [
  {
    from: 'pending',
    to: 'confirmed',
    label: 'تأیید سفارش',
    description: 'تأیید سفارش و شروع پردازش',
    requiresConfirmation: false,
    allowedRoles: ['super_admin', 'admin', 'moderator'],
    autoActions: ['send_confirmation_email']
  },
  {
    from: 'confirmed',
    to: 'processing',
    label: 'شروع پردازش',
    description: 'شروع آماده‌سازی سفارش',
    requiresConfirmation: false,
    allowedRoles: ['super_admin', 'admin', 'moderator'],
    autoActions: ['allocate_inventory']
  },
  {
    from: 'processing',
    to: 'shipped',
    label: 'ارسال سفارش',
    description: 'سفارش ارسال شده است',
    requiresConfirmation: true,
    allowedRoles: ['super_admin', 'admin', 'moderator'],
    autoActions: ['generate_tracking', 'send_shipping_email']
  },
  {
    from: 'shipped',
    to: 'delivered',
    label: 'تحویل سفارش',
    description: 'سفارش تحویل مشتری شده است',
    requiresConfirmation: false,
    allowedRoles: ['super_admin', 'admin'],
    autoActions: ['send_delivery_email', 'request_review']
  },
  {
    from: 'pending',
    to: 'cancelled',
    label: 'لغو سفارش',
    description: 'لغو سفارش',
    requiresConfirmation: true,
    allowedRoles: ['super_admin', 'admin'],
    autoActions: ['refund_payment', 'send_cancellation_email']
  },
  {
    from: 'confirmed',
    to: 'cancelled',
    label: 'لغو سفارش',
    description: 'لغو سفارش تأیید شده',
    requiresConfirmation: true,
    allowedRoles: ['super_admin', 'admin'],
    autoActions: ['refund_payment', 'send_cancellation_email']
  }
];
