/**
 * Generate PWA Icons Script
 * Creates placeholder PWA icons for development
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Icon sizes needed for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create SVG icon template
const createSVGIcon = (size) => {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#eb4d7c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d93861;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)" rx="${size * 0.1}"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size * 0.15}" 
        fill="white" text-anchor="middle" dominant-baseline="middle" font-weight="bold">
    آرامش
  </text>
  <text x="50%" y="${size * 0.7}" font-family="Arial, sans-serif" font-size="${size * 0.1}" 
        fill="white" text-anchor="middle" dominant-baseline="middle">
    پوست
  </text>
</svg>`;
};

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate SVG icons
iconSizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated ${filename}`);
});

// Create a simple PNG placeholder script (for demonstration)
const createPNGPlaceholder = (size) => {
  // This would normally use a library like sharp or canvas to create actual PNG files
  // For now, we'll create a simple HTML file that can be used to generate PNGs manually
  return `<!DOCTYPE html>
<html>
<head>
  <title>PWA Icon Generator</title>
  <style>
    .icon {
      width: ${size}px;
      height: ${size}px;
      background: linear-gradient(135deg, #eb4d7c, #d93861);
      border-radius: ${size * 0.1}px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: Arial, sans-serif;
      font-weight: bold;
    }
    .main-text {
      font-size: ${size * 0.15}px;
      margin-bottom: ${size * 0.05}px;
    }
    .sub-text {
      font-size: ${size * 0.1}px;
    }
  </style>
</head>
<body>
  <div class="icon">
    <div class="main-text">آرامش</div>
    <div class="sub-text">پوست</div>
  </div>
  <script>
    // Instructions for manual PNG generation:
    // 1. Right-click on the icon
    // 2. "Inspect Element"
    // 3. Right-click on the div.icon element in DevTools
    // 4. "Capture node screenshot"
    // 5. Save as icon-${size}x${size}.png
    console.log('To generate PNG: Right-click icon > Inspect > Right-click div.icon > Capture node screenshot');
  </script>
</body>
</html>`;
};

// Generate HTML files for PNG creation
iconSizes.forEach(size => {
  const htmlContent = createPNGPlaceholder(size);
  const filename = `generate-icon-${size}x${size}.html`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, htmlContent);
  console.log(`Generated ${filename} (for PNG creation)`);
});

console.log('\nPWA icons generated successfully!');
console.log('SVG icons are ready to use.');
console.log('For PNG icons, open the HTML files in a browser and follow the instructions.');
