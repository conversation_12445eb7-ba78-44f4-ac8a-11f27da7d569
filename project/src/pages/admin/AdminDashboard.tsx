import React from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  ShoppingBag,
  Package,
  TrendingUp,
  Star,
  MessageSquare,
  DollarSign,
  Activity
} from 'lucide-react';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import SEOHead from '../../components/seo/SEOHead';
import AdminLayout from '../../components/admin/layout/AdminLayout';
import { AdminStatsCard, AdminActionCard } from '../../components/admin/common/AdminCard';

const AdminDashboard: React.FC = () => {
  const { user } = useAdminAuth();

  // Mock dashboard data
  const stats = [
    {
      title: 'کل فروش امروز',
      value: '۱۲,۳۴۵,۶۷۸',
      unit: 'تومان',
      change: '+12.5%',
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'bg-green-500'
    },
    {
      title: 'سفارشات جدید',
      value: '۲۴',
      unit: 'سفارش',
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: ShoppingBag,
      color: 'bg-blue-500'
    },
    {
      title: 'مشتریان جدید',
      value: '۱۸',
      unit: 'نفر',
      change: '+15.3%',
      changeType: 'increase' as const,
      icon: Users,
      color: 'bg-purple-500'
    },
    {
      title: 'محصولات',
      value: '۱۲۳',
      unit: 'محصول',
      change: '+2.1%',
      changeType: 'increase' as const,
      icon: Package,
      color: 'bg-orange-500'
    },
    {
      title: 'نظرات جدید',
      value: '۷',
      unit: 'نظر',
      change: '-3.2%',
      changeType: 'decrease' as const,
      icon: MessageSquare,
      color: 'bg-pink-500'
    },
    {
      title: 'میانگین امتیاز',
      value: '۴.۸',
      unit: 'از ۵',
      change: '+0.2',
      changeType: 'increase' as const,
      icon: Star,
      color: 'bg-yellow-500'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'order',
      message: 'سفارش جدید از علی احمدی',
      time: '۵ دقیقه پیش',
      icon: ShoppingBag,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'review',
      message: 'نظر جدید برای کرم آبرسان',
      time: '۱۰ دقیقه پیش',
      icon: Star,
      color: 'text-yellow-600'
    },
    {
      id: 3,
      type: 'user',
      message: 'عضویت کاربر جدید: مریم کریمی',
      time: '۱۵ دقیقه پیش',
      icon: Users,
      color: 'text-green-600'
    },
    {
      id: 4,
      type: 'product',
      message: 'محصول جدید اضافه شد',
      time: '۳۰ دقیقه پیش',
      icon: Package,
      color: 'text-purple-600'
    }
  ];

  return (
    <>
      <SEOHead
        title="داشبورد مدیریت | گلو رویا"
        description="داشبورد مدیریت فروشگاه گلو رویا"
        robots="noindex, nofollow"
      />

      <AdminLayout
        title="داشبورد مدیریت"
        subtitle={`خوش آمدید ${user?.firstName} ${user?.lastName}`}
        actions={
          <div className="text-right">
            <p className="text-sm text-gray-500">
              آخرین بروزرسانی
            </p>
            <p className="text-sm font-medium text-gray-900">
              {new Date().toLocaleDateString('fa-IR')} - {new Date().toLocaleTimeString('fa-IR')}
            </p>
          </div>
        }
      >

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <AdminStatsCard
                title={stat.title}
                value={stat.value}
                unit={stat.unit}
                change={stat.change}
                changeType={stat.changeType}
                icon={stat.icon}
                color={stat.color}
              />
            </motion.div>
          ))}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activities */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                فعالیت‌های اخیر
              </h3>
              <Activity className="w-5 h-5 text-gray-400" />
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => {
                const Icon = activity.icon;
                return (
                  <div key={activity.id} className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg bg-gray-100`}>
                      <Icon className={`w-4 h-4 ${activity.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                عملیات سریع
              </h3>
              <TrendingUp className="w-5 h-5 text-gray-400" />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <AdminActionCard
                title="افزودن محصول"
                description="محصول جدید اضافه کنید"
                icon={Package}
                onClick={() => console.log('Navigate to add product')}
              />
              <AdminActionCard
                title="مدیریت سفارشات"
                description="مشاهده و مدیریت سفارشات"
                icon={ShoppingBag}
                onClick={() => console.log('Navigate to orders')}
                badge="جدید"
              />
              <AdminActionCard
                title="مشتریان"
                description="مدیریت اطلاعات مشتریان"
                icon={Users}
                onClick={() => console.log('Navigate to customers')}
              />
              <AdminActionCard
                title="نظرات"
                description="بررسی و تأیید نظرات"
                icon={MessageSquare}
                onClick={() => console.log('Navigate to reviews')}
              />
            </div>
          </motion.div>
        </div>
      </AdminLayout>
    </>
  );
};

export default AdminDashboard;
