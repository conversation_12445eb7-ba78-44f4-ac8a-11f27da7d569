import https from 'https';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { promisify } from 'util';

const mkdir = promisify(fs.mkdir);

interface ProductImage {
  url: string;
  filename: string;
  category: string;
  alt: string;
}

class ProductImageDownloader {
  private uploadsPath: string;
  private productImages: ProductImage[];

  constructor() {
    this.uploadsPath = path.resolve(__dirname, '../uploads');
    this.productImages = [
      {
        url: 'https://images.pexels.com/photos/4465124/pexels-photo-4465124.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'hyaluronic-acid-serum-1.jpg',
        category: 'serums',
        alt: 'سرم هیالورونیک اسید'
      },
      {
        url: 'https://images.pexels.com/photos/3685523/pexels-photo-3685523.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'bb-cream-spf-30-1.jpg',
        category: 'creams',
        alt: 'کرم BB با SPF 30'
      },
      {
        url: 'https://images.pexels.com/photos/3762879/pexels-photo-3762879.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'night-revitalizing-mask-1.jpg',
        category: 'masks',
        alt: 'ماسک شب احیاکننده'
      },
      {
        url: 'https://images.pexels.com/photos/8128069/pexels-photo-8128069.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'gentle-cleansing-gel-1.jpg',
        category: 'cleansers',
        alt: 'ژل شستشوی ملایم'
      },
      {
        url: 'https://images.pexels.com/photos/5069610/pexels-photo-5069610.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'vitamin-c-serum-1.jpg',
        category: 'serums',
        alt: 'سرم ویتامین C'
      },
      {
        url: 'https://images.pexels.com/photos/3788612/pexels-photo-3788612.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'rose-toner-1.jpg',
        category: 'toners',
        alt: 'تونر آبرسان گل رز'
      },
      {
        url: 'https://images.pexels.com/photos/6621462/pexels-photo-6621462.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'eye-cream-1.jpg',
        category: 'creams',
        alt: 'کرم دور چشم'
      },
      {
        url: 'https://images.pexels.com/photos/7262404/pexels-photo-7262404.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1',
        filename: 'cleansing-oil-1.jpg',
        category: 'cleansers',
        alt: 'روغن پاک کننده'
      }
    ];
  }

  async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await mkdir(dirPath, { recursive: true });
      console.log(`✅ Directory ensured: ${dirPath}`);
    } catch (error) {
      if ((error as any).code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async downloadImage(url: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(outputPath);
      
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }

        response.pipe(file);

        file.on('finish', () => {
          file.close();
          resolve();
        });

        file.on('error', (error) => {
          fs.unlink(outputPath, () => {}); // Delete partial file
          reject(error);
        });
      }).on('error', (error) => {
        reject(error);
      });
    });
  }

  async optimizeImage(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Create multiple optimized versions
      const baseDir = path.dirname(outputPath);
      const baseName = path.basename(outputPath, path.extname(outputPath));
      
      // Original optimized version (800x600)
      await sharp(inputPath)
        .resize(800, 600, { 
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ 
          quality: 85,
          progressive: true
        })
        .toFile(outputPath);

      // Thumbnail version (300x225)
      const thumbPath = path.join(baseDir, `${baseName}-thumb.jpg`);
      await sharp(inputPath)
        .resize(300, 225, { 
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ 
          quality: 80,
          progressive: true
        })
        .toFile(thumbPath);

      // WebP version for modern browsers
      const webpPath = path.join(baseDir, `${baseName}.webp`);
      await sharp(inputPath)
        .resize(800, 600, { 
          fit: 'cover',
          position: 'center'
        })
        .webp({ 
          quality: 85,
          effort: 6
        })
        .toFile(webpPath);

      console.log(`✅ Optimized: ${path.basename(outputPath)} (+ thumb + webp)`);
      
    } catch (error) {
      console.error(`❌ Failed to optimize ${inputPath}:`, (error as Error).message);
      throw error;
    }
  }

  async downloadAndOptimizeImages(): Promise<void> {
    console.log('🖼️  Starting product images download and optimization...');
    
    for (const image of this.productImages) {
      try {
        const categoryDir = path.join(this.uploadsPath, 'products', image.category);
        await this.ensureDirectoryExists(categoryDir);

        const tempPath = path.join(categoryDir, `temp-${image.filename}`);
        const finalPath = path.join(categoryDir, image.filename);

        console.log(`📥 Downloading: ${image.alt}...`);
        await this.downloadImage(image.url, tempPath);

        console.log(`🔧 Optimizing: ${image.alt}...`);
        await this.optimizeImage(tempPath, finalPath);

        // Clean up temp file
        fs.unlinkSync(tempPath);

        console.log(`✅ Completed: ${image.filename}`);
        
      } catch (error) {
        console.error(`❌ Failed to process ${image.filename}:`, (error as Error).message);
      }
    }
  }

  async createImageManifest(): Promise<void> {
    console.log('📋 Creating image manifest...');
    
    const manifest = {
      generated: new Date().toISOString(),
      images: this.productImages.map(img => ({
        filename: img.filename,
        category: img.category,
        alt: img.alt,
        url: `/uploads/products/${img.category}/${img.filename}`,
        thumbnailUrl: `/uploads/products/${img.category}/${path.basename(img.filename, '.jpg')}-thumb.jpg`,
        webpUrl: `/uploads/products/${img.category}/${path.basename(img.filename, '.jpg')}.webp`,
      })),
      categories: [...new Set(this.productImages.map(img => img.category))],
      totalImages: this.productImages.length
    };

    const manifestPath = path.join(this.uploadsPath, 'image-manifest.json');
    await fs.promises.writeFile(manifestPath, JSON.stringify(manifest, null, 2), 'utf8');
    
    console.log('✅ Image manifest created');
  }

  async run(): Promise<void> {
    try {
      await this.ensureDirectoryExists(this.uploadsPath);
      await this.downloadAndOptimizeImages();
      await this.createImageManifest();

      console.log('\n✅ Product Images Download Completed!');
      console.log('📊 Summary:');
      console.log(`   - Downloaded: ${this.productImages.length} product images`);
      console.log(`   - Categories: ${[...new Set(this.productImages.map(img => img.category))].join(', ')}`);
      console.log(`   - Formats: JPEG (original), JPEG (thumbnail), WebP (modern)`);
      console.log(`   - Optimization: Progressive JPEG, WebP compression`);
      console.log(`   - Manifest: Created at uploads/image-manifest.json`);
      
    } catch (error) {
      console.error('\n❌ Download failed:', (error as Error).message);
      process.exit(1);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const downloader = new ProductImageDownloader();
  downloader.run().catch(console.error);
}

export default ProductImageDownloader;
