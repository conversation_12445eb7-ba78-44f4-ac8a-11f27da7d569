import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Award, Truck, RotateCcw, Phone, CheckCircle } from 'lucide-react';

interface TrustBadge {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

interface TrustBadgesProps {
  variant?: 'horizontal' | 'vertical' | 'grid';
  showDescriptions?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const TrustBadges: React.FC<TrustBadgesProps> = ({
  variant = 'horizontal',
  showDescriptions = true,
  size = 'medium'
}) => {
  const badges: TrustBadge[] = [
    {
      id: 'authenticity',
      title: 'ضمانت اصالت کالا',
      description: 'تمامی محصولات اصل و با ضمانت معتبر',
      icon: <Shield className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      id: 'quality',
      title: 'کیفیت تضمینی',
      description: 'بالاترین استانداردهای کیفی',
      icon: <Award className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'shipping',
      title: 'ارسال سریع',
      description: 'ارسال رایگان برای خریدهای بالای ۵۰۰ هزار تومان',
      icon: <Truck className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'return',
      title: 'ضمانت بازگشت',
      description: '۷ روز ضمانت بازگشت کالا',
      icon: <RotateCcw className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      id: 'support',
      title: 'پشتیبانی ۲۴/۷',
      description: 'پشتیبانی همه روزه از ساعت ۸ تا ۲۲',
      icon: <Phone className="w-6 h-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    {
      id: 'verified',
      title: 'فروشگاه معتبر',
      description: 'دارای مجوزهای لازم از وزارت بهداشت',
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    }
  ];

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'p-3',
          icon: 'w-4 h-4',
          title: 'text-xs font-medium',
          description: 'text-xs'
        };
      case 'large':
        return {
          container: 'p-6',
          icon: 'w-8 h-8',
          title: 'text-lg font-semibold',
          description: 'text-sm'
        };
      default:
        return {
          container: 'p-4',
          icon: 'w-6 h-6',
          title: 'text-sm font-medium',
          description: 'text-xs'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const getLayoutClasses = () => {
    switch (variant) {
      case 'vertical':
        return 'flex flex-col space-y-4';
      case 'grid':
        return 'grid grid-cols-2 md:grid-cols-3 gap-4';
      default:
        return 'flex flex-wrap justify-center gap-4';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <motion.div
      className={getLayoutClasses()}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {badges.map((badge, index) => (
        <motion.div
          key={badge.id}
          variants={itemVariants}
          className={`
            ${badge.bgColor} ${sizeClasses.container}
            rounded-lg border border-gray-100 
            hover:shadow-md transition-all duration-200
            ${variant === 'grid' ? 'text-center' : 'flex items-center gap-3'}
          `}
          whileHover={{ scale: 1.02 }}
        >
          <div className={`${badge.color} ${variant === 'grid' ? 'mx-auto mb-2' : ''}`}>
            {React.cloneElement(badge.icon as React.ReactElement, {
              className: sizeClasses.icon
            })}
          </div>
          
          <div className={variant === 'grid' ? 'text-center' : 'flex-1'}>
            <h4 className={`${sizeClasses.title} text-text-primary mb-1`}>
              {badge.title}
            </h4>
            {showDescriptions && (
              <p className={`${sizeClasses.description} text-text-secondary leading-relaxed`}>
                {badge.description}
              </p>
            )}
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default TrustBadges;
