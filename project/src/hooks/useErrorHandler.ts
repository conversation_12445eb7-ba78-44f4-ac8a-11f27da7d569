import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { errorReporter, ErrorContext, PERSIAN_ERROR_MESSAGES } from '../utils/errorReporting';

// Types
export interface ErrorState {
  hasError: boolean;
  error: Error | null;
  errorId: string | null;
  isRetrying: boolean;
  retryCount: number;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  toastDuration?: number;
  maxRetries?: number;
  retryDelay?: number;
  component?: string;
  onError?: (error: Error, errorId: string) => void;
  onRetry?: (retryCount: number) => void;
  onMaxRetriesReached?: (error: Error) => void;
}

export interface UseErrorHandlerReturn {
  errorState: ErrorState;
  handleError: (error: Error, context?: ErrorContext) => string;
  handleAsyncError: <T>(
    asyncFn: () => Promise<T>,
    context?: ErrorContext
  ) => Promise<T | null>;
  clearError: () => void;
  retry: () => Promise<void>;
  reportError: (error: Error, context?: ErrorContext) => string;
  reportWarning: (message: string, context?: ErrorContext) => string;
}

// Default options
const DEFAULT_OPTIONS: Required<ErrorHandlerOptions> = {
  showToast: true,
  toastDuration: 4000,
  maxRetries: 3,
  retryDelay: 1000,
  component: 'Unknown Component',
  onError: () => {},
  onRetry: () => {},
  onMaxRetriesReached: () => {}
};

/**
 * Custom hook for comprehensive error handling
 */
export const useErrorHandler = (options: ErrorHandlerOptions = {}): UseErrorHandlerReturn => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    errorId: null,
    isRetrying: false,
    retryCount: 0
  });

  const [lastFailedAction, setLastFailedAction] = useState<(() => Promise<void>) | null>(null);

  // Get user-friendly error message
  const getErrorMessage = useCallback((error: Error): string => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return PERSIAN_ERROR_MESSAGES.network;
    }
    
    if (message.includes('timeout')) {
      return PERSIAN_ERROR_MESSAGES.timeout;
    }
    
    if (message.includes('401') || message.includes('unauthorized')) {
      return PERSIAN_ERROR_MESSAGES.unauthorized;
    }
    
    if (message.includes('403') || message.includes('forbidden')) {
      return PERSIAN_ERROR_MESSAGES.forbidden;
    }
    
    if (message.includes('404') || message.includes('not found')) {
      return PERSIAN_ERROR_MESSAGES.notFound;
    }
    
    if (message.includes('500') || message.includes('server')) {
      return PERSIAN_ERROR_MESSAGES.serverError;
    }
    
    if (message.includes('validation')) {
      return PERSIAN_ERROR_MESSAGES.validation;
    }
    
    if (message.includes('permission')) {
      return PERSIAN_ERROR_MESSAGES.permission;
    }
    
    return error.message || PERSIAN_ERROR_MESSAGES.unknown;
  }, []);

  // Handle error
  const handleError = useCallback((error: Error, context: ErrorContext = {}): string => {
    const errorId = errorReporter.reportError(error, {
      component: config.component,
      ...context
    });

    setErrorState(prev => ({
      ...prev,
      hasError: true,
      error,
      errorId,
      retryCount: prev.retryCount
    }));

    // Show toast notification
    if (config.showToast) {
      const userMessage = getErrorMessage(error);
      toast.error(userMessage, {
        duration: config.toastDuration,
        id: errorId // Prevent duplicate toasts
      });
    }

    // Call custom error handler
    config.onError(error, errorId);

    return errorId;
  }, [config, getErrorMessage]);

  // Handle async operations with error handling
  const handleAsyncError = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    context: ErrorContext = {}
  ): Promise<T | null> => {
    try {
      setErrorState(prev => ({ ...prev, hasError: false, error: null, errorId: null }));
      const result = await asyncFn();
      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      
      // Store the failed action for retry
      setLastFailedAction(() => async () => {
        await asyncFn();
      });
      
      handleError(err, context);
      return null;
    }
  }, [handleError]);

  // Clear error state
  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorId: null,
      isRetrying: false,
      retryCount: 0
    });
    setLastFailedAction(null);
  }, []);

  // Retry failed action
  const retry = useCallback(async (): Promise<void> => {
    if (!lastFailedAction || errorState.retryCount >= config.maxRetries) {
      if (errorState.error) {
        config.onMaxRetriesReached(errorState.error);
      }
      return;
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }));

    config.onRetry(errorState.retryCount + 1);

    try {
      // Add delay before retry
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      
      await lastFailedAction();
      
      // Success - clear error state
      clearError();
      
      toast.success('عملیات با موفقیت انجام شد', {
        duration: 2000
      });
      
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      
      setErrorState(prev => ({
        ...prev,
        isRetrying: false,
        error: err
      }));

      // Report retry failure
      errorReporter.reportError(err, {
        component: config.component,
        action: 'retry',
        extra: {
          retryCount: errorState.retryCount + 1,
          maxRetries: config.maxRetries
        }
      });

      if (errorState.retryCount + 1 >= config.maxRetries) {
        config.onMaxRetriesReached(err);
        toast.error('عملیات پس از چندین تلاش ناموفق بود', {
          duration: config.toastDuration
        });
      }
    }
  }, [lastFailedAction, errorState, config, clearError]);

  // Report error manually
  const reportError = useCallback((error: Error, context: ErrorContext = {}): string => {
    return errorReporter.reportError(error, {
      component: config.component,
      ...context
    });
  }, [config.component]);

  // Report warning manually
  const reportWarning = useCallback((message: string, context: ErrorContext = {}): string => {
    return errorReporter.reportWarning(message, {
      component: config.component,
      ...context
    });
  }, [config.component]);

  // Auto-clear error after some time (optional)
  useEffect(() => {
    if (errorState.hasError && !errorState.isRetrying) {
      const timer = setTimeout(() => {
        if (errorState.retryCount === 0) {
          clearError();
        }
      }, 30000); // Clear after 30 seconds if no retry attempted

      return () => clearTimeout(timer);
    }
  }, [errorState.hasError, errorState.isRetrying, errorState.retryCount, clearError]);

  return {
    errorState,
    handleError,
    handleAsyncError,
    clearError,
    retry,
    reportError,
    reportWarning
  };
};

// Hook for API error handling
export const useApiErrorHandler = (component: string = 'API') => {
  const { handleAsyncError, ...rest } = useErrorHandler({
    component,
    showToast: true,
    maxRetries: 2,
    retryDelay: 2000
  });

  const handleApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T | null> => {
    return handleAsyncError(apiCall, {
      action: 'API Call',
      ...context
    });
  }, [handleAsyncError]);

  return {
    ...rest,
    handleApiCall
  };
};

// Hook for form error handling
export const useFormErrorHandler = (formName: string = 'Form') => {
  const { handleError, reportWarning, ...rest } = useErrorHandler({
    component: formName,
    showToast: true,
    maxRetries: 1
  });

  const handleValidationError = useCallback((errors: Record<string, string>) => {
    const errorMessage = Object.values(errors).join(', ');
    const validationError = new Error(`Validation failed: ${errorMessage}`);
    
    return handleError(validationError, {
      action: 'Form Validation',
      extra: { validationErrors: errors }
    });
  }, [handleError]);

  const handleSubmissionError = useCallback((error: Error) => {
    return handleError(error, {
      action: 'Form Submission'
    });
  }, [handleError]);

  return {
    ...rest,
    handleError,
    handleValidationError,
    handleSubmissionError,
    reportWarning
  };
};

export default useErrorHandler;
