// Settings data types for admin configuration
export interface GeneralSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  supportEmail: string;
  phone: string;
  address: string;
  timezone: string;
  language: string;
  currency: string;
  dateFormat: string;
  timeFormat: string;
  maintenanceMode: boolean;
  maintenanceMessage: string;
  allowRegistration: boolean;
  requireEmailVerification: boolean;
  defaultUserRole: string;
  maxLoginAttempts: number;
  sessionTimeout: number;
  enableTwoFactor: boolean;
}

export interface PaymentSettings {
  enabledMethods: PaymentMethod[];
  defaultMethod: string;
  currency: string;
  taxRate: number;
  shippingTaxIncluded: boolean;
  pricesIncludeTax: boolean;
  roundingPrecision: number;
  minimumOrderAmount: number;
  maximumOrderAmount: number;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'bank_card' | 'wallet' | 'cash_on_delivery';
  enabled: boolean;
  testMode: boolean;
  configuration: {
    merchantId?: string;
    apiKey?: string;
    secretKey?: string;
    webhookUrl?: string;
    [key: string]: any;
  };
  fees: {
    fixed: number;
    percentage: number;
  };
  limits: {
    minimum: number;
    maximum: number;
  };
  supportedCurrencies: string[];
  description: string;
  icon?: string;
}

export interface ShippingSettings {
  enabledMethods: ShippingMethod[];
  defaultMethod: string;
  freeShippingThreshold: number;
  enableFreeShipping: boolean;
  weightUnit: 'kg' | 'g';
  dimensionUnit: 'cm' | 'm';
  originAddress: Address;
  enableShippingZones: boolean;
  enableShippingCalculator: boolean;
  maxPackageWeight: number;
  maxPackageDimensions: {
    length: number;
    width: number;
    height: number;
  };
}

export interface ShippingMethod {
  id: string;
  name: string;
  type: 'flat_rate' | 'weight_based' | 'zone_based' | 'free';
  enabled: boolean;
  cost: number;
  freeThreshold?: number;
  estimatedDays: {
    min: number;
    max: number;
  };
  zones: string[];
  weightRanges?: WeightRange[];
  description: string;
  icon?: string;
}

export interface WeightRange {
  min: number;
  max: number;
  cost: number;
}

export interface Address {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
}

export interface NotificationSettings {
  emailNotifications: EmailNotificationSettings;
  smsNotifications: SmsNotificationSettings;
  pushNotifications: PushNotificationSettings;
  adminNotifications: AdminNotificationSettings;
  templates: NotificationTemplate[];
}

export interface EmailNotificationSettings {
  enabled: boolean;
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpEncryption: 'none' | 'tls' | 'ssl';
  fromEmail: string;
  fromName: string;
  replyToEmail: string;
  enableHtml: boolean;
  enableTracking: boolean;
}

export interface SmsNotificationSettings {
  enabled: boolean;
  provider: string;
  apiKey: string;
  apiSecret: string;
  fromNumber: string;
  enableDeliveryReports: boolean;
}

export interface PushNotificationSettings {
  enabled: boolean;
  firebaseServerKey: string;
  vapidPublicKey: string;
  vapidPrivateKey: string;
  enableBrowserNotifications: boolean;
  enableMobileNotifications: boolean;
}

export interface AdminNotificationSettings {
  newOrderNotification: boolean;
  lowStockNotification: boolean;
  newReviewNotification: boolean;
  newCustomerNotification: boolean;
  systemErrorNotification: boolean;
  dailyReportNotification: boolean;
  weeklyReportNotification: boolean;
  monthlyReportNotification: boolean;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'push';
  event: string;
  subject: string;
  content: string;
  variables: string[];
  enabled: boolean;
  language: string;
}

export interface SecuritySettings {
  enableSsl: boolean;
  forceHttps: boolean;
  enableCsrfProtection: boolean;
  enableRateLimiting: boolean;
  maxRequestsPerMinute: number;
  enableIpWhitelist: boolean;
  whitelistedIps: string[];
  enableIpBlacklist: boolean;
  blacklistedIps: string[];
  passwordPolicy: PasswordPolicy;
  sessionSecurity: SessionSecurity;
  enableAuditLog: boolean;
  auditLogRetentionDays: number;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventCommonPasswords: boolean;
  passwordHistoryCount: number;
  passwordExpiryDays: number;
}

export interface SessionSecurity {
  sessionTimeout: number;
  enableRememberMe: boolean;
  rememberMeDuration: number;
  enableConcurrentSessions: boolean;
  maxConcurrentSessions: number;
  enableSessionRotation: boolean;
}

export interface TaxSettings {
  enableTax: boolean;
  defaultTaxRate: number;
  taxIncludedInPrices: boolean;
  enableTaxByLocation: boolean;
  taxRules: TaxRule[];
  taxClasses: TaxClass[];
  enableTaxExemptions: boolean;
  exemptUserRoles: string[];
}

export interface TaxRule {
  id: string;
  name: string;
  rate: number;
  provinces: string[];
  cities: string[];
  productCategories: string[];
  enabled: boolean;
}

export interface TaxClass {
  id: string;
  name: string;
  description: string;
  rate: number;
  enabled: boolean;
}

export interface BackupSettings {
  enableAutoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  backupTime: string;
  retentionDays: number;
  backupLocation: 'local' | 'cloud';
  cloudProvider?: 'aws' | 'google' | 'azure';
  cloudConfiguration?: {
    accessKey: string;
    secretKey: string;
    bucket: string;
    region: string;
  };
  enableDatabaseBackup: boolean;
  enableFileBackup: boolean;
  enableConfigBackup: boolean;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  encryptionKey?: string;
}

export interface SettingsFormData {
  general?: Partial<GeneralSettings>;
  payment?: Partial<PaymentSettings>;
  shipping?: Partial<ShippingSettings>;
  notifications?: Partial<NotificationSettings>;
  security?: Partial<SecuritySettings>;
  tax?: Partial<TaxSettings>;
  backup?: Partial<BackupSettings>;
}

// Persian labels for settings
export const SETTINGS_LABELS = {
  general: {
    siteName: 'نام سایت',
    siteDescription: 'توضیحات سایت',
    siteUrl: 'آدرس سایت',
    adminEmail: 'ایمیل مدیر',
    supportEmail: 'ایمیل پشتیبانی',
    phone: 'شماره تلفن',
    address: 'آدرس',
    timezone: 'منطقه زمانی',
    language: 'زبان',
    currency: 'واحد پول',
    dateFormat: 'فرمت تاریخ',
    timeFormat: 'فرمت زمان',
    maintenanceMode: 'حالت تعمیر',
    allowRegistration: 'امکان ثبت‌نام',
    requireEmailVerification: 'تأیید ایمیل الزامی'
  },
  payment: {
    enabledMethods: 'روش‌های پرداخت فعال',
    defaultMethod: 'روش پیش‌فرض',
    currency: 'واحد پول',
    taxRate: 'نرخ مالیات',
    minimumOrderAmount: 'حداقل مبلغ سفارش'
  },
  shipping: {
    enabledMethods: 'روش‌های ارسال فعال',
    freeShippingThreshold: 'آستانه ارسال رایگان',
    weightUnit: 'واحد وزن',
    dimensionUnit: 'واحد ابعاد'
  },
  notifications: {
    emailNotifications: 'اعلان‌های ایمیل',
    smsNotifications: 'اعلان‌های پیامک',
    pushNotifications: 'اعلان‌های فوری'
  }
};
