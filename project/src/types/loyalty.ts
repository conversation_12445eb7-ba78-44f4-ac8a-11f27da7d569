export interface LoyaltyMember {
  id: string;
  userId: string;
  membershipNumber: string;
  tier: LoyaltyTier;
  points: number;
  totalEarned: number;
  totalSpent: number;
  joinDate: string;
  lastActivity: string;
  isActive: boolean;
  benefits: LoyaltyBenefit[];
  nextTierProgress: {
    currentPoints: number;
    requiredPoints: number;
    percentage: number;
  };
}

export interface LoyaltyTier {
  id: string;
  name: string;
  persianName: string;
  level: number;
  minPoints: number;
  maxPoints?: number;
  color: string;
  icon: string;
  benefits: string[];
  discountPercentage: number;
  freeShippingThreshold: number;
  birthdayBonus: number;
  description: string;
}

export interface LoyaltyBenefit {
  id: string;
  type: 'discount' | 'free_shipping' | 'early_access' | 'birthday_bonus' | 'exclusive_product';
  title: string;
  description: string;
  value: number;
  isActive: boolean;
  expiryDate?: string;
  usageLimit?: number;
  usedCount: number;
}

export interface PointTransaction {
  id: string;
  memberId: string;
  type: 'earned' | 'redeemed' | 'expired' | 'bonus';
  points: number;
  description: string;
  orderId?: string;
  createdAt: string;
  expiryDate?: string;
}

export interface LoyaltyReward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  type: 'discount' | 'product' | 'shipping' | 'experience';
  value: number;
  isAvailable: boolean;
  stock?: number;
  image?: string;
  validUntil?: string;
  terms: string[];
}

export interface LoyaltyContextType {
  member: LoyaltyMember | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  joinProgram: () => Promise<void>;
  earnPoints: (orderId: string, amount: number) => Promise<void>;
  redeemReward: (rewardId: string) => Promise<void>;
  getPointHistory: () => Promise<PointTransaction[]>;
  getAvailableRewards: () => Promise<LoyaltyReward[]>;
  calculatePointsForOrder: (orderAmount: number) => number;
  checkTierUpgrade: () => Promise<void>;
}

// Persian loyalty program constants
export const LOYALTY_TIERS: LoyaltyTier[] = [
  {
    id: 'bronze',
    name: 'Bronze',
    persianName: 'برنزی',
    level: 1,
    minPoints: 0,
    maxPoints: 999,
    color: '#CD7F32',
    icon: '🥉',
    benefits: [
      'کسب ۱ امتیاز به ازای هر ۱۰ هزار تومان خرید',
      'اطلاع از تخفیف‌های ویژه',
      'پشتیبانی اولویت‌دار'
    ],
    discountPercentage: 0,
    freeShippingThreshold: 500000,
    birthdayBonus: 50,
    description: 'عضویت پایه در باشگاه مشتریان گلورویا'
  },
  {
    id: 'silver',
    name: 'Silver',
    persianName: 'نقره‌ای',
    level: 2,
    minPoints: 1000,
    maxPoints: 2999,
    color: '#C0C0C0',
    icon: '🥈',
    benefits: [
      'کسب ۱.۵ امتیاز به ازای هر ۱۰ هزار تومان خرید',
      '۵٪ تخفیف در تمام خریدها',
      'ارسال رایگان برای خریدهای بالای ۳۰۰ هزار تومان',
      'دسترسی زودهنگام به محصولات جدید'
    ],
    discountPercentage: 5,
    freeShippingThreshold: 300000,
    birthdayBonus: 100,
    description: 'عضویت نقره‌ای با مزایای ویژه'
  },
  {
    id: 'gold',
    name: 'Gold',
    persianName: 'طلایی',
    level: 3,
    minPoints: 3000,
    maxPoints: 7999,
    color: '#FFD700',
    icon: '🥇',
    benefits: [
      'کسب ۲ امتیاز به ازای هر ۱۰ هزار تومان خرید',
      '۱۰٪ تخفیف در تمام خریدها',
      'ارسال رایگان برای تمام خریدها',
      'هدیه تولد ویژه',
      'مشاوره رایگان پوست'
    ],
    discountPercentage: 10,
    freeShippingThreshold: 0,
    birthdayBonus: 200,
    description: 'عضویت طلایی با بیشترین مزایا'
  },
  {
    id: 'platinum',
    name: 'Platinum',
    persianName: 'پلاتینی',
    level: 4,
    minPoints: 8000,
    color: '#E5E4E2',
    icon: '💎',
    benefits: [
      'کسب ۳ امتیاز به ازای هر ۱۰ هزار تومان خرید',
      '۱۵٪ تخفیف در تمام خریدها',
      'ارسال رایگان و سریع',
      'دسترسی به محصولات اکسکلوسیو',
      'مشاوره اختصاصی',
      'هدایای ویژه فصلی'
    ],
    discountPercentage: 15,
    freeShippingThreshold: 0,
    birthdayBonus: 500,
    description: 'بالاترین سطح عضویت با امتیازات اکسکلوسیو'
  }
];

export const LOYALTY_REWARDS: LoyaltyReward[] = [
  {
    id: 'discount-50k',
    title: 'تخفیف ۵۰ هزار تومانی',
    description: 'کد تخفیف ۵۰ هزار تومانی برای خرید بعدی',
    pointsCost: 500,
    type: 'discount',
    value: 50000,
    isAvailable: true,
    terms: [
      'حداقل خرید ۲۰۰ هزار تومان',
      'قابل استفاده تا ۳۰ روز',
      'غیرقابل تجمیع با سایر تخفیف‌ها'
    ]
  },
  {
    id: 'free-shipping',
    title: 'ارسال رایگان',
    description: 'ارسال رایگان برای سفارش بعدی',
    pointsCost: 200,
    type: 'shipping',
    value: 25000,
    isAvailable: true,
    terms: [
      'قابل استفاده برای یک سفارش',
      'اعتبار ۱۵ روزه'
    ]
  },
  {
    id: 'sample-kit',
    title: 'کیت نمونه محصولات',
    description: 'کیت نمونه شامل ۵ محصول پرطرفدار',
    pointsCost: 800,
    type: 'product',
    value: 150000,
    isAvailable: true,
    stock: 50,
    terms: [
      'ارسال رایگان',
      'محدود به یک عدد در ماه'
    ]
  },
  {
    id: 'consultation',
    title: 'مشاوره تخصصی پوست',
    description: 'جلسه مشاوره آنلاین با متخصص پوست',
    pointsCost: 1000,
    type: 'experience',
    value: 200000,
    isAvailable: true,
    stock: 10,
    terms: [
      'رزرو قبلی الزامی',
      'مدت جلسه ۳۰ دقیقه',
      'امکان ضبط جلسه'
    ]
  }
];

export const POINT_EARNING_RULES = {
  purchaseMultiplier: {
    bronze: 1,
    silver: 1.5,
    gold: 2,
    platinum: 3
  },
  baseRate: 10000, // 1 point per 10,000 tomans
  bonusActivities: {
    firstPurchase: 100,
    review: 20,
    referral: 200,
    birthday: 100,
    socialShare: 10
  },
  pointExpiry: 365 // days
};

export const PERSIAN_LOYALTY_MESSAGES = {
  welcome: {
    title: 'به باشگاه مشتریان گلورویا خوش آمدید!',
    description: 'با هر خرید امتیاز کسب کنید و از مزایای ویژه بهره‌مند شوید',
    firstBonus: 'هدیه عضویت: ۱۰۰ امتیاز'
  },
  tierUpgrade: {
    title: 'تبریک! سطح عضویت شما ارتقا یافت',
    description: 'اکنون از مزایای بیشتری بهره‌مند خواهید شد'
  },
  pointsEarned: {
    title: 'امتیاز جدید دریافت کردید!',
    description: '{points} امتیاز به حساب شما اضافه شد'
  },
  rewardRedeemed: {
    title: 'جایزه با موفقیت دریافت شد',
    description: 'جایزه انتخابی شما آماده استفاده است'
  },
  errors: {
    insufficientPoints: 'امتیاز کافی برای دریافت این جایزه ندارید',
    rewardUnavailable: 'این جایزه در حال حاضر موجود نیست',
    networkError: 'خطا در ارتباط با سرور'
  }
};

export const LOYALTY_FEATURES = {
  pointsPerToman: 0.0001, // 1 point per 10,000 tomans
  minimumRedemption: 100,
  maxPointsPerOrder: 1000,
  referralBonus: 200,
  reviewBonus: 20,
  birthdayBonusMultiplier: 2
};
