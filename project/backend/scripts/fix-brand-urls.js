const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixBrandUrls() {
  try {
    console.log('🔧 Fixing brand logo URLs...');
    
    // Get all brands with logo URLs
    const brands = await prisma.brand.findMany({
      where: {
        logo: {
          not: null
        }
      }
    });

    console.log(`Found ${brands.length} brands with logos`);

    for (const brand of brands) {
      if (brand.logo && brand.logo.includes('localhost:5000')) {
        const newLogoUrl = brand.logo.replace('localhost:5000', 'localhost:3001');
        
        await prisma.brand.update({
          where: { id: brand.id },
          data: { logo: newLogoUrl }
        });
        
        console.log(`✅ Updated ${brand.name}: ${brand.logo} -> ${newLogoUrl}`);
      } else {
        console.log(`⏭️  Skipped ${brand.name}: ${brand.logo || 'no logo'}`);
      }
    }

    console.log('✅ Brand URL fix completed!');
  } catch (error) {
    console.error('❌ Error fixing brand URLs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixBrandUrls();
