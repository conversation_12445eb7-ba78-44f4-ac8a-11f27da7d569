import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import {
  NewsletterSubscription,
  NewsletterFormData,
  NewsletterPreferences,
  PERSIAN_NEWSLETTER_MESSAGES,
  NEWSLETTER_STORAGE_KEYS
} from '../types/newsletter';
import { EmailValidator } from '../utils/authUtils';

// Mock newsletter API
class MockNewsletterAPI {
  private static subscriptions: NewsletterSubscription[] = [];

  static async subscribe(data: NewsletterFormData): Promise<NewsletterSubscription> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Validate email
    if (!EmailValidator.validate(data.email)) {
      throw new Error(PERSIAN_NEWSLETTER_MESSAGES.errors.invalidEmail);
    }

    // Check if already subscribed
    const existing = this.subscriptions.find(sub => 
      sub.email === data.email && sub.isActive
    );
    
    if (existing) {
      throw new Error(PERSIAN_NEWSLETTER_MESSAGES.errors.emailExists);
    }

    const subscription: NewsletterSubscription = {
      id: Date.now().toString(),
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      isActive: true,
      subscribedAt: new Date().toISOString(),
      preferences: {
        productUpdates: true,
        promotions: true,
        skincareTips: true,
        newArrivals: true,
        exclusiveOffers: true,
        weeklyDigest: false,
        frequency: 'weekly',
        language: 'fa',
        ...data.preferences
      },
      source: data.source,
      tags: []
    };

    this.subscriptions.push(subscription);
    return subscription;
  }

  static async unsubscribe(email: string, reason?: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 800));

    const subscription = this.subscriptions.find(sub => 
      sub.email === email && sub.isActive
    );

    if (!subscription) {
      throw new Error(PERSIAN_NEWSLETTER_MESSAGES.errors.emailNotFound);
    }

    subscription.isActive = false;
    subscription.unsubscribedAt = new Date().toISOString();
  }

  static async updatePreferences(
    email: string, 
    preferences: Partial<NewsletterPreferences>
  ): Promise<NewsletterSubscription> {
    await new Promise(resolve => setTimeout(resolve, 800));

    const subscription = this.subscriptions.find(sub => 
      sub.email === email && sub.isActive
    );

    if (!subscription) {
      throw new Error(PERSIAN_NEWSLETTER_MESSAGES.errors.emailNotFound);
    }

    subscription.preferences = { ...subscription.preferences, ...preferences };
    return subscription;
  }

  static async checkSubscription(email: string): Promise<NewsletterSubscription | null> {
    await new Promise(resolve => setTimeout(resolve, 500));

    return this.subscriptions.find(sub => 
      sub.email === email && sub.isActive
    ) || null;
  }

  static async resendConfirmation(email: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const subscription = this.subscriptions.find(sub => sub.email === email);
    if (!subscription) {
      throw new Error(PERSIAN_NEWSLETTER_MESSAGES.errors.emailNotFound);
    }
  }
}

// Newsletter storage utilities
class NewsletterStorage {
  static setSubscription(subscription: NewsletterSubscription): void {
    localStorage.setItem(
      NEWSLETTER_STORAGE_KEYS.SUBSCRIPTION, 
      JSON.stringify(subscription)
    );
  }

  static getSubscription(): NewsletterSubscription | null {
    const stored = localStorage.getItem(NEWSLETTER_STORAGE_KEYS.SUBSCRIPTION);
    if (!stored) return null;
    
    try {
      return JSON.parse(stored);
    } catch {
      return null;
    }
  }

  static clearSubscription(): void {
    localStorage.removeItem(NEWSLETTER_STORAGE_KEYS.SUBSCRIPTION);
  }

  static setPopupShown(): void {
    localStorage.setItem(NEWSLETTER_STORAGE_KEYS.POPUP_SHOWN, 'true');
    localStorage.setItem(NEWSLETTER_STORAGE_KEYS.LAST_POPUP, Date.now().toString());
  }

  static shouldShowPopup(): boolean {
    const shown = localStorage.getItem(NEWSLETTER_STORAGE_KEYS.POPUP_SHOWN);
    const lastPopup = localStorage.getItem(NEWSLETTER_STORAGE_KEYS.LAST_POPUP);
    
    if (!shown) return true;
    
    // Show popup again after 7 days
    if (lastPopup) {
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      return parseInt(lastPopup) < sevenDaysAgo;
    }
    
    return false;
  }
}

export const useNewsletter = (email?: string) => {
  const [subscription, setSubscription] = useState<NewsletterSubscription | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load subscription from storage on mount
  useEffect(() => {
    const stored = NewsletterStorage.getSubscription();
    if (stored && stored.isActive) {
      setSubscription(stored);
      setIsSubscribed(true);
    }
  }, []);

  // Check subscription status for provided email
  useEffect(() => {
    if (email && EmailValidator.validate(email)) {
      checkSubscription(email);
    }
  }, [email]);

  const subscribe = useCallback(async (data: NewsletterFormData): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const newSubscription = await MockNewsletterAPI.subscribe(data);
      
      setSubscription(newSubscription);
      setIsSubscribed(true);
      NewsletterStorage.setSubscription(newSubscription);
      
      toast.success(PERSIAN_NEWSLETTER_MESSAGES.success.subscribed);
    } catch (err: any) {
      const errorMessage = err.message || PERSIAN_NEWSLETTER_MESSAGES.errors.subscriptionFailed;
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const unsubscribe = useCallback(async (email: string, reason?: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      await MockNewsletterAPI.unsubscribe(email, reason);
      
      setSubscription(null);
      setIsSubscribed(false);
      NewsletterStorage.clearSubscription();
      
      toast.success(PERSIAN_NEWSLETTER_MESSAGES.success.unsubscribed);
    } catch (err: any) {
      const errorMessage = err.message || PERSIAN_NEWSLETTER_MESSAGES.errors.unsubscribeFailed;
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updatePreferences = useCallback(async (
    preferences: Partial<NewsletterPreferences>
  ): Promise<void> => {
    if (!subscription) {
      throw new Error('No active subscription found');
    }

    setIsLoading(true);
    setError(null);

    try {
      const updatedSubscription = await MockNewsletterAPI.updatePreferences(
        subscription.email, 
        preferences
      );
      
      setSubscription(updatedSubscription);
      NewsletterStorage.setSubscription(updatedSubscription);
      
      toast.success(PERSIAN_NEWSLETTER_MESSAGES.success.preferencesUpdated);
    } catch (err: any) {
      const errorMessage = err.message || 'خطا در به‌روزرسانی تنظیمات';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [subscription]);

  const checkSubscription = useCallback(async (email: string): Promise<boolean> => {
    if (!EmailValidator.validate(email)) return false;

    try {
      const sub = await MockNewsletterAPI.checkSubscription(email);
      
      if (sub) {
        setSubscription(sub);
        setIsSubscribed(true);
        NewsletterStorage.setSubscription(sub);
        return true;
      } else {
        setSubscription(null);
        setIsSubscribed(false);
        return false;
      }
    } catch (err) {
      console.error('Error checking subscription:', err);
      return false;
    }
  }, []);

  const resendConfirmation = useCallback(async (email: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      await MockNewsletterAPI.resendConfirmation(email);
      toast.success(PERSIAN_NEWSLETTER_MESSAGES.success.confirmationSent);
    } catch (err: any) {
      const errorMessage = err.message || 'خطا در ارسال ایمیل تأیید';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    subscription,
    isSubscribed,
    isLoading,
    error,
    
    // Actions
    subscribe,
    unsubscribe,
    updatePreferences,
    checkSubscription,
    resendConfirmation,
    clearError
  };
};

// Utility hook for popup management
export const useNewsletterPopup = () => {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Check if popup should be shown after a delay
    const timer = setTimeout(() => {
      if (NewsletterStorage.shouldShowPopup()) {
        setShouldShow(true);
      }
    }, 10000); // Show after 10 seconds

    return () => clearTimeout(timer);
  }, []);

  const markAsShown = useCallback(() => {
    NewsletterStorage.setPopupShown();
    setShouldShow(false);
  }, []);

  const dismiss = useCallback(() => {
    setShouldShow(false);
  }, []);

  return {
    shouldShow,
    markAsShown,
    dismiss
  };
};

export default useNewsletter;
