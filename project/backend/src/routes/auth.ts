import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import AuthController from '../controllers/authController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// User registration
router.post('/register',
  ValidationSets.register(),
  handleValidationErrors,
  AuthController.register
);

// User login
router.post('/login',
  ValidationSets.login(),
  handleValidationErrors,
  AuthController.login
);

// Token refresh
router.post('/refresh',
  AuthController.refreshToken
);

// User logout
router.post('/logout',
  authenticate,
  AuthController.logout
);

// Logout from all devices
router.post('/logout-all',
  authenticate,
  AuthController.logoutAll
);

// Request password reset
router.post('/forgot-password',
  ValidationSets.requestPasswordReset(),
  handleValidationErrors,
  AuthController.requestPasswordReset
);

// Reset password
router.post('/reset-password',
  ValidationSets.resetPassword(),
  handleValidationErrors,
  AuthController.resetPassword
);

// Verify email
router.post('/verify-email',
  ValidationSets.verifyEmail(),
  handleValidationErrors,
  AuthController.verifyEmail
);

// Get current user
router.get('/me',
  authenticate,
  AuthController.getCurrentUser
);

export default router;
