import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Product } from '../types';
import { products } from '../data/products';
import { 
  searchProducts, 
  getSearchSuggestions, 
  getPopularSearchTerms,
  getSearchHistory,
  trackSearchAnalytics,
  normalizePersianText
} from '../utils/persianSearch';

export interface UseAdvancedSearchOptions {
  minQueryLength?: number;
  maxSuggestions?: number;
  debounceMs?: number;
  enableHistory?: boolean;
  enableAnalytics?: boolean;
}

export interface SearchState {
  query: string;
  results: Product[];
  suggestions: string[];
  popularTerms: string[];
  searchHistory: string[];
  isLoading: boolean;
  hasSearched: boolean;
  showSuggestions: boolean;
  activeIndex: number;
}

export const useAdvancedSearch = (options: UseAdvancedSearchOptions = {}) => {
  const {
    minQueryLength = 2,
    maxSuggestions = 8,
    debounceMs = 300,
    enableHistory = true,
    enableAnalytics = true
  } = options;

  const navigate = useNavigate();
  
  const [state, setState] = useState<SearchState>({
    query: '',
    results: [],
    suggestions: [],
    popularTerms: [],
    searchHistory: [],
    isLoading: false,
    hasSearched: false,
    showSuggestions: false,
    activeIndex: -1
  });

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (state.query.length >= minQueryLength) {
        performSearch(state.query);
      } else {
        setState(prev => ({
          ...prev,
          results: [],
          suggestions: [],
          isLoading: false,
          hasSearched: false
        }));
      }
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [state.query, minQueryLength, debounceMs]);

  // Load initial data
  useEffect(() => {
    setState(prev => ({
      ...prev,
      popularTerms: getPopularSearchTerms(),
      searchHistory: enableHistory ? getSearchHistory() : []
    }));
  }, [enableHistory]);

  const performSearch = useCallback((query: string) => {
    setState(prev => ({ ...prev, isLoading: true }));

    // Simulate API delay for better UX
    setTimeout(() => {
      const searchResults = searchProducts(products, query, {
        searchInDescription: true,
        searchInIngredients: true,
        searchInBenefits: true
      });

      const suggestions = getSearchSuggestions(query, maxSuggestions);

      setState(prev => ({
        ...prev,
        results: searchResults,
        suggestions,
        isLoading: false,
        hasSearched: true,
        showSuggestions: suggestions.length > 0
      }));

      // Track analytics
      if (enableAnalytics) {
        trackSearchAnalytics({
          query,
          timestamp: Date.now(),
          resultsCount: searchResults.length
        });
      }
    }, 100);
  }, [maxSuggestions, enableAnalytics]);

  const setQuery = useCallback((newQuery: string) => {
    setState(prev => ({
      ...prev,
      query: newQuery,
      activeIndex: -1,
      showSuggestions: newQuery.length >= minQueryLength
    }));
  }, [minQueryLength]);

  const selectSuggestion = useCallback((suggestion: string) => {
    setState(prev => ({
      ...prev,
      query: suggestion,
      showSuggestions: false,
      activeIndex: -1
    }));
    performSearch(suggestion);
  }, [performSearch]);

  const selectProduct = useCallback((product: Product) => {
    // Track click analytics
    if (enableAnalytics) {
      trackSearchAnalytics({
        query: state.query,
        timestamp: Date.now(),
        resultsCount: state.results.length,
        clickedResult: product.id
      });
    }

    // Navigate to product
    navigate(`/product/${product.id}`);
  }, [navigate, enableAnalytics, state.query, state.results.length]);

  const clearSearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      query: '',
      results: [],
      suggestions: [],
      hasSearched: false,
      showSuggestions: false,
      activeIndex: -1,
      isLoading: false
    }));
  }, []);

  const navigateToResults = useCallback(() => {
    if (state.query.length >= minQueryLength) {
      navigate(`/products?search=${encodeURIComponent(state.query)}`);
    }
  }, [navigate, state.query, minQueryLength]);

  // Keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { key } = event;
    const totalItems = state.suggestions.length + state.results.length;

    switch (key) {
      case 'ArrowDown':
        event.preventDefault();
        setState(prev => ({
          ...prev,
          activeIndex: prev.activeIndex < totalItems - 1 ? prev.activeIndex + 1 : 0
        }));
        break;

      case 'ArrowUp':
        event.preventDefault();
        setState(prev => ({
          ...prev,
          activeIndex: prev.activeIndex > 0 ? prev.activeIndex - 1 : totalItems - 1
        }));
        break;

      case 'Enter':
        event.preventDefault();
        if (state.activeIndex >= 0) {
          if (state.activeIndex < state.suggestions.length) {
            // Select suggestion
            selectSuggestion(state.suggestions[state.activeIndex]);
          } else {
            // Select product
            const productIndex = state.activeIndex - state.suggestions.length;
            if (state.results[productIndex]) {
              selectProduct(state.results[productIndex]);
            }
          }
        } else if (state.query.length >= minQueryLength) {
          navigateToResults();
        }
        break;

      case 'Escape':
        setState(prev => ({
          ...prev,
          showSuggestions: false,
          activeIndex: -1
        }));
        break;
    }
  }, [state.suggestions, state.results, state.activeIndex, state.query, minQueryLength, selectSuggestion, selectProduct, navigateToResults]);

  // Memoized computed values
  const hasResults = useMemo(() => state.results.length > 0, [state.results.length]);
  const hasSuggestions = useMemo(() => state.suggestions.length > 0, [state.suggestions.length]);
  const showNoResults = useMemo(() => 
    state.hasSearched && !state.isLoading && !hasResults && state.query.length >= minQueryLength,
    [state.hasSearched, state.isLoading, hasResults, state.query.length, minQueryLength]
  );

  return {
    // State
    ...state,
    
    // Computed
    hasResults,
    hasSuggestions,
    showNoResults,
    
    // Actions
    setQuery,
    selectSuggestion,
    selectProduct,
    clearSearch,
    navigateToResults,
    handleKeyDown,
    
    // Utils
    normalizePersianText
  };
};
