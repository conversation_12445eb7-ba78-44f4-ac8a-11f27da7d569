import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Star, ShoppingBag, Heart, Share2, Minus, Plus, Shield, Truck, AlertCircle, Building2, Eye } from 'lucide-react';
import { Product, SelectedVariants, ProductVariant } from '../../types';
import { useCart } from '../../context/CartContext';
import { useWishlist } from '../../hooks/useWishlist';
import { useProductComparison } from '../../hooks/useProductComparison';
import ProductVariants from './ProductVariants';
import SocialShare from '../social/SocialShare';
import { getBrandInfo } from '../../utils/brandUtils';
import { generateProductShareData } from '../../utils/socialUtils';

interface ProductInfoProps {
  product: Product;
}

const ProductInfo: React.FC<ProductInfoProps> = ({ product }) => {
  const [quantity, setQuantity] = useState(1);
  const [selectedVariants, setSelectedVariants] = useState<SelectedVariants>({});
  const [variantErrors, setVariantErrors] = useState<string[]>([]);
  const { addItem } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();
  const { addItem: addToComparison, isInComparison, canAddMore } = useProductComparison();

  // Initialize default variants
  useEffect(() => {
    if (product.variants) {
      const defaultVariants: SelectedVariants = {};

      product.variants.forEach(group => {
        const defaultVariant = group.variants.find(v => v.isDefault) || group.variants[0];
        if (defaultVariant) {
          defaultVariants[group.type] = defaultVariant;
        }
      });

      setSelectedVariants(defaultVariants);
    }
  }, [product.variants]);

  // Handle variant selection
  const handleVariantChange = (variantType: string, variant: ProductVariant) => {
    setSelectedVariants(prev => ({
      ...prev,
      [variantType]: variant
    }));
    setVariantErrors(prev => prev.filter(error => error !== variantType));
  };

  // Validate required variants
  const validateVariants = (): boolean => {
    if (!product.variants) return true;

    const errors: string[] = [];

    product.variants.forEach(group => {
      if (group.required && !selectedVariants[group.type]) {
        errors.push(group.type);
      }
    });

    setVariantErrors(errors);
    return errors.length === 0;
  };

  // Calculate current price with variants
  const getCurrentPrice = () => {
    let basePrice = product.discountedPrice || product.price;

    Object.values(selectedVariants).forEach(variant => {
      if (variant.price) {
        basePrice += variant.price;
      }
    });

    return basePrice;
  };

  // Get current stock based on selected variants
  const getCurrentStock = () => {
    if (!product.variants || Object.keys(selectedVariants).length === 0) {
      return product.stock;
    }

    // Find the minimum stock among selected variants
    const variantStocks = Object.values(selectedVariants).map(variant => variant.stock);
    return Math.min(product.stock, ...variantStocks);
  };

  const handleQuantityChange = (change: number) => {
    const currentStock = getCurrentStock();
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= currentStock) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (!validateVariants()) {
      return;
    }

    addItem(product, quantity, Object.keys(selectedVariants).length > 0 ? selectedVariants : undefined);
  };

  const handleToggleWishlist = () => {
    toggleWishlist(product);
  };

  const handleAddToComparison = () => {
    addToComparison(product);
  };

  // Generate share data for the product
  const shareData = generateProductShareData(product);

  const currentPrice = getCurrentPrice();
  const currentStock = getCurrentStock();

  const discountPercentage = product.discountedPrice
    ? Math.round((1 - product.discountedPrice / product.price) * 100)
    : 0;

  return (
    <div className="space-y-6">
      {/* Product Title and Rating */}
      <div>
        <h1 className="text-2xl md:text-3xl font-bold text-text-primary mb-2">
          {product.name}
        </h1>
        
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center">
            <div className="flex items-center text-accent-500">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.rating) ? 'fill-current' : 'stroke-current fill-transparent'
                  }`}
                />
              ))}
            </div>
            <span className="mr-2 text-sm font-medium text-text-primary">
              {product.rating}
            </span>
            <span className="mr-2 text-sm text-text-secondary">
              ({product.reviewCount} نظر)
            </span>
          </div>
          
          {product.brand && (
            <div className="flex items-center gap-2 text-sm text-text-secondary">
              <span>برند:</span>
              {(() => {
                const brandInfo = getBrandInfo(product.brand);
                return (
                  <div className="flex items-center gap-1">
                    {brandInfo.logo ? (
                      <img
                        src={brandInfo.logo}
                        alt={`لوگو ${brandInfo.name}`}
                        className="w-5 h-5 rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : (
                      <Building2 className="w-4 h-4 text-gray-400" />
                    )}
                    <span className="font-medium text-text-primary">{brandInfo.name}</span>
                  </div>
                );
              })()}
            </div>
          )}
        </div>

        {/* Category and Stock */}
        <div className="flex items-center gap-4 text-sm text-text-secondary">
          <span>دسته‌بندی: <span className="text-primary-600">{product.category}</span></span>
          <span className={`${currentStock > 10 ? 'text-green-600' : currentStock > 0 ? 'text-orange-600' : 'text-red-600'}`}>
            {currentStock > 0 ? `${currentStock} عدد موجود` : 'ناموجود'}
          </span>
        </div>
      </div>

      {/* Price */}
      <div className="bg-primary-50 rounded-xl p-4">
        <div className="flex items-center justify-between">
          <div>
            {product.discountedPrice ? (
              <div className="space-y-1">
                <div className="flex items-center gap-3">
                  <span className="text-2xl font-bold text-primary-600">
                    {currentPrice.toLocaleString()} تومان
                  </span>
                  <span className="bg-red-500 text-white text-sm px-2 py-1 rounded-full">
                    {discountPercentage}% تخفیف
                  </span>
                </div>
                <span className="line-through text-text-secondary text-lg">
                  {(product.price + (currentPrice - (product.discountedPrice || product.price))).toLocaleString()} تومان
                </span>
              </div>
            ) : (
              <span className="text-2xl font-bold text-text-primary">
                {currentPrice.toLocaleString()} تومان
              </span>
            )}
          </div>
          
          {/* Product Size/Weight */}
          {(product.size || product.weight) && (
            <div className="text-sm text-text-secondary">
              {product.size && <div>حجم: {product.size}</div>}
              {product.weight && <div>وزن: {product.weight}</div>}
            </div>
          )}
        </div>
      </div>

      {/* Product Variants */}
      {product.variants && product.variants.length > 0 && (
        <div>
          <ProductVariants
            variantGroups={product.variants}
            selectedVariants={selectedVariants}
            onVariantChange={handleVariantChange}
            disabled={currentStock === 0}
          />

          {/* Variant Errors */}
          {variantErrors.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm font-medium">لطفاً گزینه‌های زیر را انتخاب کنید:</span>
              </div>
              <ul className="mt-2 text-sm text-red-600">
                {variantErrors.map(errorType => {
                  const group = product.variants?.find(g => g.type === errorType);
                  return (
                    <li key={errorType}>• {group?.name}</li>
                  );
                })}
              </ul>
            </motion.div>
          )}
        </div>
      )}

      {/* Description */}
      <div>
        <h3 className="text-lg font-semibold mb-3">توضیحات محصول</h3>
        <p className="text-text-secondary leading-relaxed">
          {product.description}
        </p>
      </div>

      {/* Quantity and Add to Cart */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <span className="text-sm font-medium">تعداد:</span>
          <div className="flex items-center border border-gray-200 rounded-lg">
            <button
              onClick={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
              className="p-2 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Minus className="w-4 h-4" />
            </button>
            <span className="px-4 py-2 min-w-[3rem] text-center">{quantity}</span>
            <button
              onClick={() => handleQuantityChange(1)}
              disabled={quantity >= currentStock}
              className="p-2 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="flex gap-3">
          <motion.button
            onClick={handleAddToCart}
            disabled={currentStock === 0 || variantErrors.length > 0}
            className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ShoppingBag className="w-5 h-5" />
            {variantErrors.length > 0 ? 'ابتدا گزینه‌ها را انتخاب کنید' : 'افزودن به سبد خرید'}
          </motion.button>

          <button
            onClick={handleToggleWishlist}
            className={`p-3 rounded-full border transition-colors ${
              isInWishlist(product.id)
                ? 'bg-red-50 border-red-200 text-red-600'
                : 'bg-white border-gray-200 text-text-secondary hover:bg-gray-50'
            }`}
            aria-label={isInWishlist(product.id) ? 'حذف از علاقه‌مندی‌ها' : 'افزودن به علاقه‌مندی‌ها'}
          >
            <Heart className={`w-5 h-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
          </button>

          <button
            onClick={handleAddToComparison}
            disabled={isInComparison(product.id) || !canAddMore}
            className={`p-3 rounded-full border transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
              isInComparison(product.id)
                ? 'bg-blue-50 border-blue-200 text-blue-600'
                : 'bg-white border-gray-200 text-text-secondary hover:bg-gray-50'
            }`}
            aria-label={isInComparison(product.id) ? 'در مقایسه' : 'افزودن به مقایسه'}
          >
            <Eye className="w-5 h-5" />
          </button>

          <SocialShare
            title={shareData.title}
            description={shareData.text}
            url={shareData.url}
            hashtags={shareData.hashtags}
            variant="dropdown"
            size="md"
            showLabel={false}
            className="p-3 rounded-full border border-gray-200 text-text-secondary hover:bg-gray-50 transition-colors"
          />
        </div>
      </div>

      {/* Trust Signals */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-6 border-t border-gray-100">
        <div className="flex items-center gap-3 text-sm text-text-secondary">
          <Shield className="w-5 h-5 text-green-600" />
          <span>ضمانت اصالت کالا</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-text-secondary">
          <Truck className="w-5 h-5 text-blue-600" />
          <span>ارسال رایگان بالای ۵۰۰ هزار تومان</span>
        </div>
      </div>
    </div>
  );
};

export default ProductInfo;
