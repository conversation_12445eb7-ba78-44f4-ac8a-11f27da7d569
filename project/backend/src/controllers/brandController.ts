import { Request, Response, NextFunction } from 'express';
import BrandService, { CreateBrandData, UpdateBrandData } from '../services/brandService';
import { logger } from '../config/logger';

export class BrandController {
  // Get all brands
  static async getBrands(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        includeInactive = 'false',
        search,
        page = '1',
        limit = '50'
      } = req.query;

      const pageNum = parseInt(page as string, 10);
      const limitNum = parseInt(limit as string, 10);
      const offset = (pageNum - 1) * limitNum;

      const brands = await BrandService.getBrands({
        includeInactive: includeInactive === 'true',
        search: search as string,
        limit: limitNum,
        offset,
      });

      res.json({
        success: true,
        message: 'برندها با موفقیت دریافت شد',
        data: brands,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: brands.length,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get brand by ID
  static async getBrandById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const brand = await BrandService.getBrandById(id);

      res.json({
        success: true,
        message: 'برند با موفقیت دریافت شد',
        data: brand,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get brand by slug
  static async getBrandBySlug(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { slug } = req.params;
      const brand = await BrandService.getBrandBySlug(slug);

      res.json({
        success: true,
        message: 'برند با موفقیت دریافت شد',
        data: brand,
      });
    } catch (error) {
      next(error);
    }
  }

  // Create brand (admin only)
  static async createBrand(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const brandData: CreateBrandData = req.body;
      const brand = await BrandService.createBrand(brandData);

      logger.info(`Brand created by admin: ${req.user?.email}, brand: ${brand.name}`);

      res.status(201).json({
        success: true,
        message: 'برند با موفقیت ایجاد شد',
        data: brand,
      });
    } catch (error) {
      next(error);
    }
  }

  // Update brand (admin only)
  static async updateBrand(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateBrandData = req.body;

      const brand = await BrandService.updateBrand(id, updateData);

      logger.info(`Brand updated by admin: ${req.user?.email}, brand: ${brand.name}`);

      res.json({
        success: true,
        message: 'برند با موفقیت بروزرسانی شد',
        data: brand,
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete brand (admin only)
  static async deleteBrand(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await BrandService.deleteBrand(id);

      logger.info(`Brand deleted by admin: ${req.user?.email}, brandId: ${id}`);

      res.json({
        success: true,
        message: 'برند با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get brand statistics (admin only)
  static async getBrandStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const stats = await BrandService.getBrandStats();

      res.json({
        success: true,
        message: 'آمار برندها با موفقیت دریافت شد',
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  }

  // Upload brand logo (admin only)
  static async uploadBrandLogo(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const file = req.file;

      if (!file) {
        res.status(400).json({
          success: false,
          message: 'هیچ فایلی آپلود نشده است',
        });
        return;
      }

      // Get file URL
      const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
      const logoUrl = `${baseUrl}/uploads/brands/${file.filename}`;

      // Update brand with new logo
      const brand = await BrandService.updateBrand(id, {
        logo: logoUrl,
      });

      logger.info(`Brand logo uploaded by admin: ${req.user?.email}, brandId: ${id}`);

      res.json({
        success: true,
        message: 'لوگو برند با موفقیت آپلود شد',
        data: {
          brand,
          logo: {
            url: logoUrl,
            filename: file.filename,
            originalName: file.originalname,
            size: file.size,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default BrandController;
