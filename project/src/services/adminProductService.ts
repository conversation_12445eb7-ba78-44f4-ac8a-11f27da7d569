/**
 * Admin Product Management API Service
 * Handles all product-related operations with backend API
 */

import { ApiService, ApiResponse } from './apiService';
import { AdminProduct, ProductFormData, AdminProductFilters, BulkProductOperation } from '../types/adminProduct';

// Backend product interface (from API response)
interface BackendProduct {
  id: string;
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  brandId?: string;
  price: string; // Backend returns as string
  comparePrice?: string;
  costPrice?: string;
  weight?: number;
  dimensions?: any;
  isActive: boolean;
  isFeatured: boolean;
  isDigital: boolean;
  requiresShipping: boolean;
  trackQuantity: boolean;
  allowBackorder: boolean;
  metaTitle?: string;
  metaDescription?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  brand?: {
    id: string;
    name: string;
    nameEn?: string;
    slug: string;
  };
  categories?: Array<{
    category: {
      id: string;
      name: string;
      nameEn?: string;
      slug: string;
    };
  }>;
  images?: Array<{
    id: string;
    url: string;
    alt?: string;
    isPrimary?: boolean;
    sortOrder?: number;
  }>;
  inventory?: {
    quantity: number;
    lowStockThreshold?: number;
  };
}

// Helper function to get full image URL
const getFullImageUrl = (imagePath?: string): string => {
  if (!imagePath) return '/images/products/placeholder.svg';
  if (imagePath.startsWith('http')) return imagePath;
  // Handle relative paths from backend - use correct port 3001
  if (imagePath.startsWith('/')) {
    return `http://localhost:3001${imagePath}`;
  }
  return `http://localhost:3001/uploads/${imagePath}`;
};

// Transform backend product to frontend AdminProduct
function transformBackendProduct(backendProduct: BackendProduct): AdminProduct {
  // Get primary image from images array or use placeholder
  const primaryImage = backendProduct.images?.find(img => img.isPrimary) || backendProduct.images?.[0];
  const imageSrc = primaryImage ? getFullImageUrl(primaryImage.url) : '/images/products/placeholder.jpg';

  // Get all additional images
  const additionalImages = backendProduct.images?.filter(img => !img.isPrimary).map(img => getFullImageUrl(img.url)) || [];

  // Debug logging for image handling
  if (process.env.NODE_ENV === 'development') {
    console.log('Product image transformation:', {
      productName: backendProduct.name,
      rawImages: backendProduct.images,
      primaryImage,
      finalImageSrc: imageSrc,
      additionalImages
    });
  }

  return {
    id: parseInt(backendProduct.id.replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000000), // Convert string ID to number
    name: backendProduct.name,
    description: backendProduct.description || '',
    category: backendProduct.categories?.[0]?.category?.name || 'عمومی',
    brand: backendProduct.brand?.name || '',
    price: parseFloat(backendProduct.price) || 0,
    discountedPrice: backendProduct.comparePrice ? parseFloat(backendProduct.comparePrice) : undefined,
    rating: 0, // Default rating
    reviewCount: 0, // Default review count
    imageSrc: imageSrc,
    images: additionalImages,
    benefits: [], // Default empty benefits
    ingredients: [], // Default empty ingredients
    howToUse: '', // Default empty how to use
    isNew: false, // Default not new
    isBestSeller: backendProduct.isFeatured,
    stock: backendProduct.inventory?.quantity || 0,
    size: '', // Default empty size
    weight: backendProduct.weight?.toString() || '',
    variants: [], // Default empty variants
    hasVariants: false, // Default no variants

    // Admin-specific fields
    sku: backendProduct.sku,
    barcode: backendProduct.barcode || '',
    status: backendProduct.isActive ? 'active' : 'draft',
    visibility: backendProduct.isActive ? 'visible' : 'hidden',
    featured: backendProduct.isFeatured,
    seoTitle: backendProduct.metaTitle || backendProduct.name,
    seoDescription: backendProduct.metaDescription || backendProduct.shortDescription || '',
    seoKeywords: backendProduct.tags.join(', '),
    slug: backendProduct.slug,
    trackInventory: backendProduct.trackQuantity,
    allowBackorder: backendProduct.allowBackorder,
    lowStockThreshold: backendProduct.inventory?.lowStockThreshold || 10,
    costPrice: backendProduct.costPrice ? parseFloat(backendProduct.costPrice) : undefined,
    compareAtPrice: backendProduct.comparePrice ? parseFloat(backendProduct.comparePrice) : undefined,
    taxable: true, // Default taxable
    taxClass: 'standard', // Default tax class
    requiresShipping: backendProduct.requiresShipping,
    shippingWeight: backendProduct.weight || 0,
    shippingDimensions: backendProduct.dimensions || { length: 0, width: 0, height: 0 },
    createdAt: backendProduct.createdAt,
    updatedAt: backendProduct.updatedAt,
    publishedAt: backendProduct.isActive ? backendProduct.createdAt : undefined,
    createdBy: 'system',
    updatedBy: 'system',
    tags: backendProduct.tags,
    vendor: backendProduct.brand?.name || '',
    productType: backendProduct.categories?.[0]?.category?.name || 'عمومی',
    collections: [],
    priceHistory: [],
    inventoryHistory: []
  };
}

export interface ProductListResponse {
  products: AdminProduct[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters?: {
    categories: Array<{ id: string; name: string; count: number }>;
    brands: Array<{ name: string; count: number }>;
    stockStatus: Array<{ status: string; count: number }>;
  };
}

export interface ProductCreateRequest {
  name: string;
  nameEn?: string;
  description?: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  brandId?: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  isActive?: boolean;
  isFeatured?: boolean;
  isDigital?: boolean;
  requiresShipping?: boolean;
  trackQuantity?: boolean;
  allowBackorder?: boolean;
  metaTitle?: string;
  metaDescription?: string;
  tags?: string[];
  categoryIds?: string[];
  stock?: number;
  lowStockThreshold?: number;
}

export interface ProductUpdateRequest extends Partial<ProductCreateRequest> {
  id: string;
}

export interface ProductInventoryUpdate {
  stock: number;
  operation: 'set' | 'add' | 'subtract';
  reason?: string;
  notes?: string;
}

export interface BulkUpdateRequest {
  productIds: string[];
  updates: {
    status?: 'active' | 'draft' | 'archived';
    visibility?: 'visible' | 'hidden';
    categoryId?: string;
    featured?: boolean;
    priceAdjustment?: {
      type: 'percentage' | 'fixed';
      value: number;
      operation: 'increase' | 'decrease';
    };
    tags?: {
      action: 'add' | 'remove' | 'replace';
      tags: string[];
    };
  };
}

/**
 * Admin Product Service
 */
export class AdminProductService {
  /**
   * Get products with filtering and pagination
   */
  static async getProducts(filters?: AdminProductFilters & {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ProductListResponse> {
    try {
      const params: Record<string, string | number> = {};
      
      if (filters) {
        if (filters.search) params.search = filters.search;
        if (filters.category) params.category = filters.category;
        if (filters.brand) params.brand = filters.brand;
        if (filters.status) params.status = filters.status;
        if (filters.visibility) params.visibility = filters.visibility;
        if (filters.featured !== undefined) params.featured = filters.featured ? '1' : '0';
        if (filters.stockStatus) params.stockStatus = filters.stockStatus;
        if (filters.priceMin !== undefined) params.priceMin = filters.priceMin;
        if (filters.priceMax !== undefined) params.priceMax = filters.priceMax;
        if (filters.page) params.page = filters.page;
        if (filters.limit) params.limit = filters.limit;
        if (filters.sortBy) params.sortBy = filters.sortBy;
        if (filters.sortOrder) params.sortOrder = filters.sortOrder;
      }

      const response = await ApiService.Http.get<BackendProduct[]>('/products', params);

      if (response.success && response.data) {
        // Transform the API response to match our expected interface
        const backendProducts = Array.isArray(response.data) ? response.data : [];
        const products = backendProducts.map(transformBackendProduct);
        const pagination = response.pagination || {
          page: 1,
          limit: products.length,
          total: products.length,
          totalPages: 1
        };

        return {
          products,
          pagination,
          filters: {
            categories: [],
            brands: [],
            stockStatus: []
          }
        };
      }

      throw new Error('PRODUCTS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get product by ID
   */
  static async getProductById(id: string): Promise<AdminProduct> {
    try {
      const response = await ApiService.Http.get<AdminProduct>(`/products/${id}`);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('PRODUCT_NOT_FOUND');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new product
   */
  static async createProduct(productData: ProductCreateRequest): Promise<AdminProduct> {
    try {
      const response = await ApiService.Http.post<AdminProduct>('/products', productData);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('PRODUCT_CREATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update existing product
   */
  static async updateProduct(id: string, productData: Partial<ProductCreateRequest>): Promise<AdminProduct> {
    try {
      const response = await ApiService.Http.put<AdminProduct>(`/products/${id}`, productData);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('PRODUCT_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete product
   */
  static async deleteProduct(id: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/products/${id}`);

      if (!response.success) {
        throw new Error('PRODUCT_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upload product images
   */
  static async uploadProductImages(
    productId: string, 
    images: {
      primaryImage?: File;
      galleryImages?: File[];
    }
  ): Promise<{
    primaryImage?: { id: string; url: string; alt?: string };
    galleryImages?: Array<{ id: string; url: string; alt?: string; sortOrder: number }>;
  }> {
    try {
      const formData = new FormData();
      
      if (images.primaryImage) {
        formData.append('primaryImage', images.primaryImage);
      }
      
      if (images.galleryImages) {
        images.galleryImages.forEach((file, index) => {
          formData.append('galleryImages', file);
        });
      }

      const response = await ApiService.Http.post(
        `/products/${productId}/images`,
        formData
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('IMAGE_UPLOAD_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove product image
   */
  static async removeProductImage(productId: string, imageId: string): Promise<void> {
    try {
      const response = await ApiService.Http.delete(`/products/${productId}/images/${imageId}`);

      if (!response.success) {
        throw new Error('IMAGE_DELETE_FAILED');
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update product inventory
   */
  static async updateInventory(
    productId: string, 
    inventoryData: ProductInventoryUpdate
  ): Promise<{
    currentStock: number;
    previousStock: number;
    operation: string;
    timestamp: string;
  }> {
    try {
      const response = await ApiService.Http.put(
        `/products/${productId}/inventory`,
        inventoryData
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('INVENTORY_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get low stock products
   */
  static async getLowStockProducts(threshold?: number): Promise<AdminProduct[]> {
    try {
      const params: Record<string, string | number> = {};
      if (threshold) params.threshold = threshold;

      const response = await ApiService.Http.get<AdminProduct[]>('/products/admin/low-stock', params);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('LOW_STOCK_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk update products
   */
  static async bulkUpdateProducts(bulkData: BulkUpdateRequest): Promise<{
    updated: number;
    failed: number;
    errors: Array<{ productId: string; error: string }>;
  }> {
    try {
      const response = await ApiService.Http.patch('/products/admin/bulk-update', bulkData);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('BULK_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get product categories
   */
  static async getCategories(): Promise<Array<{
    id: string;
    name: string;
    slug: string;
    parentId?: string;
    children?: Array<{ id: string; name: string; slug: string }>;
    productCount: number;
  }>> {
    try {
      const response = await ApiService.Http.get('/categories');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('CATEGORIES_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get product brands
   */
  static async getBrands(): Promise<Array<{
    id: string;
    name: string;
    slug: string;
    logo?: string;
    productCount: number;
  }>> {
    try {
      const response = await ApiService.Http.get('/brands');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('BRANDS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Export products to CSV
   */
  static async exportProducts(filters?: AdminProductFilters): Promise<Blob> {
    try {
      const params: Record<string, string | number> = { format: 'csv' };
      
      if (filters) {
        if (filters.search) params.search = filters.search;
        if (filters.category) params.category = filters.category;
        if (filters.brand) params.brand = filters.brand;
        if (filters.status) params.status = filters.status;
      }

      // Note: This would need special handling for file downloads
      const response = await fetch(
        `${ApiService.Http}/products/export?${new URLSearchParams(params as any)}`,
        {
          headers: {
            'Authorization': `Bearer ${ApiService.TokenManager.getToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('EXPORT_FAILED');
      }

      return await response.blob();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get product analytics
   */
  static async getProductAnalytics(productId: string, period?: string): Promise<{
    views: number;
    sales: number;
    revenue: number;
    conversionRate: number;
    averageRating: number;
    reviewCount: number;
    chartData: Array<{
      date: string;
      views: number;
      sales: number;
      revenue: number;
    }>;
  }> {
    try {
      const params: Record<string, string | number> = {};
      if (period) params.period = period;

      const response = await ApiService.Http.get(
        `/products/${productId}/analytics`,
        params
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('ANALYTICS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }
}

export default AdminProductService;
