import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import CustomerController from '../controllers/customerController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// All customer management routes require admin authentication
router.use(authenticate);
router.use(adminOnly);

// Get all customers with filtering and pagination
router.get('/',
  CustomerController.getCustomers
);

// Get customer statistics
router.get('/statistics',
  CustomerController.getCustomerStatistics
);

// Search customers
router.get('/search',
  ValidationSets.searchCustomers(),
  handleValidationErrors,
  CustomerController.searchCustomers
);

// Export customers
router.get('/export',
  CustomerController.exportCustomers
);

// Create new customer
router.post('/',
  ValidationSets.createCustomer(),
  handleValidationErrors,
  CustomerController.createCustomer
);

// Bulk update customer status
router.patch('/bulk-status',
  ValidationSets.bulkUpdateCustomerStatus(),
  handleValidationErrors,
  CustomerController.bulkUpdateCustomerStatus
);

// Get customer by ID
router.get('/:id',
  CustomerController.getCustomerById
);

// Update customer
router.put('/:id',
  ValidationSets.updateCustomer(),
  handleValidationErrors,
  CustomerController.updateCustomer
);

// Delete customer
router.delete('/:id',
  CustomerController.deleteCustomer
);

// Get customer order history
router.get('/:id/orders',
  CustomerController.getCustomerOrderHistory
);

export default router;
