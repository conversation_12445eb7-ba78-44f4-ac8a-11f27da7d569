<!DOCTYPE html>
<html>
<head>
  <title>PWA Icon Generator</title>
  <style>
    .icon {
      width: 128px;
      height: 128px;
      background: linear-gradient(135deg, #eb4d7c, #d93861);
      border-radius: 12.8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: Arial, sans-serif;
      font-weight: bold;
    }
    .main-text {
      font-size: 19.2px;
      margin-bottom: 6.4px;
    }
    .sub-text {
      font-size: 12.8px;
    }
  </style>
</head>
<body>
  <div class="icon">
    <div class="main-text">آرامش</div>
    <div class="sub-text">پوست</div>
  </div>
  <script>
    // Instructions for manual PNG generation:
    // 1. Right-click on the icon
    // 2. "Inspect Element"
    // 3. Right-click on the div.icon element in DevTools
    // 4. "Capture node screenshot"
    // 5. Save as icon-128x128.png
    console.log('To generate PNG: Right-click icon > Inspect > Right-click div.icon > Capture node screenshot');
  </script>
</body>
</html>