import React from 'react';
import { HeroSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';
import AdminFileUpload from '../../common/AdminFileUpload';
import AdminColorPicker from '../../common/AdminColorPicker';
import AdminSlider from '../../common/AdminSlider';

interface HeroSectionEditorProps {
  section: HeroSectionData;
  onChange: (updates: Partial<HeroSectionData['content']>) => void;
}

const HeroSectionEditor: React.FC<HeroSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof HeroSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  const handleButtonChange = (
    buttonType: 'primaryButton' | 'secondaryButton',
    field: string,
    value: any
  ) => {
    const currentButton = section.content[buttonType];
    if (currentButton) {
      onChange({
        [buttonType]: {
          ...currentButton,
          [field]: value
        }
      });
    }
  };

  const handleAnimationChange = (field: string, value: any) => {
    onChange({
      animation: {
        ...section.content.animation,
        [field]: value
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Background Settings */}
      <AdminCard title="تصاویر پس‌زمینه" icon="Image">
        <div className="space-y-4">
          <AdminFormField 
            label="تصویر پس‌زمینه دسکتاپ" 
            required
            help="ابعاد توصیه شده: 1920x1080 پیکسل"
          >
            <AdminFileUpload
              value={section.content.backgroundImage}
              onChange={(file, url) => url && handleChange('backgroundImage', url)}
              accept="image/*"
              aspectRatio="16:9"
              placeholder="تصویر پس‌زمینه دسکتاپ را انتخاب کنید"
            />
          </AdminFormField>

          <AdminFormField 
            label="تصویر پس‌زمینه موبایل"
            help="ابعاد توصیه شده: 768x1024 پیکسل (اختیاری)"
          >
            <AdminFileUpload
              value={section.content.mobileBackgroundImage || ''}
              onChange={(file, url) => handleChange('mobileBackgroundImage', url || undefined)}
              accept="image/*"
              aspectRatio="3:4"
              placeholder="تصویر پس‌زمینه موبایل را انتخاب کنید"
            />
          </AdminFormField>

          <AdminFormField label="شفافیت پوشش">
            <AdminSlider
              value={section.content.overlayOpacity}
              onChange={(value) => handleChange('overlayOpacity', value)}
              min={0}
              max={1}
              step={0.1}
              label={`${Math.round(section.content.overlayOpacity * 100)}%`}
            />
          </AdminFormField>

          <AdminFormField label="رنگ پس‌زمینه">
            <AdminColorPicker
              value={section.content.backgroundColor || '#000000'}
              onChange={(color) => handleChange('backgroundColor', color)}
              presets={[
                '#000000', '#ffffff', '#f3f4f6', '#1f2937',
                '#3b82f6', '#10b981', '#f59e0b', '#ef4444'
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Text Content */}
      <AdminCard title="محتوای متنی" icon="Type">
        <div className="space-y-4">
          <AdminFormField label="عنوان فرعی">
            <AdminInput
              value={section.content.subtitle}
              onChange={(e) => handleChange('subtitle', e.target.value)}
              placeholder="نام برند یا عنوان فرعی"
            />
          </AdminFormField>

          <AdminFormField label="عنوان اصلی" required>
            <AdminTextarea
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="عنوان اصلی بخش هیرو"
              rows={2}
            />
          </AdminFormField>

          <AdminFormField label="توضیحات">
            <AdminTextarea
              value={section.content.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="توضیحات کوتاه درباره محصولات یا خدمات"
              rows={3}
            />
          </AdminFormField>

          <AdminFormField label="تراز متن">
            <AdminSelect
              value={section.content.textAlignment}
              onChange={(value) => handleChange('textAlignment', value)}
              options={[
                { value: 'left', label: 'راست (RTL)' },
                { value: 'center', label: 'وسط' },
                { value: 'right', label: 'چپ (LTR)' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="رنگ متن">
            <AdminColorPicker
              value={section.content.textColor || '#ffffff'}
              onChange={(color) => handleChange('textColor', color)}
              presets={[
                '#ffffff', '#000000', '#374151', '#6b7280',
                '#3b82f6', '#10b981', '#f59e0b', '#ef4444'
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Primary Button */}
      <AdminCard title="دکمه اصلی" icon="MousePointer">
        <div className="space-y-4">
          <AdminFormField label="متن دکمه" required>
            <AdminInput
              value={section.content.primaryButton.text}
              onChange={(e) => handleButtonChange('primaryButton', 'text', e.target.value)}
              placeholder="مشاهده محصولات"
            />
          </AdminFormField>

          <AdminFormField label="آدرس لینک" required>
            <AdminInput
              value={section.content.primaryButton.url}
              onChange={(e) => handleButtonChange('primaryButton', 'url', e.target.value)}
              placeholder="/products"
            />
          </AdminFormField>

          <AdminFormField label="نوع لینک">
            <AdminSelect
              value={section.content.primaryButton.type}
              onChange={(value) => handleButtonChange('primaryButton', 'type', value)}
              options={[
                { value: 'internal', label: 'لینک داخلی' },
                { value: 'external', label: 'لینک خارجی' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Secondary Button */}
      <AdminCard title="دکمه ثانویه" icon="MousePointer">
        <div className="space-y-4">
          <AdminFormField label="فعال کردن دکمه ثانویه">
            <AdminToggle
              checked={!!section.content.secondaryButton}
              onChange={(checked) => {
                if (checked) {
                  handleChange('secondaryButton', {
                    text: 'درباره ما',
                    url: '/about',
                    type: 'internal'
                  });
                } else {
                  handleChange('secondaryButton', undefined);
                }
              }}
              label="نمایش دکمه ثانویه"
            />
          </AdminFormField>

          {section.content.secondaryButton && (
            <>
              <AdminFormField label="متن دکمه">
                <AdminInput
                  value={section.content.secondaryButton.text}
                  onChange={(e) => handleButtonChange('secondaryButton', 'text', e.target.value)}
                  placeholder="درباره ما"
                />
              </AdminFormField>

              <AdminFormField label="آدرس لینک">
                <AdminInput
                  value={section.content.secondaryButton.url}
                  onChange={(e) => handleButtonChange('secondaryButton', 'url', e.target.value)}
                  placeholder="/about"
                />
              </AdminFormField>

              <AdminFormField label="نوع لینک">
                <AdminSelect
                  value={section.content.secondaryButton.type}
                  onChange={(value) => handleButtonChange('secondaryButton', 'type', value)}
                  options={[
                    { value: 'internal', label: 'لینک داخلی' },
                    { value: 'external', label: 'لینک خارجی' }
                  ]}
                />
              </AdminFormField>
            </>
          )}
        </div>
      </AdminCard>

      {/* Animation Settings */}
      <AdminCard title="تنظیمات انیمیشن" icon="Zap">
        <div className="space-y-4">
          <AdminFormField label="فعال کردن انیمیشن">
            <AdminToggle
              checked={section.content.animation.enabled}
              onChange={(checked) => handleAnimationChange('enabled', checked)}
              label="انیمیشن ورود فعال باشد"
            />
          </AdminFormField>

          {section.content.animation.enabled && (
            <>
              <AdminFormField label="نوع انیمیشن">
                <AdminSelect
                  value={section.content.animation.type}
                  onChange={(value) => handleAnimationChange('type', value)}
                  options={[
                    { value: 'fade', label: 'محو شدن' },
                    { value: 'slide', label: 'لغزش' },
                    { value: 'zoom', label: 'زوم' }
                  ]}
                />
              </AdminFormField>

              <AdminFormField label="مدت زمان انیمیشن">
                <AdminSlider
                  value={section.content.animation.duration}
                  onChange={(value) => handleAnimationChange('duration', value)}
                  min={0.2}
                  max={2}
                  step={0.1}
                  label={`${section.content.animation.duration} ثانیه`}
                />
              </AdminFormField>
            </>
          )}
        </div>
      </AdminCard>
    </div>
  );
};

export default HeroSectionEditor;
