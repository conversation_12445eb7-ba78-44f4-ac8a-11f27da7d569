import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Eye, EyeOff, Mail, Lock, User, Phone, UserPlus, Check, X } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { RegisterFormData, PERSIAN_AUTH_MESSAGES } from '../../types/auth';
import { EmailValidator, PhoneValidator, PasswordValidator } from '../../utils/authUtils';

// Validation schema
const registerSchema = yup.object({
  firstName: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.firstNameRequired)
    .min(2, 'نام باید حداقل ۲ کاراکتر باشد'),
  lastName: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.lastNameRequired)
    .min(2, 'نام خانوادگی باید حداقل ۲ کاراکتر باشد'),
  email: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.emailRequired)
    .test('email', PERSIAN_AUTH_MESSAGES.errors.emailInvalid, (value) => 
      value ? EmailValidator.validate(value) : false
    ),
  phone: yup
    .string()
    .test('phone', PERSIAN_AUTH_MESSAGES.errors.phoneInvalid, (value) => 
      !value || PhoneValidator.validateMobile(value)
    ),
  password: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.passwordRequired)
    .test('password', PERSIAN_AUTH_MESSAGES.errors.weakPassword, (value) => 
      value ? PasswordValidator.validate(value).isValid : false
    ),
  confirmPassword: yup
    .string()
    .required('تکرار رمز عبور الزامی است')
    .oneOf([yup.ref('password')], PERSIAN_AUTH_MESSAGES.errors.passwordMismatch),
  acceptTerms: yup
    .boolean()
    .oneOf([true], PERSIAN_AUTH_MESSAGES.errors.termsRequired),
  newsletter: yup.boolean()
});

interface RegisterFormProps {
  onSuccess?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { register, isLoading, error, clearError } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setError
  } = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
      newsletter: false
    },
    mode: 'onChange'
  });

  const watchedPassword = watch('password');
  const passwordStrength = watchedPassword ? PasswordValidator.getStrength(watchedPassword) : null;

  const onSubmit = async (data: RegisterFormData) => {
    try {
      clearError();
      await register({
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        email: EmailValidator.normalize(data.email),
        phone: data.phone ? PhoneValidator.normalizeMobile(data.phone) : undefined,
        password: data.password,
        confirmPassword: data.confirmPassword,
        acceptTerms: data.acceptTerms,
        newsletter: data.newsletter
      });
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      // Handle specific validation errors
      if (error.message.includes('ایمیل')) {
        setError('email', { message: error.message });
      } else if (error.message.includes('موبایل')) {
        setError('phone', { message: error.message });
      }
    }
  };

  const getPasswordStrengthColor = (strength: string) => {
    switch (strength) {
      case 'weak': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'strong': return 'bg-green-500';
      default: return 'bg-gray-300';
    }
  };

  const getPasswordStrengthText = (strength: string) => {
    switch (strength) {
      case 'weak': return 'ضعیف';
      case 'medium': return 'متوسط';
      case 'strong': return 'قوی';
      default: return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md mx-auto"
    >
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <UserPlus className="w-8 h-8 text-primary-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            ثبت‌نام در گلو رویا
          </h2>
          <p className="text-gray-600">
            حساب کاربری جدید ایجاد کنید
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline ml-1" />
                {PERSIAN_AUTH_MESSAGES.labels.firstName} *
              </label>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    placeholder={PERSIAN_AUTH_MESSAGES.placeholders.firstName}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                      errors.firstName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              {errors.firstName && (
                <p className="mt-1 text-xs text-red-600">{errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {PERSIAN_AUTH_MESSAGES.labels.lastName} *
              </label>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    placeholder={PERSIAN_AUTH_MESSAGES.placeholders.lastName}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                      errors.lastName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              {errors.lastName && (
                <p className="mt-1 text-xs text-red-600">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 inline ml-1" />
              {PERSIAN_AUTH_MESSAGES.labels.email} *
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="email"
                  placeholder={PERSIAN_AUTH_MESSAGES.placeholders.email}
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                    errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
              )}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Phone Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="w-4 h-4 inline ml-1" />
              شماره موبایل (اختیاری)
            </label>
            <Controller
              name="phone"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="tel"
                  placeholder={PERSIAN_AUTH_MESSAGES.placeholders.phone}
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                    errors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
              )}
            />
            {errors.phone && (
              <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Lock className="w-4 h-4 inline ml-1" />
              {PERSIAN_AUTH_MESSAGES.labels.password} *
            </label>
            <div className="relative">
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showPassword ? 'text' : 'password'}
                    placeholder={PERSIAN_AUTH_MESSAGES.placeholders.password}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors pr-12 ${
                      errors.password ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            
            {/* Password Strength Indicator */}
            {watchedPassword && (
              <div className="mt-2">
                <div className="flex items-center gap-2 mb-1">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength!)}`}
                      style={{ 
                        width: passwordStrength === 'weak' ? '33%' : 
                               passwordStrength === 'medium' ? '66%' : '100%' 
                      }}
                    />
                  </div>
                  <span className="text-xs text-gray-600">
                    {getPasswordStrengthText(passwordStrength!)}
                  </span>
                </div>
              </div>
            )}
            
            {errors.password && (
              <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Lock className="w-4 h-4 inline ml-1" />
              {PERSIAN_AUTH_MESSAGES.labels.confirmPassword} *
            </label>
            <div className="relative">
              <Controller
                name="confirmPassword"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder={PERSIAN_AUTH_MESSAGES.placeholders.confirmPassword}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors pr-12 ${
                      errors.confirmPassword ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Terms and Newsletter */}
          <div className="space-y-3">
            <Controller
              name="acceptTerms"
              control={control}
              render={({ field }) => (
                <label className="flex items-start gap-3">
                  <input
                    {...field}
                    type="checkbox"
                    checked={field.value}
                    className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    disabled={isLoading}
                  />
                  <span className="text-sm text-gray-600">
                    {PERSIAN_AUTH_MESSAGES.labels.acceptTerms} *
                    <Link to="/terms" className="text-primary-600 hover:text-primary-700 mr-1">
                      قوانین و مقررات
                    </Link>
                  </span>
                </label>
              )}
            />
            {errors.acceptTerms && (
              <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
            )}

            <Controller
              name="newsletter"
              control={control}
              render={({ field }) => (
                <label className="flex items-center gap-3">
                  <input
                    {...field}
                    type="checkbox"
                    checked={field.value}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    disabled={isLoading}
                  />
                  <span className="text-sm text-gray-600">
                    {PERSIAN_AUTH_MESSAGES.labels.newsletter}
                  </span>
                </label>
              )}
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                در حال ثبت‌نام...
              </>
            ) : (
              <>
                <UserPlus className="w-5 h-5" />
                {PERSIAN_AUTH_MESSAGES.buttons.register}
              </>
            )}
          </button>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-3"
            >
              <p className="text-sm text-red-600 text-center">{error}</p>
            </motion.div>
          )}

          {/* Login Link */}
          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              قبلاً ثبت‌نام کرده‌اید؟{' '}
              <Link
                to="/login"
                className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
              >
                {PERSIAN_AUTH_MESSAGES.buttons.login}
              </Link>
            </p>
          </div>
        </form>
      </div>
    </motion.div>
  );
};

export default RegisterForm;
