import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico'],
      manifest: {
        name: 'آرامش پوست - محصولات مراقبت از پوست و زیبایی',
        short_name: 'آرامش پوست',
        description: 'فروشگاه آنلاین محصولات مراقبت از پوست، آرایشی و بهداشتی با کیفیت بالا و قیمت مناسب',
        theme_color: '#eb4d7c',
        background_color: '#fefbf8',
        display: 'standalone',
        orientation: 'portrait-primary',
        scope: '/',
        start_url: '/',
        lang: 'fa',
        dir: 'rtl',
        icons: [
          {
            src: 'icons/icon-72x72.png',
            sizes: '72x72',
            type: 'image/png'
          },
          {
            src: 'icons/icon-96x96.png',
            sizes: '96x96',
            type: 'image/png'
          },
          {
            src: 'icons/icon-128x128.png',
            sizes: '128x128',
            type: 'image/png'
          },
          {
            src: 'icons/icon-144x144.png',
            sizes: '144x144',
            type: 'image/png'
          },
          {
            src: 'icons/icon-152x152.png',
            sizes: '152x152',
            type: 'image/png'
          },
          {
            src: 'icons/icon-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icons/icon-384x384.png',
            sizes: '384x384',
            type: 'image/png'
          },
          {
            src: 'icons/icon-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ],
        shortcuts: [
          {
            name: 'محصولات',
            short_name: 'محصولات',
            description: 'مشاهده تمام محصولات',
            url: '/products',
            icons: [{ src: 'icons/shortcut-products.png', sizes: '96x96' }]
          },
          {
            name: 'سبد خرید',
            short_name: 'سبد خرید',
            description: 'مشاهده سبد خرید',
            url: '/cart',
            icons: [{ src: 'icons/shortcut-cart.png', sizes: '96x96' }]
          },
          {
            name: 'علاقه‌مندی‌ها',
            short_name: 'علاقه‌مندی‌ها',
            description: 'مشاهده محصولات مورد علاقه',
            url: '/wishlist',
            icons: [{ src: 'icons/shortcut-wishlist.png', sizes: '96x96' }]
          },
          {
            name: 'باشگاه مشتریان',
            short_name: 'باشگاه مشتریان',
            description: 'مشاهده امتیازات و جوایز',
            url: '/loyalty',
            icons: [{ src: 'icons/shortcut-loyalty.png', sizes: '96x96' }]
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        navigateFallback: '/index.html',
        navigateFallbackDenylist: [
          /^\/api/,
          /^\/admin\/api/,
          /\.js$/,
          /\.css$/,
          /\.png$/,
          /\.svg$/,
          /\.ico$/,
          /registerSW/,
          /sw\.js/,
          /workbox/,
          /manifest\.json/
        ],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          }
        ]
      },
      devOptions: {
        enabled: false
      }
    })
  ],

  build: {
    // Performance optimizations
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    },
    rollupOptions: {
      output: {
        // Simplified manual chunk splitting to avoid loading issues
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('framer-motion')) {
              return 'animation-vendor';
            }
            if (id.includes('lucide-react')) {
              return 'icons-vendor';
            }
            return 'vendor';
          }
          // Keep all app code in main chunk to avoid loading issues
          return undefined;
        },
        // Optimize chunk file names with better hashing
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `assets/js/[name]-[hash].js`;
        },
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name!.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name]-[hash].[ext]`;
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `assets/fonts/[name]-[hash].[ext]`;
          }
          return `assets/[ext]/[name]-[hash].[ext]`;
        }
      },
      // External dependencies for CDN loading (optional)
      external: [],
      // Optimize imports
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        unknownGlobalSideEffects: false
      }
    },
    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000,
    // Enable source maps for production debugging (disabled for performance)
    sourcemap: false,
    // Optimize CSS
    cssCodeSplit: true,
    // Enable asset inlining for small files
    assetsInlineLimit: 4096,
    // Enable CSS minification
    cssMinify: true,
    // Report compressed size
    reportCompressedSize: true,
    // Write bundle info
    write: true
  },

  // Development optimizations
  server: {
    // Enable HTTP/2
    https: false,
    // Optimize HMR
    hmr: {
      overlay: false
    },
    // Preload modules
    preTransformRequests: true,
    // Enable compression
    compress: true,
    // Enable history API fallback for SPA routing
    historyApiFallback: true
  },

  // Dependency optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react',
      'react-hot-toast',
      'react-helmet-async'
    ],
    exclude: [
      // Exclude large dependencies that should be loaded on demand
    ]
  },

  // Enable experimental features
  experimental: {
    // Enable build optimizations
    renderBuiltUrl: (filename, { hostType }) => {
      if (hostType === 'js') {
        return { js: `/${filename}` };
      } else {
        return { relative: true };
      }
    }
  },
  preview: {
    // Preview server optimizations
    port: 4173,
    strictPort: true
  },
  // CSS preprocessing optimizations
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "src/styles/variables.scss";`
      }
    }
  },
  // Define global constants for performance monitoring
  define: {
    __PERFORMANCE_MONITORING__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
});
