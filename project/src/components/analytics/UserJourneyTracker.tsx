import React, { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAnalytics } from '../../hooks/useAnalytics';
import { UserJourney, AnalyticsEventData } from '../../types/analytics';
import { getSessionId, getUserId, getTrafficSource, getUTMParameters } from '../../utils/analyticsUtils';

interface UserJourneyTrackerProps {
  children: React.ReactNode;
  enablePathTracking?: boolean;
  enableTimeTracking?: boolean;
  enableInteractionTracking?: boolean;
  enableScrollTracking?: boolean;
  enableEngagementTracking?: boolean;
  maxJourneyLength?: number;
  sessionTimeoutMinutes?: number;
}

const UserJourneyTracker: React.FC<UserJourneyTrackerProps> = ({
  children,
  enablePathTracking = true,
  enableTimeTracking = true,
  enableInteractionTracking = true,
  enableScrollTracking = true,
  enableEngagementTracking = true,
  maxJourneyLength = 100,
  sessionTimeoutMinutes = 30
}) => {
  const location = useLocation();
  const { trackEvent } = useAnalytics();
  
  const journeyRef = useRef<UserJourney | null>(null);
  const pageStartTimeRef = useRef<number>(Date.now());
  const interactionCountRef = useRef<number>(0);
  const scrollDepthRef = useRef<number>(0);
  const lastActivityRef = useRef<number>(Date.now());
  const engagementTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize or get existing journey
  const initializeJourney = useCallback(() => {
    const sessionId = getSessionId();
    const userId = getUserId();
    const existingJourney = sessionStorage.getItem('user_journey');
    
    if (existingJourney) {
      try {
        const parsed = JSON.parse(existingJourney) as UserJourney;
        
        // Check if session is still valid
        const sessionTimeout = sessionTimeoutMinutes * 60 * 1000;
        const isSessionValid = Date.now() - lastActivityRef.current < sessionTimeout;
        
        if (isSessionValid && parsed.session_id === sessionId) {
          journeyRef.current = parsed;
          return;
        }
      } catch (error) {
        console.warn('Failed to parse existing journey:', error);
      }
    }
    
    // Create new journey
    const utmParams = getUTMParameters();
    const trafficSource = getTrafficSource();
    
    journeyRef.current = {
      session_id: sessionId,
      user_id: userId,
      start_time: Date.now(),
      pages_visited: [],
      events: [],
      conversion_events: [],
      total_engagement_time: 0,
      traffic_source: {
        source: utmParams.utm_source || trafficSource,
        medium: utmParams.utm_medium || 'unknown',
        campaign: utmParams.utm_campaign
      }
    };
    
    // Track journey start
    trackEvent('journey_start', {
      session_id: sessionId,
      user_id: userId,
      traffic_source: trafficSource,
      utm_source: utmParams.utm_source,
      utm_medium: utmParams.utm_medium,
      utm_campaign: utmParams.utm_campaign
    });
  }, [sessionTimeoutMinutes, trackEvent]);

  // Save journey to session storage
  const saveJourney = useCallback(() => {
    if (journeyRef.current) {
      try {
        sessionStorage.setItem('user_journey', JSON.stringify(journeyRef.current));
      } catch (error) {
        console.warn('Failed to save journey:', error);
      }
    }
  }, []);

  // Add event to journey
  const addEventToJourney = useCallback((eventData: Partial<AnalyticsEventData>) => {
    if (!journeyRef.current) return;
    
    const event: AnalyticsEventData = {
      event_name: eventData.event_name || 'unknown',
      timestamp: Date.now(),
      session_id: journeyRef.current.session_id,
      user_id: journeyRef.current.user_id,
      page_url: window.location.href,
      page_title: document.title,
      user_agent: navigator.userAgent,
      ...eventData
    };
    
    journeyRef.current.events.push(event);
    
    // Limit journey length
    if (journeyRef.current.events.length > maxJourneyLength) {
      journeyRef.current.events = journeyRef.current.events.slice(-maxJourneyLength);
    }
    
    lastActivityRef.current = Date.now();
    saveJourney();
  }, [maxJourneyLength, saveJourney]);

  // Track page visit
  const trackPageVisit = useCallback(() => {
    if (!enablePathTracking || !journeyRef.current) return;
    
    const currentPage = {
      url: window.location.href,
      title: document.title,
      timestamp: Date.now(),
      referrer: document.referrer
    };
    
    // Add to pages visited
    journeyRef.current.pages_visited.push(window.location.pathname);
    
    // Track page view event
    addEventToJourney({
      event_name: 'page_view',
      custom_dimensions: {
        page_path: window.location.pathname,
        page_search: window.location.search,
        page_hash: window.location.hash
      }
    });
    
    // Reset page-specific counters
    pageStartTimeRef.current = Date.now();
    interactionCountRef.current = 0;
    scrollDepthRef.current = 0;
    
    saveJourney();
  }, [enablePathTracking, addEventToJourney, saveJourney]);

  // Track page engagement time
  const trackPageEngagement = useCallback(() => {
    if (!enableTimeTracking || !journeyRef.current) return;
    
    const engagementTime = Date.now() - pageStartTimeRef.current;
    journeyRef.current.total_engagement_time += engagementTime;
    
    addEventToJourney({
      event_name: 'page_engagement',
      custom_dimensions: {
        engagement_time: engagementTime,
        interaction_count: interactionCountRef.current,
        max_scroll_depth: scrollDepthRef.current
      }
    });
    
    saveJourney();
  }, [enableTimeTracking, addEventToJourney, saveJourney]);

  // Track user interactions
  useEffect(() => {
    if (!enableInteractionTracking) return;
    
    const trackInteraction = (event: Event) => {
      interactionCountRef.current++;
      lastActivityRef.current = Date.now();
      
      const target = event.target as HTMLElement;
      const interactionType = event.type;
      
      addEventToJourney({
        event_name: 'user_interaction',
        custom_dimensions: {
          interaction_type: interactionType,
          element_tag: target.tagName.toLowerCase(),
          element_class: target.className,
          element_id: target.id,
          interaction_count: interactionCountRef.current
        }
      });
    };
    
    // Track various interaction types
    const events = ['click', 'keydown', 'touchstart', 'mousemove'];
    events.forEach(eventType => {
      document.addEventListener(eventType, trackInteraction, { passive: true });
    });
    
    return () => {
      events.forEach(eventType => {
        document.removeEventListener(eventType, trackInteraction);
      });
    };
  }, [enableInteractionTracking, addEventToJourney]);

  // Track scroll depth
  useEffect(() => {
    if (!enableScrollTracking) return;
    
    const trackScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > scrollDepthRef.current) {
        scrollDepthRef.current = scrollPercent;
        lastActivityRef.current = Date.now();
        
        // Track significant scroll milestones
        if (scrollPercent % 25 === 0 && scrollPercent > 0) {
          addEventToJourney({
            event_name: 'scroll_milestone',
            custom_dimensions: {
              scroll_depth: scrollPercent,
              page_height: document.documentElement.scrollHeight,
              viewport_height: window.innerHeight
            }
          });
        }
      }
    };
    
    window.addEventListener('scroll', trackScroll, { passive: true });
    return () => window.removeEventListener('scroll', trackScroll);
  }, [enableScrollTracking, addEventToJourney]);

  // Engagement time tracking
  useEffect(() => {
    if (!enableEngagementTracking) return;
    
    const startEngagementTimer = () => {
      if (engagementTimerRef.current) {
        clearInterval(engagementTimerRef.current);
      }
      
      engagementTimerRef.current = setInterval(() => {
        const timeSinceLastActivity = Date.now() - lastActivityRef.current;
        
        // If user has been inactive for more than 30 seconds, pause tracking
        if (timeSinceLastActivity > 30000) {
          return;
        }
        
        // Track engagement every 30 seconds of active time
        if (journeyRef.current) {
          journeyRef.current.total_engagement_time += 30000;
          
          addEventToJourney({
            event_name: 'engagement_heartbeat',
            custom_dimensions: {
              total_engagement_time: journeyRef.current.total_engagement_time,
              page_engagement_time: Date.now() - pageStartTimeRef.current,
              is_active: timeSinceLastActivity < 5000
            }
          });
        }
      }, 30000);
    };
    
    startEngagementTimer();
    
    return () => {
      if (engagementTimerRef.current) {
        clearInterval(engagementTimerRef.current);
      }
    };
  }, [enableEngagementTracking, addEventToJourney]);

  // Initialize journey on mount
  useEffect(() => {
    initializeJourney();
  }, [initializeJourney]);

  // Track page visits on route change
  useEffect(() => {
    trackPageVisit();
  }, [location.pathname, trackPageVisit]);

  // Track page engagement when leaving page
  useEffect(() => {
    const handleBeforeUnload = () => {
      trackPageEngagement();
      
      // Mark journey end
      if (journeyRef.current) {
        journeyRef.current.end_time = Date.now();
        journeyRef.current.exit_page = window.location.pathname;
        
        // Calculate bounce rate (single page session)
        const bounceRate = journeyRef.current.pages_visited.length === 1 ? 1 : 0;
        journeyRef.current.bounce_rate = bounceRate;
        
        // Track journey end
        trackEvent('journey_end', {
          session_id: journeyRef.current.session_id,
          total_pages: journeyRef.current.pages_visited.length,
          total_engagement_time: journeyRef.current.total_engagement_time,
          bounce_rate: bounceRate,
          exit_page: journeyRef.current.exit_page
        });
        
        saveJourney();
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [trackPageEngagement, trackEvent, saveJourney]);

  // Expose journey data for external access
  useEffect(() => {
    (window as any).getUserJourney = () => journeyRef.current;
    (window as any).addJourneyEvent = addEventToJourney;
  }, [addEventToJourney]);

  return <>{children}</>;
};

export default UserJourneyTracker;
