import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Copy, 
  CheckCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { ErrorFallbackProps } from './ErrorBoundary';

interface ErrorFallbackComponentProps extends ErrorFallbackProps {
  variant?: 'full' | 'inline' | 'minimal';
  showDetails?: boolean;
  showReportButton?: boolean;
  showHomeButton?: boolean;
  className?: string;
}

const ErrorFallback: React.FC<ErrorFallbackComponentProps> = ({
  error,
  errorInfo,
  resetError,
  errorId,
  variant = 'full',
  showDetails = true,
  showReportButton = true,
  showHomeButton = true,
  className = ''
}) => {
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleCopyError = async () => {
    const errorDetails = {
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      errorId,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // console.error('Failed to copy error details:', err);
    }
  };

  const handleReportError = () => {
    // Open email client with error details
    const subject = encodeURIComponent(`خطای اپلیکیشن - ${errorId}`);
    const body = encodeURIComponent(`
خطای زیر در اپلیکیشن آرامش پوست رخ داده است:

شناسه خطا: ${errorId}
زمان: ${new Date().toLocaleString('fa-IR')}
صفحه: ${window.location.href}
پیام خطا: ${error?.message}

جزئیات تکنیکی:
${error?.stack}

لطفاً این خطا را بررسی کنید.
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  // Minimal variant for inline errors
  if (variant === 'minimal') {
    return (
      <div className={`inline-flex items-center gap-2 text-red-600 text-sm ${className}`}>
        <AlertTriangle className="w-4 h-4" />
        <span>خطایی رخ داده است</span>
        <button
          onClick={resetError}
          className="text-primary-600 hover:text-primary-700 underline"
        >
          تلاش مجدد
        </button>
      </div>
    );
  }

  // Inline variant for component-level errors
  if (variant === 'inline') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`
          bg-red-50 border border-red-200 rounded-lg p-4 text-center
          ${className}
        `}
      >
        <div className="flex items-center justify-center gap-2 text-red-600 mb-3">
          <AlertTriangle className="w-5 h-5" />
          <h3 className="font-medium">خطایی رخ داده است</h3>
        </div>
        
        <p className="text-red-700 text-sm mb-4">
          متأسفانه در بارگذاری این بخش خطایی رخ داده است. لطفاً دوباره تلاش کنید.
        </p>
        
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={resetError}
            className="
              inline-flex items-center gap-2 px-4 py-2 bg-red-600 text-white 
              rounded-lg hover:bg-red-700 transition-colors text-sm
            "
          >
            <RefreshCw className="w-4 h-4" />
            تلاش مجدد
          </button>
          
          {showDetails && (
            <button
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="
                inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700
                rounded-lg hover:bg-gray-200 transition-colors text-sm
              "
            >
              <Bug className="w-4 h-4" />
              جزئیات
              {showErrorDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>
          )}
        </div>

        {showErrorDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-4 p-3 bg-gray-100 rounded text-left text-xs font-mono overflow-auto max-h-40"
          >
            <div className="text-red-600 mb-2">Error: {error?.message}</div>
            {errorId && <div className="text-gray-600">ID: {errorId}</div>}
          </motion.div>
        )}
      </motion.div>
    );
  }

  // Full page error fallback
  return (
    <div className={`min-h-screen bg-gray-50 flex items-center justify-center p-4 ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center"
      >
        {/* Error Icon */}
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>

        {/* Error Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          خطایی رخ داده است
        </h1>
        
        <p className="text-gray-600 mb-6">
          متأسفانه در اجرای برنامه خطایی رخ داده است. تیم فنی ما از این مشکل مطلع شده‌اند.
        </p>

        {/* Error ID */}
        {errorId && (
          <div className="bg-gray-100 rounded-lg p-3 mb-6">
            <div className="text-sm text-gray-600 mb-1">شناسه خطا:</div>
            <div className="font-mono text-sm text-gray-800">{errorId}</div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={resetError}
            className="
              w-full flex items-center justify-center gap-2 px-4 py-3 
              bg-primary-600 text-white rounded-lg hover:bg-primary-700 
              transition-colors font-medium
            "
          >
            <RefreshCw className="w-4 h-4" />
            تلاش مجدد
          </button>

          {showHomeButton && (
            <button
              onClick={handleGoHome}
              className="
                w-full flex items-center justify-center gap-2 px-4 py-3 
                bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 
                transition-colors font-medium
              "
            >
              <Home className="w-4 h-4" />
              بازگشت به صفحه اصلی
            </button>
          )}

          <div className="flex gap-2">
            {showReportButton && (
              <button
                onClick={handleReportError}
                className="
                  flex-1 flex items-center justify-center gap-2 px-4 py-2 
                  bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 
                  transition-colors text-sm
                "
              >
                <Bug className="w-4 h-4" />
                گزارش خطا
              </button>
            )}

            {showDetails && (
              <button
                onClick={handleCopyError}
                className="
                  flex-1 flex items-center justify-center gap-2 px-4 py-2 
                  bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 
                  transition-colors text-sm
                "
              >
                {copied ? (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    کپی شد
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    کپی جزئیات
                  </>
                )}
              </button>
            )}
          </div>
        </div>

        {/* Error Details (Collapsible) */}
        {showDetails && (
          <div className="mt-6">
            <button
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="
                flex items-center justify-center gap-2 text-gray-500 
                hover:text-gray-700 transition-colors text-sm
              "
            >
              جزئیات تکنیکی
              {showErrorDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>

            {showErrorDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-3 p-3 bg-gray-100 rounded text-left text-xs font-mono overflow-auto max-h-40"
              >
                <div className="text-red-600 mb-2">
                  <strong>Error:</strong> {error?.message}
                </div>
                {error?.stack && (
                  <div className="text-gray-600 whitespace-pre-wrap">
                    <strong>Stack:</strong><br />
                    {error.stack}
                  </div>
                )}
              </motion.div>
            )}
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default ErrorFallback;
