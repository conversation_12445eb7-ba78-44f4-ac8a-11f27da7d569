/**
 * API Configuration for GlowRoya Frontend
 * Centralized configuration for all API endpoints and settings
 */

// Environment-based API configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

// API endpoints configuration
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    VERIFY_EMAIL: '/auth/verify-email',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },

  // Admin Authentication
  ADMIN_AUTH: {
    LOGIN: '/admin/auth/login',
    LOGOUT: '/admin/auth/logout',
    PROFILE: '/admin/auth/profile',
    REFRESH: '/admin/auth/refresh',
  },

  // Products
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products/:id',
    SEARCH: '/products/search',
    FEATURED: '/products/featured',
    NEW: '/products/new',
    BESTSELLERS: '/products/bestsellers',
    DISCOUNTED: '/products/discounted',
    RELATED: '/products/:id/related',
    REVIEWS: '/products/:id/reviews',
  },

  // Admin Products
  ADMIN_PRODUCTS: {
    LIST: '/admin/products',
    CREATE: '/admin/products',
    DETAIL: '/admin/products/:id',
    UPDATE: '/admin/products/:id',
    DELETE: '/admin/products/:id',
    INVENTORY: '/admin/inventory',
    INVENTORY_UPDATE: '/admin/inventory/:id',
    LOW_STOCK: '/admin/inventory/low-stock',
  },

  // Categories
  CATEGORIES: {
    LIST: '/categories',
    DETAIL: '/categories/:id',
  },

  // Admin Categories
  ADMIN_CATEGORIES: {
    LIST: '/admin/categories',
    CREATE: '/admin/categories',
    DETAIL: '/admin/categories/:id',
    UPDATE: '/admin/categories/:id',
    DELETE: '/admin/categories/:id',
  },

  // Reviews
  REVIEWS: {
    LIST: '/reviews',
    CREATE: '/products/:id/reviews',
    VOTE: '/reviews/:id/vote',
  },

  // Admin Reviews
  ADMIN_REVIEWS: {
    LIST: '/admin/reviews',
    DETAIL: '/admin/reviews/:id',
    MODERATE: '/admin/reviews/:id/moderate',
    DELETE: '/admin/reviews/:id',
  },

  // Orders
  ORDERS: {
    LIST: '/orders',
    CREATE: '/orders',
    DETAIL: '/orders/:id',
    CANCEL: '/orders/:id/cancel',
  },

  // Admin Orders
  ADMIN_ORDERS: {
    LIST: '/admin/orders',
    DETAIL: '/admin/orders/:id',
    UPDATE_STATUS: '/admin/orders/:id/status',
    PENDING: '/admin/orders/pending',
    PROCESSING: '/admin/orders/processing',
    SHIPPED: '/admin/orders/shipped',
    DELIVERED: '/admin/orders/delivered',
    CANCELLED: '/admin/orders/cancelled',
  },

  // Customers
  CUSTOMERS: {
    PROFILE: '/customers/profile',
    UPDATE_PROFILE: '/customers/profile',
    ORDERS: '/customers/orders',
    WISHLIST: '/customers/wishlist',
    ADDRESSES: '/customers/addresses',
  },

  // Admin Customers
  ADMIN_CUSTOMERS: {
    LIST: '/admin/customers',
    DETAIL: '/admin/customers/:id',
    UPDATE: '/admin/customers/:id',
    DELETE: '/admin/customers/:id',
  },

  // Cart & Wishlist
  CART: {
    GET: '/cart',
    ADD: '/cart/items',
    UPDATE: '/cart/items/:id',
    REMOVE: '/cart/items/:id',
    CLEAR: '/cart/clear',
  },

  WISHLIST: {
    GET: '/wishlist',
    ADD: '/wishlist/items',
    REMOVE: '/wishlist/items/:id',
    CLEAR: '/wishlist/clear',
  },

  // Analytics
  ADMIN_ANALYTICS: {
    OVERVIEW: '/admin/analytics/overview',
    SALES: '/admin/analytics/sales',
    CUSTOMERS: '/admin/analytics/customers',
    TRAFFIC: '/admin/analytics/traffic',
    PRODUCTS: '/admin/analytics/products',
  },

  // Notifications
  ADMIN_NOTIFICATIONS: {
    LIST: '/admin/notifications',
    CREATE: '/admin/notifications',
    MARK_READ: '/admin/notifications/:id/read',
    MARK_ALL_READ: '/admin/notifications/mark-all-read',
    DELETE: '/admin/notifications/:id',
  },

  // Content Management
  CONTENT: {
    HOME: '/content/home',
    TESTIMONIALS: '/content/testimonials',
  },

  ADMIN_CONTENT: {
    HOME: '/admin/content/home',
    TESTIMONIALS: '/admin/testimonials',
  },

  // Brands
  BRANDS: {
    LIST: '/brands',
    DETAIL: '/brands/:id',
  },

  ADMIN_BRANDS: {
    LIST: '/admin/brands',
    CREATE: '/admin/brands',
    DETAIL: '/admin/brands/:id',
    UPDATE: '/admin/brands/:id',
    DELETE: '/admin/brands/:id',
  },

  // Loyalty Program
  LOYALTY: {
    MEMBER: '/loyalty/member',
    EARN: '/loyalty/earn',
    REDEEM: '/loyalty/redeem',
    HISTORY: '/loyalty/history',
  },

  // Newsletter
  NEWSLETTER: {
    SUBSCRIBE: '/newsletter/subscribe',
    UNSUBSCRIBE: '/newsletter/unsubscribe',
  },

  // File Upload
  UPLOAD: {
    IMAGE: '/upload/image',
    MULTIPLE: '/upload/multiple',
  },

  // Health Check
  HEALTH: '/health',
} as const;

// Request timeout configuration
export const API_TIMEOUT = {
  DEFAULT: 30000, // 30 seconds
  UPLOAD: 120000, // 2 minutes for file uploads
  LONG_RUNNING: 300000, // 5 minutes for long operations
} as const;

// Cache configuration
export const CACHE_CONFIG = {
  PRODUCTS: {
    duration: 5 * 60 * 1000, // 5 minutes
    key: 'products_cache'
  },
  CATEGORIES: {
    duration: 10 * 60 * 1000, // 10 minutes
    key: 'categories_cache'
  },
  BRANDS: {
    duration: 10 * 60 * 1000, // 10 minutes
    key: 'brands_cache'
  },
  USER_DATA: {
    duration: 30 * 60 * 1000, // 30 minutes
    key: 'user_cache'
  },
  ANALYTICS: {
    duration: 1 * 60 * 1000, // 1 minute
    key: 'analytics_cache'
  },
  TESTIMONIALS: {
    duration: 10 * 60 * 1000, // 10 minutes
    key: 'testimonials_cache'
  }
} as const;

// Helper function to build full API URL
export const buildApiUrl = (endpoint: string): string => {
  return `${API_BASE_URL}/api/v1${endpoint}`;
};

// Helper function to replace URL parameters
export const replaceUrlParams = (url: string, params: Record<string, string | number>): string => {
  let result = url;
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, String(value));
  });
  return result;
};

// Helper function to get full image URL
export const getFullImageUrl = (imagePath?: string): string => {
  if (!imagePath) return '/images/placeholder.jpg';
  if (imagePath.startsWith('http')) return imagePath;
  return `${API_BASE_URL}${imagePath}`;
};

// API Error codes
export const API_ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const;

// Persian error messages
export const PERSIAN_API_ERRORS: Record<string, string> = {
  [API_ERROR_CODES.UNAUTHORIZED]: 'دسترسی غیرمجاز',
  [API_ERROR_CODES.FORBIDDEN]: 'عدم دسترسی',
  [API_ERROR_CODES.NOT_FOUND]: 'یافت نشد',
  [API_ERROR_CODES.VALIDATION_ERROR]: 'خطای اعتبارسنجی',
  [API_ERROR_CODES.SERVER_ERROR]: 'خطای سرور',
  [API_ERROR_CODES.NETWORK_ERROR]: 'خطا در اتصال به سرور',
  [API_ERROR_CODES.TIMEOUT_ERROR]: 'زمان انتظار تمام شد',
  
  // Specific error messages
  'PRODUCT_FETCH_FAILED': 'خطا در بارگذاری محصولات',
  'CATEGORY_FETCH_FAILED': 'خطا در بارگذاری دسته‌بندی‌ها',
  'REVIEW_SUBMIT_FAILED': 'خطا در ثبت نظر',
  'INVENTORY_UPDATE_FAILED': 'خطا در به‌روزرسانی موجودی',
  'USER_CREATE_FAILED': 'خطا در ایجاد کاربر',
  'ANALYTICS_FETCH_FAILED': 'خطا در بارگذاری آمار',
  'NOTIFICATION_FETCH_FAILED': 'خطا در بارگذاری اعلان‌ها',
  'CONTENT_UPDATE_FAILED': 'خطا در به‌روزرسانی محتوا',
  'LOGIN_FAILED': 'خطا در ورود به سیستم',
  'REGISTRATION_FAILED': 'خطا در ثبت‌نام',
  'PASSWORD_RESET_FAILED': 'خطا در بازنشانی رمز عبور',
  'EMAIL_VERIFICATION_FAILED': 'خطا در تأیید ایمیل',
  'ORDER_CREATE_FAILED': 'خطا در ثبت سفارش',
  'PAYMENT_FAILED': 'خطا در پرداخت',
  'UPLOAD_FAILED': 'خطا در آپلود فایل',
} as const;

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  API_TIMEOUT,
  CACHE_CONFIG,
  buildApiUrl,
  replaceUrlParams,
  getFullImageUrl,
  API_ERROR_CODES,
  PERSIAN_API_ERRORS,
};
