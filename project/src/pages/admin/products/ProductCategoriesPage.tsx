import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Edit,
  Trash2,
  FolderOpen,
  Package,
  RefreshCw,
  Search,
  MoreHorizontal
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import AdminLayout, { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import { ApiService } from '../../../services/apiService';
import AdminProductService from '../../../services/adminProductService';
import toast from 'react-hot-toast';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  productCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: Category[];
}

const ProductCategoriesPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Load categories from API
  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await AdminProductService.getCategories();

      // Transform API response to match our interface
      const transformedCategories: Category[] = response.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: '', // API doesn't provide description yet
        parentId: cat.parentId,
        productCount: cat.productCount,
        isActive: true, // Assume active if returned by API
        createdAt: new Date().toISOString(), // Default to now
        updatedAt: new Date().toISOString(), // Default to now
        children: cat.children?.map(child => ({
          id: child.id,
          name: child.name,
          slug: child.slug,
          description: '',
          productCount: 0,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }));

      setCategories(transformedCategories);
      toast.success('دسته‌بندی‌ها با موفقیت بارگذاری شد');

    } catch (err) {
      console.error('Error loading categories:', err);
      setError('خطا در بارگذاری دسته‌بندی‌ها');
      toast.error('خطا در بارگذاری دسته‌بندی‌ها');

      // Fallback to empty array
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleCreateCategory = () => {
    // Navigate to category creation page
    toast.info('صفحه ایجاد دسته‌بندی در حال توسعه است');
  };

  const handleEditCategory = (category: Category) => {
    // Navigate to category edit page
    toast.info(`ویرایش دسته‌بندی "${category.name}" در حال توسعه است`);
  };

  const handleDeleteCategory = async (category: Category) => {
    if (category.productCount > 0) {
      toast.error('نمی‌توان دسته‌بندی حاوی محصول را حذف کرد');
      return;
    }

    const confirmed = window.confirm(`آیا از حذف دسته‌بندی "${category.name}" اطمینان دارید؟`);
    if (confirmed) {
      try {
        // TODO: Implement delete API call when available
        // await ApiService.delete(`/categories/${category.id}`);

        // For now, just remove from local state
        setCategories(prev => prev.filter(cat => cat.id !== category.id));
        toast.success(`دسته‌بندی "${category.name}" حذف شد`);
      } catch (err) {
        toast.error('خطا در حذف دسته‌بندی');
      }
    }
  };

  const handleToggleStatus = async (category: Category) => {
    try {
      const newStatus = !category.isActive;

      // TODO: Implement status update API call when available
      // await ApiService.patch(`/categories/${category.id}`, { isActive: newStatus });

      // For now, just update local state
      setCategories(prev => prev.map(cat =>
        cat.id === category.id ? { ...cat, isActive: newStatus } : cat
      ));

      toast.success(`دسته‌بندی "${category.name}" ${newStatus ? 'فعال' : 'غیرفعال'} شد`);
    } catch (err) {
      toast.error('خطا در تغییر وضعیت دسته‌بندی');
    }
  };

  const handleRefresh = () => {
    loadCategories();
  };

  const getParentCategoryName = (parentId?: string) => {
    if (!parentId) return '-';
    const parent = categories.find(cat => cat.id === parentId);
    return parent ? parent.name : '-';
  };

  const columns: AdminTableColumn<Category>[] = [
    {
      key: 'name',
      title: 'نام دسته‌بندی',
      sortable: true,
      render: (category: Category) => (
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            category.isActive ? 'bg-blue-100' : 'bg-gray-100'
          }`}>
            <FolderOpen className={`w-5 h-5 ${
              category.isActive ? 'text-blue-600' : 'text-gray-400'
            }`} />
          </div>
          <div>
            <h4 className="font-medium text-gray-900">{category.name}</h4>
            <p className="text-sm text-gray-500">{category.slug}</p>
          </div>
        </div>
      )
    },
    {
      key: 'description',
      title: 'توضیحات',
      render: (category: Category) => (
        <p className="text-sm text-gray-600 max-w-xs truncate">
          {category.description || '-'}
        </p>
      )
    },
    {
      key: 'parent',
      title: 'دسته والد',
      render: (category: Category) => (
        <span className="text-sm text-gray-600">
          {getParentCategoryName(category.parentId)}
        </span>
      )
    },
    {
      key: 'productCount',
      title: 'تعداد محصولات',
      sortable: true,
      align: 'center',
      render: (category: Category) => (
        <div className="flex items-center justify-center">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {category.productCount.toLocaleString('fa-IR')}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (category: Category) => (
        <button
          onClick={() => handleToggleStatus(category)}
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
            category.isActive
              ? 'bg-green-100 text-green-800 hover:bg-green-200'
              : 'bg-red-100 text-red-800 hover:bg-red-200'
          }`}
        >
          {category.isActive ? 'فعال' : 'غیرفعال'}
        </button>
      )
    },
    {
      key: 'updatedAt',
      title: 'آخرین بروزرسانی',
      sortable: true,
      render: (category: Category) => (
        <span className="text-sm text-gray-600">
          {new Date(category.updatedAt).toLocaleDateString('fa-IR')}
        </span>
      )
    }
  ];

  const renderRowActions = (category: Category, index: number) => (
    <div className="flex items-center gap-2">
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Edit}
        onClick={() => handleEditCategory(category)}
      />
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Trash2}
        onClick={() => handleDeleteCategory(category)}
        disabled={category.productCount > 0}
      />
    </div>
  );

  const getCategoryStats = () => {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.isActive).length;
    const totalProducts = categories.reduce((sum, cat) => sum + cat.productCount, 0);
    const parentCategories = categories.filter(cat => !cat.parentId).length;

    return [
      {
        title: 'کل دسته‌بندی‌ها',
        value: totalCategories.toLocaleString('fa-IR'),
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        icon: FolderOpen
      },
      {
        title: 'دسته‌بندی‌های فعال',
        value: activeCategories.toLocaleString('fa-IR'),
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: FolderOpen
      },
      {
        title: 'کل محصولات',
        value: totalProducts.toLocaleString('fa-IR'),
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        icon: Package
      },
      {
        title: 'دسته‌های اصلی',
        value: parentCategories.toLocaleString('fa-IR'),
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        icon: FolderOpen
      }
    ];
  };

  if (!checkPermission('products', 'read')) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600">
            شما دسترسی لازم برای مشاهده دسته‌بندی‌ها را ندارید.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminListLayout
      title="مدیریت دسته‌بندی‌ها"
      subtitle="مدیریت دسته‌بندی‌های محصولات و سازماندهی فروشگاه"
      searchValue={searchValue}
      onSearchChange={setSearchValue}
      searchPlaceholder="جستجو در دسته‌بندی‌ها..."
      filters={
        <div className="flex items-center gap-2">
          {checkPermission('products', 'create') && (
            <AdminButton
              variant="primary"
              size="sm"
              icon={Plus}
              onClick={handleCreateCategory}
            >
              دسته‌بندی جدید
            </AdminButton>
          )}
          
          <AdminButton
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={handleRefresh}
            loading={loading}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin/products')}
          >
            همه محصولات
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Category Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {getCategoryStats().map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Categories Table */}
        <AdminCard>
          <AdminTable
            columns={columns}
            data={filteredCategories}
            loading={loading}
            rowActions={renderRowActions}
            emptyMessage="هیچ دسته‌بندی یافت نشد"
            hoverable
            striped
          />
        </AdminCard>

        {/* Empty State */}
        {!loading && filteredCategories.length === 0 && searchValue && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              هیچ دسته‌بندی یافت نشد
            </h3>
            <p className="text-gray-600 mb-4">
              با کلمات کلیدی مختلف جستجو کنید یا دسته‌بندی جدید ایجاد کنید.
            </p>
            <AdminButton
              variant="outline"
              onClick={() => setSearchValue('')}
            >
              پاک کردن جستجو
            </AdminButton>
          </div>
        )}
      </div>
    </AdminListLayout>
  );
};

export default ProductCategoriesPage;
