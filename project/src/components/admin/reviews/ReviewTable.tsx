import React, { useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';
import {
  Eye,
  Edit,
  MessageSquare,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Star,
  User,
  Package,
  Calendar,
  TrendingUp,
  MoreHorizontal,
  <PERSON>n,
  <PERSON>n<PERSON>ff,
  <PERSON><PERSON>ff,
  Trash2
} from 'lucide-react';
import AdminTable, { AdminTableColumn, AdminTableBadge } from '../common/AdminTable';
import { AdminIconButton } from '../common/AdminButton';
import { AdminReview, PERSIAN_REVIEW_ADMIN_MESSAGES } from '../../../types/adminReview';
import { 
  getModerationStatusColor,
  getContentFlagColor,
  formatPersianDateTime
} from '../../../utils/reviewModeration';
import { formatPersianCurrency } from '../../../utils/customerUtils';

interface ReviewTableProps {
  reviews: AdminReview[];
  loading?: boolean;
  selectedReviews: string[];
  onSelectReview: (reviewId: string) => void;
  onSelectAll: (selected: boolean) => void;
  onViewReview: (review: AdminReview) => void;
  onModerateReview: (review: AdminReview, action: 'approve' | 'reject' | 'flag') => void;
  onTogglePin: (review: AdminReview) => void;
  onToggleHighlight: (review: AdminReview) => void;
  onHideReview: (review: AdminReview) => void;
  onDeleteReview: (review: AdminReview) => void;
  onViewCustomer: (review: AdminReview) => void;
  onViewProduct: (review: AdminReview) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
}

const ReviewTable: React.FC<ReviewTableProps> = ({
  reviews,
  loading = false,
  selectedReviews,
  onSelectReview,
  onSelectAll,
  onViewReview,
  onModerateReview,
  onTogglePin,
  onToggleHighlight,
  onHideReview,
  onDeleteReview,
  onViewCustomer,
  onViewProduct,
  searchValue = '',
  onSearchChange
}) => {
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const getStatusIcon = (status: AdminReview['moderation']['status']) => {
    switch (status) {
      case 'approved':
      case 'auto_approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected':
      case 'auto_rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'flagged':
        return <Flag className="w-4 h-4 text-orange-500" />;
      case 'pending':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'requires_review':
        return <AlertTriangle className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600 mr-1">{rating}</span>
      </div>
    );
  };

  const columns: AdminTableColumn<AdminReview>[] = [
    {
      key: 'select',
      title: '',
      width: '50px',
      render: (review: AdminReview) => (
        <input
          type="checkbox"
          checked={selectedReviews.includes(review.id)}
          onChange={() => onSelectReview(review.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      )
    },
    {
      key: 'review',
      title: 'نظر',
      sortable: true,
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-2 max-w-xs">
          <div className="flex items-center gap-2">
            {renderStars(review.rating)}
            {review.isPinned && <Pin className="w-4 h-4 text-blue-500" />}
            {review.isHighlighted && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
          </div>
          <div>
            <p className="font-medium text-gray-900 truncate" title={review.title}>
              {review.title}
            </p>
            <p className="text-sm text-gray-600 line-clamp-2" title={review.content}>
              {review.content}
            </p>
          </div>
          {review.images && review.images.length > 0 && (
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Package className="w-3 h-3" />
              <span>{review.images.length} تصویر</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'customer',
      title: 'مشتری',
      sortable: true,
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-gray-400" />
            <button
              onClick={() => onViewCustomer(review)}
              className="font-medium text-blue-600 hover:text-blue-800 truncate"
              title={review.customer?.name || `${review.user.firstName} ${review.user.lastName}`}
            >
              {review.customer?.name || `${review.user.firstName} ${review.user.lastName}`}
            </button>
          </div>
          <div className="text-xs text-gray-500">
            {review.customer?.totalReviews || 1} نظر
          </div>
          {review.isVerified && (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
              خرید تأیید شده
            </span>
          )}
          {review.customer?.loyaltyTier && (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
              {review.customer.loyaltyTier}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'product',
      title: 'محصول',
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-1">
          <button
            onClick={() => onViewProduct(review)}
            className="font-medium text-blue-600 hover:text-blue-800 truncate max-w-xs"
            title={review.product.name}
          >
            {review.product.name}
          </button>
          <div className="text-xs text-gray-500">
            {review.product.brand} • {review.product.category}
          </div>
          <div className="text-xs text-gray-600">
            {formatPersianCurrency(review.product.currentPrice)}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            {getStatusIcon(review.moderation?.status || review.moderationStatus?.toLowerCase() || 'pending')}
            <AdminTableBadge
              variant={(review.moderation?.status || review.moderationStatus?.toLowerCase()) === 'approved' ? 'success' :
                      (review.moderation?.status || review.moderationStatus?.toLowerCase()) === 'rejected' ? 'danger' : 'warning'}
            >
              {PERSIAN_REVIEW_ADMIN_MESSAGES.moderationStatus[review.moderation?.status || review.moderationStatus || 'PENDING']}
            </AdminTableBadge>
          </div>
          {review.moderation?.autoModerated && (
            <span className="text-xs text-gray-500">خودکار</span>
          )}
        </div>
      )
    },
    {
      key: 'quality',
      title: 'کیفیت',
      sortable: true,
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-1 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-600">کیفیت:</span>
            <span className={`font-medium ${
              (review.moderation?.qualityScore || 85) >= 70 ? 'text-green-600' :
              (review.moderation?.qualityScore || 85) >= 40 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {review.moderation?.qualityScore || 85}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-600">اسپم:</span>
            <span className={`font-medium ${
              (review.moderation?.spamScore || 10) <= 30 ? 'text-green-600' :
              (review.moderation?.spamScore || 10) <= 60 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {review.moderation?.spamScore || 10}
            </span>
          </div>
          {review.moderation?.contentFlags && review.moderation.contentFlags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {review.moderation.contentFlags.slice(0, 2).map((flag, index) => (
                <span
                  key={index}
                  className={`text-xs px-1.5 py-0.5 rounded ${getContentFlagColor(flag.severity)}`}
                  title={flag.description}
                >
                  {PERSIAN_REVIEW_ADMIN_MESSAGES.contentFlags[flag.type]}
                </span>
              ))}
              {review.moderation.contentFlags.length > 2 && (
                <span className="text-xs text-gray-500">
                  +{review.moderation.contentFlags.length - 2}
                </span>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'engagement',
      title: 'تعامل',
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-1 text-sm">
          <div className="flex items-center gap-1">
            <TrendingUp className="w-3 h-3 text-gray-400" />
            <span className="text-gray-600">
              {review.helpfulVotes}/{review.totalVotes}
            </span>
          </div>
          <div className="text-xs text-gray-500">
            {review.analytics.viewCount} بازدید
          </div>
          {review.analytics.reportCount > 0 && (
            <div className="flex items-center gap-1 text-xs text-red-600">
              <Flag className="w-3 h-3" />
              <span>{review.analytics.reportCount} گزارش</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'date',
      title: 'تاریخ',
      sortable: true,
      render: (review: AdminReview) => (
        <div className="flex flex-col gap-1 text-sm">
          <div className="flex items-center gap-1 text-gray-600">
            <Calendar className="w-3 h-3" />
            <span>{formatPersianDateTime(review.createdAt)}</span>
          </div>
          {review.moderation?.moderatedAt && (
            <div className="text-xs text-gray-500">
              بررسی: {formatPersianDateTime(review.moderation.moderatedAt)}
            </div>
          )}
          {review.moderation?.moderatedByName && (
            <div className="text-xs text-gray-500">
              توسط: {review.moderation.moderatedByName}
            </div>
          )}
        </div>
      )
    }
  ];

  const handleRowClick = (review: AdminReview, index: number) => {
    onViewReview(review);
  };

  const handleMenuToggle = (review: AdminReview, e: React.MouseEvent) => {
    e.stopPropagation();

    if (actionMenuOpen === review.id) {
      setActionMenuOpen(null);
      return;
    }

    const button = buttonRefs.current[review.id];
    if (button) {
      const rect = button.getBoundingClientRect();
      const menuWidth = 200;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate horizontal position
      let left = rect.left + window.scrollX - menuWidth + rect.width;
      if (left < 10) {
        left = rect.right + window.scrollX + 8;
      }
      if (left + menuWidth > viewportWidth - 10) {
        left = rect.left + window.scrollX - menuWidth - 8;
      }

      // Calculate vertical position
      let top = rect.bottom + window.scrollY + 4;
      const menuHeight = 400; // Approximate height for review menu
      if (top + menuHeight > viewportHeight + window.scrollY - 10) {
        top = rect.top + window.scrollY - menuHeight - 4;
      }

      setMenuPosition({ top, left });
    }

    setActionMenuOpen(review.id);
  };

  const renderRowActions = (review: AdminReview, index: number) => (
    <>
      <button
        ref={(el) => { buttonRefs.current[review.id] = el; }}
        onClick={(e) => handleMenuToggle(review, e)}
        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        title="عملیات"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>

      {actionMenuOpen === review.id && createPortal(
        <>
          <div
            className="fixed inset-0 z-[9999]"
            onClick={() => setActionMenuOpen(null)}
          />
          <div
            className="fixed bg-white border border-gray-200 rounded-lg shadow-xl py-1 min-w-[200px] z-[9999]"
            style={{
              top: `${menuPosition.top}px`,
              left: `${menuPosition.left}px`
            }}
          >
          <button
            onClick={(e) => {
              e.stopPropagation();
              onViewReview(review);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            مشاهده جزئیات
          </button>
          
          {(review.moderation?.status === 'pending' || review.moderationStatus === 'PENDING') && (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onModerateReview(review, 'approve');
                  setActionMenuOpen(null);
                }}
                className="w-full px-4 py-2 text-right text-sm text-green-700 hover:bg-gray-50 flex items-center gap-2"
              >
                <CheckCircle className="w-4 h-4" />
                تأیید
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onModerateReview(review, 'reject');
                  setActionMenuOpen(null);
                }}
                className="w-full px-4 py-2 text-right text-sm text-red-700 hover:bg-gray-50 flex items-center gap-2"
              >
                <XCircle className="w-4 h-4" />
                رد
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onModerateReview(review, 'flag');
                  setActionMenuOpen(null);
                }}
                className="w-full px-4 py-2 text-right text-sm text-orange-700 hover:bg-gray-50 flex items-center gap-2"
              >
                <Flag className="w-4 h-4" />
                علامت‌گذاری
              </button>
            </>
          )}
          
          <div className="border-t border-gray-100 my-1" />
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onTogglePin(review);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            {review.isPinned ? <PinOff className="w-4 h-4" /> : <Pin className="w-4 h-4" />}
            {review.isPinned ? 'حذف سنجاق' : 'سنجاق کردن'}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleHighlight(review);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <Star className="w-4 h-4" />
            {review.isHighlighted ? 'حذف برجستگی' : 'برجسته کردن'}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onHideReview(review);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <EyeOff className="w-4 h-4" />
            {review.isHidden ? 'نمایش' : 'مخفی کردن'}
          </button>
          
          <div className="border-t border-gray-100 my-1" />
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDeleteReview(review);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-red-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            حذف
          </button>
          </div>
        </>,
        document.body
      )}
    </>
  );

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedReviews.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedReviews.length} نظر انتخاب شده
            </span>
            <div className="flex items-center gap-2">
              {/* Bulk action buttons would go here */}
            </div>
          </div>
        </motion.div>
      )}

      {/* Select All Checkbox */}
      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          checked={reviews.length > 0 && selectedReviews.length === reviews.length}
          onChange={(e) => onSelectAll(e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span className="text-sm text-gray-600">انتخاب همه</span>
      </div>

      {/* Reviews Table */}
      <AdminTable
        columns={columns}
        data={reviews}
        loading={loading}
        onRowClick={handleRowClick}
        rowActions={renderRowActions}
        searchable={!!onSearchChange}
        searchValue={searchValue}
        onSearchChange={onSearchChange}
        searchPlaceholder="جستجو در نظرات..."
        emptyMessage="هیچ نظری یافت نشد"
        hoverable
        striped
      />
    </div>
  );
};

export default ReviewTable;
