import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Download, 
  Upload, 
  Filter, 
  MoreHorizontal,
  Package,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import AdminButton, { AdminButtonGroup } from '../../../components/admin/common/AdminButton';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminModal, { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import ProductTable from '../../../components/admin/products/ProductTable';
import { useAdminProducts } from '../../../hooks/useAdminProducts';
import { AdminProduct, AdminProductFilters } from '../../../types/adminProduct';
import { formatPrice } from '../../../utils/formatters';
import SEOHead from '../../../components/seo/SEOHead';
import toast from 'react-hot-toast';

const ProductsListPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    products,
    allProducts,
    loading,
    error,
    filters,
    setFilters,
    deleteProduct,
    bulkOperation
  } = useAdminProducts();

  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState<AdminProduct | null>(null);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  const [searchValue, setSearchValue] = useState(filters.search || '');

  // Calculate statistics
  const stats = {
    total: allProducts.length,
    active: allProducts.filter(p => p.status === 'active').length,
    lowStock: allProducts.filter(p => p.trackInventory && p.stock <= p.lowStockThreshold).length,
    outOfStock: allProducts.filter(p => p.stock === 0).length,
    totalValue: allProducts.reduce((sum, p) => sum + (p.price * p.stock), 0)
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setFilters({ ...filters, search: value });
  };

  const handleCreateProduct = () => {
    navigate('/admin/products/create');
  };

  const handleEditProduct = (product: AdminProduct) => {
    navigate(`/admin/products/${product.id}/edit`);
  };

  const handleViewProduct = (product: AdminProduct) => {
    navigate(`/admin/products/${product.id}`);
  };

  const handleDuplicateProduct = (product: AdminProduct) => {
    navigate(`/admin/products/create?duplicate=${product.id}`);
  };

  const handleDeleteProduct = (product: AdminProduct) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const confirmDeleteProduct = async () => {
    if (productToDelete) {
      try {
        await deleteProduct(productToDelete.id);
        setShowDeleteModal(false);
        setProductToDelete(null);
        toast.success('محصول با موفقیت حذف شد');
      } catch (error) {
        toast.error('خطا در حذف محصول');
      }
    }
  };

  const handleBulkDelete = () => {
    if (selectedProducts.length > 0) {
      setShowBulkDeleteModal(true);
    }
  };

  const confirmBulkDelete = async () => {
    try {
      await bulkOperation({
        type: 'delete',
        productIds: selectedProducts
      });
      setSelectedProducts([]);
      setShowBulkDeleteModal(false);
      toast.success(`${selectedProducts.length} محصول حذف شد`);
    } catch (error) {
      toast.error('خطا در حذف محصولات');
    }
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    toast.success('خروجی محصولات در حال آماده‌سازی است');
  };

  const handleImport = () => {
    // TODO: Implement import functionality
    navigate('/admin/products/import');
  };

  const bulkActions = selectedProducts.length > 0 && (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-4"
    >
      <AdminCard className="bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-blue-900">
            {selectedProducts.length} محصول انتخاب شده
          </span>
          <AdminButtonGroup>
            <AdminButton
              variant="outline"
              size="sm"
              onClick={handleBulkDelete}
            >
              حذف گروهی
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              onClick={() => setSelectedProducts([])}
            >
              لغو انتخاب
            </AdminButton>
          </AdminButtonGroup>
        </div>
      </AdminCard>
    </motion.div>
  );

  const actions = (
    <AdminButtonGroup>
      <AdminButton
        variant="outline"
        icon={Download}
        onClick={handleExport}
      >
        خروجی
      </AdminButton>
      <AdminButton
        variant="outline"
        icon={Upload}
        onClick={handleImport}
      >
        ورودی
      </AdminButton>
      <AdminButton
        variant="primary"
        icon={Plus}
        onClick={handleCreateProduct}
      >
        افزودن محصول
      </AdminButton>
    </AdminButtonGroup>
  );

  if (error) {
    return (
      <AdminListLayout
        title="مدیریت محصولات"
        subtitle="مدیریت کامل محصولات فروشگاه"
      >
        <AdminCard>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">خطا در بارگذاری</h3>
            <p className="text-gray-600">{error}</p>
          </div>
        </AdminCard>
      </AdminListLayout>
    );
  }

  return (
    <>
      <SEOHead
        title="مدیریت محصولات | پنل مدیریت گلو رویا"
        description="مدیریت کامل محصولات فروشگاه گلو رویا"
        robots="noindex, nofollow"
      />

      <AdminListLayout
        title="مدیریت محصولات"
        subtitle="مدیریت کامل محصولات فروشگاه"
        actions={actions}
        searchValue={searchValue}
        onSearchChange={handleSearch}
        searchPlaceholder="جستجو در محصولات..."
      >
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <AdminStatsCard
            title="کل محصولات"
            value={stats.total.toLocaleString('fa-IR')}
            icon={Package}
            color="bg-blue-500"
          />
          <AdminStatsCard
            title="محصولات فعال"
            value={stats.active.toLocaleString('fa-IR')}
            icon={CheckCircle}
            color="bg-green-500"
          />
          <AdminStatsCard
            title="موجودی کم"
            value={stats.lowStock.toLocaleString('fa-IR')}
            icon={AlertTriangle}
            color="bg-yellow-500"
          />
          <AdminStatsCard
            title="ناموجود"
            value={stats.outOfStock.toLocaleString('fa-IR')}
            icon={AlertTriangle}
            color="bg-red-500"
          />
          <AdminStatsCard
            title="ارزش کل موجودی"
            value={formatPrice(stats.totalValue)}
            icon={Package}
            color="bg-purple-500"
          />
        </div>

        {/* Bulk Actions */}
        {bulkActions}

        {/* Products Table */}
        <ProductTable
          products={products}
          loading={loading}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
          onDuplicate={handleDuplicateProduct}
          onViewDetails={handleViewProduct}
          onBulkSelect={setSelectedProducts}
          selectedProducts={selectedProducts}
          searchValue={searchValue}
          onSearchChange={handleSearch}
        />

        {/* Delete Product Modal */}
        <AdminConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDeleteProduct}
          title="حذف محصول"
          message={`آیا از حذف محصول "${productToDelete?.name}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />

        {/* Bulk Delete Modal */}
        <AdminConfirmModal
          isOpen={showBulkDeleteModal}
          onClose={() => setShowBulkDeleteModal(false)}
          onConfirm={confirmBulkDelete}
          title="حذف گروهی محصولات"
          message={`آیا از حذف ${selectedProducts.length} محصول انتخاب شده اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
          confirmText="حذف همه"
          cancelText="انصراف"
          variant="danger"
        />
      </AdminListLayout>
    </>
  );
};

export default ProductsListPage;
