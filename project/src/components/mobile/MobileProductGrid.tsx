import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Grid, List, Filter, SortAsc } from 'lucide-react';
import { Product } from '../../types/product';
import ProductCard from '../common/ProductCard';
import VirtualizedList from '../common/VirtualizedList';
import { useMobileDetection, useViewportSize } from '../../hooks/useMobileDetection';
import {
  getResponsiveColumns,
  calculateOptimalColumns
} from '../../utils/mobileUtils';
import {
  MobileGridConfig,
  DEFAULT_MOBILE_GRID,
  PERSIAN_MOBILE_MESSAGES
} from '../../types/mobile';

interface MobileProductGridProps {
  products: Product[];
  loading?: boolean;
  error?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
  gridConfig?: Partial<MobileGridConfig>;
  showViewToggle?: boolean;
  showSortButton?: boolean;
  showFilterButton?: boolean;
  onFilterClick?: () => void;
  onSortClick?: () => void;
  enableVirtualization?: boolean;
  virtualizedHeight?: number;
  className?: string;
}

type ViewMode = 'grid' | 'list';

const MobileProductGrid: React.FC<MobileProductGridProps> = ({
  products,
  loading = false,
  error,
  onLoadMore,
  hasMore = false,
  gridConfig = {},
  showViewToggle = true,
  showSortButton = true,
  showFilterButton = true,
  onFilterClick,
  onSortClick,
  enableVirtualization = false,
  virtualizedHeight = 600,
  className = ''
}) => {
  const { isMobile, isTablet } = useMobileDetection();
  const { width: viewportWidth } = useViewportSize();
  
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Merge grid configuration with defaults
  const config: MobileGridConfig = { ...DEFAULT_MOBILE_GRID, ...gridConfig };

  // Calculate responsive columns
  const columns = useMemo(() => {
    if (viewMode === 'list') return 1;
    
    const containerWidth = viewportWidth - 32; // Account for padding
    const optimalColumns = calculateOptimalColumns(
      containerWidth,
      config.minItemWidth,
      16 // gap in pixels
    );
    
    const responsiveColumns = getResponsiveColumns(viewportWidth, config);
    
    return Math.min(optimalColumns, responsiveColumns);
  }, [viewportWidth, viewMode, config]);

  // Handle load more
  const handleLoadMore = async () => {
    if (isLoadingMore || !hasMore || !onLoadMore) return;
    
    setIsLoadingMore(true);
    try {
      await onLoadMore();
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Auto-adjust view mode based on device
  useEffect(() => {
    if (isMobile && viewMode === 'grid' && columns === 1) {
      setViewMode('list');
    }
  }, [isMobile, columns, viewMode]);

  // Don't render if not mobile/tablet
  if (!isMobile && !isTablet) return null;

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-red-500 text-lg font-medium mb-2">خطا در بارگذاری</div>
        <div className="text-gray-600">{error}</div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Mobile Controls */}
      <div className="flex items-center justify-between mb-4 px-4">
        {/* View Toggle */}
        {showViewToggle && (
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white text-rose-600 shadow-sm'
                  : 'text-gray-600'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-rose-600 shadow-sm'
                  : 'text-gray-600'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center space-x-2 space-x-reverse">
          {showFilterButton && (
            <button
              onClick={onFilterClick}
              className="flex items-center space-x-2 space-x-reverse px-3 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span className="text-sm font-medium">
                {PERSIAN_MOBILE_MESSAGES.filters.showFilters}
              </span>
            </button>
          )}
          
          {showSortButton && (
            <button
              onClick={onSortClick}
              className="flex items-center space-x-2 space-x-reverse px-3 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <SortAsc className="w-4 h-4" />
              <span className="text-sm font-medium">
                {PERSIAN_MOBILE_MESSAGES.navigation.sort}
              </span>
            </button>
          )}
        </div>
      </div>

      {/* Products Grid/List */}
      <AnimatePresence>
        {loading && products.length === 0 ? (
          <LoadingGrid columns={columns} />
        ) : products.length === 0 ? (
          <EmptyState />
        ) : enableVirtualization && products.length > 20 ? (
          <VirtualizedList
            items={products}
            itemHeight={viewMode === 'list' ? 120 : 280}
            containerHeight={virtualizedHeight}
            renderItem={(product, index, style) => (
              <div style={style} className="px-4 pb-4">
                <ProductCard
                  product={product}
                  variant={viewMode === 'list' ? 'horizontal' : 'vertical'}
                  showQuickActions={!isMobile}
                  className={viewMode === 'list' ? 'w-full' : ''}
                />
              </div>
            )}
            getItemKey={(product) => product.id}
            overscan={5}
          />
        ) : (
          <motion.div
            key={`${viewMode}-${columns}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className={`px-4 ${
              viewMode === 'grid'
                ? `grid gap-4 grid-cols-${columns}`
                : 'space-y-4'
            }`}
            style={
              viewMode === 'grid'
                ? {
                    gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
                    gap: config.gap
                  }
                : undefined
            }
          >
            {products.map((product, index) => (
              <div key={product.id}>
                <ProductCard
                  product={product}
                  variant={viewMode === 'list' ? 'horizontal' : 'vertical'}
                  showQuickActions={!isMobile}
                  className={viewMode === 'list' ? 'w-full' : ''}
                />
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Load More Button */}
      {hasMore && products.length > 0 && (
        <div className="flex justify-center mt-8 px-4">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="px-6 py-3 bg-rose-600 text-white rounded-lg font-medium hover:bg-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoadingMore ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>{PERSIAN_MOBILE_MESSAGES.grid.loading}</span>
              </div>
            ) : (
              PERSIAN_MOBILE_MESSAGES.grid.loadMore
            )}
          </button>
        </div>
      )}
    </div>
  );
};

// Loading skeleton component
const LoadingGrid: React.FC<{ columns: number }> = ({ columns }) => (
  <div
    className="grid gap-4 px-4"
    style={{
      gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`
    }}
  >
    {Array.from({ length: columns * 3 }).map((_, index) => (
      <div
        key={index}
        className="bg-gray-200 rounded-lg animate-pulse"
        style={{ aspectRatio: '3/4', minHeight: '200px' }}
      />
    ))}
  </div>
);

// Empty state component
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center justify-center py-12 text-center px-4">
    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
      <Grid className="w-8 h-8 text-gray-400" />
    </div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      {PERSIAN_MOBILE_MESSAGES.grid.noResults}
    </h3>
    <p className="text-gray-600 max-w-sm">
      متأسفانه محصولی با فیلترهای انتخابی شما یافت نشد. لطفاً فیلترها را تغییر دهید.
    </p>
  </div>
);

export default MobileProductGrid;
