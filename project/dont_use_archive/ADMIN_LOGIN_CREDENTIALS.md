# Admin Login Credentials

## Available Test Accounts

### 1. Super Admin (Full Access)
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** Super Admin
- **Permissions:** Full access to all features including user management
- **Department:** مدیریت

### 2. Admin (Limited Access)
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** Admin
- **Permissions:** Most features except user management and some settings
- **Department:** فروش

### 3. Moderator (Content Focus)
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** Moderator
- **Permissions:** Content moderation, limited product/order access
- **Department:** محتوا

## User Management Access

**IMPORTANT:** Only the **Super Admin** account (`<EMAIL>`) has access to:
- `/admin/users` - User Management Dashboard
- `/admin/users/admins` - Admin Users Management
- `/admin/users/roles` - Roles & Permissions

## Permission Matrix

| Feature | Super Admin | Admin | Moderator | Viewer |
|---------|-------------|-------|-----------|--------|
| User Management | ✅ | ❌ | ❌ | ❌ |
| Product Management | ✅ | ✅ | ✅ | ✅ (Read Only) |
| Order Management | ✅ | ✅ | ✅ | ✅ (Read Only) |
| Customer Management | ✅ | ✅ | ❌ | ✅ (Read Only) |
| Review Management | ✅ | ✅ | ✅ | ✅ (Read Only) |
| Content Management | ✅ | ✅ | ✅ | ❌ |
| Analytics | ✅ | ✅ | ✅ | ✅ (Read Only) |
| System Settings | ✅ | ✅ (Read Only) | ❌ | ❌ |

## Testing User Management

1. **Login** with Super Admin credentials
2. **Navigate** to `/admin/users` or use the sidebar menu
3. **Test Features:**
   - View admin users list
   - Check roles and permissions matrix
   - Test user management operations (view, edit, status toggle)

## Security Notes

- User management is restricted to Super Admin role only
- This is by design for security purposes
- Regular admins cannot manage other admin users
- Permission system is role-based and strictly enforced
