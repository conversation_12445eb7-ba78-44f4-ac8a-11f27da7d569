# Task 1.7: Review and Rating System API Documentation

## Overview

This document provides comprehensive documentation for the Review and Rating System API implemented in Task 1.7. The API includes full CRUD operations for reviews, review moderation, voting system, analytics, and loyalty point integration with Persian language support.

## Base URL
```
http://localhost:3001/api/v1/reviews
```

## Authentication

- **Public routes**: Get reviews, product statistics (with optional authentication)
- **Customer routes**: Require user authentication
- **Admin routes**: Require admin authentication with JW<PERSON> token

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true|false,
  "message": "Persian message",
  "data": {
    // Response data
  },
  "timestamp": "2025-06-06T03:00:00.000Z"
}
```

## Error Response Format

```json
{
  "success": false,
  "error": {
    "message": "Persian error message",
    "code": "ERROR_CODE"
  },
  "timestamp": "2025-06-06T03:00:00.000Z",
  "path": "/api/v1/reviews",
  "method": "POST"
}
```

## Data Models

### Review Model
```json
{
  "id": "string",
  "productId": "string",
  "userId": "string",
  "rating": 1-5,
  "title": "string (optional)",
  "content": "string",
  "pros": ["string"],
  "cons": ["string"],
  "isVerified": boolean,
  "isApproved": boolean,
  "isRecommended": boolean,
  "helpfulCount": number,
  "unhelpfulCount": number,
  "skinType": "string (optional)",
  "ageRange": "string (optional)",
  "usageDuration": "string (optional)",
  "moderationStatus": "PENDING|APPROVED|REJECTED|FLAGGED",
  "moderationNotes": "string (optional)",
  "createdAt": "ISO date",
  "updatedAt": "ISO date",
  "user": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "avatar": "string|null"
  },
  "product": {
    "id": "string",
    "name": "string",
    "slug": "string"
  },
  "images": [
    {
      "id": "string",
      "url": "string",
      "alt": "string",
      "sortOrder": number
    }
  ],
  "votes": [
    {
      "id": "string",
      "userId": "string",
      "isHelpful": boolean
    }
  ],
  "responses": [
    {
      "id": "string",
      "userId": "string",
      "content": "string",
      "isOfficial": boolean,
      "createdAt": "ISO date",
      "user": {
        "id": "string",
        "firstName": "string",
        "lastName": "string",
        "role": "string"
      }
    }
  ]
}
```

### Review Statistics Model
```json
{
  "totalReviews": number,
  "averageRating": number,
  "ratingDistribution": {
    "1": number,
    "2": number,
    "3": number,
    "4": number,
    "5": number
  },
  "verifiedPurchasePercentage": number,
  "recommendationPercentage": number
}
```

## API Endpoints

### Public Routes

#### Get Reviews
```http
GET /api/v1/reviews
```

**Query Parameters:**
- `productId` (string, optional): Filter by product ID
- `rating` (number, optional): Filter by rating (1-5)
- `isVerified` (boolean, optional): Filter by verified purchases
- `isApproved` (boolean, optional): Filter by approval status
- `isRecommended` (boolean, optional): Filter by recommendation
- `skinType` (string, optional): Filter by skin type
- `ageRange` (string, optional): Filter by age range
- `search` (string, optional): Search in title and content
- `sortBy` (string, optional): Sort by newest|oldest|rating_high|rating_low|helpful
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "message": "نظرات با موفقیت دریافت شد",
  "data": {
    "reviews": [Review],
    "total": number,
    "page": number,
    "limit": number,
    "totalPages": number
  }
}
```

#### Get Review by ID
```http
GET /api/v1/reviews/:id
```

**Response:**
```json
{
  "success": true,
  "message": "نظر با موفقیت دریافت شد",
  "data": {
    "review": Review
  }
}
```

#### Get Product Review Statistics
```http
GET /api/v1/reviews/product/:productId/stats
```

**Response:**
```json
{
  "success": true,
  "message": "آمار نظرات محصول با موفقیت دریافت شد",
  "data": {
    "stats": ReviewStatistics
  }
}
```

### Customer Routes (Require Authentication)

#### Create Review
```http
POST /api/v1/reviews
```

**Request Body:**
```json
{
  "productId": "string",
  "rating": 1-5,
  "title": "string (optional)",
  "content": "string",
  "pros": ["string"] (optional),
  "cons": ["string"] (optional),
  "isRecommended": boolean (optional),
  "skinType": "string (optional)",
  "ageRange": "string (optional)",
  "usageDuration": "string (optional)",
  "images": ["string"] (optional)
}
```

**Response:**
```json
{
  "success": true,
  "message": "نظر شما با موفقیت ثبت شد و در انتظار تأیید است",
  "data": {
    "review": Review
  }
}
```

#### Get User's Reviews
```http
GET /api/v1/reviews/my-reviews
```

**Query Parameters:**
- `page` (number, optional): Page number
- `limit` (number, optional): Items per page
- `sortBy` (string, optional): Sort order

**Response:**
```json
{
  "success": true,
  "message": "نظرات شما با موفقیت دریافت شد",
  "data": {
    "reviews": [Review],
    "total": number,
    "page": number,
    "limit": number,
    "totalPages": number
  }
}
```

#### Check Review Permission
```http
GET /api/v1/reviews/product/:productId/can-review
```

**Response:**
```json
{
  "success": true,
  "message": "وضعیت امکان نظردهی بررسی شد",
  "data": {
    "canReview": boolean,
    "hasReviewed": boolean
  }
}
```

#### Update Review
```http
PUT /api/v1/reviews/:id
```

**Request Body:**
```json
{
  "rating": 1-5 (optional),
  "title": "string (optional)",
  "content": "string (optional)",
  "pros": ["string"] (optional),
  "cons": ["string"] (optional),
  "isRecommended": boolean (optional),
  "skinType": "string (optional)",
  "ageRange": "string (optional)",
  "usageDuration": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "نظر با موفقیت به‌روزرسانی شد",
  "data": {
    "review": Review
  }
}
```

#### Delete Review
```http
DELETE /api/v1/reviews/:id
```

**Response:**
```json
{
  "success": true,
  "message": "نظر با موفقیت حذف شد"
}
```

#### Vote on Review
```http
POST /api/v1/reviews/:id/vote
```

**Request Body:**
```json
{
  "isHelpful": boolean
}
```

**Response:**
```json
{
  "success": true,
  "message": "رأی شما با موفقیت ثبت شد"
}
```

### Admin Routes (Require Admin Authentication)

#### Add Review Response
```http
POST /api/v1/reviews/:id/response
```

**Request Body:**
```json
{
  "content": "string",
  "isOfficial": boolean (optional)
}
```

**Response:**
```json
{
  "success": true,
  "message": "پاسخ با موفقیت افزوده شد",
  "data": {
    "response": ReviewResponse
  }
}
```

#### Moderate Review
```http
PATCH /api/v1/reviews/:id/moderate
```

**Request Body:**
```json
{
  "moderationStatus": "PENDING|APPROVED|REJECTED|FLAGGED",
  "moderationNotes": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "نظر با موفقیت تعدیل شد",
  "data": {
    "review": Review
  }
}
```

#### Get Review Analytics
```http
GET /api/v1/reviews/admin/analytics
```

**Response:**
```json
{
  "success": true,
  "message": "آمار نظرات با موفقیت دریافت شد",
  "data": {
    "analytics": {
      "totalReviews": number,
      "pendingReviews": number,
      "approvedReviews": number,
      "rejectedReviews": number,
      "averageRating": number,
      "reviewsThisMonth": number,
      "reviewsLastMonth": number,
      "topRatedProducts": [
        {
          "productId": "string",
          "productName": "string",
          "averageRating": number,
          "reviewCount": number
        }
      ],
      "recentReviews": [Review]
    }
  }
}
```

## Validation Rules

### Create Review Validation
- `productId`: Required, valid product ID
- `rating`: Required, integer between 1-5
- `title`: Optional, 3-100 characters
- `content`: Required, 10-2000 characters
- `pros`: Optional, array of max 5 items, each 2-100 characters
- `cons`: Optional, array of max 5 items, each 2-100 characters
- `isRecommended`: Optional, boolean
- `skinType`: Optional, 2-50 characters
- `ageRange`: Optional, 2-20 characters
- `usageDuration`: Optional, 2-50 characters
- `images`: Optional, array of max 5 valid URLs

### Update Review Validation
- All fields optional but follow same rules as create
- User can only update their own reviews
- Resets moderation status to PENDING

### Vote Review Validation
- `isHelpful`: Required, boolean
- User cannot vote on their own reviews
- One vote per user per review (can be updated)

### Moderate Review Validation
- `moderationStatus`: Required, valid enum value
- `moderationNotes`: Optional, 5-500 characters
- Admin only operation

### Add Response Validation
- `content`: Required, 10-1000 characters
- `isOfficial`: Optional, boolean
- Admin only operation

## Error Codes

### Authentication Errors
- `NO_TOKEN`: No authentication token provided
- `INVALID_TOKEN`: Invalid or expired token
- `USER_NOT_FOUND`: User not found
- `ACCOUNT_INACTIVE`: User account is inactive
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions

### Review Errors
- `REVIEW_NOT_FOUND`: Review not found
- `REVIEW_ALREADY_EXISTS`: User already reviewed this product
- `PRODUCT_NOT_FOUND`: Product not found
- `UNAUTHORIZED_REVIEW_UPDATE`: User cannot update this review
- `UNAUTHORIZED_REVIEW_DELETE`: User cannot delete this review

### Validation Errors
- `VALIDATION_ERROR`: Request validation failed
- `INVALID_RATING`: Rating must be 1-5
- `CONTENT_TOO_SHORT`: Content too short
- `CONTENT_TOO_LONG`: Content too long
- `TOO_MANY_PROS`: Too many pros items
- `TOO_MANY_CONS`: Too many cons items
- `TOO_MANY_IMAGES`: Too many images

### System Errors
- `CREATE_REVIEW_ERROR`: Error creating review
- `UPDATE_REVIEW_ERROR`: Error updating review
- `DELETE_REVIEW_ERROR`: Error deleting review
- `GET_REVIEWS_ERROR`: Error fetching reviews
- `VOTE_REVIEW_ERROR`: Error voting on review
- `ADD_RESPONSE_ERROR`: Error adding response
- `MODERATE_REVIEW_ERROR`: Error moderating review
- `GET_REVIEW_STATS_ERROR`: Error getting statistics
- `GET_ANALYTICS_ERROR`: Error getting analytics

## Features

### Review Management
- ✅ Create, read, update, delete reviews
- ✅ Rich review content with pros/cons
- ✅ Image upload support (up to 5 images)
- ✅ User metadata (skin type, age range, usage duration)
- ✅ Verified purchase detection
- ✅ One review per user per product

### Review Moderation
- ✅ Admin moderation workflow
- ✅ Moderation status tracking
- ✅ Moderation notes
- ✅ Automatic approval/rejection
- ✅ Content filtering capabilities

### Voting System
- ✅ Helpful/unhelpful voting
- ✅ Vote tracking and aggregation
- ✅ One vote per user per review
- ✅ Vote update capability
- ✅ Vote count display

### Analytics & Statistics
- ✅ Product review statistics
- ✅ Rating distribution
- ✅ Verification percentage
- ✅ Recommendation percentage
- ✅ Admin analytics dashboard
- ✅ Top-rated products
- ✅ Recent activity tracking

### Loyalty Integration
- ✅ Points awarded for writing reviews
- ✅ Automatic loyalty account creation
- ✅ Tier-based point multipliers
- ✅ Review-based rewards

### Persian/RTL Support
- ✅ Persian error messages
- ✅ Persian validation messages
- ✅ Persian content support
- ✅ RTL text handling
- ✅ Persian date formatting

### Advanced Features
- ✅ Review responses (admin/brand)
- ✅ Official response marking
- ✅ Review filtering and sorting
- ✅ Search functionality
- ✅ Pagination support
- ✅ Real-time statistics

## Usage Examples

### Create a Review
```bash
curl -X POST http://localhost:3001/api/v1/reviews \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "productId": "product_id_here",
    "rating": 5,
    "title": "محصول عالی",
    "content": "این محصول واقعاً فوق‌العاده است. پوستم را نرم و صاف کرده.",
    "pros": ["کیفیت عالی", "قیمت مناسب", "تأثیر سریع"],
    "cons": ["بوی قوی"],
    "isRecommended": true,
    "skinType": "خشک",
    "ageRange": "25-35",
    "usageDuration": "یک ماه"
  }'
```

### Get Product Reviews
```bash
curl -X GET "http://localhost:3001/api/v1/reviews?productId=PRODUCT_ID&sortBy=newest&limit=5"
```

### Vote on Review
```bash
curl -X POST http://localhost:3001/api/v1/reviews/REVIEW_ID/vote \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"isHelpful": true}'
```

### Moderate Review (Admin)
```bash
curl -X PATCH http://localhost:3001/api/v1/reviews/REVIEW_ID/moderate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "moderationStatus": "APPROVED",
    "moderationNotes": "محتوای مناسب و مفید"
  }'
```

## Database Schema

### Reviews Table
```sql
CREATE TABLE reviews (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title TEXT,
  content TEXT NOT NULL,
  pros TEXT[],
  cons TEXT[],
  is_verified BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT FALSE,
  is_recommended BOOLEAN DEFAULT TRUE,
  helpful_count INTEGER DEFAULT 0,
  unhelpful_count INTEGER DEFAULT 0,
  skin_type TEXT,
  age_range TEXT,
  usage_duration TEXT,
  moderation_status TEXT DEFAULT 'PENDING',
  moderation_notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(product_id, user_id)
);
```

### Review Images Table
```sql
CREATE TABLE review_images (
  id TEXT PRIMARY KEY,
  review_id TEXT NOT NULL,
  url TEXT NOT NULL,
  alt TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Review Votes Table
```sql
CREATE TABLE review_votes (
  id TEXT PRIMARY KEY,
  review_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  is_helpful BOOLEAN NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(review_id, user_id)
);
```

### Review Responses Table
```sql
CREATE TABLE review_responses (
  id TEXT PRIMARY KEY,
  review_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  content TEXT NOT NULL,
  is_official BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Performance Considerations

### Database Optimization
- Indexed foreign keys for fast lookups
- Composite unique constraints
- Efficient pagination queries
- Aggregation queries for statistics

### Caching Strategy
- Product review statistics caching
- Popular reviews caching
- Analytics data caching
- User permission caching

### Rate Limiting
- Review creation: 5 per hour per user
- Vote submission: 100 per hour per user
- Review updates: 10 per hour per user

## Security Features

### Input Validation
- Comprehensive request validation
- SQL injection prevention
- XSS protection
- File upload validation

### Authorization
- Role-based access control
- Resource ownership validation
- Admin-only operations
- User permission checks

### Data Protection
- Sensitive data filtering
- User privacy protection
- Audit trail logging
- Secure file handling
