import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  Mail,
  Phone,
  Send,
  ArrowLeft,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  Filter,
  Search,
  Plus,
  FileText,
  Headphones
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { AdminCustomer, CommunicationRecord, SupportTicket } from '../../../types/adminCustomer';
import { formatCustomerName, formatPersianDate, formatPersianDateTime } from '../../../utils/customerUtils';
import toast from 'react-hot-toast';

const CustomerCommunicationPage: React.FC = () => {
  const { id: customerId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getCustomerById, sendMessage, createSupportTicket } = useAdminCustomers();
  
  const [customer, setCustomer] = useState<AdminCustomer | null>(null);
  const [activeTab, setActiveTab] = useState<'messages' | 'tickets'>('messages');
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  const [messageForm, setMessageForm] = useState({
    subject: '',
    content: '',
    type: 'email' as 'email' | 'sms' | 'push',
    priority: 'normal' as 'low' | 'normal' | 'high'
  });

  const [ticketForm, setTicketForm] = useState({
    subject: '',
    description: '',
    category: 'general' as SupportTicket['category'],
    priority: 'normal' as SupportTicket['priority']
  });

  useEffect(() => {
    if (customerId) {
      const customerData = getCustomerById(customerId);
      if (customerData) {
        setCustomer(customerData);
      } else {
        toast.error('مشتری یافت نشد');
        navigate('/admin/customers');
      }
    }
  }, [customerId, getCustomerById, navigate]);

  const handleSendMessage = async () => {
    if (!customer || !messageForm.subject || !messageForm.content) {
      toast.error('لطفاً تمام فیلدها را پر کنید');
      return;
    }

    try {
      await sendMessage(customer.id, {
        subject: messageForm.subject,
        content: messageForm.content,
        type: messageForm.type,
        priority: messageForm.priority
      });
      
      toast.success('پیام با موفقیت ارسال شد');
      setShowMessageModal(false);
      setMessageForm({
        subject: '',
        content: '',
        type: 'email',
        priority: 'normal'
      });
    } catch (error) {
      toast.error('خطا در ارسال پیام');
    }
  };

  const handleCreateTicket = async () => {
    if (!customer || !ticketForm.subject || !ticketForm.description) {
      toast.error('لطفاً تمام فیلدها را پر کنید');
      return;
    }

    try {
      await createSupportTicket(customer.id, {
        subject: ticketForm.subject,
        description: ticketForm.description,
        category: ticketForm.category,
        priority: ticketForm.priority
      });
      
      toast.success('تیکت پشتیبانی با موفقیت ایجاد شد');
      setShowTicketModal(false);
      setTicketForm({
        subject: '',
        description: '',
        category: 'general',
        priority: 'normal'
      });
    } catch (error) {
      toast.error('خطا در ایجاد تیکت');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'read':
        return <CheckCircle className="w-4 h-4 text-purple-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'sms':
        return <MessageSquare className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'normal':
        return 'text-blue-600 bg-blue-100';
      case 'low':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (!customer) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  const filteredCommunications = customer.communicationHistory.filter(comm => {
    const matchesSearch = comm.subject.toLowerCase().includes(searchValue.toLowerCase()) ||
                         comm.content.toLowerCase().includes(searchValue.toLowerCase());
    const matchesType = filterType === 'all' || comm.type === filterType;
    return matchesSearch && matchesType;
  });

  const filteredTickets = customer.supportTickets.filter(ticket => {
    const matchesSearch = ticket.subject.toLowerCase().includes(searchValue.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchValue.toLowerCase());
    const matchesType = filterType === 'all' || ticket.status === filterType;
    return matchesSearch && matchesType;
  });

  return (
    <AdminLayout
      title={`ارتباطات ${formatCustomerName(customer)}`}
      subtitle="تاریخچه پیام‌ها و تیکت‌های پشتیبانی"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <AdminButton
            variant="outline"
            onClick={() => navigate(`/admin/customers/${customer.id}`)}
            icon={ArrowLeft}
          >
            بازگشت به جزئیات مشتری
          </AdminButton>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              onClick={() => setShowTicketModal(true)}
              icon={Headphones}
            >
              ایجاد تیکت پشتیبانی
            </AdminButton>
            <AdminButton
              onClick={() => setShowMessageModal(true)}
              icon={Send}
            >
              ارسال پیام
            </AdminButton>
          </div>
        </div>

        {/* Customer Info Summary */}
        <AdminCard>
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-lg font-bold">
              {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {formatCustomerName(customer)}
              </h3>
              <p className="text-sm text-gray-600">{customer.email}</p>
            </div>
            <div className="mr-auto text-left">
              <div className="text-sm text-gray-600">
                کل پیام‌ها: {customer.communicationHistory.length}
              </div>
              <div className="text-sm text-gray-600">
                تیکت‌های فعال: {customer.supportTickets.filter(t => ['open', 'in_progress'].includes(t.status)).length}
              </div>
            </div>
          </div>
        </AdminCard>

        {/* Tabs and Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('messages')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'messages'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                پیام‌ها ({customer.communicationHistory.length})
              </button>
              <button
                onClick={() => setActiveTab('tickets')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'tickets'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                تیکت‌ها ({customer.supportTickets.length})
              </button>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="جستجو..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">همه</option>
              {activeTab === 'messages' ? (
                <>
                  <option value="email">ایمیل</option>
                  <option value="sms">پیامک</option>
                  <option value="push">اعلان</option>
                </>
              ) : (
                <>
                  <option value="open">باز</option>
                  <option value="in_progress">در حال بررسی</option>
                  <option value="resolved">حل شده</option>
                  <option value="closed">بسته</option>
                </>
              )}
            </select>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'messages' ? (
          <AdminCard title="تاریخچه پیام‌ها">
            <div className="space-y-4">
              {filteredCommunications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  هیچ پیامی یافت نشد
                </div>
              ) : (
                filteredCommunications.map((comm) => (
                  <motion.div
                    key={comm.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(comm.type)}
                          <span className="font-medium text-gray-900">{comm.subject}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(comm.priority)}`}>
                          {comm.priority === 'high' ? 'بالا' : comm.priority === 'normal' ? 'عادی' : 'پایین'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        {getStatusIcon(comm.status)}
                        <span>{formatPersianDateTime(comm.sentAt)}</span>
                      </div>
                    </div>
                    <p className="text-gray-700 text-sm">{comm.content}</p>
                    {comm.response && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <div className="text-sm font-medium text-blue-900 mb-1">پاسخ مشتری:</div>
                        <p className="text-sm text-blue-800">{comm.response.content}</p>
                        <div className="text-xs text-blue-600 mt-1">
                          {formatPersianDateTime(comm.response.receivedAt)}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))
              )}
            </div>
          </AdminCard>
        ) : (
          <AdminCard title="تیکت‌های پشتیبانی">
            <div className="space-y-4">
              {filteredTickets.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  هیچ تیکتی یافت نشد
                </div>
              ) : (
                filteredTickets.map((ticket) => (
                  <motion.div
                    key={ticket.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-900">#{ticket.ticketNumber}</span>
                        <span className="text-gray-700">{ticket.subject}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority === 'high' ? 'بالا' : ticket.priority === 'normal' ? 'عادی' : 'پایین'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatPersianDateTime(ticket.createdAt)}
                      </div>
                    </div>
                    <p className="text-gray-700 text-sm mb-3">{ticket.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>دسته: {ticket.category}</span>
                        <span>وضعیت: {ticket.status}</span>
                      </div>
                      <AdminButton size="sm" variant="outline">
                        مشاهده جزئیات
                      </AdminButton>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </AdminCard>
        )}
      </div>

      {/* Send Message Modal */}
      <AdminFormModal
        isOpen={showMessageModal}
        onClose={() => setShowMessageModal(false)}
        onSubmit={handleSendMessage}
        title="ارسال پیام جدید"
        submitText="ارسال پیام"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع پیام
              </label>
              <select
                value={messageForm.type}
                onChange={(e) => setMessageForm(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="email">ایمیل</option>
                <option value="sms">پیامک</option>
                <option value="push">اعلان اپلیکیشن</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اولویت
              </label>
              <select
                value={messageForm.priority}
                onChange={(e) => setMessageForm(prev => ({ ...prev, priority: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="low">پایین</option>
                <option value="normal">عادی</option>
                <option value="high">بالا</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              موضوع
            </label>
            <input
              type="text"
              value={messageForm.subject}
              onChange={(e) => setMessageForm(prev => ({ ...prev, subject: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="موضوع پیام"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              متن پیام
            </label>
            <textarea
              rows={4}
              value={messageForm.content}
              onChange={(e) => setMessageForm(prev => ({ ...prev, content: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="متن پیام خود را وارد کنید"
            />
          </div>
        </div>
      </AdminFormModal>

      {/* Create Ticket Modal */}
      <AdminFormModal
        isOpen={showTicketModal}
        onClose={() => setShowTicketModal(false)}
        onSubmit={handleCreateTicket}
        title="ایجاد تیکت پشتیبانی"
        submitText="ایجاد تیکت"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                دسته‌بندی
              </label>
              <select
                value={ticketForm.category}
                onChange={(e) => setTicketForm(prev => ({ ...prev, category: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="general">عمومی</option>
                <option value="order">سفارش</option>
                <option value="payment">پرداخت</option>
                <option value="shipping">ارسال</option>
                <option value="product">محصول</option>
                <option value="account">حساب کاربری</option>
                <option value="technical">فنی</option>
                <option value="complaint">شکایت</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اولویت
              </label>
              <select
                value={ticketForm.priority}
                onChange={(e) => setTicketForm(prev => ({ ...prev, priority: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="low">پایین</option>
                <option value="normal">عادی</option>
                <option value="high">بالا</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              موضوع
            </label>
            <input
              type="text"
              value={ticketForm.subject}
              onChange={(e) => setTicketForm(prev => ({ ...prev, subject: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="موضوع تیکت"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              شرح مسئله
            </label>
            <textarea
              rows={4}
              value={ticketForm.description}
              onChange={(e) => setTicketForm(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="شرح کاملی از مسئله ارائه دهید"
            />
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default CustomerCommunicationPage;
