import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Filter, 
  SortAsc, 
  Star, 
  Shield, 
  ThumbsUp,
  Search,
  X,
  ChevronDown
} from 'lucide-react';
import { Review, ReviewFilters, PERSIAN_REVIEW_TEMPLATES } from '../../types/review';
import ReviewCard from './ReviewCard';
import { RatingDistribution } from './RatingStars';

interface ReviewListProps {
  reviews: Review[];
  totalReviews: number;
  averageRating: number;
  ratingDistribution: { [key: number]: number };
  filters: ReviewFilters;
  onFiltersChange: (filters: Partial<ReviewFilters>) => void;
  onVote?: (reviewId: string, isHelpful: boolean) => void;
  onReport?: (reviewId: string) => void;
  onShare?: (reviewId: string) => void;
  isLoading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
}

const ReviewList: React.FC<ReviewListProps> = ({
  reviews,
  totalReviews,
  averageRating,
  ratingDistribution,
  filters,
  onFiltersChange,
  onVote,
  onReport,
  onShare,
  isLoading = false,
  hasMore = false,
  onLoadMore
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleRatingFilter = (rating: number) => {
    onFiltersChange({
      rating: filters.rating === rating ? undefined : rating
    });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      rating: undefined,
      verified: undefined,
      recommended: undefined,
      skinType: undefined
    });
    setSearchQuery('');
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.rating) count++;
    if (filters.verified !== undefined) count++;
    if (filters.recommended !== undefined) count++;
    if (filters.skinType) count++;
    if (searchQuery) count++;
    return count;
  };

  const filteredReviews = reviews.filter(review => {
    if (searchQuery && !review.comment.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !review.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Reviews Summary */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Overall Rating */}
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">
              {averageRating.toLocaleString('fa-IR', { 
                minimumFractionDigits: 1, 
                maximumFractionDigits: 1 
              })}
            </div>
            <div className="flex justify-center mb-2">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-5 h-5 ${
                      star <= averageRating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
            <p className="text-gray-600 text-sm">
              بر اساس {totalReviews.toLocaleString('fa-IR')} نظر
            </p>
          </div>

          {/* Rating Distribution */}
          <div className="lg:col-span-2">
            <h4 className="font-medium text-gray-900 mb-4">توزیع امتیازات</h4>
            <RatingDistribution
              distribution={ratingDistribution}
              totalReviews={totalReviews}
              onRatingFilter={handleRatingFilter}
            />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="جستجو در نظرات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Filter Controls */}
          <div className="flex items-center gap-3">
            {/* Sort */}
            <select
              value={filters.sortBy}
              onChange={(e) => onFiltersChange({ sortBy: e.target.value as any })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {PERSIAN_REVIEW_TEMPLATES.sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`
                flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors
                ${showFilters ? 'bg-primary-50 border-primary-300 text-primary-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'}
              `}
            >
              <Filter className="w-4 h-4" />
              <span>فیلتر</span>
              {getActiveFiltersCount() > 0 && (
                <span className="bg-primary-500 text-white text-xs px-2 py-0.5 rounded-full">
                  {getActiveFiltersCount()}
                </span>
              )}
              <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="mt-4 pt-4 border-t border-gray-200 overflow-hidden"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Rating Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    امتیاز
                  </label>
                  <select
                    value={filters.rating || ''}
                    onChange={(e) => onFiltersChange({ 
                      rating: e.target.value ? Number(e.target.value) : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">همه امتیازات</option>
                    <option value="5">۵ ستاره</option>
                    <option value="4">۴ ستاره</option>
                    <option value="3">۳ ستاره</option>
                    <option value="2">۲ ستاره</option>
                    <option value="1">۱ ستاره</option>
                  </select>
                </div>

                {/* Verified Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع خرید
                  </label>
                  <select
                    value={filters.verified === undefined ? '' : filters.verified.toString()}
                    onChange={(e) => onFiltersChange({ 
                      verified: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">همه نظرات</option>
                    <option value="true">خرید تأیید شده</option>
                    <option value="false">سایر نظرات</option>
                  </select>
                </div>

                {/* Recommendation Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    پیشنهاد
                  </label>
                  <select
                    value={filters.recommended === undefined ? '' : filters.recommended.toString()}
                    onChange={(e) => onFiltersChange({ 
                      recommended: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">همه نظرات</option>
                    <option value="true">پیشنهاد می‌کنند</option>
                    <option value="false">پیشنهاد نمی‌کنند</option>
                  </select>
                </div>

                {/* Skin Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع پوست
                  </label>
                  <select
                    value={filters.skinType || ''}
                    onChange={(e) => onFiltersChange({ 
                      skinType: e.target.value || undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">همه انواع پوست</option>
                    {PERSIAN_REVIEW_TEMPLATES.skinTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Clear Filters */}
              {getActiveFiltersCount() > 0 && (
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={handleClearFilters}
                    className="text-sm text-red-600 hover:text-red-700 transition-colors"
                  >
                    پاک کردن همه فیلترها
                  </button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Reviews List */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            نظرات کاربران ({filteredReviews.length.toLocaleString('fa-IR')})
          </h3>
        </div>

        {isLoading && filteredReviews.length === 0 ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse">
                <div className="flex items-start gap-3 mb-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredReviews.length > 0 ? (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-4"
          >
            {filteredReviews.map((review) => (
              <motion.div key={review.id} variants={itemVariants}>
                <ReviewCard
                  review={review}
                  onVote={onVote}
                  onReport={onReport}
                  onShare={onShare}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
            <div className="text-gray-400 mb-4">
              <Star className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              نظری یافت نشد
            </h3>
            <p className="text-gray-600">
              {getActiveFiltersCount() > 0 
                ? 'با فیلترهای انتخاب شده نظری یافت نشد. فیلترها را تغییر دهید.'
                : 'هنوز نظری برای این محصول ثبت نشده است.'
              }
            </p>
          </div>
        )}

        {/* Load More */}
        {hasMore && !isLoading && (
          <div className="mt-6 text-center">
            <button
              onClick={onLoadMore}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              نمایش نظرات بیشتر
            </button>
          </div>
        )}

        {/* Loading More */}
        {isLoading && filteredReviews.length > 0 && (
          <div className="mt-6 text-center">
            <div className="inline-flex items-center gap-2 text-gray-600">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin"></div>
              در حال بارگذاری...
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewList;
