import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import toast from 'react-hot-toast';
import {
  AdminUser,
  AdminAuthState,
  AdminAuthContextType,
  AdminLoginCredentials,
  AdminResource,
  AdminAction,
  AdminRole,
  PERSIAN_ADMIN_MESSAGES,
  ADMIN_ROLE_PERMISSIONS
} from '../types/admin';
import {
  AdminStorage,
  AdminErrorHandler,
  AdminSessionManager
} from '../utils/adminAuthUtils';
import AdminAuthService from '../services/adminAuthService';
import { ApiService } from '../services/apiService';
import { AdminPermissionChecker } from '../utils/adminAuthUtils';
import { TokenUtils } from '../utils/authUtils';

// Admin auth actions
type AdminAuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: AdminUser; token: string; sessionExpiry: number } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: AdminUser }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SESSION_WARNING' }
  | { type: 'EXTEND_SESSION'; payload: number };

// Initial state
const initialState: AdminAuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false, // Start with false to avoid context issues
  error: null,
  token: null,
  sessionExpiry: null
};

// Admin auth reducer
const adminAuthReducer = (state: AdminAuthState, action: AdminAuthAction): AdminAuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        sessionExpiry: action.payload.sessionExpiry,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        token: null,
        sessionExpiry: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload
      };

    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        sessionExpiry: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    case 'SESSION_WARNING':
      return {
        ...state,
        error: 'جلسه کاری شما به زودی منقضی می‌شود'
      };

    case 'EXTEND_SESSION':
      return {
        ...state,
        sessionExpiry: action.payload,
        error: null
      };

    default:
      return state;
  }
};

// Create context with default value
const defaultContextValue: AdminAuthContextType = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  sessionExpiry: null,
  login: async () => { throw new Error('AdminAuthProvider not found'); },
  logout: async () => { throw new Error('AdminAuthProvider not found'); },
  refreshToken: async () => { throw new Error('AdminAuthProvider not found'); },
  checkPermission: () => false,
  hasRole: () => false,
  hasAnyRole: () => false,
  clearError: () => { throw new Error('AdminAuthProvider not found'); },
  extendSession: async () => { throw new Error('AdminAuthProvider not found'); },
  getSessionTimeRemaining: () => 0
};

const AdminAuthContext = createContext<AdminAuthContextType>(defaultContextValue);

// Admin auth provider component
export const AdminAuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(adminAuthReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    const initializeAdminAuth = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        const token = AdminStorage.getToken();
        const user = AdminStorage.getUser();
        const sessionExpiry = AdminStorage.getSessionExpiry();

        // console.log('Admin auth initialization:', {
        //   hasToken: !!token,
        //   hasUser: !!user,
        //   sessionExpiry,
        //   isExpired: AdminStorage.isSessionExpired(),
        //   currentTime: Date.now()
        // });

        if (token && user && sessionExpiry && !AdminStorage.isSessionExpired()) {
          // Validate token
          if (TokenUtils.isTokenValid(token)) {
            // console.log('Valid session found, restoring authentication');
            dispatch({
              type: 'AUTH_SUCCESS',
              payload: { user, token, sessionExpiry }
            });

            // Start session monitoring
            AdminSessionManager.startSessionMonitoring(
              () => dispatch({ type: 'SESSION_WARNING' }),
              () => handleSessionExpiry()
            );
          } else {
            // Token is invalid, clear session
            // console.log('Invalid token found, clearing session');
            AdminStorage.clearAll();
            dispatch({ type: 'SET_LOADING', payload: false });
          }
        } else {
          // No valid session found
          // console.log('No valid session found, clearing storage');
          AdminStorage.clearAll();
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        AdminStorage.clearAll();
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAdminAuth();

    // Cleanup on unmount
    return () => {
      AdminSessionManager.clearSessionMonitoring();
    };
  }, []);

  const handleSessionExpiry = async () => {
    AdminSessionManager.clearSessionMonitoring();
    AdminStorage.clearAll();
    dispatch({ type: 'LOGOUT' });
    toast.error(PERSIAN_ADMIN_MESSAGES.auth.sessionExpired);
  };

  const login = async (credentials: AdminLoginCredentials): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      // Use real API service for login
      const response = await AdminAuthService.login({
        email: credentials.email,
        password: credentials.password,
        twoFactorCode: credentials.twoFactorCode,
        rememberMe: credentials.rememberMe || false
      });

      // Store auth data - setToken will handle session expiry storage
      AdminStorage.setToken(response.token, credentials.rememberMe || false);
      AdminStorage.setRefreshToken(response.refreshToken);
      AdminStorage.setUser(response.user);
      AdminStorage.setRememberMe(credentials.rememberMe || false);

      // Get the actual session expiry from storage (which was set by setToken)
      const sessionExpiry = AdminStorage.getSessionExpiry();

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          sessionExpiry: sessionExpiry!
        }
      });

      // Start session monitoring
      AdminSessionManager.startSessionMonitoring(
        () => dispatch({ type: 'SESSION_WARNING' }),
        () => handleSessionExpiry()
      );

      toast.success(PERSIAN_ADMIN_MESSAGES.auth.loginSuccess);
    } catch (error) {
      const errorMessage = ApiService.ErrorHandler.handleError(error as Error);
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await AdminAuthService.logout();
    } catch (error) {
      // Silently handle logout errors
    } finally {
      // Always clear local state and storage
      AdminSessionManager.clearSessionMonitoring();
      AdminStorage.clearAll();
      dispatch({ type: 'LOGOUT' });
      toast.success(PERSIAN_ADMIN_MESSAGES.auth.logoutSuccess);
    }
  };

  const refreshToken = async (): Promise<void> => {
    try {
      const response = await AdminAuthService.refreshToken();

      // Update stored data - setToken will handle session expiry storage
      AdminStorage.setToken(response.token, AdminStorage.getRememberMe());

      // Get the actual session expiry from storage
      const sessionExpiry = AdminStorage.getSessionExpiry();

      // Get updated user profile and transform to AdminUser
      const userProfile = await AdminAuthService.getProfile();

      // Transform AdminProfile to AdminUser
      const adminUser: AdminUser = {
        ...userProfile,
        permissions: ADMIN_ROLE_PERMISSIONS[userProfile.role] || [],
        lastLogin: userProfile.lastLoginAt,
        twoFactorEnabled: false // Default value, can be enhanced later
      };

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: adminUser,
          token: response.token,
          sessionExpiry: sessionExpiry!
        }
      });

      // Restart session monitoring
      AdminSessionManager.startSessionMonitoring(
        () => dispatch({ type: 'SESSION_WARNING' }),
        () => handleSessionExpiry()
      );
    } catch (error) {
      // console.error('Token refresh failed:', error);
      await handleSessionExpiry();
    }
  };

  const extendSession = async (): Promise<void> => {
    try {
      AdminSessionManager.extendSession();
      const newExpiry = AdminStorage.getSessionExpiry();
      
      if (newExpiry) {
        dispatch({ type: 'EXTEND_SESSION', payload: newExpiry });
        toast.success(PERSIAN_ADMIN_MESSAGES.auth.sessionExtended);
      }
    } catch (error) {
      // console.error('Session extension failed:', error);
      toast.error('خطا در تمدید جلسه');
    }
  };

  const checkPermission = (resource: AdminResource, action: AdminAction): boolean => {
    return AdminPermissionChecker.hasPermission(state.user, resource, action);
  };

  const hasRole = (role: AdminRole): boolean => {
    return AdminPermissionChecker.hasRole(state.user, role);
  };

  const hasAnyRole = (roles: AdminRole[]): boolean => {
    return AdminPermissionChecker.hasAnyRole(state.user, roles);
  };

  const getSessionTimeRemaining = (): number => {
    return AdminStorage.getTimeUntilExpiry();
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AdminAuthContextType = {
    // State
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    sessionExpiry: state.sessionExpiry,
    
    // Actions
    login,
    logout,
    refreshToken,
    checkPermission,
    hasRole,
    hasAnyRole,
    clearError,
    
    // Session management
    extendSession,
    getSessionTimeRemaining
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Custom hook to use admin auth context
export const useAdminAuth = (): AdminAuthContextType => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

// Export the context for advanced usage
export { AdminAuthContext };
