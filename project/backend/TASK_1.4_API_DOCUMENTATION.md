# Task 1.4: Order Management API Documentation

## Overview

This document provides comprehensive documentation for the Order Management API implemented in Task 1.4. The API includes full CRUD operations for orders, payments, order status management, and customer order tracking with admin authentication and Persian language support.

## Base URL
```
http://localhost:3001/api/v1/orders
```

## Authentication

- **Public routes**: Order creation (guest and authenticated users)
- **Customer routes**: Require user authentication
- **Admin routes**: Require admin authentication with JWT token

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "message": "عملیات با موفقیت انجام شد",
  "data": { ... },
  "pagination": { ... } // Only for paginated endpoints
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "پیام خطا",
    "code": "ERROR_CODE"
  },
  "timestamp": "2025-06-06T02:21:09.519Z",
  "path": "/api/v1/orders/endpoint",
  "method": "POST"
}
```

---

## Order Management API

### 1. Create Order (Public/Authenticated)
**POST** `/orders`

Create a new order for authenticated users or guests.

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <jwt_token> // Optional for authenticated users
```

**Request Body:**
```json
{
  "guestEmail": "<EMAIL>", // Required for guest orders
  "items": [
    {
      "productId": "product-id",
      "variantId": "variant-id", // Optional
      "quantity": 2
    }
  ],
  "shippingAddressId": "address-id", // For authenticated users
  "billingAddressId": "address-id", // Optional
  "shippingAddress": { // For guest orders or new address
    "firstName": "علی",
    "lastName": "احمدی",
    "phone": "09123456789",
    "province": "تهران",
    "city": "تهران",
    "district": "منطقه ۱", // Optional
    "street": "خیابان ولیعصر، پلاک ۱۲۳",
    "postalCode": "1234567890"
  },
  "shippingMethod": "standard", // Optional
  "notes": "یادداشت سفارش" // Optional
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "سفارش با موفقیت ایجاد شد",
  "data": {
    "id": "order-id",
    "orderNumber": "GR76463874095",
    "userId": null,
    "guestEmail": "<EMAIL>",
    "status": "PENDING",
    "paymentStatus": "PENDING",
    "fulfillmentStatus": "UNFULFILLED",
    "subtotal": "900000",
    "taxAmount": "0",
    "shippingAmount": "0",
    "discountAmount": "0",
    "totalAmount": "900000",
    "notes": "یادداشت سفارش\n\nآدرس ارسال: علی احمدی, خیابان ولیعصر، پلاک ۱۲۳, تهران, تهران, کد پستی: 1234567890, تلفن: 09123456789",
    "placedAt": "2025-06-06T02:21:04.505Z",
    "createdAt": "2025-06-06T02:21:04.508Z",
    "items": [
      {
        "id": "item-id",
        "productId": "product-id",
        "quantity": 2,
        "unitPrice": "450000",
        "totalPrice": "900000",
        "product": {
          "id": "product-id",
          "name": "پاک‌کننده فوم‌ساز سراوی",
          "slug": "cerave-foaming-cleanser",
          "sku": "CRV-FC-355",
          "price": "450000",
          "isActive": true
        }
      }
    ]
  }
}
```

### 2. Get Customer Orders (Authenticated Users)
**GET** `/orders/my-orders`

Get orders for the authenticated user.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 50) - Items per page
- `include` (string, default: "items,shippingAddress") - Relations to include

**Example Request:**
```bash
curl -H "Authorization: Bearer <jwt_token>" \
  "http://localhost:3001/api/v1/orders/my-orders?page=1&limit=10"
```

### 3. Get Order by ID (Customer/Admin)
**GET** `/orders/{id}`

**Parameters:**
- `id` (string, required) - Order ID
- `include` (query string) - Relations to include

**Authentication:** User must own the order or be an admin.

### 4. Get Order by Order Number (Customer/Admin)
**GET** `/orders/number/{orderNumber}`

**Parameters:**
- `orderNumber` (string, required) - Order number (e.g., GR76463874095)
- `include` (query string) - Relations to include

**Authentication:** User must own the order or be an admin.

### 5. Cancel Order (Customer/Admin)
**PATCH** `/orders/{id}/cancel`

**Request Body:**
```json
{
  "reason": "دلیل لغو سفارش" // Optional
}
```

**Authentication:** User must own the order or be an admin.

---

## Admin Order Management API

### 1. Get All Orders (Admin Only)
**GET** `/orders/admin/all`

**Query Parameters:**
- `page`, `limit`, `sortBy`, `sortOrder` - Pagination and sorting
- `search` (string) - Search in order number, customer info, notes
- `userId` (string) - Filter by user ID
- `status` (string) - Filter by order status
- `paymentStatus` (string) - Filter by payment status
- `fulfillmentStatus` (string) - Filter by fulfillment status
- `dateFrom`, `dateTo` (ISO date) - Date range filter
- `minAmount`, `maxAmount` (number) - Amount range filter
- `tags` (string) - Comma-separated tags
- `hasTrackingNumber` (boolean) - Filter orders with/without tracking
- `include` (string) - Relations to include

**Order Statuses:**
- `PENDING` - سفارش در انتظار تایید
- `CONFIRMED` - سفارش تایید شده
- `PROCESSING` - در حال پردازش
- `SHIPPED` - ارسال شده
- `DELIVERED` - تحویل داده شده
- `CANCELLED` - لغو شده
- `REFUNDED` - بازگشت داده شده

**Payment Statuses:**
- `PENDING` - در انتظار پرداخت
- `PROCESSING` - در حال پردازش
- `COMPLETED` - پرداخت شده
- `FAILED` - پرداخت ناموفق
- `CANCELLED` - لغو شده
- `REFUNDED` - بازگشت داده شده

**Fulfillment Statuses:**
- `UNFULFILLED` - تحویل نشده
- `PARTIAL` - تحویل جزئی
- `FULFILLED` - تحویل شده
- `SHIPPED` - ارسال شده
- `DELIVERED` - تحویل داده شده
- `RETURNED` - برگشت خورده

### 2. Update Order (Admin Only)
**PUT** `/orders/admin/{id}`

**Request Body:**
```json
{
  "status": "CONFIRMED",
  "paymentStatus": "COMPLETED",
  "fulfillmentStatus": "FULFILLED",
  "shippingMethod": "express",
  "trackingNumber": "TRK123456789",
  "notes": "یادداشت عمومی",
  "internalNotes": "یادداشت داخلی",
  "tags": ["urgent", "vip"]
}
```

### 3. Update Order Status (Admin Only)
**PATCH** `/orders/admin/{id}/status`

**Request Body:**
```json
{
  "status": "SHIPPED",
  "reason": "دلیل تغییر وضعیت", // Optional
  "notifyCustomer": true // Optional
}
```

### 4. Get Order Statistics (Admin Only)
**GET** `/orders/admin/statistics`

**Query Parameters:**
- `dateFrom`, `dateTo` (ISO date) - Date range filter

**Example Response:**
```json
{
  "success": true,
  "message": "آمار سفارشات با موفقیت دریافت شد",
  "data": {
    "overview": {
      "totalOrders": 150,
      "totalRevenue": 45000000,
      "averageOrderValue": 300000
    },
    "statusDistribution": {
      "PENDING": 25,
      "CONFIRMED": 40,
      "PROCESSING": 30,
      "SHIPPED": 35,
      "DELIVERED": 15,
      "CANCELLED": 5
    },
    "paymentStatusDistribution": {
      "PENDING": 30,
      "COMPLETED": 100,
      "FAILED": 15,
      "REFUNDED": 5
    },
    "recentOrders": [...]
  }
}
```

---

## Payment Management API

### 1. Get All Payments (Admin Only)
**GET** `/orders/admin/payments`

**Query Parameters:**
- `page`, `limit`, `sortBy`, `sortOrder` - Pagination and sorting
- `orderId` (string) - Filter by order ID
- `status` (string) - Filter by payment status
- `method` (string) - Filter by payment method
- `gateway` (string) - Filter by payment gateway
- `dateFrom`, `dateTo` (ISO date) - Date range filter
- `minAmount`, `maxAmount` (number) - Amount range filter

**Payment Methods:**
- `CARD` - کارت بانکی
- `BANK_TRANSFER` - انتقال بانکی
- `WALLET` - کیف پول
- `CASH_ON_DELIVERY` - پرداخت در محل

### 2. Create Payment (Admin Only)
**POST** `/orders/admin/payments`

**Request Body:**
```json
{
  "orderId": "order-id",
  "amount": 450000,
  "method": "CARD",
  "gateway": "mellat", // Optional
  "gatewayTransactionId": "TXN123456", // Optional
  "gatewayResponse": {} // Optional
}
```

### 3. Process Payment (Admin Only)
**POST** `/orders/admin/payments/{id}/process`

**Request Body:**
```json
{
  "gatewayResponse": {
    "success": true,
    "transactionId": "TXN123456789",
    "timestamp": "2025-06-06T02:21:09.519Z"
  }
}
```

### 4. Refund Payment (Admin Only)
**POST** `/orders/admin/payments/{id}/refund`

**Request Body:**
```json
{
  "refundAmount": 450000, // Optional, defaults to full amount
  "reason": "دلیل بازگشت وجه"
}
```

### 5. Simulate Payment Gateway (Admin Only - Testing)
**POST** `/orders/admin/payments/simulate`

**Request Body:**
```json
{
  "paymentId": "payment-id",
  "success": true, // true for success, false for failure
  "transactionId": "TXN123456789" // Optional
}
```

### 6. Get Payment Statistics (Admin Only)
**GET** `/orders/admin/payments/statistics`

**Query Parameters:**
- `dateFrom`, `dateTo` (ISO date) - Date range filter

---

## Order Workflow

### Status Transitions

**Valid Order Status Transitions:**
- `PENDING` → `CONFIRMED`, `CANCELLED`
- `CONFIRMED` → `PROCESSING`, `CANCELLED`
- `PROCESSING` → `SHIPPED`, `CANCELLED`
- `SHIPPED` → `DELIVERED`, `CANCELLED`
- `DELIVERED` → `REFUNDED`
- `CANCELLED` → (No transitions)
- `REFUNDED` → (No transitions)

### Inventory Management

**Order Creation:**
- Validates product availability
- Reserves inventory (increases `reservedQuantity`)

**Order Confirmation:**
- Reduces actual inventory (`quantity`)
- Reduces reserved inventory (`reservedQuantity`)

**Order Cancellation:**
- Releases reserved inventory
- Restores actual inventory if order was confirmed

---

## Error Codes

### Order Errors
- `ORDER_NOT_FOUND` - سفارش یافت نشد
- `ORDER_ACCESS_DENIED` - دسترسی به سفارش مجاز نیست
- `ORDER_NOT_CANCELLABLE` - سفارش قابل لغو نیست
- `ORDER_ALREADY_PAID` - سفارش قبلاً پرداخت شده
- `INVALID_STATUS_TRANSITION` - تغییر وضعیت مجاز نیست

### Product/Inventory Errors
- `PRODUCT_NOT_FOUND` - محصول یافت نشد
- `PRODUCT_INACTIVE` - محصول غیرفعال است
- `VARIANT_NOT_FOUND` - نوع محصول یافت نشد
- `VARIANT_INACTIVE` - نوع محصول غیرفعال است
- `INSUFFICIENT_STOCK` - موجودی کافی نیست

### Payment Errors
- `PAYMENT_NOT_FOUND` - پرداخت یافت نشد
- `PAYMENT_ALREADY_PROCESSED` - پرداخت قبلاً پردازش شده
- `PAYMENT_NOT_REFUNDABLE` - پرداخت قابل بازگشت نیست
- `INVALID_AMOUNT` - مبلغ نامعتبر
- `AMOUNT_EXCEEDS_ORDER` - مبلغ بیش از مبلغ سفارش

---

## Testing Results

✅ **Order Creation:** Successfully created guest order GR76463874095
✅ **Inventory Management:** Properly reserved 2 units from inventory
✅ **Price Calculation:** Correct pricing (450,000 × 2 = 900,000)
✅ **Guest Address Handling:** Stored address in order notes
✅ **Transaction Integrity:** All operations completed in database transaction
✅ **Authentication:** Properly protecting admin and customer endpoints
✅ **Error Handling:** Persian error messages working
✅ **Database Integration:** Complex Prisma queries executing correctly

## Next Steps

1. Test admin authentication with real JWT tokens
2. Test order status transitions and workflow
3. Test payment processing and refunds
4. Add comprehensive test cases for all endpoints
5. Implement order notification system
6. Add order export functionality
