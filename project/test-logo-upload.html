<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logo Upload</title>
</head>
<body>
    <h1>Test Logo Upload Functionality</h1>
    <div>
        <h2>Create Brand with Logo</h2>
        <button onclick="testCreateBrandWithLogo()">Test Create Brand with Logo</button>
        <div id="createResult"></div>
    </div>
    
    <div>
        <h2>Update Brand Logo</h2>
        <button onclick="testUpdateBrandLogo()">Test Update Brand Logo</button>
        <div id="updateResult"></div>
    </div>

    <script>
        // Create a mock file for testing
        function createMockFile() {
            // Create a simple 1x1 pixel PNG
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 1, 1);
            
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    const file = new File([blob], 'test-logo.png', { type: 'image/png' });
                    resolve(file);
                }, 'image/png');
            });
        }

        async function testCreateBrandWithLogo() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Import the service (this would need to be adapted for actual testing)
                console.log('Testing brand creation with logo...');
                
                const mockFile = await createMockFile();
                console.log('Mock file created:', mockFile);
                
                // This would be the actual test
                const brandData = {
                    name: 'Test Brand',
                    nameEn: 'Test Brand',
                    slug: 'test-brand',
                    description: 'Test brand description',
                    country: 'ایران',
                    isActive: true
                };
                
                console.log('Brand data:', brandData);
                console.log('File for upload:', mockFile);
                
                resultDiv.innerHTML = `
                    <p>✅ Mock file created successfully</p>
                    <p>📝 Brand data prepared</p>
                    <p>🔄 Ready for actual API test</p>
                `;
                
            } catch (error) {
                console.error('Test failed:', error);
                resultDiv.innerHTML = `<p style="color: red;">❌ Test failed: ${error.message}</p>`;
            }
        }

        async function testUpdateBrandLogo() {
            const resultDiv = document.getElementById('updateResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing brand logo update...');
                
                const mockFile = await createMockFile();
                console.log('Mock file created for update:', mockFile);
                
                // Mock brand ID (would use existing brand in real test)
                const brandId = '1';
                
                console.log('Brand ID for update:', brandId);
                console.log('File for upload:', mockFile);
                
                resultDiv.innerHTML = `
                    <p>✅ Mock file created successfully</p>
                    <p>🆔 Brand ID: ${brandId}</p>
                    <p>🔄 Ready for actual API test</p>
                `;
                
            } catch (error) {
                console.error('Test failed:', error);
                resultDiv.innerHTML = `<p style="color: red;">❌ Test failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
