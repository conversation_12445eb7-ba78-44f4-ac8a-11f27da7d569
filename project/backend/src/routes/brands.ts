import { Router } from 'express';
import { authenticate, adminOnly } from '../middleware/auth';
import BrandController from '../controllers/brandController';
import { uploadSingleImage } from '../services/uploadService';

const router = Router();

// Public routes
router.get('/', BrandController.getBrands);
router.get('/slug/:slug', BrandController.getBrandBySlug);
router.get('/:id', BrandController.getBrandById);

// Admin routes
router.get('/admin/stats', authenticate, adminOnly, BrandController.getBrandStats);

router.post('/', authenticate, adminOnly, BrandController.createBrand);
router.put('/:id', authenticate, adminOnly, BrandController.updateBrand);
router.delete('/:id', authenticate, adminOnly, BrandController.deleteBrand);

// Upload brand logo
router.post('/:id/logo', authenticate, adminOnly, uploadSingleImage, BrandController.uploadBrandLogo);

export default router;
