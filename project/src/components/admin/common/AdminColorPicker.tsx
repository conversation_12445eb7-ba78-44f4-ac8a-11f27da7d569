import React, { useState, useRef, useEffect } from 'react';
import { Palette, Check } from 'lucide-react';

interface AdminColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  presets?: string[];
  disabled?: boolean;
  className?: string;
}

const AdminColorPicker: React.FC<AdminColorPickerProps> = ({
  value,
  onChange,
  presets = [
    '#000000', '#ffffff', '#f3f4f6', '#1f2937',
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
  ],
  disabled = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setCustomColor(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleColorSelect = (color: string) => {
    onChange(color);
    setCustomColor(color);
    setIsOpen(false);
  };

  const handleCustomColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setCustomColor(newColor);
    onChange(newColor);
  };

  const isValidColor = (color: string): boolean => {
    const s = new Option().style;
    s.color = color;
    return s.color !== '';
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Color Display Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-10 px-3 py-2 border border-gray-300 rounded-lg
          flex items-center gap-3 bg-white
          ${disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:border-gray-400 focus:ring-2 focus:ring-admin-500 focus:border-admin-500'
          }
          transition-colors duration-200
        `}
      >
        {/* Color Preview */}
        <div 
          className="w-6 h-6 rounded border border-gray-300 flex-shrink-0"
          style={{ backgroundColor: value }}
        />
        
        {/* Color Value */}
        <span className="flex-1 text-left text-sm text-gray-700 font-mono">
          {value.toUpperCase()}
        </span>
        
        {/* Icon */}
        <Palette className="w-4 h-4 text-gray-400" />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4">
          {/* Preset Colors */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رنگ‌های آماده
            </label>
            <div className="grid grid-cols-6 gap-2">
              {presets.map((preset) => (
                <button
                  key={preset}
                  type="button"
                  onClick={() => handleColorSelect(preset)}
                  className={`
                    w-8 h-8 rounded border-2 transition-all duration-200
                    hover:scale-110 focus:outline-none focus:ring-2 focus:ring-admin-500
                    ${value === preset 
                      ? 'border-admin-500 ring-2 ring-admin-500 ring-opacity-50' 
                      : 'border-gray-300 hover:border-gray-400'
                    }
                  `}
                  style={{ backgroundColor: preset }}
                  title={preset}
                >
                  {value === preset && (
                    <Check 
                      className={`w-4 h-4 mx-auto ${
                        preset === '#ffffff' || preset === '#f3f4f6' 
                          ? 'text-gray-600' 
                          : 'text-white'
                      }`} 
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Color Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رنگ سفارشی
            </label>
            <div className="flex gap-2">
              {/* Color Input */}
              <input
                type="color"
                value={customColor}
                onChange={handleCustomColorChange}
                className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              
              {/* Text Input */}
              <input
                type="text"
                value={customColor}
                onChange={(e) => {
                  const newColor = e.target.value;
                  setCustomColor(newColor);
                  if (isValidColor(newColor)) {
                    onChange(newColor);
                  }
                }}
                placeholder="#000000"
                className={`
                  flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm font-mono
                  focus:ring-2 focus:ring-admin-500 focus:border-admin-500
                  ${!isValidColor(customColor) ? 'border-red-300 bg-red-50' : ''}
                `}
              />
            </div>
            
            {!isValidColor(customColor) && (
              <p className="text-xs text-red-600 mt-1">
                رنگ وارد شده معتبر نیست
              </p>
            )}
          </div>

          {/* Common Color Names */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رنگ‌های متداول
            </label>
            <div className="grid grid-cols-2 gap-1 text-xs">
              {[
                { name: 'سفید', value: '#ffffff' },
                { name: 'سیاه', value: '#000000' },
                { name: 'خاکستری', value: '#6b7280' },
                { name: 'آبی', value: '#3b82f6' },
                { name: 'سبز', value: '#10b981' },
                { name: 'قرمز', value: '#ef4444' },
                { name: 'زرد', value: '#f59e0b' },
                { name: 'بنفش', value: '#8b5cf6' }
              ].map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => handleColorSelect(color.value)}
                  className={`
                    px-2 py-1 text-right rounded hover:bg-gray-100
                    ${value === color.value ? 'bg-admin-50 text-admin-700' : 'text-gray-600'}
                  `}
                >
                  {color.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminColorPicker;
