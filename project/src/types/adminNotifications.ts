// Notification types for admin system
export interface AdminNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
  readAt?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: NotificationCategory;
  actionUrl?: string;
  actionText?: string;
  icon?: string;
  color?: string;
}

export type NotificationType = 
  | 'order_new'
  | 'order_cancelled'
  | 'order_completed'
  | 'product_low_stock'
  | 'product_out_of_stock'
  | 'review_new'
  | 'review_flagged'
  | 'customer_new'
  | 'customer_complaint'
  | 'payment_failed'
  | 'payment_refund'
  | 'system_error'
  | 'system_update'
  | 'security_alert'
  | 'backup_completed'
  | 'backup_failed';

export type NotificationCategory = 
  | 'orders'
  | 'products'
  | 'customers'
  | 'reviews'
  | 'payments'
  | 'system'
  | 'security';

export interface NotificationFilters {
  category?: NotificationCategory[];
  type?: NotificationType[];
  priority?: ('low' | 'medium' | 'high' | 'urgent')[];
  read?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface NotificationSettings {
  enableEmailNotifications: boolean;
  enablePushNotifications: boolean;
  enableSoundNotifications: boolean;
  categories: {
    [key in NotificationCategory]: {
      enabled: boolean;
      email: boolean;
      push: boolean;
      sound: boolean;
    };
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
  frequency: 'immediate' | 'hourly' | 'daily';
}

export interface AuditLogEntry {
  id: string;
  userId: string;
  userEmail: string;
  userRole: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: any;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  success: boolean;
  errorMessage?: string;
}

export interface AuditLogFilters {
  userId?: string;
  action?: string[];
  resource?: string[];
  success?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  ipAddress?: string;
}

// Persian labels for notifications
export const NOTIFICATION_LABELS = {
  types: {
    order_new: 'سفارش جدید',
    order_cancelled: 'لغو سفارش',
    order_completed: 'تکمیل سفارش',
    product_low_stock: 'موجودی کم',
    product_out_of_stock: 'اتمام موجودی',
    review_new: 'نظر جدید',
    review_flagged: 'نظر گزارش شده',
    customer_new: 'مشتری جدید',
    customer_complaint: 'شکایت مشتری',
    payment_failed: 'خطای پرداخت',
    payment_refund: 'درخواست مرجوعی',
    system_error: 'خطای سیستم',
    system_update: 'بروزرسانی سیستم',
    security_alert: 'هشدار امنیتی',
    backup_completed: 'پشتیبان‌گیری موفق',
    backup_failed: 'خطای پشتیبان‌گیری'
  },
  categories: {
    orders: 'سفارشات',
    products: 'محصولات',
    customers: 'مشتریان',
    reviews: 'نظرات',
    payments: 'پرداخت‌ها',
    system: 'سیستم',
    security: 'امنیت'
  },
  priorities: {
    low: 'کم',
    medium: 'متوسط',
    high: 'بالا',
    urgent: 'فوری'
  },
  actions: {
    view: 'مشاهده',
    edit: 'ویرایش',
    delete: 'حذف',
    approve: 'تأیید',
    reject: 'رد',
    process: 'پردازش',
    resolve: 'حل شده'
  }
};

export const AUDIT_ACTIONS = {
  'user.login': 'ورود کاربر',
  'user.logout': 'خروج کاربر',
  'user.create': 'ایجاد کاربر',
  'user.update': 'ویرایش کاربر',
  'user.delete': 'حذف کاربر',
  'product.create': 'ایجاد محصول',
  'product.update': 'ویرایش محصول',
  'product.delete': 'حذف محصول',
  'order.create': 'ایجاد سفارش',
  'order.update': 'ویرایش سفارش',
  'order.cancel': 'لغو سفارش',
  'order.complete': 'تکمیل سفارش',
  'customer.create': 'ایجاد مشتری',
  'customer.update': 'ویرایش مشتری',
  'customer.delete': 'حذف مشتری',
  'review.create': 'ایجاد نظر',
  'review.approve': 'تأیید نظر',
  'review.reject': 'رد نظر',
  'review.delete': 'حذف نظر',
  'settings.update': 'ویرایش تنظیمات',
  'backup.create': 'ایجاد پشتیبان',
  'backup.restore': 'بازیابی پشتیبان'
};

export const AUDIT_RESOURCES = {
  user: 'کاربر',
  admin: 'مدیر',
  product: 'محصول',
  category: 'دسته‌بندی',
  order: 'سفارش',
  customer: 'مشتری',
  review: 'نظر',
  payment: 'پرداخت',
  settings: 'تنظیمات',
  backup: 'پشتیبان',
  system: 'سیستم'
};
