<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Integration Test</h1>
        
        <div>
            <button onclick="testHealthCheck()">Test Health Check</button>
            <button onclick="testProductsAPI()">Test Products API</button>
            <button onclick="testDocsAPI()">Test Docs API</button>
        </div>

        <div id="status"></div>
        <div id="result"></div>
    </div>

    <script>
        function setStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function setResult(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        async function testHealthCheck() {
            setStatus('Testing health check...', 'loading');
            try {
                const response = await fetch('http://localhost:3001/health');
                const data = await response.json();
                
                if (response.ok) {
                    setStatus('✅ Health check successful!', 'success');
                    setResult(data);
                } else {
                    setStatus('❌ Health check failed!', 'error');
                    setResult(data);
                }
            } catch (error) {
                setStatus(`❌ Health check error: ${error.message}`, 'error');
                setResult({ error: error.message });
            }
        }

        async function testProductsAPI() {
            setStatus('Testing products API...', 'loading');
            try {
                const response = await fetch('http://localhost:3001/api/v1/products');
                const data = await response.json();
                
                if (response.ok) {
                    setStatus(`✅ Products API successful! Found ${data.data?.length || 0} products`, 'success');
                    setResult(data);
                } else {
                    setStatus('❌ Products API failed!', 'error');
                    setResult(data);
                }
            } catch (error) {
                setStatus(`❌ Products API error: ${error.message}`, 'error');
                setResult({ error: error.message });
            }
        }

        async function testDocsAPI() {
            setStatus('Testing docs API...', 'loading');
            try {
                const response = await fetch('http://localhost:3001/api/v1/docs');
                const data = await response.json();
                
                if (response.ok) {
                    setStatus('✅ Docs API successful!', 'success');
                    setResult(data);
                } else {
                    setStatus('❌ Docs API failed!', 'error');
                    setResult(data);
                }
            } catch (error) {
                setStatus(`❌ Docs API error: ${error.message}`, 'error');
                setResult({ error: error.message });
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
