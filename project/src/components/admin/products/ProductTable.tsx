import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Edit,
  Trash2,
  Eye,
  Copy,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  Building2
} from 'lucide-react';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../common/AdminTable';
import AdminImagePreview from '../common/AdminImagePreview';
import { AdminProduct, PERSIAN_PRODUCT_MESSAGES } from '../../../types/adminProduct';
import { AdminIconButton } from '../common/AdminButton';
import { formatPrice } from '../../../utils/formatters';
import { getBrandInfo } from '../../../utils/brandUtils';

interface ProductTableProps {
  products: AdminProduct[];
  loading?: boolean;
  onEdit: (product: AdminProduct) => void;
  onDelete: (product: AdminProduct) => void;
  onDuplicate: (product: AdminProduct) => void;
  onViewDetails: (product: AdminProduct) => void;
  onBulkSelect?: (productIds: number[]) => void;
  selectedProducts?: number[];
  searchValue?: string;
  onSearchChange?: (value: string) => void;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading = false,
  onEdit,
  onDelete,
  onDuplicate,
  onViewDetails,
  onBulkSelect,
  selectedProducts = [],
  searchValue = '',
  onSearchChange
}) => {
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (onBulkSelect) {
      onBulkSelect(checked ? products.map(p => p.id) : []);
    }
  };

  const handleSelectProduct = (productId: number, checked: boolean) => {
    if (onBulkSelect) {
      const newSelection = checked
        ? [...selectedProducts, productId]
        : selectedProducts.filter(id => id !== productId);
      onBulkSelect(newSelection);
      setSelectAll(newSelection.length === products.length);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'success' as const, label: PERSIAN_PRODUCT_MESSAGES.status.active },
      draft: { variant: 'warning' as const, label: PERSIAN_PRODUCT_MESSAGES.status.draft },
      archived: { variant: 'default' as const, label: PERSIAN_PRODUCT_MESSAGES.status.archived }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <AdminTableBadge variant={config.variant}>{config.label}</AdminTableBadge>;
  };

  const getStockStatus = (product: AdminProduct) => {
    if (product.stock === 0) {
      return <AdminTableBadge variant="danger">ناموجود</AdminTableBadge>;
    }
    if (product.trackInventory && product.stock <= product.lowStockThreshold) {
      return <AdminTableBadge variant="warning">موجودی کم</AdminTableBadge>;
    }
    return <AdminTableBadge variant="success">موجود</AdminTableBadge>;
  };

  const columns: AdminTableColumn<AdminProduct>[] = [
    // Bulk selection checkbox
    ...(onBulkSelect ? [{
      key: 'select',
      title: (
        <input
          type="checkbox"
          checked={selectAll}
          onChange={(e) => handleSelectAll(e.target.checked)}
          className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
        />
      ),
      width: '50px',
      render: (product: AdminProduct, index: number) => (
        <input
          type="checkbox"
          checked={selectedProducts.includes(product.id)}
          onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
          className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
        />
      )
    }] : []),
    
    // Product image and name
    {
      key: 'product',
      title: 'محصول',
      width: '300px',
      render: (product: AdminProduct, index: number) => (
        <div className="flex items-center gap-3">
          <AdminImagePreview
            src={product.imageSrc}
            alt={product.name}
            size="md"
            shape="rounded"
            fallbackIcon={Package}
            className="w-12 h-12"
          />
          <div className="min-w-0 flex-1">
            <Link
              to={`/admin/products/${product.id}`}
              className="font-medium text-gray-900 hover:text-admin-600 transition-colors"
            >
              {product.name}
            </Link>
            <div className="text-sm text-gray-500">
              SKU: {product.sku}
            </div>
            {product.featured && (
              <div className="flex items-center gap-1 mt-1">
                <CheckCircle className="w-3 h-3 text-yellow-500" />
                <span className="text-xs text-yellow-600">ویژه</span>
              </div>
            )}
          </div>
        </div>
      )
    },

    // Category
    {
      key: 'category',
      title: 'دسته‌بندی',
      sortable: true,
      width: '150px',
      render: (product: AdminProduct, index: number) => (
        <div className="font-medium text-gray-900">{product.category}</div>
      )
    },

    // Brand
    {
      key: 'brand',
      title: 'برند',
      sortable: true,
      width: '180px',
      render: (product: AdminProduct, index: number) => {
        if (!product.brand) {
          return <span className="text-gray-400">بدون برند</span>;
        }

        const brandInfo = getBrandInfo(product.brand);

        return (
          <div className="flex items-center gap-2">
            <AdminImagePreview
              src={brandInfo.logo}
              alt={`لوگو ${brandInfo.name}`}
              size="sm"
              shape="rounded"
              fallbackIcon={Building2}
            />
            <div>
              <div className="font-medium text-gray-900">{brandInfo.name}</div>
              {brandInfo.nameEn && (
                <div className="text-xs text-gray-500">{brandInfo.nameEn}</div>
              )}
            </div>
          </div>
        );
      }
    },

    // Status
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      width: '120px',
      render: (product: AdminProduct, index: number) => getStatusBadge(product.status)
    },

    // Stock
    {
      key: 'stock',
      title: 'موجودی',
      sortable: true,
      width: '120px',
      align: 'center',
      render: (product: AdminProduct, index: number) => (
        <div className="text-center">
          <div className="font-medium text-gray-900">{product.stock.toLocaleString('fa-IR')}</div>
          {getStockStatus(product)}
        </div>
      )
    },

    // Price
    {
      key: 'price',
      title: 'قیمت',
      sortable: true,
      width: '150px',
      align: 'center',
      render: (product: AdminProduct, index: number) => (
        <div className="text-center">
          <div className="font-medium text-gray-900">
            {formatPrice(product.price)}
          </div>
          {product.discountedPrice && (
            <div className="text-sm text-red-600">
              {formatPrice(product.discountedPrice)}
            </div>
          )}
        </div>
      )
    },

    // Sales metrics
    {
      key: 'metrics',
      title: 'عملکرد',
      width: '120px',
      align: 'center',
      render: (product: AdminProduct, index: number) => (
        <div className="text-center">
          <div className="flex items-center justify-center gap-1">
            <span className="text-sm font-medium">{product.rating.toFixed(1)}</span>
            <span className="text-yellow-400">★</span>
          </div>
          <div className="text-xs text-gray-500">
            {product.reviewCount} نظر
          </div>
        </div>
      )
    },

    // Last updated
    {
      key: 'updatedAt',
      title: 'آخرین بروزرسانی',
      sortable: true,
      width: '150px',
      render: (product: AdminProduct, index: number) => (
        <div className="text-sm text-gray-600">
          {new Date(product.updatedAt).toLocaleDateString('fa-IR')}
        </div>
      )
    }
  ];

  const getRowActions = (product: AdminProduct, index: number) => (
    <AdminActionMenu
      actions={[
        {
          label: 'مشاهده جزئیات',
          onClick: () => onViewDetails(product)
        },
        {
          label: 'ویرایش',
          onClick: () => onEdit(product)
        },
        {
          label: 'کپی محصول',
          onClick: () => onDuplicate(product)
        },
        {
          label: 'حذف',
          onClick: () => onDelete(product),
          variant: 'danger'
        }
      ]}
    />
  );

  return (
    <AdminTable
      columns={columns}
      data={products}
      loading={loading}
      rowActions={getRowActions}
      searchable={!!onSearchChange}
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      searchPlaceholder="جستجو در محصولات..."
      emptyMessage="هیچ محصولی یافت نشد"
      className="mt-6"
    />
  );
};

export default ProductTable;
