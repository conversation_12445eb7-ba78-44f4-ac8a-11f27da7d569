import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  X,
  Package,
  User,
  MapPin,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Clock,
  Truck
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormField, AdminInput, AdminSelect, AdminTextarea } from '../../../components/admin/common/AdminForm';
import { useAdminOrders } from '../../../hooks/useAdminOrders';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import { formatPrice } from '../../../utils/formatters';
import { formatPersianDate } from '../../../utils/dateUtils';
import toast from 'react-hot-toast';

const OrderEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();
  const { getOrder, updateOrder } = useAdminOrders();

  const [order, setOrder] = useState<AdminOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    status: '',
    priority: '',
    shippingAddress: {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      province: '',
      city: '',
      address: '',
      postalCode: ''
    },
    paymentMethod: {
      type: '',
      title: '',
      description: ''
    },
    notes: '',
    trackingNumber: '',
    estimatedDelivery: ''
  });

  useEffect(() => {
    if (!checkPermission('orders', 'update')) {
      navigate('/admin/orders');
      return;
    }

    if (id) {
      const foundOrder = getOrder(id);
      if (foundOrder) {
        setOrder(foundOrder);
        setFormData({
          status: foundOrder.status,
          priority: foundOrder.priority,
          shippingAddress: foundOrder.shippingAddress,
          paymentMethod: foundOrder.paymentMethod,
          notes: foundOrder.notes || '',
          trackingNumber: foundOrder.trackingNumber || '',
          estimatedDelivery: foundOrder.estimatedDelivery || ''
        });
      }
      setLoading(false);
    }
  }, [id, getOrder, checkPermission, navigate]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddressChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      shippingAddress: {
        ...prev.shippingAddress,
        [field]: value
      }
    }));
  };

  const handlePaymentChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethod: {
        ...prev.paymentMethod,
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!order) return;

    setSaving(true);
    try {
      const updatedOrder: AdminOrder = {
        ...order,
        status: formData.status as Order['status'],
        priority: formData.priority as Order['priority'],
        shippingAddress: formData.shippingAddress,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        trackingNumber: formData.trackingNumber,
        estimatedDelivery: formData.estimatedDelivery,
        updatedAt: new Date().toISOString()
      };

      await updateOrder(updatedOrder);
      toast.success('سفارش با موفقیت به‌روزرسانی شد');
      navigate(`/admin/orders/${order.id}`);
    } catch (error) {
      toast.error('خطا در به‌روزرسانی سفارش');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout title="ویرایش سفارش" subtitle="در حال بارگذاری...">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!order) {
    return (
      <AdminLayout title="ویرایش سفارش" subtitle="سفارش یافت نشد">
        <AdminCard>
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">سفارش یافت نشد</h3>
            <p className="text-gray-600 mb-4">سفارش مورد نظر در سیستم موجود نیست.</p>
            <AdminButton
              variant="outline"
              onClick={() => navigate('/admin/orders')}
              icon={ArrowLeft}
            >
              بازگشت به لیست سفارشات
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const statusOptions = [
    { value: 'pending', label: 'در انتظار تأیید' },
    { value: 'confirmed', label: 'تأیید شده' },
    { value: 'processing', label: 'در حال پردازش' },
    { value: 'shipped', label: 'ارسال شده' },
    { value: 'delivered', label: 'تحویل داده شده' },
    { value: 'cancelled', label: 'لغو شده' }
  ];

  const priorityOptions = [
    { value: 'low', label: 'کم' },
    { value: 'normal', label: 'عادی' },
    { value: 'high', label: 'بالا' },
    { value: 'urgent', label: 'فوری' }
  ];

  const paymentTypeOptions = [
    { value: 'card', label: 'کارت بانکی' },
    { value: 'wallet', label: 'کیف پول' },
    { value: 'cash_on_delivery', label: 'پرداخت در محل' }
  ];

  return (
    <AdminLayout
      title={`ویرایش سفارش ${order.orderNumber}`}
      subtitle={`شناسه: ${order.id}`}
      actions={
        <div className="flex items-center gap-3">
          <AdminButton
            variant="outline"
            size="sm"
            onClick={() => navigate(`/admin/orders/${order.id}`)}
            icon={ArrowLeft}
          >
            بازگشت
          </AdminButton>
        </div>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Order Status & Priority */}
        <AdminCard>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Package className="h-5 w-5" />
              وضعیت و اولویت سفارش
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <AdminFormField label="وضعیت سفارش" required>
                <AdminSelect
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  placeholder="انتخاب وضعیت"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </AdminSelect>
              </AdminFormField>

              <AdminFormField label="اولویت" required>
                <AdminSelect
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                  placeholder="انتخاب اولویت"
                >
                  {priorityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </AdminSelect>
              </AdminFormField>
            </div>
          </div>
        </AdminCard>

        {/* Shipping Information */}
        <AdminCard>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              اطلاعات ارسال
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <AdminFormField label="نام" required>
                <AdminInput
                  value={formData.shippingAddress.firstName}
                  onChange={(e) => handleAddressChange('firstName', e.target.value)}
                  placeholder="نام"
                />
              </AdminFormField>

              <AdminFormField label="نام خانوادگی" required>
                <AdminInput
                  value={formData.shippingAddress.lastName}
                  onChange={(e) => handleAddressChange('lastName', e.target.value)}
                  placeholder="نام خانوادگی"
                />
              </AdminFormField>

              <AdminFormField label="تلفن" required>
                <AdminInput
                  value={formData.shippingAddress.phone}
                  onChange={(e) => handleAddressChange('phone', e.target.value)}
                  placeholder="شماره تلفن"
                />
              </AdminFormField>

              <AdminFormField label="ایمیل" required>
                <AdminInput
                  type="email"
                  value={formData.shippingAddress.email}
                  onChange={(e) => handleAddressChange('email', e.target.value)}
                  placeholder="آدرس ایمیل"
                />
              </AdminFormField>

              <AdminFormField label="استان" required>
                <AdminInput
                  value={formData.shippingAddress.province}
                  onChange={(e) => handleAddressChange('province', e.target.value)}
                  placeholder="استان"
                />
              </AdminFormField>

              <AdminFormField label="شهر" required>
                <AdminInput
                  value={formData.shippingAddress.city}
                  onChange={(e) => handleAddressChange('city', e.target.value)}
                  placeholder="شهر"
                />
              </AdminFormField>

              <AdminFormField label="کد پستی" required>
                <AdminInput
                  value={formData.shippingAddress.postalCode}
                  onChange={(e) => handleAddressChange('postalCode', e.target.value)}
                  placeholder="کد پستی"
                />
              </AdminFormField>
            </div>

            <div className="mt-6">
              <AdminFormField label="آدرس کامل" required>
                <AdminTextarea
                  value={formData.shippingAddress.address}
                  onChange={(e) => handleAddressChange('address', e.target.value)}
                  placeholder="آدرس کامل"
                  rows={3}
                />
              </AdminFormField>
            </div>
          </div>
        </AdminCard>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-4">
          <AdminButton
            type="button"
            variant="outline"
            onClick={() => navigate(`/admin/orders/${order.id}`)}
            icon={X}
          >
            انصراف
          </AdminButton>
          
          <AdminButton
            type="submit"
            loading={saving}
            icon={Save}
          >
            ذخیره تغییرات
          </AdminButton>
        </div>
      </form>
    </AdminLayout>
  );
};

export default OrderEditPage;
