/**
 * PWA Utility Functions
 * Comprehensive utilities for Progressive Web App functionality
 */

// Types
export interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export interface PWAInstallState {
  isInstallable: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  platform: string;
  canInstall: boolean;
}

export interface OfflineState {
  isOnline: boolean;
  isOffline: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
}

export interface PWACapabilities {
  serviceWorker: boolean;
  pushNotifications: boolean;
  backgroundSync: boolean;
  webShare: boolean;
  fullscreen: boolean;
  installPrompt: boolean;
}

// PWA Detection and State Management
export class PWAManager {
  private static instance: PWAManager;
  private installPrompt: BeforeInstallPromptEvent | null = null;
  private listeners: Map<string, Function[]> = new Map();

  private constructor() {
    this.setupEventListeners();
  }

  static getInstance(): PWAManager {
    if (!PWAManager.instance) {
      PWAManager.instance = new PWAManager();
    }
    return PWAManager.instance;
  }

  private setupEventListeners(): void {
    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e: Event) => {
      e.preventDefault();
      this.installPrompt = e as BeforeInstallPromptEvent;
      this.emit('installable', true);
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      this.installPrompt = null;
      this.emit('installed', true);
    });

    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.emit('online', true);
    });

    window.addEventListener('offline', () => {
      this.emit('offline', true);
    });
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // PWA Installation
  async promptInstall(): Promise<boolean> {
    if (!this.installPrompt) {
      return false;
    }

    try {
      await this.installPrompt.prompt();
      const choiceResult = await this.installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        this.installPrompt = null;
        return true;
      }
      return false;
    } catch (error) {
      // console.error('Error prompting install:', error);
      return false;
    }
  }

  // PWA State Detection
  getInstallState(): PWAInstallState {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://');

    const platform = this.detectPlatform();
    
    return {
      isInstallable: !!this.installPrompt,
      isInstalled: isStandalone,
      isStandalone,
      platform,
      canInstall: !!this.installPrompt && !isStandalone
    };
  }

  private detectPlatform(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/android/.test(userAgent)) return 'android';
    if (/iphone|ipad|ipod/.test(userAgent)) return 'ios';
    if (/windows/.test(userAgent)) return 'windows';
    if (/macintosh|mac os x/.test(userAgent)) return 'macos';
    if (/linux/.test(userAgent)) return 'linux';
    
    return 'unknown';
  }

  // Network State
  getNetworkState(): OfflineState {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    return {
      isOnline: navigator.onLine,
      isOffline: !navigator.onLine,
      connectionType: connection?.type || 'unknown',
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      rtt: connection?.rtt || 0
    };
  }

  // PWA Capabilities Detection
  getCapabilities(): PWACapabilities {
    return {
      serviceWorker: 'serviceWorker' in navigator,
      pushNotifications: 'PushManager' in window,
      backgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
      webShare: 'share' in navigator,
      fullscreen: 'requestFullscreen' in document.documentElement,
      installPrompt: !!this.installPrompt
    };
  }

  // Service Worker Management
  async registerServiceWorker(swPath: string = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      // console.warn('Service Worker not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register(swPath);
      // console.log('Service Worker registered:', registration);
      return registration;
    } catch (error) {
      // console.error('Service Worker registration failed:', error);
      return null;
    }
  }

  // Push Notifications
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      // console.warn('Notifications not supported');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    const permission = await Notification.requestPermission();
    return permission;
  }

  // Web Share API
  async shareContent(shareData: ShareData): Promise<boolean> {
    if (!('share' in navigator)) {
      // Fallback to clipboard or other sharing methods
      return this.fallbackShare(shareData);
    }

    try {
      await navigator.share(shareData);
      return true;
    } catch (error) {
      // console.error('Error sharing:', error);
      return false;
    }
  }

  private async fallbackShare(shareData: ShareData): Promise<boolean> {
    // Fallback sharing implementation
    const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
    
    if ('clipboard' in navigator) {
      try {
        await navigator.clipboard.writeText(shareText);
        return true;
      } catch (error) {
        // console.error('Clipboard write failed:', error);
      }
    }

    // Final fallback - open in new window
    const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}`;
    window.open(shareUrl, '_blank');
    return true;
  }

  // Cache Management
  async clearCache(): Promise<void> {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    }
  }

  // App Update Management
  async checkForUpdates(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      return false;
    }

    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      return false;
    }

    await registration.update();
    return !!registration.waiting;
  }

  async applyUpdate(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      return;
    }

    const registration = await navigator.serviceWorker.getRegistration();
    if (registration?.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }
}

// Singleton instance
export const pwaManager = PWAManager.getInstance();

// Utility functions
export const isPWAInstalled = (): boolean => {
  return pwaManager.getInstallState().isInstalled;
};

export const isPWAInstallable = (): boolean => {
  return pwaManager.getInstallState().canInstall;
};

export const isOnline = (): boolean => {
  return navigator.onLine;
};

export const isOffline = (): boolean => {
  return !navigator.onLine;
};

// Persian PWA messages
export const PWA_MESSAGES = {
  install: {
    title: 'نصب اپلیکیشن',
    description: 'آرامش پوست را روی دستگاه خود نصب کنید',
    button: 'نصب',
    cancel: 'بعداً',
    success: 'اپلیکیشن با موفقیت نصب شد',
    error: 'خطا در نصب اپلیکیشن'
  },
  offline: {
    title: 'حالت آفلاین',
    description: 'اتصال اینترنت برقرار نیست',
    retry: 'تلاش مجدد'
  },
  update: {
    title: 'بروزرسانی موجود',
    description: 'نسخه جدید اپلیکیشن آماده است',
    button: 'بروزرسانی',
    later: 'بعداً'
  },
  share: {
    title: 'اشتراک‌گذاری',
    success: 'با موفقیت اشتراک‌گذاری شد',
    error: 'خطا در اشتراک‌گذاری'
  }
};
