import { Request, Response, NextFunction } from 'express';
import { UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import AuthService from '../services/authService';

export class UserController {
  // Get user profile
  static async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          addresses: {
            select: {
              id: true,
              title: true,
              firstName: true,
              lastName: true,
              phone: true,
              province: true,
              city: true,
              district: true,
              street: true,
              postalCode: true,
              isDefault: true,
            },
            orderBy: { isDefault: 'desc' },
          },
        },
      });

      if (!user) {
        throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update user profile
  static async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { firstName, lastName, phone } = req.body;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Check if phone is already used by another user
      if (phone) {
        const existingUser = await prisma.user.findFirst({
          where: {
            phone,
            id: { not: userId },
          },
        });

        if (existingUser) {
          throw new AppError('این شماره تلفن قبلاً ثبت شده است', 409, 'PHONE_EXISTS');
        }
      }

      // Update user
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          firstName,
          lastName,
          phone,
          // Reset phone verification if phone changed
          ...(phone && { isPhoneVerified: false }),
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info(`User profile updated: ${userId}`);

      res.json({
        success: true,
        message: 'پروفایل با موفقیت به‌روزرسانی شد',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }

  // Change password
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Get user with password
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
      }

      // Verify current password
      const isCurrentPasswordValid = await AuthService.verifyPassword(
        currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw new AppError('رمز عبور فعلی اشتباه است', 400, 'INVALID_CURRENT_PASSWORD');
      }

      // Hash new password
      const hashedNewPassword = await AuthService.hashPassword(newPassword);

      // Update password
      await prisma.user.update({
        where: { id: userId },
        data: { password: hashedNewPassword },
      });

      // Invalidate all user sessions except current one
      await AuthService.invalidateAllUserSessions(userId);

      logger.info(`Password changed for user: ${userId}`);

      res.json({
        success: true,
        message: 'رمز عبور با موفقیت تغییر یافت',
      });
    } catch (error) {
      next(error);
    }
  }

  // Add address
  static async addAddress(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const {
        title,
        firstName,
        lastName,
        phone,
        province,
        city,
        district,
        street,
        postalCode,
        isDefault,
      } = req.body;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // If this is set as default, unset other default addresses
      if (isDefault) {
        await prisma.address.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false },
        });
      }

      // Create address
      const address = await prisma.address.create({
        data: {
          userId,
          title,
          firstName,
          lastName,
          phone,
          province,
          city,
          district,
          street,
          postalCode,
          isDefault: isDefault || false,
        },
      });

      logger.info(`Address added for user: ${userId}`);

      res.status(201).json({
        success: true,
        message: 'آدرس با موفقیت اضافه شد',
        data: { address },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update address
  static async updateAddress(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const addressId = req.params.id;
      const {
        title,
        firstName,
        lastName,
        phone,
        province,
        city,
        district,
        street,
        postalCode,
        isDefault,
      } = req.body;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Check if address belongs to user
      const existingAddress = await prisma.address.findFirst({
        where: { id: addressId, userId },
      });

      if (!existingAddress) {
        throw new AppError('آدرس یافت نشد', 404, 'ADDRESS_NOT_FOUND');
      }

      // If this is set as default, unset other default addresses
      if (isDefault) {
        await prisma.address.updateMany({
          where: { userId, isDefault: true, id: { not: addressId } },
          data: { isDefault: false },
        });
      }

      // Update address
      const updatedAddress = await prisma.address.update({
        where: { id: addressId },
        data: {
          title,
          firstName,
          lastName,
          phone,
          province,
          city,
          district,
          street,
          postalCode,
          isDefault: isDefault || false,
        },
      });

      logger.info(`Address updated for user: ${userId}`);

      res.json({
        success: true,
        message: 'آدرس با موفقیت به‌روزرسانی شد',
        data: { address: updatedAddress },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete address
  static async deleteAddress(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const addressId = req.params.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Check if address belongs to user
      const existingAddress = await prisma.address.findFirst({
        where: { id: addressId, userId },
      });

      if (!existingAddress) {
        throw new AppError('آدرس یافت نشد', 404, 'ADDRESS_NOT_FOUND');
      }

      // Delete address
      await prisma.address.delete({
        where: { id: addressId },
      });

      logger.info(`Address deleted for user: ${userId}`);

      res.json({
        success: true,
        message: 'آدرس با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Set default address
  static async setDefaultAddress(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const addressId = req.params.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Check if address belongs to user
      const existingAddress = await prisma.address.findFirst({
        where: { id: addressId, userId },
      });

      if (!existingAddress) {
        throw new AppError('آدرس یافت نشد', 404, 'ADDRESS_NOT_FOUND');
      }

      // Unset other default addresses
      await prisma.address.updateMany({
        where: { userId, isDefault: true },
        data: { isDefault: false },
      });

      // Set this address as default
      await prisma.address.update({
        where: { id: addressId },
        data: { isDefault: true },
      });

      logger.info(`Default address set for user: ${userId}`);

      res.json({
        success: true,
        message: 'آدرس پیش‌فرض تنظیم شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all users (admin only)
  static async getAllUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string;
      const role = req.query.role as UserRole;
      const status = req.query.status as UserStatus;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};
      
      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (role) {
        where.role = role;
      }

      if (status) {
        where.status = status;
      }

      // Get users with pagination
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            phone: true,
            role: true,
            status: true,
            isEmailVerified: true,
            isPhoneVerified: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.user.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user by ID (admin only)
  static async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.id;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          addresses: {
            select: {
              id: true,
              title: true,
              firstName: true,
              lastName: true,
              phone: true,
              province: true,
              city: true,
              district: true,
              street: true,
              postalCode: true,
              isDefault: true,
            },
          },
          orders: {
            select: {
              id: true,
              orderNumber: true,
              status: true,
              totalAmount: true,
              createdAt: true,
            },
            take: 5,
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!user) {
        throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update user status (admin only)
  static async updateUserStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params.id;
      const { status } = req.body;

      if (!Object.values(UserStatus).includes(status)) {
        throw new AppError('وضعیت کاربر نامعتبر است', 400, 'INVALID_STATUS');
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { status },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          status: true,
        },
      });

      // If user is suspended or banned, invalidate all sessions
      if (status === UserStatus.SUSPENDED || status === UserStatus.BANNED) {
        await AuthService.invalidateAllUserSessions(userId);
      }

      logger.info(`User status updated: ${userId} -> ${status}`);

      res.json({
        success: true,
        message: 'وضعیت کاربر با موفقیت به‌روزرسانی شد',
        data: { user: updatedUser },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default UserController;
