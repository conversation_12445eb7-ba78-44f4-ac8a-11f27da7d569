// Content Management System Types
export type ContentStatus = 'draft' | 'published' | 'scheduled' | 'archived';
export type ContentType = 'banner' | 'promotion' | 'newsletter' | 'page' | 'media';
export type BannerType = 'hero' | 'promotional' | 'announcement' | 'category';
export type PromotionType = 'percentage' | 'fixed_amount' | 'buy_one_get_one' | 'free_shipping';
export type MediaType = 'image' | 'video' | 'document';

// Base content interface
export interface BaseContent {
  id: string;
  title: string;
  status: ContentStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  publishedAt?: string;
  scheduledAt?: string;
  archivedAt?: string;
  
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  slug?: string;
  
  // Visibility and targeting
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  targetAudience?: string[];
  
  // Analytics
  views: number;
  clicks: number;
  conversions: number;
}

// Banner Management
export interface Banner extends BaseContent {
  type: BannerType;
  subtitle?: string;
  description?: string;
  
  // Media
  image: string;
  mobileImage?: string;
  altText: string;
  
  // Call to action
  ctaText?: string;
  ctaUrl?: string;
  ctaType?: 'internal' | 'external';
  
  // Display settings
  position: number;
  showOnPages: string[];
  backgroundColor?: string;
  textColor?: string;
  overlayOpacity?: number;
  
  // Animation and effects
  animationType?: 'fade' | 'slide' | 'zoom' | 'none';
  autoplay?: boolean;
  duration?: number; // seconds
}

// Promotion Management
export interface Promotion extends BaseContent {
  type: PromotionType;
  description: string;
  
  // Discount configuration
  discountValue: number; // percentage or fixed amount
  minimumOrderAmount?: number;
  maximumDiscountAmount?: number;
  
  // Product targeting
  applicableProducts?: number[];
  applicableCategories?: string[];
  excludedProducts?: number[];
  
  // Usage limits
  usageLimit?: number;
  usagePerCustomer?: number;
  currentUsage: number;
  
  // Promotion code
  code?: string;
  isCodeRequired: boolean;
  
  // Display settings
  showOnHomepage: boolean;
  showInCart: boolean;
  showOnProductPages: boolean;
  bannerImage?: string;
  
  // Terms and conditions
  terms?: string;
  conditions?: string[];
}

// Newsletter Campaign Management
export interface NewsletterCampaign extends BaseContent {
  subject: string;
  preheader?: string;
  content: string;
  htmlContent?: string;
  
  // Campaign settings
  campaignType: 'promotional' | 'informational' | 'product_update' | 'seasonal';
  template?: string;
  
  // Targeting
  recipientSegments: string[];
  recipientCount: number;
  
  // Scheduling
  sendAt?: string;
  timezone: string;
  
  // Analytics
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  clickCount: number;
  unsubscribeCount: number;
  bounceCount: number;
  
  // Performance metrics
  openRate: number;
  clickRate: number;
  unsubscribeRate: number;
  bounceRate: number;
  
  // A/B Testing
  isABTest?: boolean;
  abTestVariant?: 'A' | 'B';
  abTestResults?: {
    variantA: NewsletterMetrics;
    variantB: NewsletterMetrics;
    winner?: 'A' | 'B';
  };
}

export interface NewsletterMetrics {
  sentCount: number;
  openCount: number;
  clickCount: number;
  openRate: number;
  clickRate: number;
}

// Page Content Management
export interface PageContent extends BaseContent {
  content: string;
  excerpt?: string;
  
  // Page settings
  template: 'default' | 'landing' | 'about' | 'contact' | 'custom';
  parentPage?: string;
  menuOrder: number;
  showInMenu: boolean;
  
  // SEO and meta
  metaTitle?: string;
  metaDescription?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  
  // Featured content
  featuredImage?: string;
  featuredImageAlt?: string;
  
  // Custom fields
  customFields?: Record<string, any>;
}

// Media Library
export interface MediaItem {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number; // bytes
  type: MediaType;
  
  // URLs
  url: string;
  thumbnailUrl?: string;
  
  // Metadata
  title?: string;
  description?: string;
  altText?: string;
  caption?: string;
  
  // Image specific
  width?: number;
  height?: number;
  
  // Organization
  folder?: string;
  tags: string[];
  
  // Upload info
  uploadedAt: string;
  uploadedBy: string;
  
  // Usage tracking
  usageCount: number;
  lastUsedAt?: string;
}

// Content Analytics
export interface ContentAnalytics {
  totalContent: number;
  publishedContent: number;
  draftContent: number;
  scheduledContent: number;
  
  // Performance metrics
  totalViews: number;
  totalClicks: number;
  totalConversions: number;
  
  // Content type breakdown
  contentByType: Record<ContentType, number>;
  
  // Recent activity
  recentActivity: ContentActivity[];
  
  // Top performing content
  topBanners: Banner[];
  topPromotions: Promotion[];
  topPages: PageContent[];
}

export interface ContentActivity {
  id: string;
  type: 'created' | 'updated' | 'published' | 'archived';
  contentType: ContentType;
  contentId: string;
  contentTitle: string;
  userId: string;
  userName: string;
  timestamp: string;
}

// Form Data Interfaces
export interface BannerFormData {
  title: string;
  subtitle?: string;
  description?: string;
  type: BannerType;
  image: string;
  mobileImage?: string;
  altText: string;
  ctaText?: string;
  ctaUrl?: string;
  ctaType?: 'internal' | 'external';
  position: number;
  showOnPages: string[];
  backgroundColor?: string;
  textColor?: string;
  overlayOpacity?: number;
  animationType?: 'fade' | 'slide' | 'zoom' | 'none';
  autoplay?: boolean;
  duration?: number;
  status: ContentStatus;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface PromotionFormData {
  title: string;
  description: string;
  type: PromotionType;
  discountValue: number;
  minimumOrderAmount?: number;
  maximumDiscountAmount?: number;
  applicableProducts?: number[];
  applicableCategories?: string[];
  excludedProducts?: number[];
  usageLimit?: number;
  usagePerCustomer?: number;
  code?: string;
  isCodeRequired: boolean;
  showOnHomepage: boolean;
  showInCart: boolean;
  showOnProductPages: boolean;
  bannerImage?: string;
  terms?: string;
  conditions?: string[];
  status: ContentStatus;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
}

export interface NewsletterCampaignFormData {
  title: string;
  subject: string;
  preheader?: string;
  content: string;
  htmlContent?: string;
  campaignType: 'promotional' | 'informational' | 'product_update' | 'seasonal';
  template?: string;
  recipientSegments: string[];
  sendAt?: string;
  timezone: string;
  isABTest?: boolean;
  abTestVariant?: 'A' | 'B';
  status: ContentStatus;
}

export interface PageContentFormData {
  title: string;
  content: string;
  excerpt?: string;
  template: 'default' | 'landing' | 'about' | 'contact' | 'custom';
  parentPage?: string;
  menuOrder: number;
  showInMenu: boolean;
  metaTitle?: string;
  metaDescription?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  featuredImage?: string;
  featuredImageAlt?: string;
  customFields?: Record<string, any>;
  status: ContentStatus;
  isActive: boolean;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  slug?: string;
}

// Filter and Search Interfaces
export interface ContentFilters {
  status?: ContentStatus[];
  type?: ContentType[];
  createdBy?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
  tags?: string[];
  isActive?: boolean;
}

export interface ContentSortOptions {
  field: 'title' | 'createdAt' | 'updatedAt' | 'publishedAt' | 'views' | 'clicks';
  direction: 'asc' | 'desc';
}

// Bulk Operations
export interface BulkContentOperation {
  type: 'publish' | 'unpublish' | 'archive' | 'delete' | 'duplicate';
  contentIds: string[];
  data?: any;
}

// Content Validation
export interface ContentValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
  }>;
}

// Content Preview
export interface ContentPreview {
  id: string;
  type: ContentType;
  previewUrl: string;
  previewData: any;
  generatedAt: string;
}
