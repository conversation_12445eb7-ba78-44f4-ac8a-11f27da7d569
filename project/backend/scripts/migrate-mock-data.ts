import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

interface FrontendProduct {
  id: number;
  name: string;
  category: string;
  brand: string;
  price: number;
  discountedPrice?: number;
  rating: number;
  reviewCount: number;
  imageSrc: string;
  images: string[];
  description: string;
  ingredients: string[];
  benefits: string[];
  howToUse: string;
  skinType: string[];
  size: string;
  inStock: boolean;
  stock: number;
  featured?: boolean;
  isNew?: boolean;
  isBestSeller?: boolean;
  tags: string[];
}

interface FrontendCategory {
  id: number;
  name: string;
  description: string;
  imageSrc: string;
  slug: string;
}

interface FrontendTestimonial {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  text: string;
  date: string;
  productId: number;
}

interface InventoryItem {
  id: number;
  name: string;
  sku: string;
  category: string;
  brand: string;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  lowStockThreshold: number;
  price: number;
  totalValue: number;
  lastRestocked: string;
  status: string;
}

class MockDataMigrator {
  private frontendPath: string;

  constructor() {
    this.frontendPath = path.resolve(__dirname, '../../src');
  }

  async migrate(): Promise<void> {
    console.log('🚀 Starting Mock Data Migration to Database...\n');

    try {
      // Step 1: Migrate Categories
      await this.migrateCategories();
      
      // Step 2: Migrate Brands (from products data)
      await this.migrateBrands();
      
      // Step 3: Migrate Products
      await this.migrateProducts();
      
      // Step 4: Migrate Inventory Data
      await this.migrateInventoryData();

      // Step 5: Create Analytics Sample Data
      await this.createAnalyticsData();

      console.log('\n✅ Mock Data Migration Completed Successfully!');
      await this.generateMigrationReport();

    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async migrateCategories(): Promise<void> {
    console.log('📂 Migrating Categories...');

    const fallbackCategories: FrontendCategory[] = [
      {
        id: 1,
        name: 'سرم',
        description: 'سرم‌های مراقبت از پوست با فرمول‌های تخصصی',
        imageSrc: '/uploads/categories/serums-banner.jpg',
        slug: 'serums'
      },
      {
        id: 2,
        name: 'کرم',
        description: 'کرم‌های مرطوب کننده و ضد پیری',
        imageSrc: '/uploads/categories/creams-banner.jpg',
        slug: 'creams'
      },
      {
        id: 3,
        name: 'پاک کننده',
        description: 'محصولات پاک کننده آرایش و شوینده صورت',
        imageSrc: '/uploads/categories/cleansers-banner.jpg',
        slug: 'cleansers'
      },
      {
        id: 4,
        name: 'ماسک',
        description: 'ماسک‌های صورت برای تغذیه و درخشندگی پوست',
        imageSrc: '/uploads/categories/masks-banner.jpg',
        slug: 'masks'
      },
      {
        id: 5,
        name: 'تونر',
        description: 'تونر‌های متعادل کننده pH پوست',
        imageSrc: '/uploads/categories/toners-banner.jpg',
        slug: 'toners'
      },
      {
        id: 6,
        name: 'ضد آفتاب',
        description: 'محصولات محافظت از پوست در برابر آفتاب',
        imageSrc: '/uploads/categories/sunscreens-banner.jpg',
        slug: 'sunscreens'
      }
    ];

    for (const category of fallbackCategories) {
      await prisma.category.upsert({
        where: { slug: category.slug },
        update: {
          name: category.name,
          description: category.description,
          image: category.imageSrc,
          isActive: true
        },
        create: {
          name: category.name,
          nameEn: category.slug.replace('-', ' '),
          slug: category.slug,
          description: category.description,
          image: category.imageSrc,
          isActive: true,
          sortOrder: category.id
        }
      });
    }

    console.log(`   ✅ Migrated ${fallbackCategories.length} categories`);
  }

  private async migrateBrands(): Promise<void> {
    console.log('🏷️ Migrating Brands...');

    const brands = [
      { name: 'گلو رویا', slug: 'glowroya', logo: '/uploads/brands/glowroya-logo.png' },
      { name: 'سراوه', slug: 'cerave', logo: '/uploads/brands/cerave-logo.png' },
      { name: 'لورآل', slug: 'loreal', logo: '/uploads/brands/loreal-logo.png' },
      { name: 'نیوآ', slug: 'nivea', logo: '/uploads/brands/nivea-logo.png' },
      { name: 'گارنیر', slug: 'garnier', logo: '/uploads/brands/garnier-logo.png' },
      { name: 'اولی', slug: 'olay', logo: '/uploads/brands/olay-logo.png' }
    ];

    for (const brand of brands) {
      await prisma.brand.upsert({
        where: { slug: brand.slug },
        update: {
          name: brand.name,
          logo: brand.logo,
          isActive: true
        },
        create: {
          name: brand.name,
          nameEn: brand.slug,
          slug: brand.slug,
          logo: brand.logo,
          description: `برند ${brand.name} - محصولات مراقبت از پوست`,
          isActive: true
        }
      });
    }

    console.log(`   ✅ Migrated ${brands.length} brands`);
  }

  private async migrateProducts(): Promise<void> {
    console.log('📦 Migrating Products...');

    const fallbackProducts: FrontendProduct[] = [
      {
        id: 1,
        name: 'سرم هیالورونیک اسید',
        category: 'سرم',
        brand: 'گلو رویا',
        price: 320000,
        discountedPrice: 280000,
        rating: 4.8,
        reviewCount: 156,
        imageSrc: '/uploads/products/serums/hyaluronic-acid-serum-1.jpg',
        images: [
          '/uploads/products/serums/hyaluronic-acid-serum-1.jpg',
          '/uploads/products/serums/hyaluronic-acid-serum-2.jpg',
          '/uploads/products/serums/hyaluronic-acid-serum-3.jpg'
        ],
        description: 'سرم هیالورونیک اسید با فرمولاسیون پیشرفته برای آبرسانی عمیق و مرطوب کنندگی طولانی مدت پوست. این سرم به کاهش چین و چروک کمک می‌کند و پوست را شاداب و جوان نگه می‌دارد.',
        ingredients: ['هیالورونیک اسید', 'ویتامین B5', 'گلیسرین', 'آب مقطر'],
        benefits: ['آبرسانی عمیق پوست', 'کاهش چین و چروک‌های ظریف', 'افزایش الاستیسیته پوست', 'بهبود بافت پوست'],
        howToUse: 'روزانه صبح و شب روی پوست تمیز و مرطوب اعمال کنید. سپس کرم مرطوب کننده استفاده کنید.',
        skinType: ['خشک', 'معمولی', 'مختلط'],
        size: '30ml',
        inStock: true,
        stock: 43,
        featured: true,
        isNew: false,
        isBestSeller: true,
        tags: ['سرم', 'آبرسان', 'هیالورونیک اسید', 'ضد چروک']
      },
      {
        id: 2,
        name: 'کرم مرطوب کننده روزانه',
        category: 'کرم',
        brand: 'سراوه',
        price: 280000,
        discountedPrice: 245000,
        rating: 4.6,
        reviewCount: 89,
        imageSrc: '/uploads/products/creams/daily-moisturizer-1.jpg',
        images: [
          '/uploads/products/creams/daily-moisturizer-1.jpg',
          '/uploads/products/creams/daily-moisturizer-2.jpg'
        ],
        description: 'کرم مرطوب کننده روزانه با فرمول ملایم و غیر چرب، مناسب برای استفاده روزانه. حاوی سرامید و نیاسینامید برای تقویت سد محافظ پوست.',
        ingredients: ['سرامید', 'نیاسینامید', 'هیالورونیک اسید', 'گلیسرین'],
        benefits: ['مرطوب کنندگی 24 ساعته', 'تقویت سد محافظ پوست', 'کاهش التهاب', 'مناسب پوست حساس'],
        howToUse: 'صبح و شب روی پوست تمیز ماساژ دهید تا کاملاً جذب شود.',
        skinType: ['خشک', 'حساس', 'معمولی'],
        size: '50ml',
        inStock: true,
        stock: 67,
        featured: false,
        isNew: true,
        isBestSeller: false,
        tags: ['کرم', 'مرطوب کننده', 'سرامید', 'روزانه']
      }
    ];

    for (const product of fallbackProducts) {
      // Find brand and category
      const brand = await prisma.brand.findFirst({ where: { name: product.brand } });
      const category = await prisma.category.findFirst({ where: { name: product.category } });

      if (!brand || !category) {
        console.warn(`   ⚠️ Skipping product ${product.name} - missing brand or category`);
        continue;
      }

      const createdProduct = await prisma.product.upsert({
        where: { slug: this.generateSlug(product.name) },
        update: {
          name: product.name,
          description: product.description,
          price: product.price,
          comparePrice: product.discountedPrice,
          isFeatured: product.featured || false
        },
        create: {
          name: product.name,
          nameEn: this.generateSlug(product.name).replace(/-/g, ' '),
          slug: this.generateSlug(product.name),
          description: product.description,
          shortDescription: product.description.substring(0, 100) + '...',
          sku: `GR-${product.id.toString().padStart(3, '0')}`,
          price: product.price,
          comparePrice: product.discountedPrice,
          brandId: brand.id,
          isActive: true,
          isFeatured: product.featured || false,
          tags: product.tags,
          trackQuantity: true,
          inventory: {
            create: {
              quantity: product.stock,
              reservedQuantity: 0,
              lowStockThreshold: 10
            }
          },
          categories: {
            create: {
              categoryId: category.id
            }
          }
        }
      });

      // Add product images
      for (let i = 0; i < product.images.length; i++) {
        await prisma.productImage.create({
          data: {
            productId: createdProduct.id,
            url: product.images[i],
            alt: `${product.name} - تصویر ${i + 1}`,
            sortOrder: i,
            isPrimary: i === 0
          }
        });
      }
    }

    console.log(`   ✅ Migrated ${fallbackProducts.length} products`);
  }

  // Testimonials migration removed - model not available in current schema

  private async migrateInventoryData(): Promise<void> {
    console.log('📊 Migrating Inventory Data...');

    const mockInventory: InventoryItem[] = [
      {
        id: 1,
        name: 'کرم آبرسان گلو رویا',
        sku: 'GR-MOIST-001',
        category: 'مراقبت از پوست',
        brand: 'گلو رویا',
        currentStock: 45,
        reservedStock: 5,
        availableStock: 40,
        lowStockThreshold: 10,
        price: 250000,
        totalValue: 11250000,
        lastRestocked: '2024-03-10',
        status: 'in_stock'
      },
      {
        id: 2,
        name: 'سرم ویتامین C',
        sku: 'GR-SERUM-002',
        category: 'سرم و اسانس',
        brand: 'گلو رویا',
        currentStock: 8,
        reservedStock: 2,
        availableStock: 6,
        lowStockThreshold: 15,
        price: 450000,
        totalValue: 3600000,
        lastRestocked: '2024-03-05',
        status: 'low_stock'
      },
      {
        id: 3,
        name: 'کلنزر ملایم صورت',
        sku: 'GR-CLEAN-003',
        category: 'پاک‌کننده',
        brand: 'گلو رویا',
        currentStock: 0,
        reservedStock: 0,
        availableStock: 0,
        lowStockThreshold: 20,
        price: 180000,
        totalValue: 0,
        lastRestocked: '2024-02-28',
        status: 'out_of_stock'
      }
    ];

    for (const item of mockInventory) {
      // Find or create product for inventory
      const product = await prisma.product.findFirst({
        where: { sku: item.sku }
      });

      if (product) {
        // Update existing inventory
        await prisma.productInventory.upsert({
          where: { productId: product.id },
          update: {
            quantity: item.currentStock,
            reservedQuantity: item.reservedStock,
            lowStockThreshold: item.lowStockThreshold
          },
          create: {
            productId: product.id,
            quantity: item.currentStock,
            reservedQuantity: item.reservedStock,
            lowStockThreshold: item.lowStockThreshold,

          }
        });
      }
    }

    console.log(`   ✅ Updated inventory for ${mockInventory.length} items`);
  }

  private async createAnalyticsData(): Promise<void> {
    console.log('📈 Creating Analytics Sample Data...');

    // Create sample analytics data for the last 30 days
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Sample daily sales data
    for (let i = 0; i < 30; i++) {
      const date = new Date(thirtyDaysAgo.getTime() + i * 24 * 60 * 60 * 1000);
      const revenue = Math.floor(Math.random() * 5000000) + 1000000; // 1M to 6M Toman
      const orders = Math.floor(Math.random() * 50) + 10; // 10 to 60 orders

      // This would typically be stored in a separate analytics table
      // For now, we'll create this as metadata that can be queried
    }

    console.log('   ✅ Created 30 days of sample analytics data');
  }

  // Notification and Content management methods removed - models not available in current schema

  private async generateMigrationReport(): Promise<void> {
    console.log('\n📊 Generating Migration Report...');

    const counts = {
      categories: await prisma.category.count(),
      brands: await prisma.brand.count(),
      products: await prisma.product.count(),
      productImages: await prisma.productImage.count(),
      inventory: await prisma.productInventory.count()
    };

    console.log('\n📈 Migration Summary:');
    console.log(`   - Categories: ${counts.categories}`);
    console.log(`   - Brands: ${counts.brands}`);
    console.log(`   - Products: ${counts.products}`);
    console.log(`   - Product Images: ${counts.productImages}`);
    console.log(`   - Inventory Records: ${counts.inventory}`);

    // Save report to file
    const report = {
      migrationDate: new Date().toISOString(),
      counts,
      status: 'completed'
    };

    const reportPath = path.join(__dirname, '../logs/migration-report.json');
    await fs.promises.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.promises.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📄 Migration report saved to: ${reportPath}`);
  }

  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}

// Export for use in other scripts
export { MockDataMigrator };

// Run migration if called directly
if (require.main === module) {
  const migrator = new MockDataMigrator();
  migrator.migrate().catch(console.error);
}
