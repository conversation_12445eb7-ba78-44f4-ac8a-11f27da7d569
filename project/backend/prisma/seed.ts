import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Enhanced product data from frontend mock data
interface ProductSeedData {
  name: string;
  nameEn: string;
  slug: string;
  description: string;
  shortDescription: string;
  sku: string;
  price: number;
  comparePrice?: number;
  brandSlug: string;
  categorySlug: string;
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  trackQuantity: boolean;
  stock: number;
  lowStockThreshold: number;
  benefits?: string[];
  ingredients?: string[];
  howToUse?: string[];
  size?: string;
  weight?: string;
  images: string[];
}

async function main() {
  console.log('🌱 Starting enhanced database seeding with frontend mock data...');

  // Create comprehensive categories
  const skincare = await prisma.category.upsert({
    where: { slug: 'skincare' },
    update: {},
    create: {
      name: 'مراقبت از پوست',
      nameEn: 'Skincare',
      slug: 'skincare',
      description: 'محصولات مراقبت از پوست شامل سرم‌ها، کرم‌ها، پاک‌کننده‌ها و ماسک‌ها',
      isActive: true,
      sortOrder: 1,
    },
  });

  const serums = await prisma.category.upsert({
    where: { slug: 'serums' },
    update: {},
    create: {
      name: 'سرم‌ها',
      nameEn: 'Serums',
      slug: 'serums',
      description: 'سرم‌های مراقبت از پوست برای آبرسانی، روشن‌سازی و ضد پیری',
      parentId: skincare.id,
      isActive: true,
      sortOrder: 1,
    },
  });

  const creams = await prisma.category.upsert({
    where: { slug: 'creams' },
    update: {},
    create: {
      name: 'کرم‌ها',
      nameEn: 'Creams',
      slug: 'creams',
      description: 'کرم‌های مرطوب‌کننده، ضد آفتاب و مراقبت از پوست',
      parentId: skincare.id,
      isActive: true,
      sortOrder: 2,
    },
  });

  const cleansers = await prisma.category.upsert({
    where: { slug: 'cleansers' },
    update: {},
    create: {
      name: 'پاک‌کننده‌ها',
      nameEn: 'Cleansers',
      slug: 'cleansers',
      description: 'پاک‌کننده‌های صورت، ژل شستشو و روغن پاک‌کننده',
      parentId: skincare.id,
      isActive: true,
      sortOrder: 3,
    },
  });

  const masks = await prisma.category.upsert({
    where: { slug: 'masks' },
    update: {},
    create: {
      name: 'ماسک‌ها',
      nameEn: 'Masks',
      slug: 'masks',
      description: 'ماسک‌های شب، احیاکننده و تغذیه‌کننده پوست',
      parentId: skincare.id,
      isActive: true,
      sortOrder: 4,
    },
  });

  const toners = await prisma.category.upsert({
    where: { slug: 'toners' },
    update: {},
    create: {
      name: 'تونرها',
      nameEn: 'Toners',
      slug: 'toners',
      description: 'تونرهای آبرسان و تنظیم‌کننده pH پوست',
      parentId: skincare.id,
      isActive: true,
      sortOrder: 5,
    },
  });

  // Create comprehensive brands with logos
  const glowroya = await prisma.brand.upsert({
    where: { slug: 'glowroya' },
    update: {},
    create: {
      name: 'گلو رویا',
      nameEn: 'GlowRoya',
      slug: 'glowroya',
      description: 'برند ایرانی مراقبت از پوست با فرمولاسیون پیشرفته',
      logo: '/uploads/assets/app-logo.png',
      isActive: true,
    },
  });

  const cerave = await prisma.brand.upsert({
    where: { slug: 'cerave' },
    update: {},
    create: {
      name: 'سراوی',
      nameEn: 'CeraVe',
      slug: 'cerave',
      description: 'برند معروف آمریکایی مراقبت از پوست با سرامید',
      logo: '/uploads/brands/cerave-logo.png',
      isActive: true,
    },
  });

  const loreal = await prisma.brand.upsert({
    where: { slug: 'loreal' },
    update: {},
    create: {
      name: 'لورآل',
      nameEn: 'L\'Oreal',
      slug: 'loreal',
      description: 'برند معروف فرانسوی آرایشی و بهداشتی',
      logo: '/uploads/brands/loreal-logo.jpg',
      isActive: true,
    },
  });

  const nivea = await prisma.brand.upsert({
    where: { slug: 'nivea' },
    update: {},
    create: {
      name: 'نیویا',
      nameEn: 'Nivea',
      slug: 'nivea',
      description: 'برند معروف آلمانی مراقبت از پوست',
      logo: '/uploads/brands/nivea-logo.png',
      isActive: true,
    },
  });

  const garnier = await prisma.brand.upsert({
    where: { slug: 'garnier' },
    update: {},
    create: {
      name: 'گارنیه',
      nameEn: 'Garnier',
      slug: 'garnier',
      description: 'برند فرانسوی محصولات طبیعی مراقبت از پوست',
      logo: '/uploads/brands/garnier-logo.png',
      isActive: true,
    },
  });

  const olay = await prisma.brand.upsert({
    where: { slug: 'olay' },
    update: {},
    create: {
      name: 'اولی',
      nameEn: 'Olay',
      slug: 'olay',
      description: 'برند آمریکایی مراقبت از پوست و ضد پیری',
      isActive: true,
    },
  });

  const cinere = await prisma.brand.upsert({
    where: { slug: 'cinere' },
    update: {},
    create: {
      name: 'سینره',
      nameEn: 'Cinere',
      slug: 'cinere',
      description: 'برند مراقبت از پوست با فرمول‌های طبیعی',
      isActive: true,
    },
  });

  // Create sample admin user
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uV8O', // password123
      firstName: 'مدیر',
      lastName: 'سیستم',
      role: 'ADMIN',
      status: 'ACTIVE',
      isEmailVerified: true,
    },
  });

  // Create sample customer user
  const customerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uV8O', // password123
      firstName: 'مشتری',
      lastName: 'نمونه',
      role: 'CUSTOMER',
      status: 'ACTIVE',
      isEmailVerified: true,
    },
  });

  // Enhanced product data from frontend mock data
  const productSeedData: ProductSeedData[] = [
    {
      name: 'سرم هیالورونیک اسید',
      nameEn: 'Hyaluronic Acid Serum',
      slug: 'hyaluronic-acid-serum',
      description: 'سرم هیالورونیک اسید با فرمولاسیون پیشرفته برای آبرسانی عمیق و مرطوب کنندگی طولانی مدت پوست. این سرم به کاهش چین و چروک کمک می‌کند و پوست را شاداب و جوان نگه می‌دارد.',
      shortDescription: 'سرم آبرسان با هیالورونیک اسید',
      sku: 'GR-HAS-30',
      price: 320000,
      comparePrice: 280000,
      brandSlug: 'glowroya',
      categorySlug: 'serums',
      tags: ['سرم', 'آبرسان', 'هیالورونیک اسید', 'ضد چروک'],
      isActive: true,
      isFeatured: true,
      trackQuantity: true,
      stock: 43,
      lowStockThreshold: 10,
      benefits: [
        'آبرسانی عمیق پوست',
        'کاهش چین و چروک‌های ظریف',
        'افزایش الاستیسیته پوست',
        'مناسب برای انواع پوست'
      ],
      ingredients: [
        'هیالورونیک اسید با وزن مولکولی بالا و پایین',
        'ویتامین B5',
        'عصاره آلوئه ورا',
        'آب کوکومیس ساتیوس (خیار)'
      ],
      howToUse: [
        'صبح و شب پس از شستشوی صورت استفاده کنید',
        'چند قطره روی پوست تمیز بزنید',
        'به آرامی با نوک انگشتان ماساژ دهید',
        'قبل از کرم مرطوب کننده استفاده شود'
      ],
      size: '30 میلی‌لیتر',
      weight: '50 گرم',
      images: ['/uploads/products/serums/hyaluronic-acid-serum-1.jpg']
    },
    {
      name: 'کرم BB با SPF 30',
      nameEn: 'BB Cream with SPF 30',
      slug: 'bb-cream-spf-30',
      description: 'کرم BB چندکاره با SPF 30 برای محافظت، مرطوب کنندگی و پوشش طبیعی پوست. این کرم در رنگ‌های مختلف برای انواع پوست ارائه می‌شود و علاوه بر محافظت، پوست را یکنواخت و درخشان می‌کند.',
      shortDescription: 'کرم BB چندکاره با ضد آفتاب',
      sku: 'LOR-BB-50',
      price: 245000,
      brandSlug: 'loreal',
      categorySlug: 'creams',
      tags: ['کرم BB', 'ضد آفتاب', 'SPF 30', 'پوشش طبیعی'],
      isActive: true,
      isFeatured: true,
      trackQuantity: true,
      stock: 78,
      lowStockThreshold: 15,
      benefits: [
        'محافظت در برابر اشعه UVA و UVB',
        'پوشش طبیعی و یکنواخت کننده',
        'مرطوب کنندگی طولانی مدت',
        'فرمول سبک و غیرچرب'
      ],
      ingredients: [
        'فیلترهای ضد آفتاب فیزیکی و شیمیایی',
        'هیالورونیک اسید',
        'ویتامین E',
        'رنگدانه‌های طبیعی'
      ],
      howToUse: [
        'هر روز صبح پس از روتین مراقبت از پوست استفاده کنید',
        'مقدار کافی روی صورت و گردن بزنید',
        'به آرامی ماساژ دهید تا جذب شود',
        'هر 2 ساعت مجدداً استفاده کنید در صورت قرار گرفتن در معرض نور خورشید'
      ],
      images: ['/uploads/products/creams/bb-cream-spf-30-1.jpg']
    },
    {
      name: 'ماسک شب احیاکننده پوست',
      nameEn: 'Night Revitalizing Mask',
      slug: 'night-revitalizing-mask',
      description: 'ماسک شب احیاکننده با فرمول غنی برای تغذیه و ترمیم پوست در طول شب. این ماسک حاوی ترکیبات فعال است که به بازسازی پوست کمک می‌کنند و آثار خستگی را از بین می‌برند.',
      shortDescription: 'ماسک شب احیاکننده و ترمیم کننده',
      sku: 'NIV-NRM-75',
      price: 195000,
      comparePrice: 175000,
      brandSlug: 'nivea',
      categorySlug: 'masks',
      tags: ['ماسک شب', 'احیاکننده', 'ترمیم کننده', 'ضد پیری'],
      isActive: true,
      isFeatured: false,
      trackQuantity: true,
      stock: 31,
      lowStockThreshold: 10,
      benefits: [
        'ترمیم و احیای پوست در طول شب',
        'کاهش علائم خستگی و استرس پوست',
        'بهبود بافت و یکنواختی رنگ پوست',
        'تغذیه عمیق پوست'
      ],
      ingredients: [
        'ریتینول (ویتامین A)',
        'پپتیدها',
        'اسیدهای آمینه',
        'روغن آرگان'
      ],
      howToUse: [
        'شب‌ها پس از تمیز کردن پوست استفاده کنید',
        'لایه نازکی روی پوست بزنید',
        'اجازه دهید در طول شب عمل کند',
        'صبح صورت را با آب ولرم بشویید'
      ],
      images: ['/uploads/products/masks/night-revitalizing-mask-1.jpg']
    },
    {
      name: 'پاک کننده ژل شستشو',
      nameEn: 'Gentle Cleansing Gel',
      slug: 'gentle-cleansing-gel',
      description: 'ژل شستشوی ملایم و مؤثر برای پاکسازی کامل پوست. این پاک کننده آرایش، آلودگی‌ها و چربی اضافی را بدون خشک کردن پوست از بین می‌برد و پوست را تازه و شاداب می‌کند.',
      shortDescription: 'ژل شستشوی ملایم و مؤثر',
      sku: 'GR-GCG-200',
      price: 180000,
      brandSlug: 'glowroya',
      categorySlug: 'cleansers',
      tags: ['پاک کننده', 'ژل شستشو', 'ملایم', 'روزانه'],
      isActive: true,
      isFeatured: true,
      trackQuantity: true,
      stock: 96,
      lowStockThreshold: 20,
      benefits: [
        'پاکسازی عمیق منافذ',
        'حفظ رطوبت طبیعی پوست',
        'کاهش التهاب و قرمزی',
        'مناسب برای استفاده روزانه'
      ],
      ingredients: [
        'عصاره آلوئه ورا',
        'گلیسیرین گیاهی',
        'پانتنول',
        'روغن درخت چای'
      ],
      howToUse: [
        'صورت را مرطوب کنید',
        'مقدار کمی ژل را با آب مخلوط کنید تا کف ایجاد شود',
        'به آرامی روی صورت ماساژ دهید',
        'با آب ولرم بشویید'
      ],
      images: ['/uploads/products/cleansers/gentle-cleansing-gel-1.jpg']
    },
    {
      name: 'سرم ویتامین C',
      nameEn: 'Vitamin C Serum',
      slug: 'vitamin-c-serum',
      description: 'سرم ویتامین C با غلظت بالا برای روشن کردن پوست و کاهش لک‌های تیره. این سرم با خاصیت آنتی‌اکسیدانی قوی به محافظت از پوست در برابر رادیکال‌های آزاد کمک می‌کند.',
      shortDescription: 'سرم ویتامین C روشن کننده',
      sku: 'GR-VCS-30',
      price: 295000,
      brandSlug: 'glowroya',
      categorySlug: 'serums',
      tags: ['سرم', 'ویتامین C', 'روشن کننده', 'آنتی اکسیدان'],
      isActive: true,
      isFeatured: true,
      trackQuantity: true,
      stock: 52,
      lowStockThreshold: 10,
      benefits: [
        'روشن کننده قوی پوست',
        'کاهش لک‌های تیره و هایپرپیگمنتیشن',
        'افزایش تولید کلاژن',
        'محافظت در برابر آسیب‌های محیطی'
      ],
      ingredients: [
        'ویتامین C خالص (L-اسکوربیک اسید 20٪)',
        'ویتامین E',
        'فرولیک اسید',
        'اسید هیالورونیک'
      ],
      howToUse: [
        'صبح‌ها پس از شستشوی صورت استفاده کنید',
        '3-4 قطره روی پوست خشک بزنید',
        'به آرامی ماساژ دهید تا جذب شود',
        'همیشه بعد از استفاده از ضد آفتاب استفاده کنید'
      ],
      images: ['/uploads/products/serums/vitamin-c-serum-1.jpg']
    }
  ];

  // Create products from seed data
  console.log('📦 Creating products from enhanced seed data...');

  const createdProducts: any[] = [];

  for (const productData of productSeedData) {
    try {
      // Get brand and category
      const brand = await prisma.brand.findUnique({ where: { slug: productData.brandSlug } });
      const category = await prisma.category.findUnique({ where: { slug: productData.categorySlug } });

      if (!brand || !category) {
        console.error(`❌ Brand or category not found for product: ${productData.name}`);
        continue;
      }

      // Create product
      const product = await prisma.product.upsert({
        where: { slug: productData.slug },
        update: {},
        create: {
          name: productData.name,
          nameEn: productData.nameEn,
          slug: productData.slug,
          description: productData.description,
          shortDescription: productData.shortDescription,
          sku: productData.sku,
          brandId: brand.id,
          price: productData.price,
          comparePrice: productData.comparePrice,
          isActive: productData.isActive,
          isFeatured: productData.isFeatured,
          trackQuantity: productData.trackQuantity,
          tags: productData.tags,
        },
      });

      // Add product to category
      await prisma.productCategory.upsert({
        where: {
          productId_categoryId: {
            productId: product.id,
            categoryId: category.id,
          },
        },
        update: {},
        create: {
          productId: product.id,
          categoryId: category.id,
        },
      });

      // Create product inventory
      await prisma.productInventory.upsert({
        where: { productId: product.id },
        update: {},
        create: {
          productId: product.id,
          quantity: productData.stock,
          lowStockThreshold: productData.lowStockThreshold,
        },
      });

      // Create product images
      for (let i = 0; i < productData.images.length; i++) {
        await prisma.productImage.create({
          data: {
            productId: product.id,
            url: productData.images[i],
            alt: `${productData.name} - تصویر ${i + 1}`,
            sortOrder: i,
            isPrimary: i === 0,
          },
        });
      }

      createdProducts.push(product);
      console.log(`✅ Created product: ${product.name}`);

    } catch (error) {
      console.error(`❌ Failed to create product ${productData.name}:`, error);
    }
  }

  // Create sample reviews for products
  console.log('⭐ Creating sample reviews...');

  for (const product of createdProducts.slice(0, 3)) {
    // Create a few sample reviews for each product
    await prisma.review.create({
      data: {
        productId: product.id,
        userId: customerUser.id,
        rating: 5,
        title: 'محصول عالی',
        content: 'واقعاً راضی هستم از این محصول. کیفیت بسیار خوبی داره و نتیجه‌اش فوق‌العاده است.',
        isVerified: true,
        isApproved: true,
      },
    });

    await prisma.review.create({
      data: {
        productId: product.id,
        userId: adminUser.id,
        rating: 4,
        title: 'توصیه می‌کنم',
        content: 'محصول خوبی است و برای پوست من مناسب بود. قیمت هم منطقی است.',
        isVerified: true,
        isApproved: true,
      },
    });
  }

  console.log('✅ Enhanced database seeding completed successfully!');
  console.log('📊 Migration Summary:');
  console.log(`   - Categories: ${serums.name}, ${creams.name}, ${cleansers.name}, ${masks.name}, ${toners.name}`);
  console.log(`   - Brands: ${glowroya.name}, ${cerave.name}, ${loreal.name}, ${nivea.name}, ${garnier.name}, ${olay.name}, ${cinere.name}`);
  console.log(`   - Users: ${adminUser.email}, ${customerUser.email}`);
  console.log(`   - Products: ${createdProducts.length} products with full Persian descriptions`);
  console.log(`   - Reviews: Sample reviews created for products`);
  console.log(`   - Images: Product images configured with local backend URLs`);
  console.log('\n🎯 Next Steps:');
  console.log('   1. Run static files migration: npm run migrate:files');
  console.log('   2. Start backend server: npm run dev');
  console.log('   3. Update frontend to use backend APIs');
  console.log('   4. Upload actual product images through admin panel');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
