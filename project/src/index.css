@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Vazirmatn', sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-text-primary;
    direction: rtl;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold leading-tight;
  }

  p {
    @apply leading-relaxed;
  }

  ::selection {
    @apply bg-primary-200 text-primary-800;
  }
}

@layer components {
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center gap-2;
  }
  
  .btn-secondary {
    @apply bg-white border border-primary-200 text-text-primary hover:bg-primary-50 font-medium py-3 px-6 rounded-full transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center gap-2;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-center gap-2;
  }
  
  .card {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300 overflow-hidden;
  }
  
  .card-hover {
    @apply hover:-translate-y-1 transition-transform duration-300;
  }

  .glass {
    @apply bg-white/70 backdrop-blur-md border border-white/20 shadow-soft;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold mb-2 md:mb-4 relative inline-block;
  }
  
  .section-title::after {
    content: '';
    @apply absolute w-1/3 h-1 bg-primary-400 bottom-0 left-0 -mb-2 rounded-full;
  }

  .custom-scroll::-webkit-scrollbar {
    @apply w-1.5;
  }
  
  .custom-scroll::-webkit-scrollbar-track {
    @apply bg-primary-50 rounded-full;
  }
  
  .custom-scroll::-webkit-scrollbar-thumb {
    @apply bg-primary-200 rounded-full hover:bg-primary-300;
  }

  /* Hide scrollbar for tablet navigation */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .nav-link {
    @apply relative py-2 text-text-secondary hover:text-primary-500 transition-colors duration-300 text-base font-medium after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary-400 after:transition-all after:duration-300 hover:after:w-full;
  }

  .nav-link.active {
    @apply text-primary-500 after:w-full;
  }

  /* Mega Menu RTL Styles */
  .mega-menu {
    @apply absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-100 z-40;
  }

  .mega-menu-category {
    @apply relative group;
  }

  .mega-menu-category:hover .mega-menu-dropdown {
    @apply opacity-100 visible translate-y-0;
  }

  .mega-menu-dropdown {
    @apply absolute top-full right-0 bg-white shadow-lg rounded-lg border border-gray-100 mt-1 z-50 opacity-0 invisible translate-y-2 transition-all duration-200 ease-out min-w-64;
  }

  .mega-menu-subcategory {
    @apply block py-2 px-3 text-sm text-text-secondary hover:text-primary-600 hover:bg-primary-50 rounded-md transition-all duration-200 group;
  }

  .mega-menu-subcategory:hover {
    @apply translate-x-1;
  }

  .mega-menu-featured {
    @apply bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl p-6;
  }

  /* RTL-specific adjustments */
  [dir="rtl"] .mega-menu-dropdown {
    @apply left-0 right-auto;
  }

  [dir="rtl"] .mega-menu-subcategory:hover {
    @apply translate-x-1;
  }

  /* Mobile mega menu styles */
  @media (max-width: 768px) {
    .mega-menu {
      @apply relative shadow-none border-t-0;
    }

    .mega-menu-dropdown {
      @apply static opacity-100 visible translate-y-0 shadow-none border-0 rounded-none;
    }
  }

  /* Mobile-specific styles */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-bottom-nav {
    @apply fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-grid-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .mobile-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .mobile-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .mobile-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  /* Touch-friendly interactions */
  .touch-action-manipulation {
    touch-action: manipulation;
  }

  .touch-callout-none {
    -webkit-touch-callout: none;
  }

  .user-select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Mobile viewport optimizations */
  @media (max-width: 640px) {
    .container-custom {
      @apply px-4;
    }

    .btn-primary,
    .btn-secondary,
    .btn-accent {
      @apply py-4 px-8 text-base;
      min-height: 44px;
    }

    .card {
      @apply rounded-xl;
    }

    .section-title {
      @apply text-2xl md:text-3xl;
    }

    /* Reduce motion for mobile users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      .card-hover {
        @apply hover:translate-y-0;
      }

      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  }

  /* Tablet-specific styles */
  @media (min-width: 641px) and (max-width: 1024px) {
    .container-custom {
      @apply px-6;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
      @apply shadow-sm;
    }
  }

  /* Dark mode support for mobile */
  @media (prefers-color-scheme: dark) {
    .mobile-bottom-nav {
      @apply bg-gray-900 border-gray-700;
    }
  }

  /* Landscape orientation adjustments */
  @media (orientation: landscape) and (max-height: 500px) {
    .mobile-bottom-nav {
      @apply py-1;
    }
  }

  /* iOS Safari specific fixes */
  @supports (-webkit-touch-callout: none) {
    .mobile-safe-area {
      padding-bottom: max(env(safe-area-inset-bottom), 20px);
    }
  }

  /* Swiper equal height fixes */
  .swiper-wrapper {
    @apply items-stretch;
  }

  .swiper-slide {
    @apply h-auto;
  }

  .swiper-slide > div {
    @apply h-full;
  }
}