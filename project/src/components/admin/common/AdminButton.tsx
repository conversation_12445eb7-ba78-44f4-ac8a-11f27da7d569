import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, Loader2 } from 'lucide-react';

interface AdminButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'size'> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
  fullWidth?: boolean;
  rounded?: boolean;
}

const AdminButton: React.FC<AdminButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  loading = false,
  fullWidth = false,
  rounded = false,
  disabled,
  className = '',
  ...props
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-medium transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
    ${rounded ? 'rounded-full' : 'rounded-lg'}
  `.trim();

  const variantClasses = {
    primary: `
      bg-admin-600 text-white hover:bg-admin-700 focus:ring-admin-500
      shadow-sm hover:shadow-md
    `,
    secondary: `
      bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500
      shadow-sm hover:shadow-md
    `,
    outline: `
      border border-admin-300 text-admin-700 hover:bg-admin-50 focus:ring-admin-500
      bg-white hover:border-admin-400
    `,
    ghost: `
      text-admin-700 hover:bg-admin-100 focus:ring-admin-500
      hover:text-admin-800
    `,
    danger: `
      bg-red-600 text-white hover:bg-red-700 focus:ring-red-500
      shadow-sm hover:shadow-md
    `,
    success: `
      bg-green-600 text-white hover:bg-green-700 focus:ring-green-500
      shadow-sm hover:shadow-md
    `,
    warning: `
      bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500
      shadow-sm hover:shadow-md
    `
  };

  const sizeClasses = {
    xs: 'px-2 py-1 text-xs gap-1',
    sm: 'px-3 py-1.5 text-sm gap-1.5',
    md: 'px-4 py-2 text-sm gap-2',
    lg: 'px-6 py-3 text-base gap-2',
    xl: 'px-8 py-4 text-lg gap-3'
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  };

  const isDisabled = disabled || loading;

  return (
    <motion.button
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `.trim()}
      disabled={isDisabled}
      whileTap={!isDisabled ? { scale: 0.98 } : undefined}
      whileHover={!isDisabled ? { y: -1 } : undefined}
      {...props}
    >
      {loading && (
        <Loader2 className={`${iconSizes[size]} animate-spin`} />
      )}
      
      {!loading && Icon && iconPosition === 'left' && (
        <Icon className={iconSizes[size]} />
      )}
      
      <span>{children}</span>
      
      {!loading && Icon && iconPosition === 'right' && (
        <Icon className={iconSizes[size]} />
      )}
    </motion.button>
  );
};

// Specialized button variants
export const AdminIconButton: React.FC<{
  icon: LucideIcon;
  variant?: AdminButtonProps['variant'];
  size?: AdminButtonProps['size'];
  loading?: boolean;
  tooltip?: string;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}> = ({
  icon: Icon,
  variant = 'ghost',
  size = 'md',
  loading = false,
  tooltip,
  onClick,
  disabled,
  className = ''
}) => {
  const sizeClasses = {
    xs: 'p-1',
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
    xl: 'p-4'
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  };

  return (
    <AdminButton
      variant={variant}
      onClick={onClick}
      disabled={disabled}
      loading={loading}
      rounded
      title={tooltip}
      className={`${sizeClasses[size]} ${className}`}
    >
      {!loading && <Icon className={iconSizes[size]} />}
    </AdminButton>
  );
};

export const AdminButtonGroup: React.FC<{
  children: ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={`flex gap-2 ${className}`}>
      {children}
    </div>
  );
};

export const AdminSplitButton: React.FC<{
  children: ReactNode;
  variant?: AdminButtonProps['variant'];
  size?: AdminButtonProps['size'];
  onClick?: () => void;
  onDropdownClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}> = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  onDropdownClick,
  disabled,
  loading,
  className = ''
}) => {
  return (
    <div className={`flex ${className}`}>
      <AdminButton
        variant={variant}
        size={size}
        onClick={onClick}
        disabled={disabled}
        loading={loading}
        className="rounded-l-lg rounded-r-none border-l border-t border-b"
      >
        {children}
      </AdminButton>
      <AdminButton
        variant={variant}
        size={size}
        onClick={onDropdownClick}
        disabled={disabled}
        className="rounded-r-lg rounded-l-none border-r border-t border-b border-l-0 px-2"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </AdminButton>
    </div>
  );
};

export default AdminButton;
