import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Gift, 
  Star, 
  Truck, 
  Tag, 
  Clock,
  Check,
  AlertCircle,
  Info,
  Sparkles
} from 'lucide-react';
import { LoyaltyReward, LoyaltyMember } from '../../types/loyalty';
import toast from 'react-hot-toast';

interface RewardsShopProps {
  rewards: LoyaltyReward[];
  member: LoyaltyMember;
  onRedeemReward: (rewardId: string) => Promise<void>;
  isLoading?: boolean;
}

const RewardsShop: React.FC<RewardsShopProps> = ({
  rewards,
  member,
  onRedeemReward,
  isLoading = false
}) => {
  const [selectedReward, setSelectedReward] = useState<LoyaltyReward | null>(null);
  const [redeeming, setRedeeming] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'affordable' | 'discount' | 'product'>('all');

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'discount':
        return <Tag className="w-6 h-6 text-green-600" />;
      case 'shipping':
        return <Truck className="w-6 h-6 text-blue-600" />;
      case 'product':
        return <Gift className="w-6 h-6 text-purple-600" />;
      case 'experience':
        return <Sparkles className="w-6 h-6 text-yellow-600" />;
      default:
        return <Star className="w-6 h-6 text-gray-600" />;
    }
  };

  const getRewardTypeText = (type: string) => {
    switch (type) {
      case 'discount':
        return 'تخفیف';
      case 'shipping':
        return 'ارسال';
      case 'product':
        return 'محصول';
      case 'experience':
        return 'تجربه';
      default:
        return 'جایزه';
    }
  };

  const canAfford = (reward: LoyaltyReward) => {
    return member.points >= reward.pointsCost;
  };

  const filteredRewards = rewards.filter(reward => {
    if (!reward.isAvailable) return false;
    
    switch (filter) {
      case 'affordable':
        return canAfford(reward);
      case 'discount':
        return reward.type === 'discount';
      case 'product':
        return reward.type === 'product';
      default:
        return true;
    }
  });

  const handleRedeemClick = (reward: LoyaltyReward) => {
    if (!canAfford(reward)) {
      toast.error('امتیاز کافی برای دریافت این جایزه ندارید');
      return;
    }
    
    if (reward.stock !== undefined && reward.stock <= 0) {
      toast.error('این جایزه در حال حاضر موجود نیست');
      return;
    }
    
    setSelectedReward(reward);
  };

  const confirmRedeem = async () => {
    if (!selectedReward) return;
    
    setRedeeming(selectedReward.id);
    
    try {
      await onRedeemReward(selectedReward.id);
      setSelectedReward(null);
      toast.success('جایزه با موفقیت دریافت شد!');
    } catch (error) {
      toast.error('خطا در دریافت جایزه');
    } finally {
      setRedeeming(null);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Gift className="w-5 h-5 text-primary-600" />
          فروشگاه جوایز
        </h3>
        
        <div className="flex items-center gap-2 text-sm">
          <Star className="w-4 h-4 text-yellow-500" />
          <span className="font-medium">{member.points.toLocaleString('fa-IR')} امتیاز موجود</span>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={() => setFilter('all')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === 'all'
              ? 'bg-primary-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          همه جوایز
        </button>
        <button
          onClick={() => setFilter('affordable')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === 'affordable'
              ? 'bg-green-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          قابل دریافت
        </button>
        <button
          onClick={() => setFilter('discount')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === 'discount'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          تخفیف‌ها
        </button>
        <button
          onClick={() => setFilter('product')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filter === 'product'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          محصولات
        </button>
      </div>

      {/* Rewards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRewards.map((reward) => {
          const affordable = canAfford(reward);
          const outOfStock = reward.stock !== undefined && reward.stock <= 0;
          
          return (
            <motion.div
              key={reward.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`
                border rounded-lg p-4 transition-all duration-200 hover:shadow-md
                ${affordable && !outOfStock 
                  ? 'border-primary-200 hover:border-primary-300' 
                  : 'border-gray-200 opacity-75'
                }
              `}
            >
              {/* Reward Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {getRewardIcon(reward.type)}
                  <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                    {getRewardTypeText(reward.type)}
                  </span>
                </div>
                
                {reward.stock !== undefined && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    reward.stock > 10 
                      ? 'bg-green-100 text-green-700'
                      : reward.stock > 0
                      ? 'bg-yellow-100 text-yellow-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {reward.stock > 0 ? `${reward.stock} عدد` : 'ناموجود'}
                  </span>
                )}
              </div>

              {/* Reward Image */}
              {reward.image && (
                <img
                  src={reward.image}
                  alt={reward.title}
                  className="w-full h-32 object-cover rounded-lg mb-3"
                />
              )}

              {/* Reward Info */}
              <h4 className="font-semibold text-gray-900 mb-2">{reward.title}</h4>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{reward.description}</p>

              {/* Value */}
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-gray-500">ارزش:</span>
                <span className="font-medium text-gray-900">
                  {reward.value.toLocaleString('fa-IR')} تومان
                </span>
              </div>

              {/* Points Cost */}
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-500">هزینه:</span>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="font-bold text-primary-600">
                    {reward.pointsCost.toLocaleString('fa-IR')}
                  </span>
                </div>
              </div>

              {/* Expiry */}
              {reward.validUntil && (
                <div className="flex items-center gap-1 text-xs text-orange-600 mb-3">
                  <Clock className="w-3 h-3" />
                  <span>اعتبار تا {new Date(reward.validUntil).toLocaleDateString('fa-IR')}</span>
                </div>
              )}

              {/* Action Button */}
              <button
                onClick={() => handleRedeemClick(reward)}
                disabled={!affordable || outOfStock || isLoading}
                className={`
                  w-full py-2 px-4 rounded-lg font-medium transition-colors
                  ${affordable && !outOfStock
                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }
                `}
              >
                {outOfStock 
                  ? 'ناموجود'
                  : !affordable 
                  ? `نیاز به ${(reward.pointsCost - member.points).toLocaleString('fa-IR')} امتیاز بیشتر`
                  : 'دریافت جایزه'
                }
              </button>
            </motion.div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredRewards.length === 0 && (
        <div className="text-center py-8">
          <Gift className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            جایزه‌ای یافت نشد
          </h3>
          <p className="text-gray-600">
            {filter === 'affordable' 
              ? 'امتیاز کافی برای دریافت جایزه ندارید'
              : 'در حال حاضر جایزه‌ای در این دسته موجود نیست'
            }
          </p>
        </div>
      )}

      {/* Confirmation Modal */}
      <AnimatePresence>
        {selectedReward && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedReward(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                تأیید دریافت جایزه
              </h3>

              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">{selectedReward.title}</h4>
                <p className="text-sm text-gray-600 mb-3">{selectedReward.description}</p>
                
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <div className="flex justify-between text-sm mb-2">
                    <span>هزینه امتیاز:</span>
                    <span className="font-medium">{selectedReward.pointsCost.toLocaleString('fa-IR')}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>امتیاز باقی‌مانده:</span>
                    <span className="font-medium">
                      {(member.points - selectedReward.pointsCost).toLocaleString('fa-IR')}
                    </span>
                  </div>
                </div>

                {/* Terms */}
                {selectedReward.terms.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">شرایط استفاده:</h5>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {selectedReward.terms.map((term, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-primary-500 mt-1">•</span>
                          <span>{term}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setSelectedReward(null)}
                  disabled={redeeming === selectedReward.id}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  انصراف
                </button>
                <button
                  onClick={confirmRedeem}
                  disabled={redeeming === selectedReward.id}
                  className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {redeeming === selectedReward.id ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      در حال دریافت...
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4" />
                      تأیید دریافت
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default RewardsShop;
