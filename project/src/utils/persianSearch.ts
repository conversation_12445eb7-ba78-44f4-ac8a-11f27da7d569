import { Product } from '../types';
import { megaMenuCategories } from '../data/categories';

// Persian character normalization map
const persianNormalizationMap: { [key: string]: string } = {
  'ي': 'ی',
  'ك': 'ک',
  'ة': 'ه',
  'ء': '',
  'أ': 'ا',
  'إ': 'ا',
  'آ': 'ا',
  'ؤ': 'و',
  'ئ': 'ی',
};

// Common Persian search terms and their variations
const persianSynonyms: { [key: string]: string[] } = {
  'کرم': ['کریم', 'کرم'],
  'سرم': ['سروم', 'سرم'],
  'ماسک': ['ماسک', 'مسک'],
  'پاک کننده': ['پاک کننده', 'شوینده', 'کلنزر'],
  'مرطوب کننده': ['مرطوب کننده', 'آبرسان', 'هیدراتینگ'],
  'ضد آفتاب': ['ضد آفتاب', 'سان اسکرین', 'محافظ آفتاب'],
  'ضد پیری': ['ضد پیری', 'آنتی ایجینگ', 'جوان کننده'],
  'روشن کننده': ['روشن کننده', 'برایتنینگ', 'شفاف کننده'],
  'تونر': ['تونر', 'لوسیون'],
  'اسنس': ['اسنس', 'محلول'],
};

/**
 * Normalize Persian text for better search matching
 */
export const normalizePersianText = (text: string): string => {
  let normalized = text.toLowerCase().trim();
  
  // Replace Persian character variations
  Object.entries(persianNormalizationMap).forEach(([from, to]) => {
    normalized = normalized.replace(new RegExp(from, 'g'), to);
  });
  
  // Remove extra spaces
  normalized = normalized.replace(/\s+/g, ' ');
  
  return normalized;
};

/**
 * Get search suggestions based on query
 */
export const getSearchSuggestions = (query: string, limit: number = 8): string[] => {
  const normalizedQuery = normalizePersianText(query);
  const suggestions: Set<string> = new Set();
  
  if (normalizedQuery.length < 2) return [];
  
  // Add synonym matches
  Object.entries(persianSynonyms).forEach(([key, synonyms]) => {
    synonyms.forEach(synonym => {
      if (normalizePersianText(synonym).includes(normalizedQuery)) {
        suggestions.add(key);
        synonyms.forEach(s => suggestions.add(s));
      }
    });
  });
  
  // Add category matches
  megaMenuCategories.forEach(category => {
    if (normalizePersianText(category.name).includes(normalizedQuery)) {
      suggestions.add(category.name);
    }
    category.subcategories.forEach(sub => {
      if (normalizePersianText(sub.name).includes(normalizedQuery)) {
        suggestions.add(sub.name);
      }
    });
  });
  
  return Array.from(suggestions).slice(0, limit);
};

/**
 * Search products with Persian text support
 */
export const searchProducts = (
  products: Product[], 
  query: string,
  options: {
    searchInDescription?: boolean;
    searchInIngredients?: boolean;
    searchInBenefits?: boolean;
    exactMatch?: boolean;
  } = {}
): Product[] => {
  const {
    searchInDescription = true,
    searchInIngredients = true,
    searchInBenefits = true,
    exactMatch = false
  } = options;
  
  if (!query || query.length < 2) return [];
  
  const normalizedQuery = normalizePersianText(query);
  const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0);
  
  return products.filter(product => {
    const searchFields: string[] = [
      product.name,
      product.category,
      product.brand || '',
    ];
    
    if (searchInDescription) {
      searchFields.push(product.description);
    }
    
    if (searchInIngredients) {
      searchFields.push(...product.ingredients);
    }
    
    if (searchInBenefits) {
      searchFields.push(...product.benefits);
    }
    
    const searchText = normalizePersianText(searchFields.join(' '));
    
    if (exactMatch) {
      return searchText.includes(normalizedQuery);
    }
    
    // Check if all query words are found
    return queryWords.every(word => searchText.includes(word));
  });
};

/**
 * Get popular search terms
 */
export const getPopularSearchTerms = (): string[] => {
  return [
    'سرم هیالورونیک',
    'کرم ضد آفتاب',
    'ماسک صورت',
    'پاک کننده ملایم',
    'سرم ویتامین C',
    'کرم مرطوب کننده',
    'تونر آبرسان',
    'کرم دور چشم'
  ];
};

/**
 * Highlight search terms in text
 */
export const highlightSearchTerms = (text: string, query: string): string => {
  if (!query || query.length < 2) return text;
  
  const normalizedQuery = normalizePersianText(query);
  const queryWords = normalizedQuery.split(' ').filter(word => word.length > 0);
  
  let highlightedText = text;
  
  queryWords.forEach(word => {
    const regex = new RegExp(`(${word})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
  });
  
  return highlightedText;
};

/**
 * Get search analytics data
 */
export interface SearchAnalytics {
  query: string;
  timestamp: number;
  resultsCount: number;
  clickedResult?: number;
}

export const trackSearchAnalytics = (analytics: SearchAnalytics): void => {
  // Store search analytics in localStorage for now
  // In production, this would be sent to an analytics service
  const existingData = localStorage.getItem('searchAnalytics');
  const data = existingData ? JSON.parse(existingData) : [];
  
  data.push(analytics);
  
  // Keep only last 100 searches
  if (data.length > 100) {
    data.splice(0, data.length - 100);
  }
  
  localStorage.setItem('searchAnalytics', JSON.stringify(data));
};

/**
 * Get search history for user
 */
export const getSearchHistory = (limit: number = 5): string[] => {
  const data = localStorage.getItem('searchAnalytics');
  if (!data) return [];
  
  const analytics: SearchAnalytics[] = JSON.parse(data);
  const uniqueQueries = [...new Set(analytics.map(item => item.query))];
  
  return uniqueQueries.slice(-limit).reverse();
};
