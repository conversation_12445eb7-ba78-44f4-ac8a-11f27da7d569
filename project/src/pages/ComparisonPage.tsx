import React from 'react';
import { motion } from 'framer-motion';
import { Eye, Plus, TrendingUp, BarChart3 } from 'lucide-react';
import Layout from '../components/layout/Layout';
import ComparisonTable from '../components/product/ComparisonTable';
import ProductCard from '../components/common/ProductCard';
import SEOHead from '../components/seo/SEOHead';
import { useProductComparison } from '../hooks/useProductComparison';
import { products } from '../data/products';
import { PERSIAN_COMPARISON_MESSAGES } from '../types/comparison';

const ComparisonPage: React.FC = () => {
  const { items, addItem, canAddMore } = useProductComparison();

  const pageTitle = `مقایسه محصولات (${items.length} محصول) - گلو رویا`;
  const pageDescription = items.length > 0 
    ? `مقایسه جامع ${items.length} محصول مراقبت از پوست. بررسی قیمت، ترکیبات، و ویژگی‌ها برای انتخاب بهترین محصول.`
    : 'مقایسه محصولات مراقبت از پوست گلو رویا. انتخاب بهترین محصول با مقایسه قیمت، ترکیبات و ویژگی‌ها.';

  // Get suggested products for comparison (popular products not in current comparison)
  const suggestedProducts = products
    .filter(product => !items.some(item => item.product.id === product.id))
    .filter(product => product.isBestSeller || product.rating >= 4.5)
    .slice(0, 8);

  return (
    <Layout>
      <SEOHead
        title={pageTitle}
        description={pageDescription}
        keywords={[
          'مقایسه محصولات',
          'مراقبت از پوست',
          'گلو رویا',
          'product comparison',
          'skincare comparison',
          'بررسی محصولات'
        ]}
        ogType="website"
        structuredData={{
          '@context': 'https://schema.org',
          '@type': 'WebPage',
          name: pageTitle,
          description: pageDescription,
          url: `${window.location.origin}/comparison`,
          mainEntity: {
            '@type': 'ItemList',
            numberOfItems: items.length,
            itemListElement: items.map((item, index) => ({
              '@type': 'Product',
              position: index + 1,
              name: item.product.name,
              brand: item.product.brand,
              offers: {
                '@type': 'Offer',
                price: item.product.discountedPrice || item.product.price,
                priceCurrency: 'IRR'
              }
            }))
          }
        }}
      />

      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl font-bold text-text-primary mb-4">
              {PERSIAN_COMPARISON_MESSAGES.title}
            </h1>
            <p className="text-text-secondary max-w-2xl mx-auto">
              محصولات مراقبت از پوست را با یکدیگر مقایسه کنید و بهترین انتخاب را برای نیازهای خود پیدا کنید
            </p>
          </motion.div>

          {/* Stats Cards */}
          {items.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Eye className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {items.length}
                </div>
                <div className="text-sm text-text-secondary">
                  محصول در مقایسه
                </div>
              </div>

              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <BarChart3 className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {items.reduce((sum, item) => {
                    const criteria = ['price', 'rating', 'reviewCount', 'stock'];
                    return sum + criteria.length;
                  }, 0)}
                </div>
                <div className="text-sm text-text-secondary">
                  معیار مقایسه
                </div>
              </div>

              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {Math.round(items.reduce((sum, item) => sum + (item.product.rating || 0), 0) / items.length * 10) / 10}
                </div>
                <div className="text-sm text-text-secondary">
                  میانگین امتیاز
                </div>
              </div>
            </motion.div>
          )}

          {/* Main Comparison Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <ComparisonTable />
          </motion.div>

          {/* Add More Products Section */}
          {canAddMore && suggestedProducts.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mt-12"
            >
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center gap-3 mb-6">
                  <Plus className="w-6 h-6 text-primary" />
                  <h2 className="text-xl font-semibold text-text-primary">
                    افزودن محصولات بیشتر
                  </h2>
                  <span className="text-sm text-text-secondary">
                    (حداکثر {4 - items.length} محصول دیگر)
                  </span>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {suggestedProducts.slice(0, 4 - items.length).map(product => (
                    <div key={product.id} className="relative">
                      <ProductCard 
                        product={product}
                        showComparisonButton={true}
                      />
                      <button
                        onClick={() => addItem(product)}
                        className="absolute top-2 right-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary-dark transition-colors"
                        title="افزودن به مقایسه"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-6">
                  <button
                    onClick={() => window.location.href = '/products'}
                    className="btn-secondary"
                  >
                    مشاهده همه محصولات
                  </button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Comparison Tips */}
          {items.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mt-8"
            >
              <div className="bg-white rounded-lg border p-8">
                <h3 className="text-lg font-semibold text-text-primary mb-6 text-center">
                  نحوه استفاده از ابزار مقایسه
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-xl font-bold text-blue-600">1</span>
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      انتخاب محصولات
                    </h4>
                    <p className="text-sm text-text-secondary">
                      از صفحه محصولات، دکمه "افزودن به مقایسه" را کلیک کنید
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-xl font-bold text-green-600">2</span>
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      مقایسه جزئیات
                    </h4>
                    <p className="text-sm text-text-secondary">
                      قیمت، ترکیبات، امتیازات و ویژگی‌ها را مقایسه کنید
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-xl font-bold text-purple-600">3</span>
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      انتخاب بهترین
                    </h4>
                    <p className="text-sm text-text-secondary">
                      بر اساس نیازهای خود، بهترین محصول را انتخاب کنید
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Popular Products for Comparison */}
          {items.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-8"
            >
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-semibold text-text-primary mb-6">
                  محصولات پیشنهادی برای مقایسه
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {suggestedProducts.slice(0, 4).map(product => (
                    <div key={product.id} className="relative">
                      <ProductCard 
                        product={product}
                        showComparisonButton={true}
                      />
                      <button
                        onClick={() => addItem(product)}
                        className="absolute top-2 right-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary-dark transition-colors"
                        title="افزودن به مقایسه"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Comparison Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="mt-8"
          >
            <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg border p-8">
              <h3 className="text-lg font-semibold text-text-primary mb-6 text-center">
                مزایای مقایسه محصولات
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                    <TrendingUp className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-medium text-text-primary mb-2">
                    انتخاب آگاهانه
                  </h4>
                  <p className="text-sm text-text-secondary">
                    با مقایسه دقیق، بهترین تصمیم را بگیرید
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                    <BarChart3 className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-medium text-text-primary mb-2">
                    صرفه‌جویی در وقت
                  </h4>
                  <p className="text-sm text-text-secondary">
                    همه اطلاعات در یک نگاه
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                    <Eye className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-medium text-text-primary mb-2">
                    شفافیت کامل
                  </h4>
                  <p className="text-sm text-text-secondary">
                    مقایسه دقیق قیمت و ویژگی‌ها
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
                    <Plus className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-medium text-text-primary mb-2">
                    سهولت استفاده
                  </h4>
                  <p className="text-sm text-text-secondary">
                    رابط کاربری ساده و کاربردی
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default ComparisonPage;
