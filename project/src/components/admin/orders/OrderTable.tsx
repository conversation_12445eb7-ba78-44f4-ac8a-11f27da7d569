import React, { useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';
import { 
  Eye, 
  Edit, 
  Package, 
  Truck, 
  MoreHorizontal,
  AlertCircle,
  Gift,
  Clock,
  CheckCircle2
} from 'lucide-react';
import { AdminOrder } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminTable, { AdminTableColumn } from '../common/AdminTable';
import AdminButton, { AdminIconButton } from '../common/AdminButton';
import { getStatusColor, getPriorityColor, formatCurrency, formatPersianDate } from '../../../utils/orderUtils';

interface OrderTableProps {
  orders: AdminOrder[];
  loading?: boolean;
  onViewOrder: (order: AdminOrder) => void;
  onEditOrder: (order: AdminOrder) => void;
  onUpdateStatus: (orderId: string, status: Order['status']) => void;
  onGenerateLabel: (orderId: string) => void;
  selectedOrders: string[];
  onSelectOrder: (orderId: string) => void;
  onSelectAll: (selected: boolean) => void;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (key: string) => void;
}

const OrderTable: React.FC<OrderTableProps> = ({
  orders,
  loading = false,
  onViewOrder,
  onEditOrder,
  onUpdateStatus,
  onGenerateLabel,
  selectedOrders,
  onSelectOrder,
  onSelectAll,
  sortBy,
  sortOrder,
  onSort
}) => {
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'confirmed':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'processing':
        return <Package className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      case 'delivered':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getPriorityIcon = (priority: Order['priority']) => {
    if (priority === 'urgent') {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    return null;
  };

  const columns: AdminTableColumn<AdminOrder>[] = [
    {
      key: 'select',
      title: '',
      width: '50px',
      render: (order: AdminOrder) => (
        <input
          type="checkbox"
          checked={selectedOrders.includes(order.id)}
          onChange={() => onSelectOrder(order.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      )
    },
    {
      key: 'orderNumber',
      title: 'شماره سفارش',
      sortable: true,
      render: (order: AdminOrder) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">{order.orderNumber}</span>
          <span className="text-xs text-gray-500">
            {formatPersianDate(order.createdAt)}
          </span>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'مشتری',
      sortable: true,
      render: (order: AdminOrder) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">{order.customerInfo.name}</span>
          <span className="text-xs text-gray-500">{order.customerInfo.email}</span>
          {order.customerInfo.isVip && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mt-1 w-fit">
              VIP
            </span>
          )}
        </div>
      )
    },
    {
      key: 'items',
      title: 'محصولات',
      render: (order: AdminOrder) => (
        <div className="flex flex-col">
          <span className="text-sm text-gray-900">
            {order.items.length} محصول
          </span>
          <span className="text-xs text-gray-500">
            {order.items.reduce((sum, item) => sum + item.quantity, 0)} عدد
          </span>
        </div>
      )
    },
    {
      key: 'total',
      title: 'مبلغ',
      sortable: true,
      align: 'left',
      render: (order: AdminOrder) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">
            {formatCurrency(order.total)}
          </span>
          {order.discount > 0 && (
            <span className="text-xs text-green-600">
              تخفیف: {formatCurrency(order.discount)}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (order: AdminOrder) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(order.status)}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
            {order.status === 'pending' && 'در انتظار تأیید'}
            {order.status === 'confirmed' && 'تأیید شده'}
            {order.status === 'processing' && 'در حال پردازش'}
            {order.status === 'shipped' && 'ارسال شده'}
            {order.status === 'delivered' && 'تحویل داده شده'}
            {order.status === 'cancelled' && 'لغو شده'}
          </span>
        </div>
      )
    },
    {
      key: 'priority',
      title: 'اولویت',
      sortable: true,
      render: (order: AdminOrder) => (
        <div className="flex items-center gap-2">
          {getPriorityIcon(order.priority)}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(order.priority || 'normal')}`}>
            {order.priority === 'low' && 'کم'}
            {order.priority === 'normal' && 'عادی'}
            {order.priority === 'high' && 'بالا'}
            {order.priority === 'urgent' && 'فوری'}
            {!order.priority && 'عادی'}
          </span>
        </div>
      )
    },
    {
      key: 'flags',
      title: 'برچسب‌ها',
      render: (order: AdminOrder) => (
        <div className="flex flex-wrap gap-1">
          {order.flags.isRush && (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
              فوری
            </span>
          )}
          {order.flags.isGift && (
            <div className="flex items-center">
              <Gift className="w-3 h-3 text-pink-500" />
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-pink-100 text-pink-800 mr-1">
                هدیه
              </span>
            </div>
          )}
          {order.refundStatus !== 'none' && (
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
              مرجوعی
            </span>
          )}
        </div>
      )
    },
    {
      key: 'tracking',
      title: 'رهگیری',
      render: (order: AdminOrder) => (
        <div className="flex flex-col">
          {order.trackingNumber ? (
            <>
              <span className="text-xs font-mono text-gray-900">
                {order.trackingNumber}
              </span>
              <span className="text-xs text-gray-500">
                {order.fulfillment.shippingCarrier}
              </span>
            </>
          ) : (
            <span className="text-xs text-gray-400">بدون کد رهگیری</span>
          )}
        </div>
      )
    }
  ];

  const handleRowClick = (order: AdminOrder, index: number) => {
    onViewOrder(order);
  };

  const handleMenuToggle = (order: AdminOrder, e: React.MouseEvent) => {
    e.stopPropagation();

    if (actionMenuOpen === order.id) {
      setActionMenuOpen(null);
      return;
    }

    const button = buttonRefs.current[order.id];
    if (button) {
      const rect = button.getBoundingClientRect();
      const menuWidth = 192;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let left = rect.left + window.scrollX - menuWidth + rect.width;
      if (left < 10) {
        left = rect.right + window.scrollX + 8;
      }
      if (left + menuWidth > viewportWidth - 10) {
        left = rect.left + window.scrollX - menuWidth - 8;
      }

      let top = rect.bottom + window.scrollY + 4;
      const menuHeight = 300;
      if (top + menuHeight > viewportHeight + window.scrollY - 10) {
        top = rect.top + window.scrollY - menuHeight - 4;
      }

      setMenuPosition({ top, left });
    }

    setActionMenuOpen(order.id);
  };

  const renderRowActions = (order: AdminOrder, index: number) => (
    <>
      <button
        ref={(el) => { buttonRefs.current[order.id] = el; }}
        onClick={(e) => handleMenuToggle(order, e)}
        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        title="عملیات"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>

      {actionMenuOpen === order.id && createPortal(
        <>
          <div
            className="fixed inset-0 z-[9999]"
            onClick={() => setActionMenuOpen(null)}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed w-48 bg-white rounded-md shadow-xl border border-gray-200 py-1 z-[9999]"
            style={{
              top: `${menuPosition.top}px`,
              left: `${menuPosition.left}px`
            }}
          >
          <button
            onClick={(e) => {
              e.stopPropagation();
              onViewOrder(order);
              setActionMenuOpen(null);
            }}
            className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            <Eye className="w-4 h-4" />
            مشاهده جزئیات
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditOrder(order);
              setActionMenuOpen(null);
            }}
            className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            <Edit className="w-4 h-4" />
            ویرایش سفارش
          </button>

          {order.status === 'confirmed' && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onUpdateStatus(order.id, 'processing');
                setActionMenuOpen(null);
              }}
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <Package className="w-4 h-4" />
              شروع پردازش
            </button>
          )}

          {order.status === 'processing' && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onUpdateStatus(order.id, 'shipped');
                setActionMenuOpen(null);
              }}
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <Truck className="w-4 h-4" />
              ارسال سفارش
            </button>
          )}

          {['confirmed', 'processing'].includes(order.status) && !order.fulfillment.labelPrinted && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onGenerateLabel(order.id);
                setActionMenuOpen(null);
              }}
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <Package className="w-4 h-4" />
              چاپ برچسب
            </button>
          )}
          </motion.div>
        </>,
        document.body
      )}
    </>
  );

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedOrders.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedOrders.length} سفارش انتخاب شده
            </span>
            <div className="flex items-center gap-2">
              <AdminButton
                variant="outline"
                size="sm"
                onClick={() => {
                  // Bulk status update logic
                  console.log('Bulk update status for:', selectedOrders);
                }}
              >
                بروزرسانی وضعیت
              </AdminButton>
              <AdminButton
                variant="outline"
                size="sm"
                onClick={() => {
                  // Bulk export logic
                  console.log('Export orders:', selectedOrders);
                }}
              >
                خروجی
              </AdminButton>
            </div>
          </div>
        </motion.div>
      )}

      {/* Select All Checkbox */}
      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          checked={orders.length > 0 && selectedOrders.length === orders.length}
          onChange={(e) => onSelectAll(e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span className="text-sm text-gray-600">انتخاب همه</span>
      </div>

      {/* Orders Table */}
      <AdminTable
        columns={columns}
        data={orders}
        loading={loading}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={onSort}
        onRowClick={handleRowClick}
        rowActions={renderRowActions}
        emptyMessage="هیچ سفارشی یافت نشد"
        hoverable
        striped
      />
    </div>
  );
};

export default OrderTable;
