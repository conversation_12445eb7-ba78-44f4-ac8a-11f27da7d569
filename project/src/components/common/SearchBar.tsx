import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Search } from 'lucide-react';
import { products } from '../../data/products';

interface SearchBarProps {
  onClose: () => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<typeof products>([]);
  const navigate = useNavigate();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    if (value.length > 1) {
      const filteredResults = products.filter(product => 
        product.name.includes(value) || product.description.includes(value)
      );
      setResults(filteredResults);
    } else {
      setResults([]);
    }
  };

  const handleProductClick = (id: number) => {
    navigate(`/product/${id}`);
    onClose();
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2 bg-gray-50 rounded-lg">
        <Search className="h-5 w-5 text-gray-400 mr-3" />
        <input
          type="text"
          value={query}
          onChange={handleSearch}
          placeholder="جستجوی محصولات..."
          className="flex-grow py-3 px-0 bg-transparent focus:outline-none text-text-primary"
          autoFocus
        />
        <button 
          onClick={onClose}
          className="p-2 ml-2 rounded-full hover:bg-gray-200 transition-colors"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {results.length > 0 && (
        <div className="absolute left-0 right-0 mt-2 bg-white rounded-lg shadow-md max-h-96 overflow-y-auto custom-scroll">
          {results.map(product => (
            <div 
              key={product.id}
              className="p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 border-b border-gray-100 last:border-0"
              onClick={() => handleProductClick(product.id)}
            >
              <img 
                src={product.imageSrc} 
                alt={product.name} 
                className="w-12 h-12 object-cover rounded-md"
              />
              <div>
                <h4 className="font-medium text-text-primary">{product.name}</h4>
                <p className="text-text-secondary text-sm truncate">{product.category}</p>
              </div>
            </div>
          ))}
        </div>
      )}

      {query.length > 1 && results.length === 0 && (
        <div className="p-4 text-center text-text-secondary bg-white rounded-lg shadow-md mt-2">
          نتیجه‌ای یافت نشد!
        </div>
      )}
    </div>
  );
};

export default SearchBar;