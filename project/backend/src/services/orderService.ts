import { Order, OrderItem, Payment, OrderStatus, PaymentStatus, FulfillmentStatus, Prisma } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// Types for order operations
export interface CreateOrderData {
  userId?: string;
  guestEmail?: string;
  items: Array<{
    productId: string;
    variantId?: string;
    quantity: number;
  }>;
  shippingAddressId?: string;
  billingAddressId?: string;
  shippingAddress?: {
    firstName: string;
    lastName: string;
    phone: string;
    province: string;
    city: string;
    district?: string;
    street: string;
    postalCode: string;
  };
  shippingMethod?: string;
  notes?: string;
}

export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  fulfillmentStatus?: FulfillmentStatus;
  shippingMethod?: string;
  trackingNumber?: string;
  notes?: string;
  internalNotes?: string;
  tags?: string[];
}

export interface OrderFilters {
  search?: string;
  userId?: string;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  fulfillmentStatus?: FulfillmentStatus;
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
  tags?: string[];
  hasTrackingNumber?: boolean;
}

export interface OrderQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    user?: boolean;
    items?: boolean;
    payments?: boolean;
    shippingAddress?: boolean;
    notifications?: boolean;
  };
}

// Order with relations type
export type OrderWithRelations = Order & {
  user?: any;
  items?: (OrderItem & {
    product: any;
    variant?: any;
  })[];
  payments?: Payment[];
  shippingAddress?: any;
  notifications?: any[];
  _count?: {
    items: number;
    payments: number;
  };
};

export class OrderService {
  // Generate unique order number
  static generateOrderNumber(): string {
    const prefix = 'GR';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
  }

  // Create a new order
  static async createOrder(data: CreateOrderData): Promise<OrderWithRelations> {
    try {
      // Validate user if provided
      if (data.userId) {
        const user = await prisma.user.findUnique({
          where: { id: data.userId },
        });

        if (!user) {
          throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
        }
      }

      // Validate guest email if no user
      if (!data.userId && !data.guestEmail) {
        throw new AppError('ایمیل مهمان یا شناسه کاربر الزامی است', 400, 'MISSING_USER_INFO');
      }

      // Validate and get products with their current prices
      const productDetails = await Promise.all(
        data.items.map(async (item) => {
          const product = await prisma.product.findUnique({
            where: { id: item.productId },
            include: {
              variants: item.variantId ? {
                where: { id: item.variantId },
              } : false,
              inventory: true,
            },
          });

          if (!product) {
            throw new AppError(`محصول با شناسه ${item.productId} یافت نشد`, 404, 'PRODUCT_NOT_FOUND');
          }

          if (!product.isActive) {
            throw new AppError(`محصول ${product.name} غیرفعال است`, 400, 'PRODUCT_INACTIVE');
          }

          // Check variant if specified
          let variant = null;
          if (item.variantId) {
            variant = product.variants?.[0];
            if (!variant) {
              throw new AppError(`نوع محصول با شناسه ${item.variantId} یافت نشد`, 404, 'VARIANT_NOT_FOUND');
            }
            if (!variant.isActive) {
              throw new AppError(`نوع محصول ${variant.name} غیرفعال است`, 400, 'VARIANT_INACTIVE');
            }
          }

          // Check inventory if tracking is enabled
          if (product.trackQuantity && product.inventory) {
            const availableQuantity = product.inventory.quantity - (product.inventory.reservedQuantity || 0);
            if (availableQuantity < item.quantity) {
              throw new AppError(
                `موجودی کافی برای محصول ${product.name} وجود ندارد. موجودی: ${availableQuantity}`,
                400,
                'INSUFFICIENT_STOCK'
              );
            }
          }

          // Calculate price
          const unitPrice = Number(variant?.price || product.price);
          const totalPrice = unitPrice * item.quantity;

          return {
            ...item,
            product,
            variant,
            unitPrice,
            totalPrice,
          };
        })
      );

      // Calculate order totals
      const subtotal = productDetails.reduce((sum, item) => sum + Number(item.totalPrice), 0);
      const taxAmount = 0; // Tax calculation can be implemented here
      const shippingAmount = 0; // Shipping calculation can be implemented here
      const discountAmount = 0; // Discount calculation can be implemented here
      const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

      // Validate shipping address
      let shippingAddressId = data.shippingAddressId;
      if (data.shippingAddress && !shippingAddressId && data.userId) {
        // Create new address if provided and user is authenticated
        const newAddress = await prisma.address.create({
          data: {
            userId: data.userId,
            title: 'آدرس سفارش',
            firstName: data.shippingAddress.firstName,
            lastName: data.shippingAddress.lastName,
            phone: data.shippingAddress.phone,
            province: data.shippingAddress.province,
            city: data.shippingAddress.city,
            district: data.shippingAddress.district,
            street: data.shippingAddress.street,
            postalCode: data.shippingAddress.postalCode,
            isDefault: false,
          },
        });
        shippingAddressId = newAddress.id;
      }

      // For guest orders, we'll store shipping address info in notes for now
      // In a production system, you might want to create a separate GuestAddress table
      let orderNotes = data.notes || '';
      if (!data.userId && data.shippingAddress) {
        const addressInfo = `آدرس ارسال: ${data.shippingAddress.firstName} ${data.shippingAddress.lastName}, ${data.shippingAddress.street}, ${data.shippingAddress.city}, ${data.shippingAddress.province}, کد پستی: ${data.shippingAddress.postalCode}, تلفن: ${data.shippingAddress.phone}`;
        orderNotes = orderNotes ? `${orderNotes}\n\n${addressInfo}` : addressInfo;
      }

      // Create order with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Generate unique order number
        let orderNumber: string;
        let attempts = 0;
        do {
          orderNumber = this.generateOrderNumber();
          const existing = await tx.order.findUnique({
            where: { orderNumber },
          });
          if (!existing) break;
          attempts++;
        } while (attempts < 10);

        if (attempts >= 10) {
          throw new AppError('خطا در تولید شماره سفارش', 500, 'ORDER_NUMBER_GENERATION_FAILED');
        }

        // Create the order
        const order = await tx.order.create({
          data: {
            orderNumber,
            userId: data.userId,
            guestEmail: data.guestEmail,
            status: OrderStatus.PENDING,
            paymentStatus: PaymentStatus.PENDING,
            fulfillmentStatus: FulfillmentStatus.UNFULFILLED,
            subtotal,
            taxAmount,
            shippingAmount,
            discountAmount,
            totalAmount,
            shippingAddressId,
            billingAddressId: data.billingAddressId || shippingAddressId,
            shippingMethod: data.shippingMethod,
            notes: orderNotes,
            placedAt: new Date(),
          },
        });

        // Create order items
        const orderItems = await Promise.all(
          productDetails.map((item) =>
            tx.orderItem.create({
              data: {
                orderId: order.id,
                productId: item.productId,
                variantId: item.variantId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
              },
            })
          )
        );

        // Reserve inventory
        for (const item of productDetails) {
          if (item.product.trackQuantity && item.product.inventory) {
            await tx.productInventory.update({
              where: { id: item.product.inventory.id },
              data: {
                reservedQuantity: {
                  increment: item.quantity,
                },
              },
            });
          }
        }

        return order;
      });

      logger.info(`Order created successfully: ${result.orderNumber} for ${data.userId ? 'user' : 'guest'}`);

      // Return order with relations
      return await this.getOrderById(result.id, {
        include: {
          user: true,
          items: true,
          shippingAddress: true,
        },
      });
    } catch (error) {
      logger.error('Order creation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد سفارش', 500, 'ORDER_CREATION_FAILED');
    }
  }

  // Get order by ID
  static async getOrderById(
    id: string,
    options: OrderQueryOptions = {}
  ): Promise<OrderWithRelations> {
    try {
      const include: any = {};

      if (options.include?.user) {
        include.user = {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        };
      }

      if (options.include?.items) {
        include.items = {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                nameEn: true,
                slug: true,
                sku: true,
                price: true,
                isActive: true,
                images: {
                  where: { isPrimary: true },
                  take: 1,
                },
              },
            },
            variant: {
              select: {
                id: true,
                name: true,
                value: true,
                price: true,
                sku: true,
              },
            },
          },
        };
      }

      if (options.include?.payments) {
        include.payments = {
          orderBy: { createdAt: 'desc' },
        };
      }

      if (options.include?.shippingAddress) {
        include.shippingAddress = true;
      }

      if (options.include?.notifications) {
        include.notifications = {
          orderBy: { createdAt: 'desc' },
        };
      }

      const order = await prisma.order.findUnique({
        where: { id },
        include,
      });

      if (!order) {
        throw new AppError('سفارش یافت نشد', 404, 'ORDER_NOT_FOUND');
      }

      return order as OrderWithRelations;
    } catch (error) {
      logger.error('Get order by ID failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت سفارش', 500, 'GET_ORDER_FAILED');
    }
  }

  // Get order by order number
  static async getOrderByNumber(
    orderNumber: string,
    options: OrderQueryOptions = {}
  ): Promise<OrderWithRelations> {
    try {
      const include: any = {};

      if (options.include?.user) {
        include.user = {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        };
      }

      if (options.include?.items) {
        include.items = {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                nameEn: true,
                slug: true,
                sku: true,
                price: true,
                isActive: true,
                images: {
                  where: { isPrimary: true },
                  take: 1,
                },
              },
            },
            variant: {
              select: {
                id: true,
                name: true,
                value: true,
                price: true,
                sku: true,
              },
            },
          },
        };
      }

      if (options.include?.payments) {
        include.payments = {
          orderBy: { createdAt: 'desc' },
        };
      }

      if (options.include?.shippingAddress) {
        include.shippingAddress = true;
      }

      if (options.include?.notifications) {
        include.notifications = {
          orderBy: { createdAt: 'desc' },
        };
      }

      const order = await prisma.order.findUnique({
        where: { orderNumber },
        include,
      });

      if (!order) {
        throw new AppError('سفارش یافت نشد', 404, 'ORDER_NOT_FOUND');
      }

      return order as OrderWithRelations;
    } catch (error) {
      logger.error('Get order by number failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت سفارش', 500, 'GET_ORDER_FAILED');
    }
  }

  // Get all orders with filtering and pagination
  static async getOrders(
    filters: OrderFilters = {},
    options: OrderQueryOptions = {}
  ): Promise<{
    orders: OrderWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Build where clause
      const where: Prisma.OrderWhereInput = {};

      if (filters.search) {
        where.OR = [
          { orderNumber: { contains: filters.search, mode: 'insensitive' } },
          { guestEmail: { contains: filters.search, mode: 'insensitive' } },
          { notes: { contains: filters.search, mode: 'insensitive' } },
          { trackingNumber: { contains: filters.search, mode: 'insensitive' } },
          {
            user: {
              OR: [
                { firstName: { contains: filters.search, mode: 'insensitive' } },
                { lastName: { contains: filters.search, mode: 'insensitive' } },
                { email: { contains: filters.search, mode: 'insensitive' } },
              ],
            },
          },
        ];
      }

      if (filters.userId) {
        where.userId = filters.userId;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.paymentStatus) {
        where.paymentStatus = filters.paymentStatus;
      }

      if (filters.fulfillmentStatus) {
        where.fulfillmentStatus = filters.fulfillmentStatus;
      }

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) {
          where.createdAt.gte = filters.dateFrom;
        }
        if (filters.dateTo) {
          where.createdAt.lte = filters.dateTo;
        }
      }

      if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
        where.totalAmount = {};
        if (filters.minAmount !== undefined) {
          where.totalAmount.gte = filters.minAmount;
        }
        if (filters.maxAmount !== undefined) {
          where.totalAmount.lte = filters.maxAmount;
        }
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = {
          hasSome: filters.tags,
        };
      }

      if (filters.hasTrackingNumber !== undefined) {
        if (filters.hasTrackingNumber) {
          where.trackingNumber = { not: null };
        } else {
          where.trackingNumber = null;
        }
      }

      // Build include clause
      const include: any = {};
      if (options.include?.user) {
        include.user = {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          },
        };
      }

      if (options.include?.items) {
        include.items = {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                nameEn: true,
                slug: true,
                sku: true,
                price: true,
                isActive: true,
                images: {
                  where: { isPrimary: true },
                  take: 1,
                },
              },
            },
            variant: {
              select: {
                id: true,
                name: true,
                value: true,
                price: true,
                sku: true,
              },
            },
          },
        };
      }

      if (options.include?.payments) {
        include.payments = {
          orderBy: { createdAt: 'desc' },
        };
      }

      if (options.include?.shippingAddress) {
        include.shippingAddress = true;
      }

      if (options.include?.notifications) {
        include.notifications = {
          orderBy: { createdAt: 'desc' },
        };
      }

      // Add count for items and payments
      include._count = {
        select: {
          items: true,
          payments: true,
        },
      };

      // Build order by clause
      const orderBy: Prisma.OrderOrderByWithRelationInput = {};
      const sortBy = options.sortBy || 'createdAt';
      const sortOrder = options.sortOrder || 'desc';

      switch (sortBy) {
        case 'orderNumber':
          orderBy.orderNumber = sortOrder;
          break;
        case 'totalAmount':
          orderBy.totalAmount = sortOrder;
          break;
        case 'status':
          orderBy.status = sortOrder;
          break;
        case 'placedAt':
          orderBy.placedAt = sortOrder;
          break;
        case 'updatedAt':
          orderBy.updatedAt = sortOrder;
          break;
        case 'createdAt':
        default:
          orderBy.createdAt = sortOrder;
          break;
      }

      // Get total count
      const total = await prisma.order.count({ where });

      // Get orders
      const orders = await prisma.order.findMany({
        where,
        include,
        orderBy,
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        orders: orders as OrderWithRelations[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get orders failed:', error);
      throw new AppError('خطا در دریافت سفارشات', 500, 'GET_ORDERS_FAILED');
    }
  }

  // Update order (admin only)
  static async updateOrder(
    id: string,
    data: UpdateOrderData
  ): Promise<OrderWithRelations> {
    try {
      // Check if order exists
      const existingOrder = await prisma.order.findUnique({
        where: { id },
        include: {
          items: {
            include: {
              product: {
                include: { inventory: true },
              },
            },
          },
        },
      });

      if (!existingOrder) {
        throw new AppError('سفارش یافت نشد', 404, 'ORDER_NOT_FOUND');
      }

      // Validate status transitions
      if (data.status && data.status !== existingOrder.status) {
        this.validateStatusTransition(existingOrder.status, data.status);
      }

      // Update order with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Handle inventory changes if order is being cancelled
        if (data.status === OrderStatus.CANCELLED && existingOrder.status !== OrderStatus.CANCELLED) {
          // Release reserved inventory
          for (const item of existingOrder.items) {
            if (item.product.trackQuantity && item.product.inventory) {
              await tx.productInventory.update({
                where: { id: item.product.inventory.id },
                data: {
                  reservedQuantity: {
                    decrement: item.quantity,
                  },
                },
              });
            }
          }
        }

        // Handle inventory changes if order is being confirmed
        if (data.status === OrderStatus.CONFIRMED && existingOrder.status === OrderStatus.PENDING) {
          // Reduce actual inventory
          for (const item of existingOrder.items) {
            if (item.product.trackQuantity && item.product.inventory) {
              await tx.productInventory.update({
                where: { id: item.product.inventory.id },
                data: {
                  quantity: {
                    decrement: item.quantity,
                  },
                  reservedQuantity: {
                    decrement: item.quantity,
                  },
                },
              });
            }
          }
        }

        // Update timestamps based on status
        const updateData: any = { ...data };
        if (data.status === OrderStatus.SHIPPED && !existingOrder.shippedAt) {
          updateData.shippedAt = new Date();
        }
        if (data.status === OrderStatus.DELIVERED && !existingOrder.deliveredAt) {
          updateData.deliveredAt = new Date();
        }
        if (data.status === OrderStatus.CANCELLED && !existingOrder.cancelledAt) {
          updateData.cancelledAt = new Date();
        }

        // Update the order
        const updatedOrder = await tx.order.update({
          where: { id },
          data: updateData,
        });

        return updatedOrder;
      });

      logger.info(`Order updated successfully: ${result.orderNumber}, status: ${result.status}`);

      // Return order with relations
      return await this.getOrderById(result.id, {
        include: {
          user: true,
          items: true,
          payments: true,
          shippingAddress: true,
        },
      });
    } catch (error) {
      logger.error('Order update failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی سفارش', 500, 'ORDER_UPDATE_FAILED');
    }
  }

  // Validate status transitions
  private static validateStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): void {
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED, OrderStatus.CANCELLED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [], // Cannot transition from cancelled
      [OrderStatus.REFUNDED]: [], // Cannot transition from refunded
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    if (!allowedTransitions.includes(newStatus)) {
      throw new AppError(
        `تغییر وضعیت از ${currentStatus} به ${newStatus} مجاز نیست`,
        400,
        'INVALID_STATUS_TRANSITION'
      );
    }
  }

  // Get customer orders
  static async getCustomerOrders(
    userId: string,
    options: OrderQueryOptions = {}
  ): Promise<{
    orders: OrderWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 10, 50); // Max 50 items per page for customers
      const skip = (page - 1) * limit;

      // Build include clause
      const include: any = {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                nameEn: true,
                slug: true,
                sku: true,
                price: true,
                isActive: true,
                images: {
                  where: { isPrimary: true },
                  take: 1,
                },
              },
            },
            variant: {
              select: {
                id: true,
                name: true,
                value: true,
                price: true,
                sku: true,
              },
            },
          },
        },
        shippingAddress: true,
        _count: {
          select: {
            items: true,
          },
        },
      };

      // Get total count
      const total = await prisma.order.count({
        where: { userId },
      });

      // Get orders
      const orders = await prisma.order.findMany({
        where: { userId },
        include,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        orders: orders as OrderWithRelations[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get customer orders failed:', error);
      throw new AppError('خطا در دریافت سفارشات مشتری', 500, 'GET_CUSTOMER_ORDERS_FAILED');
    }
  }

  // Cancel order
  static async cancelOrder(
    id: string,
    reason?: string,
    userId?: string
  ): Promise<OrderWithRelations> {
    try {
      const order = await prisma.order.findUnique({
        where: { id },
        include: {
          items: {
            include: {
              product: {
                include: { inventory: true },
              },
            },
          },
        },
      });

      if (!order) {
        throw new AppError('سفارش یافت نشد', 404, 'ORDER_NOT_FOUND');
      }

      // Check if user owns the order (for customer cancellation)
      if (userId && order.userId !== userId) {
        throw new AppError('شما مجاز به لغو این سفارش نیستید', 403, 'ORDER_ACCESS_DENIED');
      }

      // Check if order can be cancelled
      const cancellableStatuses = [OrderStatus.PENDING, OrderStatus.CONFIRMED, OrderStatus.PROCESSING];
      if (!cancellableStatuses.includes(order.status as any)) {
        throw new AppError('این سفارش قابل لغو نیست', 400, 'ORDER_NOT_CANCELLABLE');
      }

      // Cancel order and release inventory
      const result = await prisma.$transaction(async (tx) => {
        // Release reserved inventory
        for (const item of order.items) {
          if (item.product.trackQuantity && item.product.inventory) {
            await tx.productInventory.update({
              where: { id: item.product.inventory.id },
              data: {
                reservedQuantity: {
                  decrement: item.quantity,
                },
                // If order was confirmed, also restore actual quantity
                ...(order.status === OrderStatus.CONFIRMED && {
                  quantity: {
                    increment: item.quantity,
                  },
                }),
              },
            });
          }
        }

        // Update order status
        const updatedOrder = await tx.order.update({
          where: { id },
          data: {
            status: OrderStatus.CANCELLED,
            cancelledAt: new Date(),
            internalNotes: reason ? `لغو شده: ${reason}` : 'لغو شده توسط مشتری',
          },
        });

        return updatedOrder;
      });

      logger.info(`Order cancelled: ${result.orderNumber}, reason: ${reason || 'Customer cancellation'}`);

      return await this.getOrderById(result.id, {
        include: {
          user: true,
          items: true,
          shippingAddress: true,
        },
      });
    } catch (error) {
      logger.error('Order cancellation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در لغو سفارش', 500, 'ORDER_CANCELLATION_FAILED');
    }
  }
}
