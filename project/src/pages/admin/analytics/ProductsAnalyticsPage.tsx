import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Package,
  TrendingUp,
  TrendingDown,
  Eye,
  ShoppingCart,
  Star,
  AlertTriangle,
  Calendar,
  Download,
  RefreshCw,
  BarChart3,
  DollarSign,
  Target,
  Layers
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminAnalytics } from '../../../hooks/useAdminAnalytics';
import { formatCurrency, formatPercentage, formatGrowthRate, getDateRangeOptions } from '../../../utils/analyticsUtils';
import { formatNumber } from '../../../utils/formatters';

const ProductsAnalyticsPage: React.FC = () => {
  const { salesData, loading, error } = useAdminAnalytics();
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const handleExport = async () => {
    console.log('Exporting product analytics...');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <AdminButton onClick={handleRefresh} className="mt-4">
            تلاش مجدد
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  // Mock additional product analytics data
  const productMetrics = {
    totalProducts: 1247,
    activeProducts: 1156,
    outOfStock: 23,
    lowStock: 45,
    averageRating: 4.3,
    totalViews: 125430,
    conversionRate: 3.2,
    returnRate: 2.1
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">عملکرد محصولات</h1>
            <p className="text-gray-600 mt-1">تحلیل فروش، محبوبیت و عملکرد محصولات</p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
            >
              <option value="all">همه دسته‌بندی‌ها</option>
              <option value="skincare">مراقبت از پوست</option>
              <option value="makeup">آرایش</option>
              <option value="haircare">مراقبت از مو</option>
              <option value="fragrance">عطر</option>
            </select>
            
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
            >
              {getDateRangeOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={handleRefresh}
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی Excel
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminStatsCard
            title="کل محصولات"
            value={formatNumber(productMetrics.totalProducts)}
            change="+5.2%"
            changeType="increase"
            icon={Package}
            color="bg-blue-500"
          />
          
          <AdminStatsCard
            title="محصولات فعال"
            value={formatNumber(productMetrics.activeProducts)}
            change="+3.1%"
            changeType="increase"
            icon={TrendingUp}
            color="bg-green-500"
          />
          
          <AdminStatsCard
            title="کل بازدید"
            value={formatNumber(productMetrics.totalViews)}
            change="+12.8%"
            changeType="increase"
            icon={Eye}
            color="bg-purple-500"
          />
          
          <AdminStatsCard
            title="نرخ تبدیل"
            value={formatPercentage(productMetrics.conversionRate)}
            change="+0.8%"
            changeType="increase"
            icon={Target}
            color="bg-orange-500"
          />
        </div>

        {/* Inventory Alerts */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(productMetrics.outOfStock)}
                </div>
                <div className="text-sm text-gray-600">محصولات تمام شده</div>
              </div>
            </div>
          </AdminCard>
          
          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(productMetrics.lowStock)}
                </div>
                <div className="text-sm text-gray-600">موجودی کم</div>
              </div>
            </div>
          </AdminCard>
          
          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(productMetrics.averageRating)}
                </div>
                <div className="text-sm text-gray-600">میانگین امتیاز</div>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Product Performance */}
        {salesData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performing Products */}
            <AdminCard title="محصولات پرفروش" icon={TrendingUp}>
              <div className="space-y-4">
                {salesData.topProducts.slice(0, 8).map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-admin-600">
                          {formatNumber(index + 1)}
                        </span>
                      </div>
                      {product.image && (
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-10 h-10 object-cover rounded-lg"
                        />
                      )}
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {product.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatNumber(product.sales)} فروش • {formatNumber(product.views || Math.floor(Math.random() * 1000) + 100)} بازدید
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-sm">
                        {formatCurrency(product.revenue)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatPercentage((product.sales / (product.views || 500)) * 100)} تبدیل
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>

            {/* Category Performance */}
            <AdminCard title="عملکرد دسته‌بندی‌ها" icon={Layers}>
              <div className="space-y-4">
                {salesData.topCategories.map((category, index) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 rounded-full" style={{
                          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
                        }}></div>
                        <h3 className="font-medium text-gray-900">{category.name}</h3>
                      </div>
                      <span className="text-sm font-bold text-gray-900">
                        {formatCurrency(category.revenue)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-xs mb-3">
                      <div>
                        <span className="text-gray-500">فروش:</span>
                        <div className="font-medium">{formatNumber(category.sales)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">محصولات:</span>
                        <div className="font-medium">{formatNumber(category.productCount || Math.floor(Math.random() * 50) + 10)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">رشد:</span>
                        <div className="font-medium text-green-600">+{formatNumber(Math.floor(Math.random() * 20) + 5)}%</div>
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${(category.revenue / salesData.topCategories[0].revenue) * 100}%` }}
                        transition={{ delay: index * 0.2, duration: 0.8 }}
                        className="h-2 rounded-full"
                        style={{
                          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
                        }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>
          </div>
        )}

        {/* Product Insights */}
        <AdminCard title="بینش‌های محصول" icon={BarChart3}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatPercentage(productMetrics.conversionRate)}
              </div>
              <div className="text-sm text-gray-600">نرخ تبدیل کلی</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Eye className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatNumber(Math.floor(productMetrics.totalViews / productMetrics.activeProducts))}
              </div>
              <div className="text-sm text-gray-600">میانگین بازدید محصول</div>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatNumber(productMetrics.averageRating)}
              </div>
              <div className="text-sm text-gray-600">میانگین رضایت</div>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingDown className="w-6 h-6 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {formatPercentage(productMetrics.returnRate)}
              </div>
              <div className="text-sm text-gray-600">نرخ برگشت</div>
            </div>
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default ProductsAnalyticsPage;
