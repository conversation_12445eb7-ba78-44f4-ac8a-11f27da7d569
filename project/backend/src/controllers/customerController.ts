import { Request, Response, NextFunction } from 'express';
import { UserRole, UserStatus } from '@prisma/client';
import CustomerService, { CustomerFilters, CustomerListOptions, CreateCustomerData, UpdateCustomerData } from '../services/customerService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class CustomerController {
  // Get all customers with filtering and pagination (admin only)
  static async getCustomers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        role,
        status,
        isEmailVerified,
        isPhoneVerified,
        hasOrders,
        registrationDateFrom,
        registrationDateTo,
        lastLoginFrom,
        lastLoginTo,
        province,
        city,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        include = 'addresses,orderStats,_count',
      } = req.query;

      // Parse filters
      const filters: CustomerFilters = {};
      if (search) filters.search = search as string;
      if (role) filters.role = role as UserRole;
      if (status) filters.status = status as UserStatus;
      if (isEmailVerified !== undefined) filters.isEmailVerified = isEmailVerified === 'true';
      if (isPhoneVerified !== undefined) filters.isPhoneVerified = isPhoneVerified === 'true';
      if (hasOrders !== undefined) filters.hasOrders = hasOrders === 'true';
      if (registrationDateFrom) filters.registrationDateFrom = new Date(registrationDateFrom as string);
      if (registrationDateTo) filters.registrationDateTo = new Date(registrationDateTo as string);
      if (lastLoginFrom) filters.lastLoginFrom = new Date(lastLoginFrom as string);
      if (lastLoginTo) filters.lastLoginTo = new Date(lastLoginTo as string);
      if (province) filters.province = province as string;
      if (city) filters.city = city as string;

      // Parse options
      const options: CustomerListOptions = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
        include: {},
      };

      // Parse include options
      const includeOptions = (include as string).split(',');
      if (includeOptions.includes('addresses')) options.include!.addresses = true;
      if (includeOptions.includes('orders')) options.include!.orders = true;
      if (includeOptions.includes('orderStats')) options.include!.orderStats = true;
      if (includeOptions.includes('loyaltyAccount')) options.include!.loyaltyAccount = true;
      if (includeOptions.includes('_count')) options.include!._count = true;

      const result = await CustomerService.getCustomers(filters, options);

      res.json({
        success: true,
        message: 'لیست مشتریان با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get customer by ID (admin only)
  static async getCustomerById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeOrders = 'true', includeAddresses = 'true', includeStats = 'true' } = req.query;

      const customer = await CustomerService.getCustomerById(id, {
        includeOrders: includeOrders === 'true',
        includeAddresses: includeAddresses === 'true',
        includeStats: includeStats === 'true',
      });

      res.json({
        success: true,
        message: 'اطلاعات مشتری با موفقیت دریافت شد',
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // Create new customer (admin only)
  static async createCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const customerData: CreateCustomerData = req.body;

      const customer = await CustomerService.createCustomer(customerData);

      res.status(201).json({
        success: true,
        message: 'مشتری جدید با موفقیت ایجاد شد',
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // Update customer (admin only)
  static async updateCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateCustomerData = req.body;

      const customer = await CustomerService.updateCustomer(id, updateData);

      res.json({
        success: true,
        message: 'اطلاعات مشتری با موفقیت به‌روزرسانی شد',
        data: { customer },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete customer (admin only)
  static async deleteCustomer(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await CustomerService.deleteCustomer(id);

      res.json({
        success: true,
        message: 'مشتری با موفقیت حذف شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get customer statistics (admin only)
  static async getCustomerStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await CustomerService.getCustomerStatistics();

      res.json({
        success: true,
        message: 'آمار مشتریان با موفقیت دریافت شد',
        data: { statistics },
      });
    } catch (error) {
      next(error);
    }
  }

  // Search customers (admin only)
  static async searchCustomers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = '10', includeInactive = 'false' } = req.query;

      if (!query) {
        throw new AppError('پارامتر جستجو الزامی است', 400, 'SEARCH_QUERY_REQUIRED');
      }

      const customers = await CustomerService.searchCustomers(query as string, {
        limit: parseInt(limit as string),
        includeInactive: includeInactive === 'true',
      });

      res.json({
        success: true,
        message: 'نتایج جستجو با موفقیت دریافت شد',
        data: { customers },
      });
    } catch (error) {
      next(error);
    }
  }

  // Bulk update customer status (admin only)
  static async bulkUpdateCustomerStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { customerIds, status, reason } = req.body;

      if (!customerIds || !Array.isArray(customerIds) || customerIds.length === 0) {
        throw new AppError('لیست شناسه مشتریان الزامی است', 400, 'CUSTOMER_IDS_REQUIRED');
      }

      if (!Object.values(UserStatus).includes(status)) {
        throw new AppError('وضعیت نامعتبر است', 400, 'INVALID_STATUS');
      }

      const result = await CustomerService.bulkUpdateCustomerStatus(customerIds, status, reason);

      res.json({
        success: true,
        message: 'وضعیت مشتریان با موفقیت به‌روزرسانی شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  // Export customers (admin only)
  static async exportCustomers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { format = 'csv', ...filterParams } = req.query;

      // Parse filters (same as getCustomers)
      const filters: CustomerFilters = {};
      if (filterParams.search) filters.search = filterParams.search as string;
      if (filterParams.role) filters.role = filterParams.role as UserRole;
      if (filterParams.status) filters.status = filterParams.status as UserStatus;
      if (filterParams.isEmailVerified !== undefined) filters.isEmailVerified = filterParams.isEmailVerified === 'true';
      if (filterParams.isPhoneVerified !== undefined) filters.isPhoneVerified = filterParams.isPhoneVerified === 'true';
      if (filterParams.hasOrders !== undefined) filters.hasOrders = filterParams.hasOrders === 'true';
      if (filterParams.registrationDateFrom) filters.registrationDateFrom = new Date(filterParams.registrationDateFrom as string);
      if (filterParams.registrationDateTo) filters.registrationDateTo = new Date(filterParams.registrationDateTo as string);
      if (filterParams.lastLoginFrom) filters.lastLoginFrom = new Date(filterParams.lastLoginFrom as string);
      if (filterParams.lastLoginTo) filters.lastLoginTo = new Date(filterParams.lastLoginTo as string);
      if (filterParams.province) filters.province = filterParams.province as string;
      if (filterParams.city) filters.city = filterParams.city as string;

      const exportData = await CustomerService.exportCustomers(filters, format as 'csv' | 'json');

      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename=customers.csv');
        
        // Convert to CSV string
        const csvContent = [
          exportData.headers.join(','),
          ...exportData.data.map((row: any[]) => row.map(cell => `"${cell}"`).join(','))
        ].join('\n');
        
        res.send('\ufeff' + csvContent); // Add BOM for proper UTF-8 encoding
      } else {
        res.json({
          success: true,
          message: 'اطلاعات مشتریان با موفقیت صادر شد',
          data: { customers: exportData },
        });
      }
    } catch (error) {
      next(error);
    }
  }

  // Get customer order history (admin only)
  static async getCustomerOrderHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10, status } = req.query;

      const result = await CustomerService.getCustomerOrderHistory(id, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string,
      });

      res.json({
        success: true,
        message: 'تاریخچه سفارش‌های مشتری با موفقیت دریافت شد',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }
}

export default CustomerController;
