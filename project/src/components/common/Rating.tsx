import React from 'react';
import { Star } from 'lucide-react';

interface RatingProps {
  value: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
}

const Rating: React.FC<RatingProps> = ({ value, size = 'md', showValue = false }) => {
  const stars = Array.from({ length: 5 }, (_, i) => {
    // Full star
    if (i < Math.floor(value)) {
      return 'full';
    }
    // Half star
    if (i === Math.floor(value) && value % 1 >= 0.5) {
      return 'half';
    }
    // Empty star
    return 'empty';
  });

  const getSizeClass = () => {
    switch (size) {
      case 'sm': return 'h-3 w-3';
      case 'lg': return 'h-6 w-6';
      default: return 'h-4 w-4';
    }
  };

  const starSize = getSizeClass();
  
  return (
    <div className="flex items-center">
      {stars.map((type, index) => (
        <span key={index} className="relative inline-block">
          <Star 
            className={`${starSize} ${
              type === 'full' ? 'text-accent-500 fill-accent-500' : 
              type === 'half' ? 'text-accent-500' : 
              'text-gray-300'
            }`} 
          />
          {type === 'half' && (
            <span className="absolute top-0 left-0 overflow-hidden" style={{ width: '50%' }}>
              <Star className={`${starSize} text-accent-500 fill-accent-500`} />
            </span>
          )}
        </span>
      ))}
      
      {showValue && <span className="mr-1 text-sm font-medium text-text-secondary">{value.toFixed(1)}</span>}
    </div>
  );
};

export default Rating;