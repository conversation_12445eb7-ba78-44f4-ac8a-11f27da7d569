import { PrismaClient, OrderStatus, PaymentStatus, FulfillmentStatus, PaymentMethod } from '@prisma/client';

const prisma = new PrismaClient();

// Helper function to generate order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `GR${timestamp}${random}`;
}

// Helper function to get random date within last 30 days
function getRandomDate(daysBack: number = 30): Date {
  const now = new Date();
  const pastDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
  const randomTime = pastDate.getTime() + Math.random() * (now.getTime() - pastDate.getTime());
  return new Date(randomTime);
}

// Helper function to get random element from array
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

async function seedOrders() {
  console.log('🛒 Starting order seeding...');

  try {
    // Get existing users and products
    const users = await prisma.user.findMany({
      where: { role: 'CUSTOMER' }
    });

    const products = await prisma.product.findMany({
      include: {
        inventory: true,
        variants: true
      }
    });

    if (users.length === 0) {
      console.log('❌ No customer users found. Please run the main seed script first.');
      return;
    }

    if (products.length === 0) {
      console.log('❌ No products found. Please run the main seed script first.');
      return;
    }

    console.log(`📦 Found ${users.length} users and ${products.length} products`);

    // Create sample addresses for orders
    const sampleAddresses = [];
    for (const user of users.slice(0, 3)) {
      const address = await prisma.address.create({
        data: {
          userId: user.id,
          title: 'آدرس منزل',
          firstName: user.firstName || 'نام',
          lastName: user.lastName || 'خانوادگی',
          phone: '09123456789',
          province: 'تهران',
          city: 'تهران',
          district: 'منطقه ۱',
          street: 'خیابان ولیعصر، پلاک ۱۲۳',
          postalCode: '1234567890',
          isDefault: true
        }
      });
      sampleAddresses.push(address);
    }

    // Order statuses with weights (more pending/confirmed orders)
    const orderStatuses = [
      { status: OrderStatus.PENDING, weight: 3 },
      { status: OrderStatus.CONFIRMED, weight: 4 },
      { status: OrderStatus.PROCESSING, weight: 3 },
      { status: OrderStatus.SHIPPED, weight: 2 },
      { status: OrderStatus.DELIVERED, weight: 2 },
      { status: OrderStatus.CANCELLED, weight: 1 }
    ];

    const paymentStatuses = [
      { status: PaymentStatus.PENDING, weight: 2 },
      { status: PaymentStatus.PAID, weight: 5 },
      { status: PaymentStatus.FAILED, weight: 1 },
      { status: PaymentStatus.REFUNDED, weight: 1 }
    ];

    const fulfillmentStatuses = [
      { status: FulfillmentStatus.UNFULFILLED, weight: 3 },
      { status: FulfillmentStatus.PARTIAL, weight: 2 },
      { status: FulfillmentStatus.FULFILLED, weight: 3 }
    ];

    // Helper to get weighted random status
    function getWeightedRandomStatus<T>(items: { status: T; weight: number }[]): T {
      const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
      let random = Math.random() * totalWeight;
      
      for (const item of items) {
        random -= item.weight;
        if (random <= 0) {
          return item.status;
        }
      }
      return items[0].status;
    }

    // Create 50 sample orders
    const ordersToCreate = 50;
    console.log(`📝 Creating ${ordersToCreate} sample orders...`);

    for (let i = 0; i < ordersToCreate; i++) {
      const user = getRandomElement(users);
      const address = sampleAddresses.find(addr => addr.userId === user.id) || sampleAddresses[0];
      
      // Random number of items (1-4)
      const itemCount = Math.floor(Math.random() * 4) + 1;
      const orderItems = [];
      let subtotal = 0;

      // Select random products for this order
      const selectedProducts = [];
      for (let j = 0; j < itemCount; j++) {
        const product = getRandomElement(products);
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = parseFloat(product.price.toString());
        const totalPrice = unitPrice * quantity;

        selectedProducts.push({
          productId: product.id,
          quantity,
          unitPrice,
          totalPrice
        });

        subtotal += totalPrice;
      }

      // Calculate order totals
      const taxAmount = subtotal * 0.09; // 9% tax
      const shippingAmount = subtotal > 500000 ? 0 : 50000; // Free shipping over 500k IRR
      const discountAmount = Math.random() > 0.7 ? subtotal * 0.1 : 0; // 10% discount for 30% of orders
      const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

      // Get random statuses
      const orderStatus = getWeightedRandomStatus(orderStatuses);
      const paymentStatus = getWeightedRandomStatus(paymentStatuses);
      const fulfillmentStatus = getWeightedRandomStatus(fulfillmentStatuses);

      // Create order date
      const createdAt = getRandomDate(30);
      let placedAt = createdAt;
      let shippedAt = null;
      let deliveredAt = null;

      // Set dates based on status
      if (orderStatus === OrderStatus.SHIPPED || orderStatus === OrderStatus.DELIVERED) {
        shippedAt = new Date(createdAt.getTime() + Math.random() * 3 * 24 * 60 * 60 * 1000); // 0-3 days after creation
      }
      if (orderStatus === OrderStatus.DELIVERED) {
        deliveredAt = new Date((shippedAt || createdAt).getTime() + Math.random() * 5 * 24 * 60 * 60 * 1000); // 0-5 days after shipping
      }

      // Create the order
      const order = await prisma.order.create({
        data: {
          orderNumber: generateOrderNumber(),
          userId: user.id,
          status: orderStatus,
          paymentStatus: paymentStatus,
          fulfillmentStatus: fulfillmentStatus,
          subtotal: subtotal,
          taxAmount: taxAmount,
          shippingAmount: shippingAmount,
          discountAmount: discountAmount,
          totalAmount: totalAmount,
          shippingAddressId: address.id,
          billingAddressId: address.id,
          shippingMethod: getRandomElement(['standard', 'express', 'overnight']),
          trackingNumber: orderStatus === OrderStatus.SHIPPED || orderStatus === OrderStatus.DELIVERED 
            ? `TRK${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}` 
            : null,
          notes: Math.random() > 0.8 ? 'لطفا با دقت بسته‌بندی شود' : null,
          placedAt: placedAt,
          shippedAt: shippedAt,
          deliveredAt: deliveredAt,
          createdAt: createdAt,
          updatedAt: createdAt
        }
      });

      // Create order items
      for (const item of selectedProducts) {
        await prisma.orderItem.create({
          data: {
            orderId: order.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice
          }
        });
      }

      // Create payment record if paid
      if (paymentStatus === PaymentStatus.PAID) {
        await prisma.payment.create({
          data: {
            orderId: order.id,
            amount: totalAmount,
            currency: 'IRR',
            method: getRandomElement([PaymentMethod.CREDIT_CARD, PaymentMethod.BANK_TRANSFER, PaymentMethod.ZARINPAL, PaymentMethod.CASH_ON_DELIVERY]),
            status: PaymentStatus.PAID,
            gateway: getRandomElement(['zarinpal', 'mellat', 'parsian']),
            gatewayTransactionId: `TXN${Math.floor(Math.random() * 1000000)}`,
            processedAt: new Date(createdAt.getTime() + Math.random() * 60 * 60 * 1000) // Within 1 hour
          }
        });
      }

      if ((i + 1) % 10 === 0) {
        console.log(`   ✅ Created ${i + 1}/${ordersToCreate} orders`);
      }
    }

    console.log('✅ Order seeding completed successfully!');
    
    // Show summary
    const orderCounts = await prisma.order.groupBy({
      by: ['status'],
      _count: true
    });

    console.log('📊 Order Summary:');
    for (const count of orderCounts) {
      console.log(`   - ${count.status}: ${count._count} orders`);
    }

  } catch (error) {
    console.error('❌ Error during order seeding:', error);
    throw error;
  }
}

async function main() {
  await seedOrders();
}

main()
  .catch((e) => {
    console.error('❌ Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
