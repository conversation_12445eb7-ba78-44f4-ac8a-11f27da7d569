import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '../config/logger';
import { config } from '../config';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error response interface
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  path: string;
  method: string;
}

// Handle Prisma errors
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field?.[0] || 'field';
      return new AppError(
        `این ${fieldName} قبلاً استفاده شده است`,
        409,
        'DUPLICATE_FIELD'
      );
    
    case 'P2025':
      // Record not found
      return new AppError(
        'رکورد مورد نظر یافت نشد',
        404,
        'RECORD_NOT_FOUND'
      );
    
    case 'P2003':
      // Foreign key constraint violation
      return new AppError(
        'عملیات به دلیل وابستگی داده‌ها امکان‌پذیر نیست',
        400,
        'FOREIGN_KEY_CONSTRAINT'
      );
    
    case 'P2014':
      // Required relation violation
      return new AppError(
        'داده‌های مرتبط الزامی موجود نیست',
        400,
        'REQUIRED_RELATION_VIOLATION'
      );
    
    default:
      return new AppError(
        'خطای پایگاه داده',
        500,
        'DATABASE_ERROR'
      );
  }
};

// Handle validation errors
const handleValidationError = (error: any): AppError => {
  const messages = error.details?.map((detail: any) => detail.message).join(', ');
  return new AppError(
    `خطای اعتبارسنجی: ${messages}`,
    400,
    'VALIDATION_ERROR'
  );
};

// Handle JWT errors
const handleJWTError = (error: Error): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError('توکن نامعتبر است', 401, 'INVALID_TOKEN');
  }
  if (error.name === 'TokenExpiredError') {
    return new AppError('توکن منقضی شده است', 401, 'EXPIRED_TOKEN');
  }
  return new AppError('خطای احراز هویت', 401, 'AUTH_ERROR');
};

// Main error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let appError: AppError;

  // Handle different types of errors
  if (error instanceof AppError) {
    appError = error;
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    appError = handlePrismaError(error);
  } else if (error.name === 'ValidationError') {
    appError = handleValidationError(error);
  } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    appError = handleJWTError(error);
  } else if (error.name === 'MulterError') {
    appError = new AppError(
      'خطای آپلود فایل',
      400,
      'FILE_UPLOAD_ERROR'
    );
  } else {
    // Unknown error
    appError = new AppError(
      config.server.env === 'development' ? error.message : 'خطای داخلی سرور',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }

  // Log error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    statusCode: appError.statusCode,
    code: appError.code,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Prepare error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: appError.message,
      code: appError.code,
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  // Include stack trace in development
  if (config.server.env === 'development') {
    errorResponse.error.stack = error.stack;
    errorResponse.error.details = error;
  }

  // Send error response
  res.status(appError.statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export default errorHandler;
