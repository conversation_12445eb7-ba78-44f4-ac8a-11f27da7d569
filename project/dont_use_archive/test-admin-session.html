<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Session Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .info { background-color: #d1ecf1; }
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Admin Session Management Test</h1>
    
    <div class="test-section info">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Click "Check Current Session" to see current state</li>
            <li>Click "Simulate Login" to create a session</li>
            <li>Refresh the page and check if session persists</li>
            <li>Navigate to admin panel and verify no redirect to login</li>
            <li>Test both "Remember Me" and regular sessions</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Session Controls:</h3>
        <button class="btn-info" onclick="checkSession()">Check Current Session</button>
        <button class="btn-primary" onclick="simulateLogin(false)">Simulate Login (Regular)</button>
        <button class="btn-primary" onclick="simulateLogin(true)">Simulate Login (Remember Me)</button>
        <button class="btn-danger" onclick="clearSession()">Clear Session</button>
        <button class="btn-success" onclick="testNavigation()">Test Admin Navigation</button>
    </div>

    <div class="test-section">
        <h3>Session Status:</h3>
        <div id="sessionStatus"></div>
    </div>

    <div class="test-section">
        <h3>Storage Contents:</h3>
        <div id="storageContents"></div>
    </div>

    <script>
        // Admin storage keys (matching the app)
        const ADMIN_STORAGE_KEYS = {
            TOKEN: 'admin_auth_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            USER: 'admin_user',
            REMEMBER_ME: 'admin_remember_me',
            LAST_LOGIN: 'admin_last_login',
            SESSION_EXPIRY: 'admin_session_expiry'
        };

        const ADMIN_SESSION_CONFIG = {
            DEFAULT_EXPIRY: 8 * 60 * 60 * 1000, // 8 hours
            REMEMBER_ME_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30 days
        };

        function checkSession() {
            const token = sessionStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN) || 
                         localStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN);
            const user = sessionStorage.getItem(ADMIN_STORAGE_KEYS.USER) || 
                        localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            const sessionExpiry = sessionStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY) || 
                                 localStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY);
            const rememberMe = localStorage.getItem(ADMIN_STORAGE_KEYS.REMEMBER_ME) === 'true';

            const isExpired = sessionExpiry ? Date.now() > parseInt(sessionExpiry) : true;
            const timeRemaining = sessionExpiry ? Math.max(0, parseInt(sessionExpiry) - Date.now()) : 0;

            const status = {
                hasToken: !!token,
                hasUser: !!user,
                sessionExpiry: sessionExpiry ? new Date(parseInt(sessionExpiry)).toLocaleString('fa-IR') : 'None',
                isExpired,
                timeRemaining: formatTime(timeRemaining),
                rememberMe,
                currentTime: new Date().toLocaleString('fa-IR')
            };

            document.getElementById('sessionStatus').innerHTML = `
                <pre>${JSON.stringify(status, null, 2)}</pre>
            `;

            updateStorageDisplay();
        }

        function simulateLogin(rememberMe) {
            // Create mock admin user
            const mockUser = {
                id: 'admin-1',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'سیستم',
                role: 'super_admin',
                isActive: true
            };

            // Create mock token
            const mockToken = btoa(JSON.stringify({
                sub: mockUser.id,
                email: mockUser.email,
                role: mockUser.role,
                type: 'admin',
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor((Date.now() + (rememberMe ? ADMIN_SESSION_CONFIG.REMEMBER_ME_EXPIRY : ADMIN_SESSION_CONFIG.DEFAULT_EXPIRY)) / 1000)
            }));

            // Store session data
            const storage = rememberMe ? localStorage : sessionStorage;
            const expiryTime = Date.now() + (rememberMe ? ADMIN_SESSION_CONFIG.REMEMBER_ME_EXPIRY : ADMIN_SESSION_CONFIG.DEFAULT_EXPIRY);

            storage.setItem(ADMIN_STORAGE_KEYS.TOKEN, mockToken);
            storage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(mockUser));
            storage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, expiryTime.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, rememberMe.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, btoa(`admin-refresh-${mockUser.id}-${Date.now()}`));

            alert(`Login simulated with ${rememberMe ? 'Remember Me' : 'Regular'} session!`);
            checkSession();
        }

        function clearSession() {
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                sessionStorage.removeItem(key);
                localStorage.removeItem(key);
            });
            alert('Session cleared!');
            checkSession();
        }

        function testNavigation() {
            window.open('http://localhost:5173/admin', '_blank');
        }

        function updateStorageDisplay() {
            const sessionData = {};
            const localData = {};

            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                const sessionValue = sessionStorage.getItem(key);
                const localValue = localStorage.getItem(key);
                
                if (sessionValue) sessionData[key] = sessionValue;
                if (localValue) localData[key] = localValue;
            });

            document.getElementById('storageContents').innerHTML = `
                <h4>Session Storage:</h4>
                <pre>${JSON.stringify(sessionData, null, 2)}</pre>
                <h4>Local Storage:</h4>
                <pre>${JSON.stringify(localData, null, 2)}</pre>
            `;
        }

        function formatTime(milliseconds) {
            if (milliseconds <= 0) return 'Expired';
            
            const hours = Math.floor(milliseconds / (1000 * 60 * 60));
            const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 0) {
                return `${hours} ساعت و ${minutes} دقیقه`;
            }
            return `${minutes} دقیقه`;
        }

        // Initialize on page load
        checkSession();
    </script>
</body>
</html>
