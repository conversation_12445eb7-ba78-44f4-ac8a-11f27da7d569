import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  Filter,
  Download,
  CheckCircle,
  XCircle,
  Flag,
  AlertTriangle,
  TrendingUp,
  Clock,
  Star,
  X
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import ReviewTable from '../../../components/admin/reviews/ReviewTable';
import { useAdminReviews } from '../../../hooks/useAdminReviews';
import { AdminReview, AdminReviewFilters, PERSIAN_REVIEW_ADMIN_MESSAGES } from '../../../types/adminReview';

const ReviewsListPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    reviews,
    loading,
    filters,
    setFilters,
    moderateReview,
    bulkModerateReviews,
    getAnalytics
  } = useAdminReviews();

  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showModerationModal, setShowModerationModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState<AdminReview | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [moderationAction, setModerationAction] = useState<'approve' | 'reject' | 'flag'>('approve');
  const [moderationNotes, setModerationNotes] = useState('');

  const analytics = getAnalytics();

  const handleSelectReview = (reviewId: string) => {
    setSelectedReviews(prev => 
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedReviews(selected ? reviews.map(r => r.id) : []);
  };

  const handleViewReview = (review: AdminReview) => {
    navigate(`/admin/reviews/${review.id}`);
  };

  const handleModerateReview = async (review: AdminReview, action: 'approve' | 'reject' | 'flag') => {
    try {
      await moderateReview(review.id, {
        reviewId: review.id,
        action,
        reason: moderationNotes,
        notes: moderationNotes
      });
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleBulkModeration = async (action: 'approve' | 'reject' | 'flag') => {
    if (selectedReviews.length === 0) return;

    try {
      await bulkModerateReviews({
        type: action,
        reviewIds: selectedReviews,
        notes: moderationNotes
      });
      setSelectedReviews([]);
      setModerationNotes('');
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleTogglePin = async (review: AdminReview) => {
    // Implementation would toggle pin status
    console.log('Toggle pin for review:', review.id);
  };

  const handleToggleHighlight = async (review: AdminReview) => {
    // Implementation would toggle highlight status
    console.log('Toggle highlight for review:', review.id);
  };

  const handleHideReview = async (review: AdminReview) => {
    // Implementation would hide/show review
    console.log('Hide review:', review.id);
  };

  const handleDeleteReview = async (review: AdminReview) => {
    // Implementation would delete review
    console.log('Delete review:', review.id);
  };

  const handleViewCustomer = (review: AdminReview) => {
    navigate(`/admin/customers/${review.customer?.id || review.user.id}`);
  };

  const handleViewProduct = (review: AdminReview) => {
    navigate(`/admin/products/${review.product.id}`);
  };

  const handleExport = async () => {
    try {
      // Implementation would export reviews
      console.log('Export reviews with filters:', filters);
    } catch (error) {
      // Error handling
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setFilters({ ...filters, search: value });
  };

  const clearFilters = () => {
    setFilters({});
    setSearchValue('');
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    key !== 'search' && filters[key as keyof AdminReviewFilters] !== undefined
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">مدیریت نظرات</h1>
            <p className="text-gray-600 mt-1">
              بررسی و مدیریت نظرات مشتریان
            </p>
          </div>
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
              loading={loading}
            >
              خروجی
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={Filter}
              onClick={() => setShowFilters(!showFilters)}
            >
              فیلتر
              {hasActiveFilters && (
                <span className="mr-1 bg-blue-500 text-white text-xs rounded-full w-2 h-2"></span>
              )}
            </AdminButton>
          </div>
        </div>

        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">کل نظرات</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.totalReviews.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">در انتظار بررسی</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.pendingReviews.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">تأیید شده</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.approvedReviews.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Flag className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">علامت‌گذاری شده</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.flaggedReviews.toLocaleString('fa-IR')}
                </p>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Quality Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AdminCard title="متریک‌های کیفیت">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">میانگین کیفیت</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.qualityMetrics.averageQualityScore)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">میانگین اسپم</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.qualityMetrics.averageSpamScore)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">خرید تأیید شده</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.qualityMetrics.verifiedPurchasePercentage)}%
                </span>
              </div>
            </div>
          </AdminCard>

          <AdminCard title="آمار بررسی">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">زمان پاسخ میانگین</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.moderationStats.averageResponseTime)} دقیقه
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">تأیید خودکار</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.moderationStats.autoApprovalRate)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">نرخ رد</span>
                <span className="font-medium text-gray-900">
                  {Math.round(analytics.moderationStats.rejectionRate)}%
                </span>
              </div>
            </div>
          </AdminCard>

          <AdminCard title="تحلیل احساسات">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">مثبت</span>
                <span className="font-medium text-green-600">
                  {analytics.contentAnalysis.sentimentDistribution.positive}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">خنثی</span>
                <span className="font-medium text-gray-600">
                  {analytics.contentAnalysis.sentimentDistribution.neutral}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">منفی</span>
                <span className="font-medium text-red-600">
                  {analytics.contentAnalysis.sentimentDistribution.negative}
                </span>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <AdminCard title="فیلترها">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وضعیت بررسی
                  </label>
                  <select
                    value={filters.status?.[0] || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      status: e.target.value ? [e.target.value as any] : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه وضعیت‌ها</option>
                    <option value="pending">در انتظار بررسی</option>
                    <option value="approved">تأیید شده</option>
                    <option value="rejected">رد شده</option>
                    <option value="flagged">علامت‌گذاری شده</option>
                    <option value="auto_approved">تأیید خودکار</option>
                    <option value="auto_rejected">رد خودکار</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    امتیاز
                  </label>
                  <select
                    value={filters.rating?.[0] || ''}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      rating: e.target.value ? [parseInt(e.target.value)] : undefined 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه امتیازها</option>
                    <option value="5">5 ستاره</option>
                    <option value="4">4 ستاره</option>
                    <option value="3">3 ستاره</option>
                    <option value="2">2 ستاره</option>
                    <option value="1">1 ستاره</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    خرید تأیید شده
                  </label>
                  <select
                    value={filters.isVerifiedPurchase === undefined ? '' : filters.isVerifiedPurchase.toString()}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      isVerifiedPurchase: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه</option>
                    <option value="true">تأیید شده</option>
                    <option value="false">تأیید نشده</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    دارای تصویر
                  </label>
                  <select
                    value={filters.hasImages === undefined ? '' : filters.hasImages.toString()}
                    onChange={(e) => setFilters({ 
                      ...filters, 
                      hasImages: e.target.value === '' ? undefined : e.target.value === 'true'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">همه</option>
                    <option value="true">دارای تصویر</option>
                    <option value="false">بدون تصویر</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <span className="text-sm text-gray-600">
                  {reviews.length} نظر یافت شد
                </span>
                <div className="flex items-center gap-2">
                  <AdminButton
                    variant="outline"
                    size="sm"
                    icon={X}
                    onClick={clearFilters}
                  >
                    پاک کردن فیلترها
                  </AdminButton>
                  <AdminButton
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(false)}
                  >
                    بستن
                  </AdminButton>
                </div>
              </div>
            </AdminCard>
          </motion.div>
        )}

        {/* Bulk Actions */}
        {selectedReviews.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedReviews.length} نظر انتخاب شده
              </span>
              <div className="flex items-center gap-2">
                <AdminButton
                  variant="success"
                  size="sm"
                  icon={CheckCircle}
                  onClick={() => handleBulkModeration('approve')}
                >
                  تأیید گروهی
                </AdminButton>
                <AdminButton
                  variant="danger"
                  size="sm"
                  icon={XCircle}
                  onClick={() => handleBulkModeration('reject')}
                >
                  رد گروهی
                </AdminButton>
                <AdminButton
                  variant="warning"
                  size="sm"
                  icon={Flag}
                  onClick={() => handleBulkModeration('flag')}
                >
                  علامت‌گذاری گروهی
                </AdminButton>
              </div>
            </div>
          </motion.div>
        )}

        {/* Reviews Table */}
        <AdminCard>
          <ReviewTable
            reviews={reviews}
            loading={loading}
            selectedReviews={selectedReviews}
            onSelectReview={handleSelectReview}
            onSelectAll={handleSelectAll}
            onViewReview={handleViewReview}
            onModerateReview={handleModerateReview}
            onTogglePin={handleTogglePin}
            onToggleHighlight={handleToggleHighlight}
            onHideReview={handleHideReview}
            onDeleteReview={handleDeleteReview}
            onViewCustomer={handleViewCustomer}
            onViewProduct={handleViewProduct}
            searchValue={searchValue}
            onSearchChange={handleSearchChange}
          />
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default ReviewsListPage;
