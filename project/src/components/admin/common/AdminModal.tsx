import React, { ReactNode, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, LucideIcon } from 'lucide-react';
import AdminButton from './AdminButton';

interface AdminModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  footer?: ReactNode;
  className?: string;
}

const AdminModal: React.FC<AdminModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  icon: Icon,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  footer,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl mx-4'
  };

  // Handle escape key
  useEffect(() => {
    if (!closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose, closeOnEscape]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            onClick={closeOnOverlayClick ? onClose : undefined}
          />

          {/* Modal Container */}
          <div className="flex min-h-full items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
              className={`
                relative w-full ${sizeClasses[size]} bg-white rounded-lg shadow-xl
                ${className}
              `.trim()}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              {(title || subtitle || Icon || showCloseButton) && (
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center gap-3">
                    {Icon && (
                      <div className="p-2 bg-admin-100 rounded-lg">
                        <Icon className="w-5 h-5 text-admin-600" />
                      </div>
                    )}
                    <div>
                      {title && (
                        <h3 className="text-lg font-semibold text-gray-900">
                          {title}
                        </h3>
                      )}
                      {subtitle && (
                        <p className="text-sm text-gray-600 mt-1">
                          {subtitle}
                        </p>
                      )}
                    </div>
                  </div>
                  {showCloseButton && (
                    <AdminButton
                      variant="ghost"
                      size="sm"
                      onClick={onClose}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-5 h-5" />
                    </AdminButton>
                  )}
                </div>
              )}

              {/* Content */}
              <div className="p-6">
                {children}
              </div>

              {/* Footer */}
              {footer && (
                <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                  {footer}
                </div>
              )}
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

// Specialized modal variants
export const AdminConfirmModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info';
  loading?: boolean;
}> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'تأیید',
  cancelText = 'انصراف',
  variant = 'info',
  loading = false
}) => {
  const variantColors = {
    danger: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-admin-600'
  };

  const variantButtons = {
    danger: 'danger',
    warning: 'warning',
    info: 'primary'
  } as const;

  return (
    <AdminModal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      title={title}
      footer={
        <>
          <AdminButton
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </AdminButton>
          <AdminButton
            variant={variantButtons[variant]}
            onClick={onConfirm}
            loading={loading}
          >
            {confirmText}
          </AdminButton>
        </>
      }
    >
      <p className={`text-gray-700 ${variantColors[variant]}`}>
        {message}
      </p>
    </AdminModal>
  );
};

export const AdminFormModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  title: string;
  subtitle?: string;
  children: ReactNode;
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  size?: AdminModalProps['size'];
}> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  subtitle,
  children,
  submitText = 'ذخیره',
  cancelText = 'انصراف',
  loading = false,
  size = 'md'
}) => {
  return (
    <AdminModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      subtitle={subtitle}
      size={size}
      footer={
        <>
          <AdminButton
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </AdminButton>
          <AdminButton
            variant="primary"
            onClick={onSubmit}
            loading={loading}
          >
            {submitText}
          </AdminButton>
        </>
      }
    >
      {children}
    </AdminModal>
  );
};

export default AdminModal;
