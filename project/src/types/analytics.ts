// Analytics and tracking types for comprehensive user behavior monitoring

export interface AnalyticsConfig {
  googleAnalyticsId?: string;
  hotjarId?: string;
  facebookPixelId?: string;
  enableDebugMode?: boolean;
  enableGDPRCompliance?: boolean;
  cookieConsentRequired?: boolean;
  dataRetentionDays?: number;
}

export interface UserProperties {
  userId?: string;
  userType: 'guest' | 'registered' | 'premium';
  language: string;
  country: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  isReturningUser: boolean;
  registrationDate?: string;
  loyaltyTier?: string;
  totalOrders?: number;
  totalSpent?: number;
  preferredCategories?: string[];
}

export interface EcommerceEvent {
  event_name: string;
  currency: string;
  value?: number;
  transaction_id?: string;
  items?: EcommerceItem[];
  coupon?: string;
  shipping?: number;
  tax?: number;
  payment_method?: string;
}

export interface EcommerceItem {
  item_id: string;
  item_name: string;
  item_category: string;
  item_category2?: string;
  item_brand?: string;
  item_variant?: string;
  price: number;
  quantity: number;
  currency: string;
  discount?: number;
  affiliation?: string;
  coupon?: string;
  creative_name?: string;
  creative_slot?: string;
  location_id?: string;
  promotion_id?: string;
  promotion_name?: string;
}

export interface PageViewEvent {
  page_title: string;
  page_location: string;
  page_referrer?: string;
  content_group1?: string; // Category
  content_group2?: string; // Subcategory
  content_group3?: string; // Product type
  custom_parameters?: Record<string, any>;
}

export interface SearchEvent {
  search_term: string;
  search_results_count: number;
  search_filters?: Record<string, any>;
  search_sort?: string;
  search_category?: string;
  search_language: string;
}

export interface UserEngagementEvent {
  engagement_time_msec: number;
  session_id: string;
  session_number: number;
  page_title: string;
  scroll_depth?: number;
  video_duration?: number;
  video_percent?: number;
  file_name?: string;
  link_url?: string;
  outbound?: boolean;
}

export interface ConversionEvent {
  event_name: string;
  conversion_value?: number;
  conversion_currency?: string;
  conversion_id?: string;
  funnel_step?: number;
  funnel_name?: string;
  source?: string;
  medium?: string;
  campaign?: string;
  content?: string;
  term?: string;
}

export interface CustomEvent {
  event_name: string;
  event_category: string;
  event_label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
  user_properties?: Partial<UserProperties>;
}

export interface AnalyticsEventData {
  event_name: string;
  timestamp: number;
  session_id: string;
  user_id?: string;
  page_url: string;
  page_title: string;
  user_agent: string;
  referrer?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
  custom_dimensions?: Record<string, any>;
  ecommerce?: EcommerceEvent;
}

export interface UserJourney {
  session_id: string;
  user_id?: string;
  start_time: number;
  end_time?: number;
  pages_visited: string[];
  events: AnalyticsEventData[];
  conversion_events: ConversionEvent[];
  total_engagement_time: number;
  bounce_rate?: number;
  exit_page?: string;
  traffic_source: {
    source: string;
    medium: string;
    campaign?: string;
  };
}

export interface ConversionFunnel {
  funnel_name: string;
  steps: ConversionFunnelStep[];
  total_users: number;
  conversion_rate: number;
  drop_off_points: string[];
}

export interface ConversionFunnelStep {
  step_number: number;
  step_name: string;
  page_url?: string;
  event_name?: string;
  users_entered: number;
  users_completed: number;
  completion_rate: number;
  average_time_spent: number;
  drop_off_rate: number;
}

export interface AnalyticsMetrics {
  page_views: number;
  unique_page_views: number;
  sessions: number;
  users: number;
  new_users: number;
  returning_users: number;
  bounce_rate: number;
  average_session_duration: number;
  pages_per_session: number;
  conversion_rate: number;
  revenue: number;
  transactions: number;
  average_order_value: number;
}

export interface RealtimeAnalytics {
  active_users: number;
  active_sessions: number;
  current_page_views: Record<string, number>;
  top_events: Array<{ event_name: string; count: number }>;
  traffic_sources: Record<string, number>;
  device_breakdown: Record<string, number>;
  geographic_data: Record<string, number>;
}

// Persian-specific analytics events
export interface PersianAnalyticsEvents {
  persian_search: {
    search_term_persian: string;
    search_method: 'typing' | 'voice' | 'suggestion';
    keyboard_layout: 'persian' | 'english' | 'mixed';
  };
  
  rtl_interaction: {
    element_type: string;
    interaction_type: 'click' | 'scroll' | 'swipe';
    rtl_optimized: boolean;
  };
  
  persian_content_engagement: {
    content_type: 'product_name' | 'description' | 'review' | 'category';
    content_language: 'persian' | 'english' | 'mixed';
    reading_time: number;
  };
  
  jalali_date_interaction: {
    date_picker_used: boolean;
    date_format_preference: 'jalali' | 'gregorian';
    calendar_navigation: string;
  };
  
  persian_number_format: {
    number_type: 'price' | 'quantity' | 'date' | 'phone';
    format_preference: 'persian' | 'english';
    user_changed_format: boolean;
  };
}

// GDPR and privacy compliance
export interface PrivacySettings {
  analytics_consent: boolean;
  marketing_consent: boolean;
  functional_consent: boolean;
  consent_timestamp: number;
  consent_version: string;
  ip_anonymization: boolean;
  data_retention_days: number;
  opt_out_requested: boolean;
}

export interface AnalyticsError {
  error_type: 'tracking_failed' | 'consent_required' | 'network_error' | 'configuration_error';
  error_message: string;
  error_timestamp: number;
  page_url: string;
  user_agent: string;
  stack_trace?: string;
}

// Analytics provider interfaces
export interface AnalyticsProvider {
  name: string;
  initialize: (config: AnalyticsConfig) => Promise<void>;
  trackEvent: (event: AnalyticsEventData) => Promise<void>;
  trackPageView: (pageView: PageViewEvent) => Promise<void>;
  trackEcommerce: (ecommerce: EcommerceEvent) => Promise<void>;
  setUserProperties: (properties: UserProperties) => Promise<void>;
  setCustomDimensions: (dimensions: Record<string, any>) => Promise<void>;
  enableDebugMode: (enabled: boolean) => void;
  optOut: () => Promise<void>;
  optIn: () => Promise<void>;
}

// Default analytics configuration
export const DEFAULT_ANALYTICS_CONFIG: AnalyticsConfig = {
  enableDebugMode: process.env.NODE_ENV === 'development',
  enableGDPRCompliance: true,
  cookieConsentRequired: true,
  dataRetentionDays: 365
};

// Persian analytics messages
export const PERSIAN_ANALYTICS_MESSAGES = {
  consent: {
    title: 'رضایت برای استفاده از کوکی‌ها',
    description: 'ما از کوکی‌ها برای بهبود تجربه شما و تحلیل عملکرد سایت استفاده می‌کنیم.',
    accept: 'موافقم',
    decline: 'مخالفم',
    customize: 'تنظیمات',
    learnMore: 'اطلاعات بیشتر'
  },
  privacy: {
    analytics: 'تجزیه و تحلیل',
    marketing: 'بازاریابی',
    functional: 'عملکردی',
    necessary: 'ضروری',
    description: {
      analytics: 'کمک به بهبود عملکرد سایت',
      marketing: 'نمایش تبلیغات مرتبط',
      functional: 'ذخیره تنظیمات شما',
      necessary: 'عملکرد اساسی سایت'
    }
  },
  errors: {
    trackingFailed: 'خطا در ردیابی رویداد',
    consentRequired: 'رضایت کاربر مورد نیاز است',
    networkError: 'خطا در اتصال به شبکه',
    configurationError: 'خطا در تنظیمات تحلیل‌گر'
  }
} as const;

export type AnalyticsEventName = 
  | 'page_view'
  | 'search'
  | 'view_item'
  | 'add_to_cart'
  | 'remove_from_cart'
  | 'view_cart'
  | 'begin_checkout'
  | 'add_payment_info'
  | 'add_shipping_info'
  | 'purchase'
  | 'refund'
  | 'view_item_list'
  | 'select_item'
  | 'view_promotion'
  | 'select_promotion'
  | 'add_to_wishlist'
  | 'share'
  | 'login'
  | 'sign_up'
  | 'generate_lead'
  | 'tutorial_begin'
  | 'tutorial_complete'
  | 'level_up'
  | 'post_score'
  | 'select_content'
  | 'spend_virtual_currency'
  | 'earn_virtual_currency'
  | 'join_group'
  | 'unlock_achievement'
  | 'view_search_results'
  | 'file_download'
  | 'video_start'
  | 'video_progress'
  | 'video_complete'
  | 'scroll'
  | 'click'
  | 'form_start'
  | 'form_submit'
  | 'exception'
  | 'timing_complete'
  | 'custom_event';

export type AnalyticsEventCategory = 
  | 'engagement'
  | 'ecommerce'
  | 'navigation'
  | 'social'
  | 'video'
  | 'download'
  | 'form'
  | 'error'
  | 'performance'
  | 'custom';

export type TrafficSource = 
  | 'direct'
  | 'organic_search'
  | 'paid_search'
  | 'social'
  | 'email'
  | 'referral'
  | 'display'
  | 'affiliate'
  | 'other';

export type DeviceCategory = 
  | 'mobile'
  | 'tablet'
  | 'desktop';

export type UserType = 
  | 'new'
  | 'returning'
  | 'loyal'
  | 'at_risk'
  | 'churned';

export type ConversionType = 
  | 'purchase'
  | 'signup'
  | 'newsletter'
  | 'download'
  | 'contact'
  | 'demo'
  | 'trial'
  | 'custom';
