import React, { useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Eye,
  Edit,
  MessageSquare,
  Shield,
  ShieldOff,
  Crown,
  UserMinus,
  MoreHorizontal,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../common/AdminTable';
import { AdminIconButton } from '../common/AdminButton';
import { AdminCustomer, PERSIAN_CUSTOMER_MESSAGES } from '../../../types/adminCustomer';
import { 
  formatCustomerName, 
  formatCustomerPhone, 
  formatPersianCurrency, 
  formatPersianDate,
  getCustomerStatusColor,
  getCustomerSegmentColor,
  getRiskScoreColor
} from '../../../utils/customerUtils';

interface CustomerTableProps {
  customers: AdminCustomer[];
  loading?: boolean;
  selectedCustomers: string[];
  onSelectCustomer: (customerId: string) => void;
  onSelectAll: (selected: boolean) => void;
  onViewCustomer: (customer: AdminCustomer) => void;
  onEditCustomer: (customer: AdminCustomer) => void;
  onToggleBlock: (customer: AdminCustomer) => void;
  onToggleVip: (customer: AdminCustomer) => void;
  onSendMessage: (customer: AdminCustomer) => void;
  onCreateTicket: (customer: AdminCustomer) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
}

const CustomerTable: React.FC<CustomerTableProps> = ({
  customers,
  loading = false,
  selectedCustomers,
  onSelectCustomer,
  onSelectAll,
  onViewCustomer,
  onEditCustomer,
  onToggleBlock,
  onToggleVip,
  onSendMessage,
  onCreateTicket,
  searchValue = '',
  onSearchChange
}) => {
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const getStatusIcon = (status: AdminCustomer['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'blocked':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'suspended':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return <XCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getRiskIcon = (riskScore: number) => {
    if (riskScore >= 80) return <AlertTriangle className="w-4 h-4 text-red-500" />;
    if (riskScore >= 60) return <AlertTriangle className="w-4 h-4 text-orange-500" />;
    if (riskScore >= 40) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return null;
  };

  const columns: AdminTableColumn<AdminCustomer>[] = [
    {
      key: 'select',
      title: '',
      width: '50px',
      render: (customer: AdminCustomer) => (
        <input
          type="checkbox"
          checked={selectedCustomers.includes(customer.id)}
          onChange={() => onSelectCustomer(customer.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      )
    },
    {
      key: 'customer',
      title: 'مشتری',
      sortable: true,
      render: (customer: AdminCustomer) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
            {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
          </div>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-900">
                {formatCustomerName(customer)}
              </span>
              {customer.isVip && (
                <Crown className="w-4 h-4 text-yellow-500" title="مشتری ویژه" />
              )}
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Mail className="w-3 h-3" />
              <span>{customer.email}</span>
            </div>
            {customer.phone && (
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Phone className="w-3 h-3" />
                <span>{formatCustomerPhone(customer.phone)}</span>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (customer: AdminCustomer) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(customer.status)}
          <AdminTableBadge
            variant={customer.status === 'active' ? 'success' :
                    customer.status === 'blocked' ? 'danger' : 'warning'}
          >
            {PERSIAN_CUSTOMER_MESSAGES.status[customer.status]}
          </AdminTableBadge>
        </div>
      )
    },
    {
      key: 'segment',
      title: 'بخش',
      sortable: true,
      render: (customer: AdminCustomer) => (
        <AdminTableBadge
          variant="info"
        >
          {PERSIAN_CUSTOMER_MESSAGES.segment[customer.segment]}
        </AdminTableBadge>
      )
    },
    {
      key: 'analytics',
      title: 'آمار خرید',
      render: (customer: AdminCustomer) => (
        <div className="flex flex-col text-sm">
          <div className="flex items-center gap-1 text-gray-900">
            <TrendingUp className="w-3 h-3" />
            <span>{customer.analytics.totalOrders} سفارش</span>
          </div>
          <div className="text-gray-600">
            {formatPersianCurrency(customer.analytics.totalSpent)}
          </div>
          <div className="text-xs text-gray-500">
            میانگین: {formatPersianCurrency(customer.analytics.averageOrderValue)}
          </div>
        </div>
      )
    },
    {
      key: 'loyalty',
      title: 'وفاداری',
      render: (customer: AdminCustomer) => (
        <div className="flex flex-col text-sm">
          {customer.loyaltyMember ? (
            <>
              <span className="font-medium text-gray-900">
                {customer.loyaltyMember.tier.persianName}
              </span>
              <span className="text-gray-600">
                {customer.loyaltyMember.points.toLocaleString('fa-IR')} امتیاز
              </span>
            </>
          ) : (
            <span className="text-gray-400">عضو نیست</span>
          )}
        </div>
      )
    },
    {
      key: 'location',
      title: 'موقعیت',
      render: (customer: AdminCustomer) => (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <MapPin className="w-3 h-3" />
          <span>
            {customer.location ?
              `${customer.location.city}, ${customer.location.province}` :
              'نامشخص'
            }
          </span>
        </div>
      )
    },
    {
      key: 'registration',
      title: 'تاریخ عضویت',
      sortable: true,
      render: (customer: AdminCustomer) => (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <Calendar className="w-3 h-3" />
          <span>{formatPersianDate(customer.createdAt)}</span>
        </div>
      )
    },
    {
      key: 'risk',
      title: 'ریسک',
      sortable: true,
      render: (customer: AdminCustomer) => (
        <div className="flex items-center gap-1">
          {getRiskIcon(customer.riskScore)}
          <span className={`text-sm font-medium ${getRiskScoreColor(customer.riskScore)}`}>
            {customer.riskScore}
          </span>
        </div>
      )
    }
  ];

  const handleRowClick = (customer: AdminCustomer, index: number) => {
    onViewCustomer(customer);
  };

  const handleMenuToggle = (customer: AdminCustomer, e: React.MouseEvent) => {
    e.stopPropagation();

    if (actionMenuOpen === customer.id) {
      setActionMenuOpen(null);
      return;
    }

    const button = buttonRefs.current[customer.id];
    if (button) {
      const rect = button.getBoundingClientRect();
      const menuWidth = 200;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let left = rect.left + window.scrollX - menuWidth + rect.width;
      if (left < 10) {
        left = rect.right + window.scrollX + 8;
      }
      if (left + menuWidth > viewportWidth - 10) {
        left = rect.left + window.scrollX - menuWidth - 8;
      }

      let top = rect.bottom + window.scrollY + 4;
      const menuHeight = 350;
      if (top + menuHeight > viewportHeight + window.scrollY - 10) {
        top = rect.top + window.scrollY - menuHeight - 4;
      }

      setMenuPosition({ top, left });
    }

    setActionMenuOpen(customer.id);
  };

  const renderRowActions = (customer: AdminCustomer, index: number) => (
    <>
      <button
        ref={(el) => { buttonRefs.current[customer.id] = el; }}
        onClick={(e) => handleMenuToggle(customer, e)}
        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        title="عملیات"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>

      {actionMenuOpen === customer.id && createPortal(
        <>
          <div
            className="fixed inset-0 z-[9999]"
            onClick={() => setActionMenuOpen(null)}
          />
          <div
            className="fixed bg-white border border-gray-200 rounded-lg shadow-xl py-1 min-w-[200px] z-[9999]"
            style={{
              top: `${menuPosition.top}px`,
              left: `${menuPosition.left}px`
            }}
          >
          <button
            onClick={(e) => {
              e.stopPropagation();
              onViewCustomer(customer);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            {PERSIAN_CUSTOMER_MESSAGES.actions.viewProfile}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditCustomer(customer);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            {PERSIAN_CUSTOMER_MESSAGES.actions.editCustomer}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onSendMessage(customer);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <MessageSquare className="w-4 h-4" />
            {PERSIAN_CUSTOMER_MESSAGES.actions.sendMessage}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onCreateTicket(customer);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <MessageSquare className="w-4 h-4" />
            {PERSIAN_CUSTOMER_MESSAGES.actions.createTicket}
          </button>
          
          <div className="border-t border-gray-100 my-1" />
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleVip(customer);
              setActionMenuOpen(null);
            }}
            className="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            {customer.isVip ? <UserMinus className="w-4 h-4" /> : <Crown className="w-4 h-4" />}
            {customer.isVip ? PERSIAN_CUSTOMER_MESSAGES.actions.removeVip : PERSIAN_CUSTOMER_MESSAGES.actions.makeVip}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleBlock(customer);
              setActionMenuOpen(null);
            }}
            className={`w-full px-4 py-2 text-right text-sm hover:bg-gray-50 flex items-center gap-2 ${
              customer.status === 'blocked' ? 'text-green-700' : 'text-red-700'
            }`}
          >
            {customer.status === 'blocked' ? <Shield className="w-4 h-4" /> : <ShieldOff className="w-4 h-4" />}
            {customer.status === 'blocked' ? PERSIAN_CUSTOMER_MESSAGES.actions.unblockCustomer : PERSIAN_CUSTOMER_MESSAGES.actions.blockCustomer}
          </button>
          </div>
        </>,
        document.body
      )}
    </>
  );

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedCustomers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedCustomers.length} مشتری انتخاب شده
            </span>
            <div className="flex items-center gap-2">
              {/* Bulk action buttons would go here */}
            </div>
          </div>
        </motion.div>
      )}

      {/* Select All Checkbox */}
      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          checked={customers.length > 0 && selectedCustomers.length === customers.length}
          onChange={(e) => onSelectAll(e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span className="text-sm text-gray-600">انتخاب همه</span>
      </div>

      {/* Customers Table */}
      <AdminTable
        columns={columns}
        data={customers}
        loading={loading}
        onRowClick={handleRowClick}
        rowActions={renderRowActions}
        searchable={!!onSearchChange}
        searchValue={searchValue}
        onSearchChange={onSearchChange}
        searchPlaceholder="جستجو در مشتریان..."
        emptyMessage="هیچ مشتری یافت نشد"
        hoverable
        striped
      />
    </div>
  );
};

export default CustomerTable;
