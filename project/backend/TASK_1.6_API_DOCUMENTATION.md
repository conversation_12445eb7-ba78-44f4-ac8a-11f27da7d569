# Task 1.6: Loyalty Program Management API Documentation

## Overview

This document provides comprehensive documentation for the Loyalty Program Management API implemented in Task 1.6. The API includes full CRUD operations for loyalty accounts, point management system, tier progression, reward catalog, analytics, and comprehensive loyalty program features with admin authentication and Persian language support.

## Base URL
```
http://localhost:3001/api/v1/loyalty
```

## Authentication

All loyalty management routes require authentication with JWT token:

```
Authorization: Bearer <jwt_token>
```

**Admin-only endpoints** require admin role (ADMIN or SUPER_ADMIN).

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "پیام فارسی",
  "data": {
    // Response data
  }
}
```

## Error Response Format

```json
{
  "success": false,
  "message": "پیام خطا",
  "code": "ERROR_CODE",
  "errors": [] // Validation errors if applicable
}
```

---

## Loyalty Tiers

The system supports four loyalty tiers with different benefits:

| Tier | Min Points | Max Points | Multiplier | Benefits |
|------|------------|------------|------------|----------|
| **BRONZE** | 0 | 999 | 1x | کسب ۱ امتیاز به ازای هر ۱۰ هزار تومان |
| **SILVER** | 1,000 | 4,999 | 1.5x | کسب ۱.۵ امتیاز + تخفیف ۵٪ |
| **GOLD** | 5,000 | 19,999 | 2x | کسب ۲ امتیاز + تخفیف ۱۰٪ |
| **PLATINUM** | 20,000+ | ∞ | 3x | کسب ۳ امتیاز + تخفیف ۱۵٪ |

---

## API Endpoints

### 1. Loyalty Account Management

#### Get All Loyalty Accounts (Admin Only)

**GET** `/api/v1/loyalty/accounts`

Get all loyalty accounts with advanced filtering and pagination.

##### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | صفحه (پیش‌فرض: 1) | `?page=2` |
| `limit` | number | تعداد نتایج (پیش‌فرض: 20) | `?limit=50` |
| `tier` | string | فیلتر بر اساس سطح | `?tier=GOLD` |
| `minPoints` | number | حداقل امتیاز | `?minPoints=1000` |
| `maxPoints` | number | حداکثر امتیاز | `?maxPoints=5000` |
| `hasTransactions` | boolean | دارای تراکنش | `?hasTransactions=true` |
| `createdFrom` | date | تاریخ شروع ایجاد | `?createdFrom=2024-01-01` |
| `createdTo` | date | تاریخ پایان ایجاد | `?createdTo=2024-12-31` |
| `search` | string | جستجو در نام کاربر | `?search=احمد` |
| `sortBy` | string | مرتب‌سازی | `?sortBy=points` |
| `sortOrder` | string | ترتیب (asc/desc) | `?sortOrder=desc` |
| `include` | string | شامل اطلاعات اضافی | `?include=user,transactions,_count` |

##### Response Example

```json
{
  "success": true,
  "message": "لیست حساب‌های وفاداری با موفقیت دریافت شد",
  "data": {
    "accounts": [
      {
        "id": "clx1234567890",
        "userId": "user123",
        "points": 2500,
        "totalEarned": 5000,
        "totalRedeemed": 2500,
        "tier": "SILVER",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "updatedAt": "2024-06-06T10:30:00.000Z",
        "user": {
          "id": "user123",
          "email": "<EMAIL>",
          "firstName": "احمد",
          "lastName": "محمدی"
        },
        "transactions": [
          {
            "id": "trans123",
            "type": "EARNED",
            "points": 100,
            "description": "خرید محصول",
            "createdAt": "2024-06-06T10:00:00.000Z"
          }
        ],
        "_count": {
          "transactions": 15
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

#### Get Current User's Loyalty Account

**GET** `/api/v1/loyalty/my-account`

Get the authenticated user's loyalty account. Creates account if it doesn't exist.

##### Response Example

```json
{
  "success": true,
  "message": "حساب وفاداری شما با موفقیت دریافت شد",
  "data": {
    "account": {
      "id": "clx1234567890",
      "userId": "user123",
      "points": 2500,
      "totalEarned": 5000,
      "totalRedeemed": 2500,
      "tier": "SILVER",
      "createdAt": "2024-01-15T08:00:00.000Z",
      "updatedAt": "2024-06-06T10:30:00.000Z",
      "user": {
        "id": "user123",
        "email": "<EMAIL>",
        "firstName": "احمد",
        "lastName": "محمدی"
      },
      "transactions": [
        {
          "id": "trans123",
          "type": "EARNED",
          "points": 100,
          "description": "خرید محصول - سفارش GR12345678901",
          "orderId": "order123",
          "expiresAt": "2025-06-06T10:00:00.000Z",
          "createdAt": "2024-06-06T10:00:00.000Z"
        }
      ],
      "_count": {
        "transactions": 15
      }
    }
  }
}
```

#### Create Loyalty Account (Admin Only)

**POST** `/api/v1/loyalty/accounts`

Create a new loyalty account for a user.

##### Request Body

```json
{
  "userId": "user123",
  "initialPoints": 100,
  "tier": "BRONZE"
}
```

##### Response Example

```json
{
  "success": true,
  "message": "حساب وفاداری با موفقیت ایجاد شد",
  "data": {
    "account": {
      "id": "clx1234567891",
      "userId": "user123",
      "points": 100,
      "totalEarned": 100,
      "totalRedeemed": 0,
      "tier": "BRONZE",
      "createdAt": "2024-06-06T12:00:00.000Z",
      "updatedAt": "2024-06-06T12:00:00.000Z",
      "user": {
        "id": "user123",
        "email": "<EMAIL>",
        "firstName": "احمد",
        "lastName": "محمدی"
      },
      "transactions": [],
      "_count": {
        "transactions": 0
      }
    }
  }
}
```

#### Get Loyalty Account by User ID (Admin Only)

**GET** `/api/v1/loyalty/accounts/user/:userId`

Get loyalty account for a specific user.

##### Response Example

```json
{
  "success": true,
  "message": "حساب وفاداری با موفقیت دریافت شد",
  "data": {
    "account": {
      "id": "clx1234567890",
      "userId": "user123",
      "points": 2500,
      "totalEarned": 5000,
      "totalRedeemed": 2500,
      "tier": "SILVER",
      "createdAt": "2024-01-15T08:00:00.000Z",
      "updatedAt": "2024-06-06T10:30:00.000Z",
      "user": {
        "id": "user123",
        "email": "<EMAIL>",
        "firstName": "احمد",
        "lastName": "محمدی"
      },
      "transactions": [
        {
          "id": "trans123",
          "type": "EARNED",
          "points": 100,
          "description": "خرید محصول",
          "createdAt": "2024-06-06T10:00:00.000Z"
        }
      ],
      "_count": {
        "transactions": 15
      }
    }
  }
}
```

### 2. Points Management

#### Earn Points (Admin Only)

**POST** `/api/v1/loyalty/points/earn`

Add points to a user's loyalty account.

##### Request Body

```json
{
  "userId": "user123",
  "points": 100,
  "description": "خرید محصول - سفارش GR12345678901",
  "orderId": "order123",
  "expiresAt": "2025-06-06T10:00:00.000Z"
}
```

##### Response Example

```json
{
  "success": true,
  "message": "امتیاز با موفقیت اضافه شد",
  "data": {
    "transaction": {
      "id": "trans124",
      "accountId": "clx1234567890",
      "type": "EARNED",
      "points": 150,
      "description": "خرید محصول - سفارش GR12345678901",
      "orderId": "order123",
      "expiresAt": "2025-06-06T10:00:00.000Z",
      "createdAt": "2024-06-06T12:00:00.000Z"
    }
  }
}
```

#### Redeem Points

**POST** `/api/v1/loyalty/points/redeem`

Redeem points from user's loyalty account. Users can only redeem their own points.

##### Request Body

```json
{
  "userId": "user123",
  "points": 500,
  "description": "استفاده از جایزه: تخفیف ۵۰ هزار تومانی",
  "orderId": "order124"
}
```

##### Response Example

```json
{
  "success": true,
  "message": "امتیاز با موفقیت استفاده شد",
  "data": {
    "transaction": {
      "id": "trans125",
      "accountId": "clx1234567890",
      "type": "REDEEMED",
      "points": -500,
      "description": "استفاده از جایزه: تخفیف ۵۰ هزار تومانی",
      "orderId": "order124",
      "expiresAt": null,
      "createdAt": "2024-06-06T12:05:00.000Z"
    }
  }
}
```

#### Adjust Points (Admin Only)

**POST** `/api/v1/loyalty/points/adjust`

Manually adjust points for a user (positive or negative adjustment).

##### Request Body

```json
{
  "userId": "user123",
  "points": -200,
  "description": "تصحیح خطای سیستم"
}
```

##### Response Example

```json
{
  "success": true,
  "message": "امتیاز با موفقیت تعدیل شد",
  "data": {
    "transaction": {
      "id": "trans126",
      "accountId": "clx1234567890",
      "type": "ADJUSTMENT",
      "points": -200,
      "description": "تعدیل امتیاز توسط مدیر - تصحیح خطای سیستم",
      "orderId": null,
      "expiresAt": null,
      "createdAt": "2024-06-06T12:10:00.000Z"
    }
  }
}
```

#### Expire Points (Admin Only)

**POST** `/api/v1/loyalty/points/expire`

Process point expiration for all accounts. Typically called by cron job.

##### Response Example

```json
{
  "success": true,
  "message": "امتیازات منقضی شده با موفقیت پردازش شد",
  "data": {
    "expiredTransactions": 25,
    "totalPointsExpired": 1500
  }
}
```

#### Calculate Points for Order

**GET** `/api/v1/loyalty/points/calculate`

Calculate points that would be earned for a given order amount and tier.

##### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `amount` | number | Yes | مبلغ سفارش (ریال) |
| `tier` | string | No | سطح وفاداری (پیش‌فرض: BRONZE) |

##### Response Example

```json
{
  "success": true,
  "message": "امتیاز قابل کسب محاسبه شد",
  "data": {
    "orderAmount": 500000,
    "tier": "SILVER",
    "points": 75,
    "basePoints": 50,
    "multiplier": 1.5
  }
}
```

### 3. Transaction Management

#### Get Loyalty Transactions

**GET** `/api/v1/loyalty/transactions`

Get loyalty transactions with filtering. Users can only see their own transactions unless admin.

##### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | صفحه | `?page=1` |
| `limit` | number | تعداد نتایج | `?limit=20` |
| `accountId` | string | شناسه حساب | `?accountId=clx123` |
| `userId` | string | شناسه کاربر (admin only) | `?userId=user123` |
| `type` | string | نوع تراکنش | `?type=EARNED` |
| `orderId` | string | شناسه سفارش | `?orderId=order123` |
| `dateFrom` | date | تاریخ شروع | `?dateFrom=2024-01-01` |
| `dateTo` | date | تاریخ پایان | `?dateTo=2024-12-31` |
| `sortBy` | string | مرتب‌سازی | `?sortBy=createdAt` |
| `sortOrder` | string | ترتیب | `?sortOrder=desc` |

##### Response Example

```json
{
  "success": true,
  "message": "تراکنش‌های امتیاز با موفقیت دریافت شد",
  "data": {
    "transactions": [
      {
        "id": "trans123",
        "accountId": "clx1234567890",
        "type": "EARNED",
        "points": 100,
        "description": "خرید محصول - سفارش GR12345678901",
        "orderId": "order123",
        "expiresAt": "2025-06-06T10:00:00.000Z",
        "createdAt": "2024-06-06T10:00:00.000Z",
        "account": {
          "id": "clx1234567890",
          "userId": "user123",
          "tier": "SILVER",
          "user": {
            "id": "user123",
            "firstName": "احمد",
            "lastName": "محمدی",
            "email": "<EMAIL>"
          }
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 4. Analytics & Statistics (Admin Only)

#### Get Loyalty Statistics

**GET** `/api/v1/loyalty/statistics`

Get comprehensive loyalty program analytics and statistics.

##### Response Example

```json
{
  "success": true,
  "message": "آمار وفاداری با موفقیت دریافت شد",
  "data": {
    "statistics": {
      "totalAccounts": 1250,
      "activeAccounts": 890,
      "totalPointsIssued": 125000,
      "totalPointsRedeemed": 45000,
      "averagePointsPerAccount": 640,
      "tierDistribution": [
        {
          "tier": "BRONZE",
          "count": 750,
          "percentage": 60
        },
        {
          "tier": "SILVER",
          "count": 350,
          "percentage": 28
        },
        {
          "tier": "GOLD",
          "count": 120,
          "percentage": 9.6
        },
        {
          "tier": "PLATINUM",
          "count": 30,
          "percentage": 2.4
        }
      ],
      "topMembers": [
        {
          "userId": "user123",
          "firstName": "احمد",
          "lastName": "محمدی",
          "email": "<EMAIL>",
          "points": 25000,
          "totalEarned": 50000,
          "tier": "PLATINUM"
        }
      ],
      "pointsActivity": [
        {
          "date": "2024-06-06",
          "earned": 1500,
          "redeemed": 800
        },
        {
          "date": "2024-06-05",
          "earned": 2200,
          "redeemed": 600
        }
      ]
    }
  }
}
```

### 5. Tier Management

#### Get Tier Benefits

**GET** `/api/v1/loyalty/tiers/:tier/benefits`

Get benefits and details for a specific loyalty tier.

##### Response Example

```json
{
  "success": true,
  "message": "مزایای سطح وفاداری با موفقیت دریافت شد",
  "data": {
    "benefits": {
      "multiplier": 2,
      "minPoints": 5000,
      "maxPoints": 19999,
      "benefits": [
        "کسب ۲ امتیاز به ازای هر ۱۰ هزار تومان خرید",
        "تخفیف ۱۰٪ در تمام خریدها",
        "ارسال رایگان برای تمام خریدها",
        "هدیه تولد ویژه",
        "مشاوره رایگان پوست"
      ]
    }
  }
}
```

### 6. Rewards Catalog

#### Get Rewards Catalog

**GET** `/api/v1/loyalty/rewards`

Get available rewards with filtering and pagination.

##### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | صفحه | `?page=1` |
| `limit` | number | تعداد نتایج | `?limit=20` |
| `type` | string | نوع جایزه | `?type=DISCOUNT` |
| `isActive` | boolean | فعال بودن | `?isActive=true` |
| `minPointsCost` | number | حداقل امتیاز | `?minPointsCost=100` |
| `maxPointsCost` | number | حداکثر امتیاز | `?maxPointsCost=1000` |
| `hasStock` | boolean | موجود بودن | `?hasStock=true` |
| `validOnly` | boolean | معتبر بودن | `?validOnly=true` |
| `search` | string | جستجو | `?search=تخفیف` |
| `sortBy` | string | مرتب‌سازی | `?sortBy=pointsCost` |
| `sortOrder` | string | ترتیب | `?sortOrder=asc` |

##### Response Example

```json
{
  "success": true,
  "message": "لیست جوایز با موفقیت دریافت شد",
  "data": {
    "rewards": [
      {
        "id": "reward_1",
        "title": "تخفیف ۵۰ هزار تومانی",
        "description": "کد تخفیف ۵۰ هزار تومانی برای خرید بعدی",
        "pointsCost": 500,
        "type": "DISCOUNT",
        "value": 50000,
        "isActive": true,
        "stock": null,
        "image": null,
        "validUntil": null,
        "terms": [
          "حداقل خرید ۲۰۰ هزار تومان",
          "قابل استفاده تا ۳۰ روز",
          "غیرقابل تجمیع با سایر تخفیف‌ها"
        ],
        "createdAt": "2024-06-06T12:00:00.000Z",
        "updatedAt": "2024-06-06T12:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 4,
      "totalPages": 1
    }
  }
}
```

#### Get Available Rewards for Current User

**GET** `/api/v1/loyalty/my-rewards`

Get rewards that the current user can afford with their points.

##### Response Example

```json
{
  "success": true,
  "message": "جوایز قابل دسترس شما با موفقیت دریافت شد",
  "data": {
    "rewards": [
      {
        "id": "reward_1",
        "title": "تخفیف ۵۰ هزار تومانی",
        "description": "کد تخفیف ۵۰ هزار تومانی برای خرید بعدی",
        "pointsCost": 500,
        "type": "DISCOUNT",
        "value": 50000,
        "isActive": true,
        "terms": [
          "حداقل خرید ۲۰۰ هزار تومان",
          "قابل استفاده تا ۳۰ روز"
        ]
      }
    ]
  }
}
```

#### Redeem Reward

**POST** `/api/v1/loyalty/rewards/redeem`

Redeem a reward using loyalty points.

##### Request Body

```json
{
  "userId": "user123",
  "rewardId": "reward_1",
  "quantity": 1
}
```

##### Response Example

```json
{
  "success": true,
  "message": "جایزه با موفقیت دریافت شد",
  "data": {
    "redemption": {
      "id": "redemption_1234567890",
      "userId": "user123",
      "rewardId": "reward_1",
      "pointsUsed": 500,
      "quantity": 1,
      "status": "PENDING",
      "redeemedAt": "2024-06-06T12:15:00.000Z",
      "user": {
        "id": "user123",
        "firstName": "احمد",
        "lastName": "محمدی",
        "email": "<EMAIL>"
      },
      "reward": {
        "id": "reward_1",
        "title": "تخفیف ۵۰ هزار تومانی",
        "description": "کد تخفیف ۵۰ هزار تومانی برای خرید بعدی",
        "pointsCost": 500,
        "type": "DISCOUNT",
        "value": 50000
      }
    }
  }
}
```

---

## Transaction Types

| Type | Description | Points | Expiration |
|------|-------------|--------|------------|
| **EARNED** | امتیاز کسب شده از خرید | مثبت | ۳۶۵ روز |
| **REDEEMED** | امتیاز استفاده شده | منفی | - |
| **BONUS** | امتیاز جایزه | مثبت | ۳۶۵ روز |
| **ADJUSTMENT** | تعدیل توسط مدیر | مثبت/منفی | - |
| **EXPIRED** | امتیاز منقضی شده | منفی | - |

---

## Reward Types

| Type | Description | Example |
|------|-------------|---------|
| **DISCOUNT** | کد تخفیف | تخفیف ۵۰ هزار تومانی |
| **PRODUCT** | محصول رایگان | کیت نمونه محصولات |
| **SHIPPING** | ارسال رایگان | ارسال رایگان سفارش بعدی |
| **EXPERIENCE** | تجربه ویژه | مشاوره تخصصی پوست |

---

## Error Codes

| Code | Description |
|------|-------------|
| `LOYALTY_ACCOUNT_NOT_FOUND` | حساب وفاداری یافت نشد |
| `LOYALTY_ACCOUNT_EXISTS` | حساب وفاداری قبلاً ایجاد شده است |
| `INSUFFICIENT_POINTS` | امتیاز کافی برای این عملیات وجود ندارد |
| `REWARD_NOT_FOUND` | جایزه یافت نشد |
| `REWARD_INACTIVE` | این جایزه در حال حاضر فعال نیست |
| `INSUFFICIENT_STOCK` | موجودی این جایزه کافی نیست |
| `REWARD_EXPIRED` | مهلت استفاده از این جایزه به پایان رسیده است |
| `INVALID_TIER` | سطح وفاداری نامعتبر است |
| `ORDER_AMOUNT_REQUIRED` | مبلغ سفارش الزامی است |
| `ADMIN_ID_REQUIRED` | شناسه مدیر یافت نشد |
| `GET_LOYALTY_ACCOUNTS_ERROR` | خطا در دریافت لیست حساب‌های وفاداری |
| `CREATE_LOYALTY_ACCOUNT_ERROR` | خطا در ایجاد حساب وفاداری |
| `EARN_POINTS_ERROR` | خطا در کسب امتیاز |
| `REDEEM_POINTS_ERROR` | خطا در استفاده از امتیاز |
| `ADJUST_POINTS_ERROR` | خطا در تعدیل امتیاز |
| `EXPIRE_POINTS_ERROR` | خطا در انقضای امتیازات |
| `GET_TRANSACTIONS_ERROR` | خطا در دریافت تراکنش‌های امتیاز |
| `GET_LOYALTY_STATS_ERROR` | خطا در دریافت آمار وفاداری |
| `GET_REWARDS_ERROR` | خطا در دریافت لیست جوایز |
| `GET_AVAILABLE_REWARDS_ERROR` | خطا در دریافت جوایز قابل دسترس |
| `REDEEM_REWARD_ERROR` | خطا در دریافت جایزه |

---

## Integration Examples

### JavaScript/Fetch

```javascript
// Get current user's loyalty account
const getMyLoyaltyAccount = async () => {
  const response = await fetch('/api/v1/loyalty/my-account', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Redeem points
const redeemPoints = async (points, description) => {
  const response = await fetch('/api/v1/loyalty/points/redeem', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      points,
      description
    })
  });
  return response.json();
};

// Get available rewards
const getMyRewards = async () => {
  const response = await fetch('/api/v1/loyalty/my-rewards', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Redeem reward
const redeemReward = async (rewardId, quantity = 1) => {
  const response = await fetch('/api/v1/loyalty/rewards/redeem', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      rewardId,
      quantity
    })
  });
  return response.json();
};
```

### cURL Examples

```bash
# Get current user's loyalty account
curl -X GET http://localhost:3001/api/v1/loyalty/my-account \
  -H "Authorization: Bearer YOUR_TOKEN"

# Earn points (admin only)
curl -X POST http://localhost:3001/api/v1/loyalty/points/earn \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user123",
    "points": 100,
    "description": "خرید محصول - سفارش GR12345678901",
    "orderId": "order123"
  }'

# Redeem points
curl -X POST http://localhost:3001/api/v1/loyalty/points/redeem \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "points": 500,
    "description": "استفاده از جایزه: تخفیف ۵۰ هزار تومانی"
  }'

# Get loyalty statistics (admin only)
curl -X GET http://localhost:3001/api/v1/loyalty/statistics \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Get tier benefits
curl -X GET http://localhost:3001/api/v1/loyalty/tiers/GOLD/benefits \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get available rewards
curl -X GET http://localhost:3001/api/v1/loyalty/my-rewards \
  -H "Authorization: Bearer YOUR_TOKEN"

# Redeem reward
curl -X POST http://localhost:3001/api/v1/loyalty/rewards/redeem \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "rewardId": "reward_1",
    "quantity": 1
  }'
```

---

## Business Logic

### Point Earning Rules

- **Base Rate:** 1 امتیاز به ازای هر ۱۰ هزار تومان خرید
- **Tier Multipliers:**
  - BRONZE: 1x
  - SILVER: 1.5x
  - GOLD: 2x
  - PLATINUM: 3x
- **Bonus Activities:**
  - اولین خرید: ۱۰۰ امتیاز
  - نوشتن نظر: ۲۰ امتیاز
  - معرفی دوست: ۲۰۰ امتیاز
  - تولد: ۱۰۰ امتیاز
  - اشتراک‌گذاری: ۱۰ امتیاز

### Point Expiration

- امتیازات کسب شده پس از ۳۶۵ روز منقضی می‌شوند
- امتیازات جایزه و تعدیل شده منقضی نمی‌شوند
- سیستم به صورت خودکار امتیازات منقضی شده را پردازش می‌کند

### Tier Progression

- ارتقای سطح بر اساس مجموع امتیازات کسب شده
- تنزل سطح فقط در صورت استفاده از امتیازات زیاد
- محاسبه سطح در هر تراکنش به‌روزرسانی می‌شود

---

## Performance Considerations

### Database Optimization

- ایندکس‌گذاری مناسب روی فیلدهای پرکاربرد
- پیجینیشن برای مدیریت داده‌های حجیم
- کش کردن آمار وفاداری برای ۵-۱۰ دقیقه
- استفاده از aggregation queries برای آمار

### Caching Strategy

- کش کردن اطلاعات حساب وفاداری کاربران فعال
- کش کردن کاتالوگ جوایز
- کش کردن مزایای سطوح وفاداری
- استفاده از Redis برای کش‌های موقت

---

## Security Features

- احراز هویت JWT برای تمام endpoints
- کنترل دسترسی مبتنی بر نقش
- محدودیت دسترسی کاربران به اطلاعات خود
- اعتبارسنجی جامع ورودی‌ها
- لاگ‌گیری تمام عملیات مهم

---

## Testing

### Unit Tests

```bash
# Run loyalty service tests
npm test -- --testPathPattern=loyaltyService

# Run reward service tests
npm test -- --testPathPattern=rewardService

# Run loyalty controller tests
npm test -- --testPathPattern=loyaltyController
```

### Integration Tests

```bash
# Run loyalty API integration tests
npm test -- --testPathPattern=loyalty.integration

# Test with real database
npm run test:integration
```
```
