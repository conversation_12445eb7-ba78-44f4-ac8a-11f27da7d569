import { Review, ReviewImage, ReviewVote, ReviewResponse, ReviewModerationStatus, User, Product } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import LoyaltyService from './loyaltyService';

// Types for review management
export interface ReviewWithDetails extends Review {
  user: {
    id: string;
    firstName: string;
    lastName: string;
    avatar: string | null;
  };
  product: {
    id: string;
    name: string;
    slug: string;
  };
  images: ReviewImage[];
  votes: ReviewVote[];
  responses: ReviewResponse[];
  _count: {
    votes: number;
  };
}

export interface CreateReviewData {
  productId: string;
  userId: string;
  rating: number;
  title?: string;
  content: string;
  pros?: string[];
  cons?: string[];
  isRecommended?: boolean;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
  images?: string[];
}

export interface UpdateReviewData {
  rating?: number;
  title?: string;
  content?: string;
  pros?: string[];
  cons?: string[];
  isRecommended?: boolean;
  skinType?: string;
  ageRange?: string;
  usageDuration?: string;
}

export interface ReviewFilters {
  productId?: string;
  userId?: string;
  rating?: number;
  isVerified?: boolean;
  isApproved?: boolean;
  moderationStatus?: ReviewModerationStatus;
  isRecommended?: boolean;
  skinType?: string;
  ageRange?: string;
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
  page?: number;
  limit?: number;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedPurchasePercentage: number;
  recommendationPercentage: number;
}

export interface ReviewAnalytics {
  totalReviews: number;
  pendingReviews: number;
  approvedReviews: number;
  rejectedReviews: number;
  averageRating: number;
  reviewsThisMonth: number;
  reviewsLastMonth: number;
  topRatedProducts: Array<{
    productId: string;
    productName: string;
    averageRating: number;
    reviewCount: number;
  }>;
  recentReviews: ReviewWithDetails[];
}

class ReviewService {
  // Create a new review
  static async createReview(data: CreateReviewData): Promise<ReviewWithDetails> {
    try {
      // Check if user already reviewed this product
      const existingReview = await prisma.review.findUnique({
        where: {
          productId_userId: {
            productId: data.productId,
            userId: data.userId,
          },
        },
      });

      if (existingReview) {
        throw new AppError('شما قبلاً برای این محصول نظر ثبت کرده‌اید', 400, 'REVIEW_ALREADY_EXISTS');
      }

      // Verify product exists
      const product = await prisma.product.findUnique({
        where: { id: data.productId },
      });

      if (!product) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      // Check if user has purchased this product (for verification)
      const hasPurchased = await prisma.orderItem.findFirst({
        where: {
          productId: data.productId,
          order: {
            userId: data.userId,
            status: 'DELIVERED',
          },
        },
      });

      const isVerified = !!hasPurchased;

      // Create review with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the review
        const review = await tx.review.create({
          data: {
            productId: data.productId,
            userId: data.userId,
            rating: data.rating,
            title: data.title,
            content: data.content,
            pros: data.pros || [],
            cons: data.cons || [],
            isRecommended: data.isRecommended ?? true,
            isVerified,
            skinType: data.skinType,
            ageRange: data.ageRange,
            usageDuration: data.usageDuration,
            moderationStatus: 'PENDING',
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            images: true,
            votes: true,
            responses: true,
            _count: {
              select: {
                votes: true,
              },
            },
          },
        });

        // Add review images if provided
        if (data.images && data.images.length > 0) {
          await tx.reviewImage.createMany({
            data: data.images.map((url, index) => ({
              reviewId: review.id,
              url,
              sortOrder: index,
            })),
          });
        }

        // Award loyalty points for review
        try {
          await LoyaltyService.earnPoints({
            userId: data.userId,
            points: 20, // Points for writing a review
            description: 'امتیاز بابت نوشتن نظر',
          });
        } catch (error) {
          logger.warn('Failed to award loyalty points for review:', error);
        }

        return review;
      });

      logger.info('Review created successfully:', {
        reviewId: result.id,
        productId: data.productId,
        userId: data.userId,
        rating: data.rating,
      });

      return result;
    } catch (error) {
      logger.error('Error creating review:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد نظر', 500, 'CREATE_REVIEW_ERROR');
    }
  }

  // Get reviews with filtering and pagination
  static async getReviews(filters: ReviewFilters = {}): Promise<{
    reviews: ReviewWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        productId,
        userId,
        rating,
        isVerified,
        isApproved,
        moderationStatus,
        isRecommended,
        skinType,
        ageRange,
        search,
        sortBy = 'newest',
        page = 1,
        limit = 10,
      } = filters;

      // Build where clause
      const where: any = {};

      if (productId) where.productId = productId;
      if (userId) where.userId = userId;
      if (rating) where.rating = rating;
      if (typeof isVerified === 'boolean') where.isVerified = isVerified;
      if (typeof isApproved === 'boolean') where.isApproved = isApproved;
      if (moderationStatus) where.moderationStatus = moderationStatus;
      if (typeof isRecommended === 'boolean') where.isRecommended = isRecommended;
      if (skinType) where.skinType = skinType;
      if (ageRange) where.ageRange = ageRange;

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } },
          { user: { firstName: { contains: search, mode: 'insensitive' } } },
          { user: { lastName: { contains: search, mode: 'insensitive' } } },
        ];
      }

      // Build order by clause
      let orderBy: any = {};
      switch (sortBy) {
        case 'newest':
          orderBy = { createdAt: 'desc' };
          break;
        case 'oldest':
          orderBy = { createdAt: 'asc' };
          break;
        case 'rating_high':
          orderBy = { rating: 'desc' };
          break;
        case 'rating_low':
          orderBy = { rating: 'asc' };
          break;
        case 'helpful':
          orderBy = { helpfulCount: 'desc' };
          break;
        default:
          orderBy = { createdAt: 'desc' };
      }

      const skip = (page - 1) * limit;

      const [reviews, total] = await Promise.all([
        prisma.review.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            images: {
              orderBy: { sortOrder: 'asc' },
            },
            votes: true,
            responses: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                  },
                },
              },
              orderBy: { createdAt: 'asc' },
            },
            _count: {
              select: {
                votes: true,
              },
            },
          },
          orderBy,
          skip,
          take: limit,
        }),
        prisma.review.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        reviews,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error('Error getting reviews:', error);
      throw new AppError('خطا در دریافت نظرات', 500, 'GET_REVIEWS_ERROR');
    }
  }

  // Get review by ID
  static async getReviewById(id: string): Promise<ReviewWithDetails> {
    try {
      const review = await prisma.review.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          votes: true,
          responses: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          _count: {
            select: {
              votes: true,
            },
          },
        },
      });

      if (!review) {
        throw new AppError('نظر یافت نشد', 404, 'REVIEW_NOT_FOUND');
      }

      return review;
    } catch (error) {
      logger.error('Error getting review by ID:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت نظر', 500, 'GET_REVIEW_ERROR');
    }
  }

  // Update review (only by owner)
  static async updateReview(id: string, userId: string, data: UpdateReviewData): Promise<ReviewWithDetails> {
    try {
      // Check if review exists and belongs to user
      const existingReview = await prisma.review.findUnique({
        where: { id },
      });

      if (!existingReview) {
        throw new AppError('نظر یافت نشد', 404, 'REVIEW_NOT_FOUND');
      }

      if (existingReview.userId !== userId) {
        throw new AppError('شما مجاز به ویرایش این نظر نیستید', 403, 'UNAUTHORIZED_REVIEW_UPDATE');
      }

      // Update review
      const updatedReview = await prisma.review.update({
        where: { id },
        data: {
          ...data,
          moderationStatus: 'PENDING', // Reset moderation status on update
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          votes: true,
          responses: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          _count: {
            select: {
              votes: true,
            },
          },
        },
      });

      logger.info('Review updated successfully:', {
        reviewId: id,
        userId,
      });

      return updatedReview;
    } catch (error) {
      logger.error('Error updating review:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی نظر', 500, 'UPDATE_REVIEW_ERROR');
    }
  }

  // Delete review (only by owner or admin)
  static async deleteReview(id: string, userId: string, isAdmin: boolean = false): Promise<void> {
    try {
      // Check if review exists
      const existingReview = await prisma.review.findUnique({
        where: { id },
      });

      if (!existingReview) {
        throw new AppError('نظر یافت نشد', 404, 'REVIEW_NOT_FOUND');
      }

      // Check authorization
      if (!isAdmin && existingReview.userId !== userId) {
        throw new AppError('شما مجاز به حذف این نظر نیستید', 403, 'UNAUTHORIZED_REVIEW_DELETE');
      }

      // Delete review (cascade will handle related records)
      await prisma.review.delete({
        where: { id },
      });

      logger.info('Review deleted successfully:', {
        reviewId: id,
        deletedBy: userId,
        isAdmin,
      });
    } catch (error) {
      logger.error('Error deleting review:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در حذف نظر', 500, 'DELETE_REVIEW_ERROR');
    }
  }

  // Vote on review helpfulness
  static async voteReview(reviewId: string, userId: string, isHelpful: boolean): Promise<void> {
    try {
      // Check if review exists
      const review = await prisma.review.findUnique({
        where: { id: reviewId },
      });

      if (!review) {
        throw new AppError('نظر یافت نشد', 404, 'REVIEW_NOT_FOUND');
      }

      // Check if user already voted
      const existingVote = await prisma.reviewVote.findUnique({
        where: {
          reviewId_userId: {
            reviewId,
            userId,
          },
        },
      });

      await prisma.$transaction(async (tx) => {
        if (existingVote) {
          // Update existing vote
          await tx.reviewVote.update({
            where: { id: existingVote.id },
            data: { isHelpful },
          });

          // Update review counters
          const oldVoteValue = existingVote.isHelpful ? 1 : -1;
          const newVoteValue = isHelpful ? 1 : -1;
          const counterDiff = newVoteValue - oldVoteValue;

          if (counterDiff !== 0) {
            await tx.review.update({
              where: { id: reviewId },
              data: {
                helpfulCount: isHelpful ? { increment: 1 } : { decrement: 1 },
                unhelpfulCount: isHelpful ? { decrement: 1 } : { increment: 1 },
              },
            });
          }
        } else {
          // Create new vote
          await tx.reviewVote.create({
            data: {
              reviewId,
              userId,
              isHelpful,
            },
          });

          // Update review counters
          await tx.review.update({
            where: { id: reviewId },
            data: {
              helpfulCount: isHelpful ? { increment: 1 } : undefined,
              unhelpfulCount: !isHelpful ? { increment: 1 } : undefined,
            },
          });
        }
      });

      logger.info('Review vote recorded:', {
        reviewId,
        userId,
        isHelpful,
      });
    } catch (error) {
      logger.error('Error voting on review:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ثبت رأی', 500, 'VOTE_REVIEW_ERROR');
    }
  }

  // Add response to review (admin only)
  static async addReviewResponse(
    reviewId: string,
    userId: string,
    content: string,
    isOfficial: boolean = false
  ): Promise<ReviewResponse> {
    try {
      // Check if review exists
      const review = await prisma.review.findUnique({
        where: { id: reviewId },
      });

      if (!review) {
        throw new AppError('نظر یافت نشد', 404, 'REVIEW_NOT_FOUND');
      }

      // Create response
      const response = await prisma.reviewResponse.create({
        data: {
          reviewId,
          userId,
          content,
          isOfficial,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
      });

      logger.info('Review response added:', {
        reviewId,
        responseId: response.id,
        userId,
        isOfficial,
      });

      return response;
    } catch (error) {
      logger.error('Error adding review response:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در افزودن پاسخ', 500, 'ADD_RESPONSE_ERROR');
    }
  }

  // Moderate review (admin only)
  static async moderateReview(
    id: string,
    moderationStatus: ReviewModerationStatus,
    moderationNotes?: string
  ): Promise<ReviewWithDetails> {
    try {
      const updatedReview = await prisma.review.update({
        where: { id },
        data: {
          moderationStatus,
          moderationNotes,
          isApproved: moderationStatus === 'APPROVED',
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          votes: true,
          responses: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          _count: {
            select: {
              votes: true,
            },
          },
        },
      });

      logger.info('Review moderated:', {
        reviewId: id,
        moderationStatus,
        moderationNotes,
      });

      return updatedReview;
    } catch (error) {
      logger.error('Error moderating review:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در تعدیل نظر', 500, 'MODERATE_REVIEW_ERROR');
    }
  }

  // Get review statistics for a product
  static async getProductReviewStats(productId: string): Promise<ReviewStats> {
    try {
      const [reviews, ratingCounts] = await Promise.all([
        prisma.review.findMany({
          where: {
            productId,
            isApproved: true,
          },
          select: {
            rating: true,
            isVerified: true,
            isRecommended: true,
          },
        }),
        prisma.review.groupBy({
          by: ['rating'],
          where: {
            productId,
            isApproved: true,
          },
          _count: {
            rating: true,
          },
        }),
      ]);

      const totalReviews = reviews.length;
      const averageRating = totalReviews > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
        : 0;

      const ratingDistribution = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
      };

      ratingCounts.forEach(({ rating, _count }) => {
        ratingDistribution[rating as keyof typeof ratingDistribution] = _count.rating;
      });

      const verifiedReviews = reviews.filter(r => r.isVerified).length;
      const verifiedPurchasePercentage = totalReviews > 0
        ? (verifiedReviews / totalReviews) * 100
        : 0;

      const recommendedReviews = reviews.filter(r => r.isRecommended).length;
      const recommendationPercentage = totalReviews > 0
        ? (recommendedReviews / totalReviews) * 100
        : 0;

      return {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10,
        ratingDistribution,
        verifiedPurchasePercentage: Math.round(verifiedPurchasePercentage),
        recommendationPercentage: Math.round(recommendationPercentage),
      };
    } catch (error) {
      logger.error('Error getting product review stats:', error);
      throw new AppError('خطا در دریافت آمار نظرات', 500, 'GET_REVIEW_STATS_ERROR');
    }
  }

  // Get review analytics for admin dashboard
  static async getReviewAnalytics(): Promise<ReviewAnalytics> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      const [
        totalReviews,
        pendingReviews,
        approvedReviews,
        rejectedReviews,
        averageRatingResult,
        reviewsThisMonth,
        reviewsLastMonth,
        topRatedProducts,
        recentReviews,
      ] = await Promise.all([
        prisma.review.count(),
        prisma.review.count({ where: { moderationStatus: 'PENDING' } }),
        prisma.review.count({ where: { moderationStatus: 'APPROVED' } }),
        prisma.review.count({ where: { moderationStatus: 'REJECTED' } }),
        prisma.review.aggregate({
          _avg: { rating: true },
          where: { isApproved: true },
        }),
        prisma.review.count({
          where: {
            createdAt: { gte: startOfMonth },
          },
        }),
        prisma.review.count({
          where: {
            createdAt: {
              gte: startOfLastMonth,
              lte: endOfLastMonth,
            },
          },
        }),
        prisma.review.groupBy({
          by: ['productId'],
          where: { isApproved: true },
          _avg: { rating: true },
          _count: { rating: true },
          orderBy: { _avg: { rating: 'desc' } },
          take: 5,
        }),
        prisma.review.findMany({
          take: 10,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            images: true,
            votes: true,
            responses: true,
            _count: {
              select: {
                votes: true,
              },
            },
          },
        }),
      ]);

      // Get product names for top rated products
      const productIds = topRatedProducts.map(p => p.productId);
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true, name: true },
      });

      const topRatedProductsWithNames = topRatedProducts.map(item => {
        const product = products.find(p => p.id === item.productId);
        return {
          productId: item.productId,
          productName: product?.name || 'نامشخص',
          averageRating: Math.round((item._avg.rating || 0) * 10) / 10,
          reviewCount: item._count.rating,
        };
      });

      return {
        totalReviews,
        pendingReviews,
        approvedReviews,
        rejectedReviews,
        averageRating: Math.round((averageRatingResult._avg.rating || 0) * 10) / 10,
        reviewsThisMonth,
        reviewsLastMonth,
        topRatedProducts: topRatedProductsWithNames,
        recentReviews,
      };
    } catch (error) {
      logger.error('Error getting review analytics:', error);
      throw new AppError('خطا در دریافت آمار نظرات', 500, 'GET_ANALYTICS_ERROR');
    }
  }
}

export default ReviewService;
