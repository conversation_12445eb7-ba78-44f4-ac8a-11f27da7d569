import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingBag,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  RefreshCw,
  Calendar,
  CreditCard,
  MapPin
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import AdminTable, { AdminTableColumn } from '../common/AdminTable';
import { AdminCustomer } from '../../../types/adminCustomer';
import { formatPersianCurrency, formatPersianDate } from '../../../utils/customerUtils';

interface CustomerOrder {
  id: string;
  orderNumber: string;
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
  total: number;
  itemsCount: number;
  paymentMethod: string;
  shippingAddress: string;
  trackingNumber?: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
    image: string;
  }[];
}

interface CustomerOrderHistoryProps {
  customer: AdminCustomer;
  onViewOrder: (orderId: string) => void;
}

const CustomerOrderHistory: React.FC<CustomerOrderHistoryProps> = ({
  customer,
  onViewOrder
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'all' | '30d' | '90d' | '1y'>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Mock order data - in real app, this would come from API
  const generateMockOrders = (): CustomerOrder[] => {
    const statuses: CustomerOrder['status'][] = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'];
    const paymentMethods = ['کارت بانکی', 'پرداخت در محل', 'کیف پول', 'اقساط'];
    
    return Array.from({ length: 15 }, (_, i) => ({
      id: `order-${i + 1}`,
      orderNumber: `GR${1000 + i}`,
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      total: Math.floor(Math.random() * 1000000) + 50000,
      itemsCount: Math.floor(Math.random() * 5) + 1,
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      shippingAddress: 'تهران، خیابان ولیعصر، پلاک ۱۲۳',
      trackingNumber: Math.random() > 0.5 ? `TRK${Math.floor(Math.random() * 1000000)}` : undefined,
      items: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, j) => ({
        id: `item-${j}`,
        name: `محصول شماره ${j + 1}`,
        quantity: Math.floor(Math.random() * 3) + 1,
        price: Math.floor(Math.random() * 200000) + 50000,
        image: '/placeholder-product.jpg'
      }))
    }));
  };

  const orders = generateMockOrders();

  const getStatusIcon = (status: CustomerOrder['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'processing':
        return <Package className="w-4 h-4 text-blue-500" />;
      case 'shipped':
        return <Truck className="w-4 h-4 text-purple-500" />;
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'returned':
        return <RefreshCw className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: CustomerOrder['status']) => {
    switch (status) {
      case 'pending': return 'در انتظار پردازش';
      case 'processing': return 'در حال پردازش';
      case 'shipped': return 'ارسال شده';
      case 'delivered': return 'تحویل داده شده';
      case 'cancelled': return 'لغو شده';
      case 'returned': return 'مرجوع شده';
      default: return 'نامشخص';
    }
  };

  const getStatusColor = (status: CustomerOrder['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'returned': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOrders = orders.filter(order => {
    if (selectedStatus !== 'all' && order.status !== selectedStatus) return false;
    
    if (selectedPeriod !== 'all') {
      const orderDate = new Date(order.date);
      const now = new Date();
      const diffDays = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (selectedPeriod) {
        case '30d': return diffDays <= 30;
        case '90d': return diffDays <= 90;
        case '1y': return diffDays <= 365;
      }
    }
    
    return true;
  });

  const totalOrderValue = filteredOrders.reduce((sum, order) => sum + order.total, 0);
  const averageOrderValue = filteredOrders.length > 0 ? totalOrderValue / filteredOrders.length : 0;

  const columns: AdminTableColumn<CustomerOrder>[] = [
    {
      key: 'orderNumber',
      title: 'شماره سفارش',
      sortable: true,
      render: (_, order) => (
        <div className="font-medium text-blue-600">
          {order.orderNumber}
        </div>
      )
    },
    {
      key: 'date',
      title: 'تاریخ',
      sortable: true,
      render: (_, order) => (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <Calendar className="w-3 h-3" />
          <span>{formatPersianDate(order.date)}</span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      render: (_, order) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(order.status)}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
            {getStatusText(order.status)}
          </span>
        </div>
      )
    },
    {
      key: 'items',
      title: 'تعداد اقلام',
      render: (_, order) => (
        <span className="text-sm text-gray-600">
          {order.itemsCount} قلم
        </span>
      )
    },
    {
      key: 'total',
      title: 'مبلغ کل',
      sortable: true,
      render: (_, order) => (
        <div className="font-medium text-gray-900">
          {formatPersianCurrency(order.total)}
        </div>
      )
    },
    {
      key: 'payment',
      title: 'روش پرداخت',
      render: (_, order) => (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <CreditCard className="w-3 h-3" />
          <span>{order.paymentMethod}</span>
        </div>
      )
    }
  ];

  const renderRowActions = (order: CustomerOrder) => (
    <AdminButton
      variant="ghost"
      size="sm"
      icon={Eye}
      onClick={() => onViewOrder(order.id)}
    >
      مشاهده
    </AdminButton>
  );

  return (
    <div className="space-y-6">
      {/* Order Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل سفارشات</p>
              <p className="text-2xl font-bold text-gray-900">
                {filteredOrders.length.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">ارزش کل خرید</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPersianCurrency(totalOrderValue)}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Package className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">میانگین سفارش</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPersianCurrency(averageOrderValue)}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Truck className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">تحویل موفق</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round((filteredOrders.filter(o => o.status === 'delivered').length / filteredOrders.length) * 100)}%
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Filters */}
      <AdminCard title="فیلترها">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              بازه زمانی
            </label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">همه زمان‌ها</option>
              <option value="30d">۳۰ روز گذشته</option>
              <option value="90d">۹۰ روز گذشته</option>
              <option value="1y">یک سال گذشته</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وضعیت سفارش
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">همه وضعیت‌ها</option>
              <option value="pending">در انتظار پردازش</option>
              <option value="processing">در حال پردازش</option>
              <option value="shipped">ارسال شده</option>
              <option value="delivered">تحویل داده شده</option>
              <option value="cancelled">لغو شده</option>
              <option value="returned">مرجوع شده</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Orders Table */}
      <AdminCard title="تاریخچه سفارشات">
        <AdminTable
          columns={columns}
          data={filteredOrders}
          rowActions={renderRowActions}
          emptyMessage="هیچ سفارشی یافت نشد"
          hoverable
          striped
        />
      </AdminCard>
    </div>
  );
};

export default CustomerOrderHistory;
