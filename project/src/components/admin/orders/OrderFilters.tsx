import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Filter, 
  X, 
  Calendar, 
  DollarSign, 
  User, 
  Package,
  ChevronDown,
  Search
} from 'lucide-react';
import { OrderFilters as OrderFiltersType } from '../../../types/adminOrder';
import { Order } from '../../../types/checkout';
import AdminButton from '../common/AdminButton';

interface OrderFiltersProps {
  filters: OrderFiltersType;
  onFiltersChange: (filters: OrderFiltersType) => void;
  onClearFilters: () => void;
  className?: string;
}

const OrderFilters: React.FC<OrderFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const statusOptions: { value: Order['status']; label: string; color: string }[] = [
    { value: 'pending', label: 'در انتظار تأیید', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'confirmed', label: 'تأیید شده', color: 'bg-blue-100 text-blue-800' },
    { value: 'processing', label: 'در حال پردازش', color: 'bg-purple-100 text-purple-800' },
    { value: 'shipped', label: 'ارسال شده', color: 'bg-indigo-100 text-indigo-800' },
    { value: 'delivered', label: 'تحویل داده شده', color: 'bg-green-100 text-green-800' },
    { value: 'cancelled', label: 'لغو شده', color: 'bg-red-100 text-red-800' }
  ];

  const priorityOptions: { value: Order['priority']; label: string; color: string }[] = [
    { value: 'low', label: 'کم', color: 'bg-gray-100 text-gray-800' },
    { value: 'normal', label: 'عادی', color: 'bg-blue-100 text-blue-800' },
    { value: 'high', label: 'بالا', color: 'bg-orange-100 text-orange-800' },
    { value: 'urgent', label: 'فوری', color: 'bg-red-100 text-red-800' }
  ];

  const paymentMethodOptions = [
    { value: 'card', label: 'پرداخت آنلاین' },
    { value: 'wallet', label: 'کیف پول' },
    { value: 'cash_on_delivery', label: 'پرداخت در محل' }
  ];

  const shippingMethodOptions = [
    { value: 'express', label: 'ارسال فوری' },
    { value: 'fast', label: 'ارسال سریع' },
    { value: 'standard', label: 'ارسال عادی' },
    { value: 'economy', label: 'ارسال اقتصادی' }
  ];

  const handleStatusChange = (status: Order['status']) => {
    const currentStatuses = filters.status || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];
    
    onFiltersChange({
      ...filters,
      status: newStatuses.length > 0 ? newStatuses : undefined
    });
  };

  const handlePriorityChange = (priority: Order['priority']) => {
    const currentPriorities = filters.priority || [];
    const newPriorities = currentPriorities.includes(priority)
      ? currentPriorities.filter(p => p !== priority)
      : [...currentPriorities, priority];
    
    onFiltersChange({
      ...filters,
      priority: newPriorities.length > 0 ? newPriorities : undefined
    });
  };

  const handlePaymentMethodChange = (method: string) => {
    const currentMethods = filters.paymentMethod || [];
    const newMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];
    
    onFiltersChange({
      ...filters,
      paymentMethod: newMethods.length > 0 ? newMethods : undefined
    });
  };

  const handleShippingMethodChange = (method: string) => {
    const currentMethods = filters.shippingMethod || [];
    const newMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];
    
    onFiltersChange({
      ...filters,
      shippingMethod: newMethods.length > 0 ? newMethods : undefined
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.status?.length) count++;
    if (filters.priority?.length) count++;
    if (filters.dateRange) count++;
    if (filters.customerSearch) count++;
    if (filters.orderNumber) count++;
    if (filters.minAmount || filters.maxAmount) count++;
    if (filters.paymentMethod?.length) count++;
    if (filters.shippingMethod?.length) count++;
    if (filters.hasRefund !== undefined) count++;
    if (filters.isRush !== undefined) count++;
    if (filters.isGift !== undefined) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-500" />
          <span className="font-medium text-gray-900">فیلترها</span>
          {activeFiltersCount > 0 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {activeFiltersCount}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {activeFiltersCount > 0 && (
            <AdminButton
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              icon={X}
            >
              پاک کردن همه
            </AdminButton>
          )}
          
          <AdminButton
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            icon={ChevronDown}
            className={`transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          >
            {isExpanded ? 'بستن' : 'باز کردن'}
          </AdminButton>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-wrap gap-2">
          {statusOptions.map(status => (
            <button
              key={status.value}
              onClick={() => handleStatusChange(status.value)}
              className={`
                inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-all
                ${filters.status?.includes(status.value) 
                  ? status.color + ' ring-2 ring-offset-1 ring-blue-500' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }
              `}
            >
              {status.label}
            </button>
          ))}
        </div>
      </div>

      {/* Expanded Filters */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-6">
              {/* Search Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    جستجوی مشتری
                  </label>
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="نام، ایمیل یا شماره تلفن"
                      value={filters.customerSearch || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        customerSearch: e.target.value || undefined
                      })}
                      className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    شماره سفارش
                  </label>
                  <input
                    type="text"
                    placeholder="GR123456789"
                    value={filters.orderNumber || ''}
                    onChange={(e) => onFiltersChange({
                      ...filters,
                      orderNumber: e.target.value || undefined
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar className="inline w-4 h-4 ml-1" />
                  بازه زمانی
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">از تاریخ</label>
                    <input
                      type="date"
                      value={filters.dateRange?.start || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        dateRange: {
                          start: e.target.value,
                          end: filters.dateRange?.end || ''
                        }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">تا تاریخ</label>
                    <input
                      type="date"
                      value={filters.dateRange?.end || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        dateRange: {
                          start: filters.dateRange?.start || '',
                          end: e.target.value
                        }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="inline w-4 h-4 ml-1" />
                  محدوده مبلغ (تومان)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">حداقل مبلغ</label>
                    <input
                      type="number"
                      placeholder="0"
                      value={filters.minAmount || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        minAmount: e.target.value ? Number(e.target.value) : undefined
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">حداکثر مبلغ</label>
                    <input
                      type="number"
                      placeholder="10000000"
                      value={filters.maxAmount || ''}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        maxAmount: e.target.value ? Number(e.target.value) : undefined
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Priority Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اولویت
                </label>
                <div className="flex flex-wrap gap-2">
                  {priorityOptions.map(priority => (
                    <button
                      key={priority.value}
                      onClick={() => handlePriorityChange(priority.value)}
                      className={`
                        inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-all
                        ${filters.priority?.includes(priority.value) 
                          ? priority.color + ' ring-2 ring-offset-1 ring-blue-500' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      {priority.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  روش پرداخت
                </label>
                <div className="flex flex-wrap gap-2">
                  {paymentMethodOptions.map(method => (
                    <button
                      key={method.value}
                      onClick={() => handlePaymentMethodChange(method.value)}
                      className={`
                        inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-all
                        ${filters.paymentMethod?.includes(method.value) 
                          ? 'bg-blue-100 text-blue-800 ring-2 ring-offset-1 ring-blue-500' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      {method.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Shipping Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  روش ارسال
                </label>
                <div className="flex flex-wrap gap-2">
                  {shippingMethodOptions.map(method => (
                    <button
                      key={method.value}
                      onClick={() => handleShippingMethodChange(method.value)}
                      className={`
                        inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-all
                        ${filters.shippingMethod?.includes(method.value) 
                          ? 'bg-blue-100 text-blue-800 ring-2 ring-offset-1 ring-blue-500' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      {method.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Special Flags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  فیلترهای ویژه
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.hasRefund === true}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        hasRefund: e.target.checked ? true : undefined
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 ml-2"
                    />
                    <span className="text-sm text-gray-700">دارای درخواست مرجوعی</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.isRush === true}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        isRush: e.target.checked ? true : undefined
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 ml-2"
                    />
                    <span className="text-sm text-gray-700">سفارشات فوری</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.isGift === true}
                      onChange={(e) => onFiltersChange({
                        ...filters,
                        isGift: e.target.checked ? true : undefined
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 ml-2"
                    />
                    <span className="text-sm text-gray-700">سفارشات هدیه</span>
                  </label>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OrderFilters;
