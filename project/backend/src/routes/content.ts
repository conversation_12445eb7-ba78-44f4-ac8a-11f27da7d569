import { Router } from 'express';
import { authenticate, adminOnly, optionalAuth } from '../middleware/auth';
import { ContentController } from '../controllers/contentController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// Public routes (with optional authentication for personalization)

// Get published banners for public display
router.get('/banners/public', optionalAuth, async (req, res, next) => {
  try {
    // Override query to only show published and active banners
    req.query = {
      ...req.query,
      status: 'published',
      isActive: 'true',
      limit: '50' // Allow more banners for public display
    };
    await ContentController.getBanners(req, res, next);
  } catch (error) {
    next(error);
  }
});

// Get published promotions for public display
router.get('/promotions/public', optionalAuth, async (req, res, next) => {
  try {
    req.query = {
      ...req.query,
      status: 'published',
      isActive: 'true',
      limit: '20'
    };
    await ContentController.getPromotions(req, res, next);
  } catch (error) {
    next(error);
  }
});

// Get published pages for public display
router.get('/pages/public', optionalAuth, async (req, res, next) => {
  try {
    req.query = {
      ...req.query,
      status: 'published',
      isActive: 'true',
      limit: '100'
    };
    await ContentController.getPages(req, res, next);
  } catch (error) {
    next(error);
  }
});

// Get page by slug (public)
router.get('/pages/slug/:slug', optionalAuth, ContentController.getPageBySlug);

// Admin-only routes (require authentication and admin role)

// Banner Management
router.get('/admin/banners', authenticate, adminOnly, ContentController.getBanners);
router.get('/admin/banners/:id', authenticate, adminOnly, ContentController.getBannerById);
router.post('/admin/banners', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.createBanner() is implemented
  ContentController.createBanner
);
router.put('/admin/banners/:id', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.updateBanner() is implemented
  ContentController.updateBanner
);
router.delete('/admin/banners/:id', authenticate, adminOnly, ContentController.deleteBanner);

// Promotion Management
router.get('/admin/promotions', authenticate, adminOnly, ContentController.getPromotions);
router.get('/admin/promotions/:id', authenticate, adminOnly, ContentController.getPromotionById);
router.post('/admin/promotions', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.createPromotion() is implemented
  ContentController.createPromotion
);
router.put('/admin/promotions/:id', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.updatePromotion() is implemented
  ContentController.updatePromotion
);
router.delete('/admin/promotions/:id', authenticate, adminOnly, ContentController.deletePromotion);

// Newsletter Management
router.get('/admin/newsletters', authenticate, adminOnly, ContentController.getNewsletters);
router.get('/admin/newsletters/:id', authenticate, adminOnly, ContentController.getNewsletterById);
router.post('/admin/newsletters', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.createNewsletter() is implemented
  ContentController.createNewsletter
);
router.put('/admin/newsletters/:id', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.updateNewsletter() is implemented
  ContentController.updateNewsletter
);
router.delete('/admin/newsletters/:id', authenticate, adminOnly, ContentController.deleteNewsletter);

// Page Content Management
router.get('/admin/pages', authenticate, adminOnly, ContentController.getPages);
router.get('/admin/pages/:id', authenticate, adminOnly, ContentController.getPageById);
router.post('/admin/pages', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.createPage() is implemented
  ContentController.createPage
);
router.put('/admin/pages/:id', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.updatePage() is implemented
  ContentController.updatePage
);
router.delete('/admin/pages/:id', authenticate, adminOnly, ContentController.deletePage);

// Media Management
router.get('/admin/media', authenticate, adminOnly, ContentController.getMediaItems);
router.post('/admin/media', 
  authenticate, 
  adminOnly,
  // Add validation here when ValidationSets.createMedia() is implemented
  ContentController.createMediaItem
);
router.delete('/admin/media/:id', authenticate, adminOnly, ContentController.deleteMediaItem);

// Analytics
router.get('/admin/analytics', authenticate, adminOnly, ContentController.getContentAnalytics);

export default router;
