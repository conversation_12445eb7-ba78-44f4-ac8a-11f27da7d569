import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Activity,
  Plus,
  Minus,
  Gift,
  Star,
  Calendar,
  User,
  Filter,
  Download,
  Search,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminTableColumn } from '../../../components/admin/common/AdminTable';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminLoyalty } from '../../../hooks/useAdminLoyalty';
import { 
  AdminPointTransaction, 
  PointTransactionFilters, 
  PointAdjustmentFormData,
  POINT_TRANSACTION_SOURCES,
  POINT_TRANSACTION_STATUSES
} from '../../../types/adminLoyalty';
import { formatNumber as formatPersianNumber, formatPersianDate } from '../../../utils/formatters';

const PointTransactionsPage: React.FC = () => {
  const { 
    transactions, 
    members, 
    loading, 
    error, 
    transactionFilters, 
    setTransactionFilters,
    adjustMemberPoints
  } = useAdminLoyalty();
  
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string>('');
  
  const [adjustmentData, setAdjustmentData] = useState<PointAdjustmentFormData>({
    memberId: '',
    points: 0,
    type: 'add',
    reason: '',
    description: '',
    expiryDate: '',
    notifyMember: true
  });

  const handlePointAdjustment = async () => {
    try {
      await adjustMemberPoints(adjustmentData);
      setShowAdjustModal(false);
      resetAdjustmentForm();
    } catch (error) {
      // Error handled by hook
    }
  };

  const resetAdjustmentForm = () => {
    setAdjustmentData({
      memberId: '',
      points: 0,
      type: 'add',
      reason: '',
      description: '',
      expiryDate: '',
      notifyMember: true
    });
  };

  const openAdjustModal = (memberId?: string) => {
    if (memberId) {
      setAdjustmentData(prev => ({ ...prev, memberId }));
    }
    setShowAdjustModal(true);
  };

  const getTransactionIcon = (transaction: AdminPointTransaction) => {
    switch (transaction.type) {
      case 'earned':
        return <Plus className="w-4 h-4 text-green-600" />;
      case 'redeemed':
        return <Minus className="w-4 h-4 text-red-600" />;
      case 'bonus':
        return <Gift className="w-4 h-4 text-purple-600" />;
      case 'expired':
        return <Clock className="w-4 h-4 text-gray-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusIcon = (status: AdminPointTransaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      case 'reversed':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTransactionColor = (transaction: AdminPointTransaction) => {
    if (transaction.type === 'earned' || transaction.type === 'bonus') {
      return 'border-green-200 bg-green-50';
    } else if (transaction.type === 'redeemed') {
      return 'border-red-200 bg-red-50';
    } else {
      return 'border-gray-200 bg-gray-50';
    }
  };

  const formatPoints = (points: number, type: string) => {
    const sign = type === 'redeemed' ? '' : '+';
    return `${sign}${formatPersianNumber(Math.abs(points))}`;
  };

  const columns: AdminTableColumn<AdminPointTransaction>[] = [
    {
      key: 'createdAt',
      title: 'تاریخ',
      sortable: true,
      render: (transaction) => (
        <div className="text-sm">
          <p className="font-medium text-gray-900">
            {formatPersianDate(transaction.createdAt)}
          </p>
          <p className="text-gray-500">
            {new Date(transaction.createdAt).toLocaleTimeString('fa-IR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'مشتری',
      render: (transaction) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{transaction.customerInfo.name}</p>
            <p className="text-sm text-gray-500">{transaction.customerInfo.tier}</p>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'نوع تراکنش',
      render: (transaction) => (
        <div className="flex items-center gap-2">
          {getTransactionIcon(transaction)}
          <div>
            <p className="font-medium text-gray-900">
              {transaction.type === 'earned' ? 'کسب امتیاز' :
               transaction.type === 'redeemed' ? 'استفاده امتیاز' :
               transaction.type === 'bonus' ? 'جایزه' : 'انقضا'}
            </p>
            <p className="text-sm text-gray-500">
              {POINT_TRANSACTION_SOURCES[transaction.metadata.source]}
            </p>
          </div>
        </div>
      )
    },
    {
      key: 'points',
      title: 'امتیاز',
      sortable: true,
      render: (transaction) => (
        <div className="text-center">
          <p className={`font-bold text-lg ${
            transaction.type === 'redeemed' ? 'text-red-600' : 'text-green-600'
          }`}>
            {formatPoints(transaction.points, transaction.type)}
          </p>
        </div>
      )
    },
    {
      key: 'description',
      title: 'توضیحات',
      render: (transaction) => (
        <div>
          <p className="text-sm text-gray-900">{transaction.description}</p>
          {transaction.orderId && (
            <p className="text-xs text-gray-500">سفارش: {transaction.orderId}</p>
          )}
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      render: (transaction) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(transaction.status)}
          <span className="text-sm">
            {POINT_TRANSACTION_STATUSES[transaction.status]}
          </span>
        </div>
      )
    },
    {
      key: 'admin',
      title: 'مدیر',
      render: (transaction) => (
        <div className="text-sm">
          {transaction.adminInfo ? (
            <>
              <p className="font-medium text-gray-900">{transaction.adminInfo.processedBy}</p>
              <p className="text-gray-500">دستی</p>
            </>
          ) : (
            <span className="text-gray-400">سیستم</span>
          )}
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={() => window.location.reload()}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  // Safety check for transactions data
  if (!transactions || transactions.length === 0) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">هیچ تراکنشی یافت نشد</p>
            <AdminButton onClick={() => setShowAdjustModal(true)}>
              تعدیل امتیاز
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const totalEarned = transactions
    .filter(t => t.type === 'earned' || t.type === 'bonus')
    .reduce((sum, t) => sum + t.points, 0);

  const totalRedeemed = transactions
    .filter(t => t.type === 'redeemed')
    .reduce((sum, t) => sum + Math.abs(t.points), 0);

  const thisMonthTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.createdAt);
    const now = new Date();
    return transactionDate.getMonth() === now.getMonth() && 
           transactionDate.getFullYear() === now.getFullYear();
  });

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Activity className="w-8 h-8 text-admin-600" />
              تراکنش‌های امتیاز
            </h1>
            <p className="text-gray-600 mt-1">
              مشاهده و مدیریت تمام تراکنش‌های امتیاز اعضا
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              icon={Download}
            >
              خروجی Excel
            </AdminButton>
            <AdminButton
              variant="primary"
              icon={Plus}
              onClick={() => openAdjustModal()}
            >
              تعدیل امتیاز
            </AdminButton>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">کل تراکنش‌ها</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(transactions.length)}
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">امتیاز کسب شده</p>
                <p className="text-2xl font-bold text-green-600">
                  +{formatPersianNumber(totalEarned)}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">امتیاز استفاده شده</p>
                <p className="text-2xl font-bold text-red-600">
                  -{formatPersianNumber(totalRedeemed)}
                </p>
              </div>
              <TrendingDown className="w-8 h-8 text-red-500" />
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">این ماه</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPersianNumber(thisMonthTransactions.length)}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-purple-500" />
            </div>
          </AdminCard>
        </div>

        {/* Filters */}
        <AdminCard title="فیلترها" icon={Filter}>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                جستجو
              </label>
              <input
                type="text"
                value={transactionFilters.search || ''}
                onChange={(e) => setTransactionFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام مشتری یا توضیحات..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع تراکنش
              </label>
              <select
                value={transactionFilters.type?.[0] || ''}
                onChange={(e) => setTransactionFilters(prev => ({ 
                  ...prev, 
                  type: e.target.value ? [e.target.value as any] : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="">همه انواع</option>
                <option value="earned">کسب امتیاز</option>
                <option value="redeemed">استفاده امتیاز</option>
                <option value="bonus">جایزه</option>
                <option value="expired">انقضا</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                منبع
              </label>
              <select
                value={transactionFilters.source?.[0] || ''}
                onChange={(e) => setTransactionFilters(prev => ({ 
                  ...prev, 
                  source: e.target.value ? [e.target.value as any] : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="">همه منابع</option>
                <option value="purchase">خرید</option>
                <option value="manual">دستی</option>
                <option value="bonus">جایزه</option>
                <option value="referral">معرفی</option>
                <option value="review">نظر</option>
                <option value="birthday">تولد</option>
                <option value="redemption">استفاده</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وضعیت
              </label>
              <select
                value={transactionFilters.status?.[0] || ''}
                onChange={(e) => setTransactionFilters(prev => ({ 
                  ...prev, 
                  status: e.target.value ? [e.target.value as any] : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              >
                <option value="">همه وضعیت‌ها</option>
                <option value="completed">تکمیل شده</option>
                <option value="pending">در انتظار</option>
                <option value="failed">ناموفق</option>
                <option value="cancelled">لغو شده</option>
                <option value="reversed">برگشت داده شده</option>
              </select>
            </div>

            <div className="flex items-end">
              <AdminButton
                variant="outline"
                onClick={() => setTransactionFilters({})}
                className="w-full"
              >
                پاک کردن فیلترها
              </AdminButton>
            </div>
          </div>
        </AdminCard>

        {/* Transactions Table */}
        <AdminCard title="لیست تراکنش‌ها" icon={Activity}>
          <AdminTable
            columns={columns}
            data={transactions}
            loading={loading}
            emptyMessage="هیچ تراکنشی یافت نشد"
            searchable={false} // We have custom search
          />
        </AdminCard>

        {/* Point Adjustment Modal */}
        <AdminFormModal
          isOpen={showAdjustModal}
          onClose={() => setShowAdjustModal(false)}
          onSubmit={handlePointAdjustment}
          title="تعدیل امتیاز عضو"
          subtitle="امتیاز عضو را به صورت دستی تعدیل کنید"
          submitText="اعمال تعدیل"
          size="md"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                انتخاب عضو *
              </label>
              <select
                value={adjustmentData.memberId}
                onChange={(e) => setAdjustmentData(prev => ({ ...prev, memberId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                required
              >
                <option value="">انتخاب کنید...</option>
                {members.map(member => (
                  <option key={member.id} value={member.id}>
                    {member.customerInfo.name} - {formatPersianNumber(member.points)} امتیاز
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع تعدیل *
                </label>
                <select
                  value={adjustmentData.type}
                  onChange={(e) => setAdjustmentData(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                >
                  <option value="add">افزودن امتیاز</option>
                  <option value="subtract">کسر امتیاز</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مقدار امتیاز *
                </label>
                <input
                  type="number"
                  value={adjustmentData.points}
                  onChange={(e) => setAdjustmentData(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                  min="1"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                دلیل تعدیل *
              </label>
              <input
                type="text"
                value={adjustmentData.reason}
                onChange={(e) => setAdjustmentData(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: جبران خطای سیستم"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                توضیحات تکمیلی
              </label>
              <textarea
                value={adjustmentData.description}
                onChange={(e) => setAdjustmentData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                rows={3}
                placeholder="توضیحات بیشتر در مورد دلیل تعدیل..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تاریخ انقضا (اختیاری)
              </label>
              <input
                type="date"
                value={adjustmentData.expiryDate}
                onChange={(e) => setAdjustmentData(prev => ({ ...prev, expiryDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
              />
            </div>

            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={adjustmentData.notifyMember}
                  onChange={(e) => setAdjustmentData(prev => ({ ...prev, notifyMember: e.target.checked }))}
                  className="rounded border-gray-300 text-admin-600 focus:ring-admin-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  اطلاع‌رسانی به عضو
                </span>
              </label>
            </div>
          </div>
        </AdminFormModal>
      </div>
    </AdminLayout>
  );
};

export default PointTransactionsPage;
