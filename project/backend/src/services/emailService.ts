import nodemailer from 'nodemailer';
import { config } from '../config';
import { logger } from '../config/logger';
import { AppError } from '../middleware/errorHandler';

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface VerificationEmailData {
  firstName: string;
  lastName: string;
  verificationUrl: string;
}

export interface PasswordResetEmailData {
  firstName: string;
  lastName: string;
  resetUrl: string;
}

export interface WelcomeEmailData {
  firstName: string;
  lastName: string;
}

export class EmailService {
  private static transporter: nodemailer.Transporter | null = null;

  // Initialize email transporter
  static async initialize(): Promise<void> {
    try {
      if (!config.email.smtp.user || !config.email.smtp.pass) {
        logger.warn('Email service not configured - SMTP credentials missing');
        return;
      }

      this.transporter = nodemailer.createTransport({
        host: config.email.smtp.host,
        port: config.email.smtp.port,
        secure: config.email.smtp.secure,
        auth: {
          user: config.email.smtp.user,
          pass: config.email.smtp.pass,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });

      // Verify connection
      if (this.transporter) {
        await this.transporter.verify();
      }
      logger.info('✅ Email service initialized successfully');
    } catch (error) {
      logger.error('❌ Email service initialization failed:', error);
      this.transporter = null;
    }
  }

  // Send email
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter) {
        logger.warn('Email service not available - skipping email send');
        return false;
      }

      const mailOptions = {
        from: config.email.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${options.to}:`, result.messageId);
      return true;
    } catch (error) {
      logger.error(`Email sending failed to ${options.to}:`, error);
      return false;
    }
  }

  // Send verification email
  static async sendVerificationEmail(
    email: string,
    data: VerificationEmailData
  ): Promise<boolean> {
    const subject = 'تأیید ایمیل - گلو رویا';
    const html = this.generateVerificationEmailTemplate(data);
    const text = `سلام ${data.firstName} ${data.lastName}،\n\nبرای تأیید ایمیل خود روی لینک زیر کلیک کنید:\n${data.verificationUrl}\n\nتیم گلو رویا`;

    return await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  // Send password reset email
  static async sendPasswordResetEmail(
    email: string,
    data: PasswordResetEmailData
  ): Promise<boolean> {
    const subject = 'بازیابی رمز عبور - گلو رویا';
    const html = this.generatePasswordResetEmailTemplate(data);
    const text = `سلام ${data.firstName} ${data.lastName}،\n\nبرای بازیابی رمز عبور خود روی لینک زیر کلیک کنید:\n${data.resetUrl}\n\nتیم گلو رویا`;

    return await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  // Send welcome email
  static async sendWelcomeEmail(
    email: string,
    data: WelcomeEmailData
  ): Promise<boolean> {
    const subject = 'خوش آمدید به گلو رویا';
    const html = this.generateWelcomeEmailTemplate(data);
    const text = `سلام ${data.firstName} ${data.lastName}،\n\nبه گلو رویا خوش آمدید!\n\nتیم گلو رویا`;

    return await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  // Generate verification email template
  private static generateVerificationEmailTemplate(data: VerificationEmailData): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="fa">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأیید ایمیل</title>
        <style>
          body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; text-align: right; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌟 گلو رویا</h1>
            <p>تأیید ایمیل شما</p>
          </div>
          <div class="content">
            <h2>سلام ${data.firstName} ${data.lastName} عزیز،</h2>
            <p>از اینکه به خانواده گلو رویا پیوستید خوشحالیم!</p>
            <p>برای تکمیل فرآیند ثبت‌نام و تأیید ایمیل خود، لطفاً روی دکمه زیر کلیک کنید:</p>
            <div style="text-align: center;">
              <a href="${data.verificationUrl}" class="button">تأیید ایمیل</a>
            </div>
            <p><strong>نکته:</strong> این لینک تا ۲۴ ساعت معتبر است.</p>
            <p>اگر شما این درخواست را نداده‌اید، لطفاً این ایمیل را نادیده بگیرید.</p>
          </div>
          <div class="footer">
            <p>با تشکر،<br>تیم گلو رویا</p>
            <p>این ایمیل به صورت خودکار ارسال شده است.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate password reset email template
  private static generatePasswordResetEmailTemplate(data: PasswordResetEmailData): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="fa">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>بازیابی رمز عبور</title>
        <style>
          body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; text-align: right; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #ff6b6b; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 گلو رویا</h1>
            <p>بازیابی رمز عبور</p>
          </div>
          <div class="content">
            <h2>سلام ${data.firstName} ${data.lastName} عزیز،</h2>
            <p>درخواست بازیابی رمز عبور برای حساب کاربری شما دریافت شد.</p>
            <p>برای تنظیم رمز عبور جدید، روی دکمه زیر کلیک کنید:</p>
            <div style="text-align: center;">
              <a href="${data.resetUrl}" class="button">بازیابی رمز عبور</a>
            </div>
            <div class="warning">
              <strong>⚠️ نکات امنیتی:</strong>
              <ul>
                <li>این لینک تا ۱ ساعت معتبر است</li>
                <li>اگر این درخواست را نداده‌اید، حساب شما در خطر است</li>
                <li>رمز عبور جدید را با کسی به اشتراک نگذارید</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>با تشکر،<br>تیم گلو رویا</p>
            <p>این ایمیل به صورت خودکار ارسال شده است.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate welcome email template
  private static generateWelcomeEmailTemplate(data: WelcomeEmailData): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="fa">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خوش آمدید</title>
        <style>
          body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; text-align: right; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .features { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 گلو رویا</h1>
            <p>خوش آمدید!</p>
          </div>
          <div class="content">
            <h2>سلام ${data.firstName} ${data.lastName} عزیز،</h2>
            <p>به خانواده بزرگ گلو رویا خوش آمدید!</p>
            <p>ما خوشحالیم که شما را در جمع علاقه‌مندان به مراقبت از پوست داریم.</p>
            
            <div class="features">
              <h3>🌟 امکانات ویژه شما:</h3>
              <ul>
                <li>دسترسی به جدیدترین محصولات مراقبت از پوست</li>
                <li>مشاوره رایگان با متخصصان</li>
                <li>تخفیف‌های ویژه اعضا</li>
                <li>ارسال رایگان برای سفارش‌های بالای ۵۰۰ هزار تومان</li>
              </ul>
            </div>
            
            <p>برای شروع خرید، به وب‌سایت ما مراجعه کنید و از تخفیف ۱۰٪ ویژه اعضای جدید استفاده کنید.</p>
          </div>
          <div class="footer">
            <p>با تشکر،<br>تیم گلو رویا</p>
            <p>این ایمیل به صورت خودکار ارسال شده است.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export default EmailService;
