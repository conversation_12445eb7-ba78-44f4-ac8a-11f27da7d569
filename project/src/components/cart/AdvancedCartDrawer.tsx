import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Plus,
  Minus,
  ShoppingBag,
  Bookmark,
  Trash2,
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  Star,
  Gift
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { formatPrice, formatNumber } from '../../utils/formatters';
import { useCart } from '../../context/CartContext';

interface AdvancedCartDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckout: () => void;
}

const AdvancedCartDrawer: React.FC<AdvancedCartDrawerProps> = ({
  isOpen,
  onClose,
  onCheckout
}) => {
  const {
    items,
    totalItems,
    totalPrice,
    updateQuantity,
    removeItem,
    clearCart
  } = useCart();

  const [showSaveReason, setShowSaveReason] = useState<string | null>(null);

  const handleSaveForLater = (productId: number, variantKey?: string) => {
    // For now, just remove the item - we can enhance this later
    removeItem(productId, variantKey);
    setShowSaveReason(null);
  };

  const formatCartItemName = (item: any): string => {
    let name = item.product.name;

    if (item.selectedVariants && Object.keys(item.selectedVariants).length > 0) {
      const variantNames = Object.values(item.selectedVariants)
        .map((variant: any) => variant.name)
        .join(' - ');
      name += ` (${variantNames})`;
    }

    return name;
  };

  const getItemValidationStatus = () => {
    // Simplified validation - just check if product is in stock
    return { type: 'success', message: 'موجود', icon: CheckCircle };
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />
          
          {/* Cart Drawer */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 20 }}
            className="fixed top-0 left-0 h-full w-full max-w-lg bg-white shadow-xl z-50 overflow-hidden flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-white">
              <h3 className="text-xl font-semibold text-text-primary flex items-center">
                <ShoppingBag className="ml-2 h-5 w-5" />
                سبد خرید
                {totalItems > 0 && (
                  <span className="bg-primary-100 text-primary-600 text-sm rounded-full px-2 py-0.5 mr-2">
                    {formatNumber(totalItems)}
                  </span>
                )}
              </h3>
              <div className="flex items-center space-x-2 space-x-reverse">
                {items.length > 0 && (
                  <button
                    onClick={clearCart}
                    className="text-gray-400 hover:text-red-500 p-1 rounded-full hover:bg-gray-100 transition-colors"
                    title="پاک کردن سبد خرید"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
                <button 
                  onClick={onClose}
                  className="text-text-secondary hover:text-text-primary p-1 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>
            
            {/* Content */}
            <div className="flex-grow overflow-y-auto custom-scroll">
              {items.length === 0 ? (
                /* Empty Cart */
                <div className="flex flex-col items-center justify-center h-full text-center p-8">
                  <ShoppingBag className="h-16 w-16 text-gray-300 mb-4" />
                  <p className="text-text-secondary mb-4">سبد خرید شما خالی است!</p>
                  <button 
                    onClick={onClose}
                    className="btn-primary"
                  >
                    ادامه خرید
                  </button>
                </div>
              ) : (
                <div className="p-4 space-y-6">
                  {/* Cart Items */}
                  <div className="space-y-4">
                    {items.map((item) => {
                      const validationStatus = getItemValidationStatus();
                      const StatusIcon = validationStatus.icon;

                      return (
                        <motion.div
                          key={`${item.product.id}-${item.variantKey || 'default'}`}
                          layout
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, height: 0 }}
                          className="bg-gray-50 rounded-lg p-4"
                        >
                          <div className="flex items-start space-x-3 space-x-reverse">
                            {/* Product Image */}
                            <Link 
                              to={`/product/${item.product.id}`}
                              className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0"
                              onClick={onClose}
                            >
                              <img 
                                src={item.product.imageSrc} 
                                alt={item.product.name} 
                                className="w-full h-full object-cover"
                              />
                            </Link>
                            
                            {/* Product Info */}
                            <div className="flex-grow min-w-0">
                              <div className="flex justify-between items-start mb-2">
                                <Link 
                                  to={`/product/${item.product.id}`}
                                  className="font-medium text-text-primary hover:text-primary-500 line-clamp-2"
                                  onClick={onClose}
                                >
                                  {formatCartItemName(item)}
                                </Link>
                                <button
                                  onClick={() => removeItem(item.product.id, item.variantKey)}
                                  className="text-text-secondary hover:text-red-500 p-1 -mt-1 -mr-1 transition-colors"
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>

                              {/* Validation Status */}
                              <div className={`flex items-center mb-2 text-xs ${
                                validationStatus.type === 'error' ? 'text-red-600' :
                                validationStatus.type === 'warning' ? 'text-yellow-600' :
                                'text-green-600'
                              }`}>
                                <StatusIcon className="w-3 h-3 ml-1" />
                                <span>{validationStatus.message}</span>
                              </div>

                              {/* Price */}
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-2 space-x-reverse">
                                  <span className="font-semibold text-text-primary">
                                    {formatPrice(item.product.discountedPrice || item.product.price)}
                                  </span>
                                  {item.product.discountedPrice && (
                                    <span className="text-sm text-gray-500 line-through">
                                      {formatPrice(item.product.price)}
                                    </span>
                                  )}
                                </div>

                                {/* Loyalty Points */}
                                <div className="flex items-center text-xs text-purple-600">
                                  <Star className="w-3 h-3 ml-1" />
                                  <span>+{formatNumber(Math.floor((item.product.price * item.quantity) / 10000))}</span>
                                </div>
                              </div>

                              {/* Quantity Controls */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center border border-gray-300 rounded-lg">
                                  <button
                                    onClick={() => updateQuantity(item.product.id, item.quantity - 1, item.variantKey)}
                                    className="p-2 hover:bg-gray-100 transition-colors"
                                    disabled={item.quantity <= 1}
                                  >
                                    <Minus className="h-3 w-3" />
                                  </button>
                                  <span className="px-3 py-2 text-sm font-medium min-w-[3rem] text-center">
                                    {formatNumber(item.quantity)}
                                  </span>
                                  <button
                                    onClick={() => updateQuantity(item.product.id, item.quantity + 1, item.variantKey)}
                                    className="p-2 hover:bg-gray-100 transition-colors"
                                    disabled={item.quantity >= item.product.stock}
                                  >
                                    <Plus className="h-3 w-3" />
                                  </button>
                                </div>

                                {/* Save for Later */}
                                <div className="relative">
                                  <button
                                    onClick={() => handleSaveForLater(item.product.id, item.variantKey)}
                                    className="flex items-center text-xs text-gray-600 hover:text-primary-600 transition-colors"
                                  >
                                    <Bookmark className="w-3 h-3 ml-1" />
                                    حذف از سبد
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Footer with Summary */}
            {items.length > 0 && (
              <div className="border-t bg-white p-4">
                <div className="space-y-4">
                  {/* Total */}
                  <div className="flex justify-between items-center text-lg font-semibold">
                    <span>مجموع:</span>
                    <span>{formatPrice(totalPrice)}</span>
                  </div>

                  {/* Checkout Button */}
                  <button
                    onClick={onCheckout}
                    className="w-full btn-primary"
                  >
                    ادامه خرید
                  </button>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default AdvancedCartDrawer;
