import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Activity,
  LogIn,
  LogOut,
  ShoppingBag,
  Star,
  MessageSquare,
  CreditCard,
  Gift,
  Settings,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminTable, { AdminTableColumn } from '../common/AdminTable';
import { AdminCustomer } from '../../../types/adminCustomer';
import { formatPersianDate } from '../../../utils/customerUtils';

interface CustomerActivity {
  id: string;
  type: 'login' | 'logout' | 'order' | 'review' | 'payment' | 'profile_update' | 'address_change' | 'loyalty_action' | 'support_ticket' | 'view_product' | 'add_to_cart' | 'wishlist_action';
  title: string;
  description: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  location?: string;
  metadata?: {
    orderId?: string;
    productId?: string;
    reviewId?: string;
    amount?: number;
    points?: number;
    ticketId?: string;
    [key: string]: any;
  };
}

interface CustomerActivityLogProps {
  customer: AdminCustomer;
  onViewDetails: (activityId: string) => void;
}

const CustomerActivityLog: React.FC<CustomerActivityLogProps> = ({
  customer,
  onViewDetails
}) => {
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedPeriod, setSelectedPeriod] = useState<'24h' | '7d' | '30d' | '90d' | 'all'>('30d');

  // Mock activity data - in real app, this would come from API
  const generateMockActivities = (): CustomerActivity[] => {
    const activities: Omit<CustomerActivity, 'id' | 'timestamp'>[] = [
      {
        type: 'login',
        title: 'ورود به حساب کاربری',
        description: 'ورود موفق به سیستم',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        deviceType: 'desktop',
        location: 'تهران، ایران'
      },
      {
        type: 'order',
        title: 'ثبت سفارش جدید',
        description: 'سفارش محصولات مراقبت از پوست',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        deviceType: 'mobile',
        location: 'تهران، ایران',
        metadata: { orderId: 'GR1001', amount: 250000 }
      },
      {
        type: 'review',
        title: 'ثبت نظر جدید',
        description: 'نظر برای سرم ویتامین C',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
        deviceType: 'tablet',
        location: 'تهران، ایران',
        metadata: { productId: 'prod-123', reviewId: 'rev-456', rating: 5 }
      },
      {
        type: 'payment',
        title: 'پرداخت موفق',
        description: 'پرداخت سفارش از طریق درگاه بانکی',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        deviceType: 'desktop',
        location: 'تهران، ایران',
        metadata: { orderId: 'GR1001', amount: 250000 }
      },
      {
        type: 'profile_update',
        title: 'بروزرسانی پروفایل',
        description: 'تغییر شماره تلفن',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        deviceType: 'mobile',
        location: 'تهران، ایران'
      },
      {
        type: 'loyalty_action',
        title: 'کسب امتیاز',
        description: 'دریافت امتیاز بابت خرید',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        deviceType: 'desktop',
        location: 'تهران، ایران',
        metadata: { points: 250, orderId: 'GR1001' }
      },
      {
        type: 'view_product',
        title: 'مشاهده محصول',
        description: 'مشاهده صفحه کرم آبرسان',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        deviceType: 'mobile',
        location: 'تهران، ایران',
        metadata: { productId: 'prod-789' }
      },
      {
        type: 'add_to_cart',
        title: 'افزودن به سبد خرید',
        description: 'افزودن ماسک طلایی به سبد',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        deviceType: 'desktop',
        location: 'تهران، ایران',
        metadata: { productId: 'prod-456', quantity: 2 }
      },
      {
        type: 'support_ticket',
        title: 'ایجاد تیکت پشتیبانی',
        description: 'سوال در مورد زمان ارسال',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',
        deviceType: 'tablet',
        location: 'تهران، ایران',
        metadata: { ticketId: 'TKT-123' }
      },
      {
        type: 'logout',
        title: 'خروج از حساب کاربری',
        description: 'خروج از سیستم',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        deviceType: 'desktop',
        location: 'تهران، ایران'
      }
    ];

    return activities.map((activity, index) => ({
      ...activity,
      id: `activity-${index + 1}`,
      timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }));
  };

  const activities = generateMockActivities();

  const filteredActivities = activities.filter(activity => {
    if (selectedType !== 'all' && activity.type !== selectedType) return false;
    
    if (selectedPeriod !== 'all') {
      const activityDate = new Date(activity.timestamp);
      const now = new Date();
      const diffHours = Math.floor((now.getTime() - activityDate.getTime()) / (1000 * 60 * 60));
      
      switch (selectedPeriod) {
        case '24h': return diffHours <= 24;
        case '7d': return diffHours <= 7 * 24;
        case '30d': return diffHours <= 30 * 24;
        case '90d': return diffHours <= 90 * 24;
      }
    }
    
    return true;
  });

  const getActivityIcon = (type: CustomerActivity['type']) => {
    switch (type) {
      case 'login':
        return <LogIn className="w-4 h-4 text-green-500" />;
      case 'logout':
        return <LogOut className="w-4 h-4 text-gray-500" />;
      case 'order':
        return <ShoppingBag className="w-4 h-4 text-blue-500" />;
      case 'review':
        return <Star className="w-4 h-4 text-yellow-500" />;
      case 'payment':
        return <CreditCard className="w-4 h-4 text-purple-500" />;
      case 'profile_update':
        return <Edit className="w-4 h-4 text-orange-500" />;
      case 'address_change':
        return <MapPin className="w-4 h-4 text-red-500" />;
      case 'loyalty_action':
        return <Gift className="w-4 h-4 text-pink-500" />;
      case 'support_ticket':
        return <MessageSquare className="w-4 h-4 text-indigo-500" />;
      case 'view_product':
        return <Eye className="w-4 h-4 text-gray-500" />;
      case 'add_to_cart':
        return <ShoppingBag className="w-4 h-4 text-green-500" />;
      case 'wishlist_action':
        return <Star className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const getDeviceIcon = (deviceType: CustomerActivity['deviceType']) => {
    switch (deviceType) {
      case 'desktop':
        return <Monitor className="w-3 h-3 text-gray-500" />;
      case 'mobile':
        return <Smartphone className="w-3 h-3 text-gray-500" />;
      case 'tablet':
        return <Tablet className="w-3 h-3 text-gray-500" />;
      default:
        return <Monitor className="w-3 h-3 text-gray-400" />;
    }
  };

  const getActivityTypeText = (type: CustomerActivity['type']) => {
    switch (type) {
      case 'login': return 'ورود';
      case 'logout': return 'خروج';
      case 'order': return 'سفارش';
      case 'review': return 'نظر';
      case 'payment': return 'پرداخت';
      case 'profile_update': return 'بروزرسانی پروفایل';
      case 'address_change': return 'تغییر آدرس';
      case 'loyalty_action': return 'امتیاز';
      case 'support_ticket': return 'پشتیبانی';
      case 'view_product': return 'مشاهده محصول';
      case 'add_to_cart': return 'سبد خرید';
      case 'wishlist_action': return 'لیست علاقه‌مندی';
      default: return 'نامشخص';
    }
  };

  const columns: AdminTableColumn<CustomerActivity>[] = [
    {
      key: 'type',
      title: 'نوع فعالیت',
      render: (_, activity) => (
        <div className="flex items-center gap-2">
          {getActivityIcon(activity.type)}
          <span className="text-sm text-gray-600">
            {getActivityTypeText(activity.type)}
          </span>
        </div>
      )
    },
    {
      key: 'title',
      title: 'عنوان',
      render: (_, activity) => (
        <div>
          <p className="font-medium text-gray-900 text-sm">{activity.title}</p>
          <p className="text-xs text-gray-500">{activity.description}</p>
          {activity.metadata && (
            <div className="flex items-center gap-2 mt-1">
              {activity.metadata.orderId && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {activity.metadata.orderId}
                </span>
              )}
              {activity.metadata.amount && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  {activity.metadata.amount.toLocaleString('fa-IR')} تومان
                </span>
              )}
              {activity.metadata.points && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                  {activity.metadata.points} امتیاز
                </span>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'timestamp',
      title: 'زمان',
      sortable: true,
      render: (_, activity) => (
        <div className="text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>{formatPersianDate(activity.timestamp)}</span>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Clock className="w-3 h-3" />
            <span>{new Date(activity.timestamp).toLocaleTimeString('fa-IR')}</span>
          </div>
        </div>
      )
    },
    {
      key: 'device',
      title: 'دستگاه',
      render: (_, activity) => (
        <div className="flex items-center gap-2">
          {getDeviceIcon(activity.deviceType)}
          <span className="text-sm text-gray-600 capitalize">
            {activity.deviceType === 'desktop' ? 'دسکتاپ' :
             activity.deviceType === 'mobile' ? 'موبایل' : 'تبلت'}
          </span>
        </div>
      )
    },
    {
      key: 'location',
      title: 'موقعیت',
      render: (_, activity) => (
        <div className="text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <MapPin className="w-3 h-3" />
            <span>{activity.location || 'نامشخص'}</span>
          </div>
          <div className="text-xs text-gray-500">
            {activity.ipAddress}
          </div>
        </div>
      )
    }
  ];

  const renderRowActions = (activity: CustomerActivity) => (
    <AdminButton
      variant="ghost"
      size="sm"
      icon={Eye}
      onClick={() => onViewDetails(activity.id)}
    >
      جزئیات
    </AdminButton>
  );

  // Calculate statistics
  const totalActivities = filteredActivities.length;
  const loginCount = filteredActivities.filter(a => a.type === 'login').length;
  const orderCount = filteredActivities.filter(a => a.type === 'order').length;
  const uniqueDevices = new Set(filteredActivities.map(a => a.deviceType)).size;

  return (
    <div className="space-y-6">
      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل فعالیت‌ها</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalActivities.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <LogIn className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">تعداد ورود</p>
              <p className="text-2xl font-bold text-gray-900">
                {loginCount.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">سفارشات</p>
              <p className="text-2xl font-bold text-gray-900">
                {orderCount.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Monitor className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">انواع دستگاه</p>
              <p className="text-2xl font-bold text-gray-900">
                {uniqueDevices.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Filters */}
      <AdminCard title="فیلترها">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع فعالیت
            </label>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">همه فعالیت‌ها</option>
              <option value="login">ورود</option>
              <option value="logout">خروج</option>
              <option value="order">سفارش</option>
              <option value="review">نظر</option>
              <option value="payment">پرداخت</option>
              <option value="profile_update">بروزرسانی پروفایل</option>
              <option value="loyalty_action">امتیاز</option>
              <option value="support_ticket">پشتیبانی</option>
              <option value="view_product">مشاهده محصول</option>
              <option value="add_to_cart">سبد خرید</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              بازه زمانی
            </label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="24h">۲۴ ساعت گذشته</option>
              <option value="7d">۷ روز گذشته</option>
              <option value="30d">۳۰ روز گذشته</option>
              <option value="90d">۹۰ روز گذشته</option>
              <option value="all">همه زمان‌ها</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Activity Log Table */}
      <AdminCard title="گزارش فعالیت‌ها">
        <AdminTable
          columns={columns}
          data={filteredActivities}
          rowActions={renderRowActions}
          emptyMessage="هیچ فعالیتی یافت نشد"
          hoverable
          striped
        />
      </AdminCard>
    </div>
  );
};

export default CustomerActivityLog;
