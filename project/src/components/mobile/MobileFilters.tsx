import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Filter,
  RotateCcw,
  Check,
  ChevronDown,
  ChevronUp,
  Star,
  Tag,
  Palette,
  Package
} from 'lucide-react';
import { ProductFilters } from '../../types/product';
import { useMobileDetection } from '../../hooks/useMobileDetection';
import { MobileFilterState, PERSIAN_MOBILE_MESSAGES } from '../../types/mobile';

interface MobileFiltersProps {
  isOpen: boolean;
  onClose: () => void;
  filters: ProductFilters;
  onFiltersChange: (filters: ProductFilters) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  className?: string;
}

interface FilterSection {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  isExpanded: boolean;
}

const MobileFilters: React.FC<MobileFiltersProps> = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  className = ''
}) => {
  const { isMobile } = useMobileDetection();
  
  const [localFilters, setLocalFilters] = useState<ProductFilters>(filters);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    brand: true,
    category: false,
    rating: false,
    availability: false,
    discount: false
  });

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Don't render on desktop
  if (!isMobile) return null;

  const filterSections: FilterSection[] = [
    {
      id: 'brand',
      title: 'برند',
      icon: Tag,
      isExpanded: expandedSections.brand
    },
    {
      id: 'category',
      title: 'دسته‌بندی',
      icon: Package,
      isExpanded: expandedSections.category
    },
    {
      id: 'rating',
      title: 'امتیاز',
      icon: Star,
      isExpanded: expandedSections.rating
    },
    {
      id: 'availability',
      title: 'موجودی',
      icon: Check,
      isExpanded: expandedSections.availability
    },
    {
      id: 'discount',
      title: 'تخفیف',
      icon: Palette,
      isExpanded: expandedSections.discount
    }
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const handleLocalFilterChange = (newFilters: Partial<ProductFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleApply = () => {
    onFiltersChange(localFilters);
    onApplyFilters();
    onClose();
  };

  const handleClear = () => {
    const clearedFilters: ProductFilters = {
      category: '',
      brand: '',
      rating: 0,
      inStock: false,
      onSale: false,
      sortBy: 'newest'
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    onClearFilters();
  };

  const hasActiveFilters = () => {
    return (
      localFilters.category ||
      localFilters.brand ||
      localFilters.rating > 0 ||
      localFilters.inStock ||
      localFilters.onSale
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* Filter Panel */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className={`fixed top-0 right-0 h-full w-full max-w-sm bg-white z-50 shadow-2xl ${className}`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white sticky top-0 z-10">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Filter className="w-5 h-5 text-rose-600" />
                <h2 className="text-lg font-bold text-gray-900">
                  {PERSIAN_MOBILE_MESSAGES.filters.showFilters}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Filter Content */}
            <div className="flex-1 overflow-y-auto pb-20">
              {filterSections.map((section) => (
                <div key={section.id} className="border-b border-gray-100">
                  <button
                    onClick={() => toggleSection(section.id)}
                    className="w-full flex items-center justify-between p-4 text-right hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <section.icon className="w-5 h-5 text-gray-600" />
                      <span className="font-medium text-gray-900">{section.title}</span>
                    </div>
                    {section.isExpanded ? (
                      <ChevronUp className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    )}
                  </button>

                  <AnimatePresence>
                    {section.isExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                      >
                        <div className="p-4 pt-0">
                          {section.id === 'brand' && (
                            <BrandFilter
                              value={localFilters.brand}
                              onChange={(brand) => handleLocalFilterChange({ brand })}
                            />
                          )}
                          {section.id === 'category' && (
                            <CategoryFilter
                              value={localFilters.category}
                              onChange={(category) => handleLocalFilterChange({ category })}
                            />
                          )}
                          {section.id === 'rating' && (
                            <RatingFilter
                              value={localFilters.rating}
                              onChange={(rating) => handleLocalFilterChange({ rating })}
                            />
                          )}
                          {section.id === 'availability' && (
                            <AvailabilityFilter
                              inStock={localFilters.inStock}
                              onSale={localFilters.onSale}
                              onChange={(updates) => handleLocalFilterChange(updates)}
                            />
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>

            {/* Footer Actions */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
              <div className="flex space-x-3 space-x-reverse">
                <button
                  onClick={handleClear}
                  disabled={!hasActiveFilters()}
                  className="flex-1 flex items-center justify-center space-x-2 space-x-reverse py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span className="font-medium">{PERSIAN_MOBILE_MESSAGES.filters.clear}</span>
                </button>
                <button
                  onClick={handleApply}
                  className="flex-1 py-3 px-4 bg-rose-600 text-white rounded-lg font-medium hover:bg-rose-700 transition-colors"
                >
                  {PERSIAN_MOBILE_MESSAGES.filters.apply}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Brand Filter Component
const BrandFilter: React.FC<{
  value: string;
  onChange: (brand: string) => void;
}> = ({ value, onChange }) => {
  const brands = ['لورآل', 'نیوآ', 'گارنیر', 'اوریفلیم', 'ایو روشه'];

  return (
    <div className="space-y-2">
      {brands.map((brand) => (
        <label key={brand} className="flex items-center space-x-3 space-x-reverse cursor-pointer">
          <input
            type="radio"
            name="brand"
            checked={value === brand}
            onChange={() => onChange(brand)}
            className="text-rose-600 focus:ring-rose-500"
          />
          <span className="text-gray-700">{brand}</span>
        </label>
      ))}
    </div>
  );
};

// Category Filter Component
const CategoryFilter: React.FC<{
  value: string;
  onChange: (category: string) => void;
}> = ({ value, onChange }) => {
  const categories = ['مراقبت از پوست', 'آرایش', 'مراقبت از مو', 'عطر و ادکلن'];

  return (
    <div className="space-y-2">
      {categories.map((category) => (
        <label key={category} className="flex items-center space-x-3 space-x-reverse cursor-pointer">
          <input
            type="radio"
            name="category"
            checked={value === category}
            onChange={() => onChange(category)}
            className="text-rose-600 focus:ring-rose-500"
          />
          <span className="text-gray-700">{category}</span>
        </label>
      ))}
    </div>
  );
};

// Rating Filter Component
const RatingFilter: React.FC<{
  value: number;
  onChange: (rating: number) => void;
}> = ({ value, onChange }) => {
  return (
    <div className="space-y-2">
      {[5, 4, 3, 2, 1].map((rating) => (
        <label key={rating} className="flex items-center space-x-3 space-x-reverse cursor-pointer">
          <input
            type="radio"
            name="rating"
            checked={value === rating}
            onChange={() => onChange(rating)}
            className="text-rose-600 focus:ring-rose-500"
          />
          <div className="flex items-center space-x-1 space-x-reverse">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                }`}
              />
            ))}
            <span className="text-gray-700 mr-2">و بالاتر</span>
          </div>
        </label>
      ))}
    </div>
  );
};

// Availability Filter Component
const AvailabilityFilter: React.FC<{
  inStock: boolean;
  onSale: boolean;
  onChange: (updates: { inStock?: boolean; onSale?: boolean }) => void;
}> = ({ inStock, onSale, onChange }) => {
  return (
    <div className="space-y-3">
      <label className="flex items-center space-x-3 space-x-reverse cursor-pointer">
        <input
          type="checkbox"
          checked={inStock}
          onChange={(e) => onChange({ inStock: e.target.checked })}
          className="text-rose-600 focus:ring-rose-500 rounded"
        />
        <span className="text-gray-700">فقط کالاهای موجود</span>
      </label>
      <label className="flex items-center space-x-3 space-x-reverse cursor-pointer">
        <input
          type="checkbox"
          checked={onSale}
          onChange={(e) => onChange({ onSale: e.target.checked })}
          className="text-rose-600 focus:ring-rose-500 rounded"
        />
        <span className="text-gray-700">فقط کالاهای تخفیف‌دار</span>
      </label>
    </div>
  );
};

export default MobileFilters;
