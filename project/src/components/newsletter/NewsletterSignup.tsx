import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Mail, Send, Check, Gift, Star, Sparkles } from 'lucide-react';
import { useNewsletter } from '../../hooks/useNewsletter';
import { useAuth } from '../../context/AuthContext';
import { 
  NewsletterFormData, 
  SubscriptionSource,
  PERSIAN_NEWSLETTER_MESSAGES 
} from '../../types/newsletter';
import { EmailValidator } from '../../utils/authUtils';

// Validation schema
const newsletterSchema = yup.object({
  email: yup
    .string()
    .required(PERSIAN_NEWSLETTER_MESSAGES.errors.emailRequired)
    .test('email', PERSIAN_NEWSLETTER_MESSAGES.errors.invalidEmail, (value) => 
      value ? EmailValidator.validate(value) : false
    ),
  firstName: yup.string(),
  lastName: yup.string(),
  acceptTerms: yup
    .boolean()
    .oneOf([true], PERSIAN_NEWSLETTER_MESSAGES.errors.termsRequired)
});

interface NewsletterSignupProps {
  source: SubscriptionSource;
  variant?: 'default' | 'compact' | 'inline';
  showBenefits?: boolean;
  className?: string;
  onSuccess?: () => void;
}

const NewsletterSignup: React.FC<NewsletterSignupProps> = ({
  source,
  variant = 'default',
  showBenefits = true,
  className = '',
  onSuccess
}) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { user } = useAuth();
  const { subscribe, isLoading, isSubscribed } = useNewsletter();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset
  } = useForm<NewsletterFormData>({
    resolver: yupResolver(newsletterSchema),
    defaultValues: {
      email: user?.email || '',
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      acceptTerms: false,
      source
    },
    mode: 'onChange'
  });

  const onSubmit = async (data: NewsletterFormData) => {
    try {
      await subscribe({
        ...data,
        email: EmailValidator.normalize(data.email),
        source
      });
      
      setIsSubmitted(true);
      reset();
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Only show success message if this specific component submitted the form
  // Don't show success for global subscription state from other components
  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`text-center p-6 bg-green-50 border border-green-200 rounded-lg ${className}`}
      >
        <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <Check className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-green-900 mb-2">
          {PERSIAN_NEWSLETTER_MESSAGES.content.welcomeTitle}
        </h3>
        <p className="text-green-700">
          {PERSIAN_NEWSLETTER_MESSAGES.content.welcomeMessage}
        </p>
      </motion.div>
    );
  }

  const benefits = PERSIAN_NEWSLETTER_MESSAGES.content.benefits;

  if (variant === 'compact') {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center gap-3 mb-3">
          <div className="bg-primary-100 rounded-full w-10 h-10 flex items-center justify-center">
            <Mail className="w-5 h-5 text-primary-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">
              {PERSIAN_NEWSLETTER_MESSAGES.content.title}
            </h3>
            <p className="text-sm text-gray-600">
              {PERSIAN_NEWSLETTER_MESSAGES.content.subtitle}
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="email"
                placeholder={PERSIAN_NEWSLETTER_MESSAGES.placeholders.email}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                  errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
            )}
          />
          {errors.email && (
            <p className="text-xs text-red-600">{errors.email.message}</p>
          )}

          <Controller
            name="acceptTerms"
            control={control}
            render={({ field }) => (
              <label className="flex items-start gap-2 text-xs text-gray-600">
                <input
                  {...field}
                  type="checkbox"
                  checked={field.value}
                  className="mt-0.5 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  disabled={isLoading}
                />
                <span>
                  {PERSIAN_NEWSLETTER_MESSAGES.labels.acceptTerms}
                </span>
              </label>
            )}
          />
          {errors.acceptTerms && (
            <p className="text-xs text-red-600">{errors.acceptTerms.message}</p>
          )}

          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                در حال ارسال...
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                {PERSIAN_NEWSLETTER_MESSAGES.buttons.subscribe}
              </>
            )}
          </button>
        </form>
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={`${className}`}>
        <form onSubmit={handleSubmit(onSubmit)} className="flex gap-2">
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="email"
                placeholder={PERSIAN_NEWSLETTER_MESSAGES.placeholders.email}
                className={`flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                  errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
            )}
          />
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </form>
        
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
        
        <Controller
          name="acceptTerms"
          control={control}
          render={({ field }) => (
            <label className="flex items-start gap-2 mt-2 text-sm text-gray-600">
              <input
                {...field}
                type="checkbox"
                checked={field.value}
                className="mt-0.5 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                disabled={isLoading}
              />
              <span>
                {PERSIAN_NEWSLETTER_MESSAGES.labels.acceptTerms}
              </span>
            </label>
          )}
        />
        {errors.acceptTerms && (
          <p className="text-sm text-red-600 mt-1">{errors.acceptTerms.message}</p>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={`bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg p-6 ${className}`}>
      <div className="text-center mb-6">
        <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <Mail className="w-8 h-8 text-primary-600" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          {PERSIAN_NEWSLETTER_MESSAGES.content.title}
        </h3>
        <p className="text-gray-600">
          {PERSIAN_NEWSLETTER_MESSAGES.content.subtitle}
        </p>
      </div>

      {showBenefits && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Gift className="w-5 h-5 text-primary-600" />
            مزایای عضویت:
          </h4>
          <ul className="space-y-2">
            {benefits.map((benefit, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-2 text-sm text-gray-700"
              >
                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                {benefit}
              </motion.li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder={PERSIAN_NEWSLETTER_MESSAGES.placeholders.firstName}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
                disabled={isLoading}
              />
            )}
          />
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder={PERSIAN_NEWSLETTER_MESSAGES.placeholders.lastName}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
                disabled={isLoading}
              />
            )}
          />
        </div>

        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <input
              {...field}
              type="email"
              placeholder={PERSIAN_NEWSLETTER_MESSAGES.placeholders.email}
              className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}
              disabled={isLoading}
            />
          )}
        />
        {errors.email && (
          <p className="text-sm text-red-600">{errors.email.message}</p>
        )}

        <Controller
          name="acceptTerms"
          control={control}
          render={({ field }) => (
            <label className="flex items-start gap-3">
              <input
                {...field}
                type="checkbox"
                checked={field.value}
                className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                disabled={isLoading}
              />
              <span className="text-sm text-gray-600">
                {PERSIAN_NEWSLETTER_MESSAGES.labels.acceptTerms}
              </span>
            </label>
          )}
        />
        {errors.acceptTerms && (
          <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
        )}

        <button
          type="submit"
          disabled={!isValid || isLoading}
          className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              در حال ارسال...
            </>
          ) : (
            <>
              <Send className="w-5 h-5" />
              {PERSIAN_NEWSLETTER_MESSAGES.buttons.subscribe}
            </>
          )}
        </button>
      </form>

      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 flex items-center justify-center gap-1">
          <Sparkles className="w-3 h-3" />
          {PERSIAN_NEWSLETTER_MESSAGES.content.privacy}
        </p>
      </div>
    </div>
  );
};

export default NewsletterSignup;
