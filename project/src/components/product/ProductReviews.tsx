import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Star, ThumbsUp, MessageCircle, User } from 'lucide-react';
import { Product } from '../../types';

interface ProductReviewsProps {
  product: Product;
}

interface Review {
  id: number;
  userName: string;
  rating: number;
  date: string;
  comment: string;
  helpful: number;
  verified: boolean;
}

// Mock reviews data - will be replaced with real data in Task 2.1
const mockReviews: Review[] = [
  {
    id: 1,
    userName: 'سارا احمدی',
    rating: 5,
    date: '۱۴۰۳/۰۹/۱۵',
    comment: 'محصول فوق‌العاده‌ای است! پوستم بعد از استفاده خیلی نرم و مرطوب شده. قطعاً دوباره خریداری می‌کنم.',
    helpful: 12,
    verified: true
  },
  {
    id: 2,
    userName: 'مریم کریمی',
    rating: 4,
    date: '۱۴۰۳/۰۹/۱۰',
    comment: 'کیفیت خوبی داره ولی قیمتش کمی بالاست. در کل راضی هستم از خریدم.',
    helpful: 8,
    verified: true
  },
  {
    id: 3,
    userName: 'فاطمه رضایی',
    rating: 5,
    date: '۱۴۰۳/۰۹/۰۵',
    comment: 'عالی! دقیقاً همون چیزی بود که پوستم نیاز داشت. تکسچرش سبک و جذب سریعی داره.',
    helpful: 15,
    verified: false
  }
];

const ProductReviews: React.FC<ProductReviewsProps> = ({ product }) => {
  const [reviews] = useState<Review[]>(mockReviews);
  const [sortBy, setSortBy] = useState<'newest' | 'helpful' | 'rating'>('newest');

  const sortedReviews = [...reviews].sort((a, b) => {
    switch (sortBy) {
      case 'helpful':
        return b.helpful - a.helpful;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
      default:
        return new Date(b.date).getTime() - new Date(a.date).getTime();
    }
  });

  const averageRating = product.rating;
  const totalReviews = product.reviewCount;

  const ratingDistribution = [
    { stars: 5, count: Math.floor(totalReviews * 0.6) },
    { stars: 4, count: Math.floor(totalReviews * 0.25) },
    { stars: 3, count: Math.floor(totalReviews * 0.1) },
    { stars: 2, count: Math.floor(totalReviews * 0.03) },
    { stars: 1, count: Math.floor(totalReviews * 0.02) }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-soft overflow-hidden">
      {/* Reviews Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-text-primary">نظرات کاربران</h3>
          <button className="btn-secondary text-sm">
            <MessageCircle className="w-4 h-4" />
            ثبت نظر
          </button>
        </div>

        {/* Rating Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Overall Rating */}
          <div className="text-center">
            <div className="text-4xl font-bold text-text-primary mb-2">
              {averageRating}
            </div>
            <div className="flex items-center justify-center mb-2">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-5 h-5 ${
                    i < Math.floor(averageRating) 
                      ? 'text-accent-500 fill-current' 
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <p className="text-text-secondary">
              بر اساس {totalReviews} نظر
            </p>
          </div>

          {/* Rating Distribution */}
          <div className="space-y-2">
            {ratingDistribution.map((item) => (
              <div key={item.stars} className="flex items-center gap-2">
                <span className="text-sm text-text-secondary w-8">
                  {item.stars} ستاره
                </span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-accent-500 h-2 rounded-full"
                    style={{
                      width: `${(item.count / totalReviews) * 100}%`
                    }}
                  />
                </div>
                <span className="text-sm text-text-secondary w-8">
                  {item.count}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Sort Options */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-4">
          <span className="text-sm font-medium text-text-primary">مرتب‌سازی:</span>
          <div className="flex gap-2">
            {[
              { value: 'newest', label: 'جدیدترین' },
              { value: 'helpful', label: 'مفیدترین' },
              { value: 'rating', label: 'بالاترین امتیاز' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setSortBy(option.value as any)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  sortBy === option.value
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-text-secondary hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="divide-y divide-gray-100">
        {sortedReviews.map((review, index) => (
          <motion.div
            key={review.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="p-6"
          >
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-primary-600" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium text-text-primary">
                    {review.userName}
                  </span>
                  {review.verified && (
                    <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                      خرید تأیید شده
                    </span>
                  )}
                  <span className="text-sm text-text-secondary">
                    {review.date}
                  </span>
                </div>
                
                <div className="flex items-center mb-3">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < review.rating 
                          ? 'text-accent-500 fill-current' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                
                <p className="text-text-secondary mb-3 leading-relaxed">
                  {review.comment}
                </p>
                
                <button className="flex items-center gap-2 text-sm text-text-secondary hover:text-primary-600 transition-colors">
                  <ThumbsUp className="w-4 h-4" />
                  مفید ({review.helpful})
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Load More */}
      <div className="p-6 text-center border-t border-gray-100">
        <button className="btn-secondary">
          مشاهده نظرات بیشتر
        </button>
      </div>
    </div>
  );
};

export default ProductReviews;
