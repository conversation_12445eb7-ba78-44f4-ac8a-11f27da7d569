import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request } from 'express';
import { config } from '../config';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// File upload types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  destination: string;
  filename: string;
  path: string;
  size: number;
}

export interface UploadResult {
  success: boolean;
  files: UploadedFile[];
  message: string;
}

// Ensure upload directories exist
const ensureUploadDirectories = (): void => {
  const directories = [
    config.upload.uploadPath,
    path.join(config.upload.uploadPath, 'products'),
    path.join(config.upload.uploadPath, 'categories'),
    path.join(config.upload.uploadPath, 'brands'),
    path.join(config.upload.uploadPath, 'temp'),
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Created upload directory: ${dir}`);
    }
  });
};

// Initialize upload directories
ensureUploadDirectories();

// Storage configuration
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    let uploadPath = config.upload.uploadPath;
    
    // Determine subdirectory based on route
    if (req.route?.path?.includes('product')) {
      uploadPath = path.join(config.upload.uploadPath, 'products');
    } else if (req.route?.path?.includes('category')) {
      uploadPath = path.join(config.upload.uploadPath, 'categories');
    } else if (req.route?.path?.includes('brand')) {
      uploadPath = path.join(config.upload.uploadPath, 'brands');
    } else {
      uploadPath = path.join(config.upload.uploadPath, 'temp');
    }

    cb(null, uploadPath);
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname).toLowerCase();
    const name = file.fieldname + '-' + uniqueSuffix + ext;
    cb(null, name);
  }
});

// File filter for images
const imageFileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check if file type is allowed
  if (!config.upload.allowedTypes.includes(file.mimetype)) {
    return cb(new AppError(
      `نوع فایل مجاز نیست. انواع مجاز: ${config.upload.allowedTypes.join(', ')}`,
      400,
      'INVALID_FILE_TYPE'
    ));
  }

  // Check file extension
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
  const ext = path.extname(file.originalname).toLowerCase();
  
  if (!allowedExtensions.includes(ext)) {
    return cb(new AppError(
      `پسوند فایل مجاز نیست. پسوندهای مجاز: ${allowedExtensions.join(', ')}`,
      400,
      'INVALID_FILE_EXTENSION'
    ));
  }

  cb(null, true);
};

// Multer configuration for single image upload
export const uploadSingleImage = multer({
  storage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 1,
  },
}).single('image');

// Multer configuration for multiple image upload
export const uploadMultipleImages = multer({
  storage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // Maximum 10 images
  },
}).array('images', 10);

// Multer configuration for product images (primary + gallery)
export const uploadProductImages = multer({
  storage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 11, // 1 primary + 10 gallery images
  },
}).fields([
  { name: 'primaryImage', maxCount: 1 },
  { name: 'galleryImages', maxCount: 10 }
]);

// Upload service class
export class UploadService {
  // Get file URL from filename
  static getFileUrl(filename: string, subfolder?: string): string {
    const port = config.server.port;
    const baseUrl = process.env.BASE_URL || `http://localhost:${port}`;
    const filePath = subfolder ? `${subfolder}/${filename}` : filename;
    return `${baseUrl}/uploads/${filePath}`;
  }

  // Delete file
  static async deleteFile(filename: string, subfolder?: string): Promise<boolean> {
    try {
      const filePath = subfolder 
        ? path.join(config.upload.uploadPath, subfolder, filename)
        : path.join(config.upload.uploadPath, filename);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.info(`File deleted: ${filePath}`);
        return true;
      }
      
      logger.warn(`File not found for deletion: ${filePath}`);
      return false;
    } catch (error) {
      logger.error('File deletion failed:', error);
      return false;
    }
  }

  // Delete multiple files
  static async deleteFiles(filenames: string[], subfolder?: string): Promise<number> {
    let deletedCount = 0;
    
    for (const filename of filenames) {
      const deleted = await this.deleteFile(filename, subfolder);
      if (deleted) deletedCount++;
    }
    
    return deletedCount;
  }

  // Validate uploaded file
  static validateUploadedFile(file: Express.Multer.File): void {
    if (!file) {
      throw new AppError('فایل آپلود نشده است', 400, 'NO_FILE_UPLOADED');
    }

    // Check file size
    if (file.size > config.upload.maxFileSize) {
      throw new AppError(
        `حجم فایل بیش از حد مجاز است. حداکثر: ${config.upload.maxFileSize / 1024 / 1024}MB`,
        400,
        'FILE_TOO_LARGE'
      );
    }

    // Check file type
    if (!config.upload.allowedTypes.includes(file.mimetype)) {
      throw new AppError(
        `نوع فایل مجاز نیست. انواع مجاز: ${config.upload.allowedTypes.join(', ')}`,
        400,
        'INVALID_FILE_TYPE'
      );
    }
  }

  // Validate multiple uploaded files
  static validateUploadedFiles(files: Express.Multer.File[]): void {
    if (!files || files.length === 0) {
      throw new AppError('فایل آپلود نشده است', 400, 'NO_FILES_UPLOADED');
    }

    files.forEach((file, index) => {
      try {
        this.validateUploadedFile(file);
      } catch (error) {
        if (error instanceof AppError) {
          throw new AppError(
            `فایل ${index + 1}: ${error.message}`,
            error.statusCode,
            error.code
          );
        }
        throw error;
      }
    });
  }

  // Get file info
  static getFileInfo(file: Express.Multer.File, subfolder?: string) {
    return {
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url: this.getFileUrl(file.filename, subfolder),
      path: file.path,
    };
  }

  // Process uploaded files for API response
  static processUploadedFiles(files: Express.Multer.File | Express.Multer.File[], subfolder?: string) {
    if (Array.isArray(files)) {
      return files.map(file => this.getFileInfo(file, subfolder));
    } else {
      return this.getFileInfo(files, subfolder);
    }
  }
}

export default UploadService;
