import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CartContextType, CartItem, Product, SelectedVariants } from '../types';
import {
  AdvancedCartItem,
  SavedForLaterItem,
  CartItemSource,
  SaveReason,
  AdvancedCartContextType
} from '../types/advancedCart';
import {
  generateCartItemId,
  saveCartToStorage,
  loadCartFromStorage
} from '../utils/cartUtils';
import { useAuth } from './AuthContext';
import { useAdminAuth } from './AdminAuthContext';
import toast from 'react-hot-toast';

const CartContext = createContext<CartContextType | undefined>(undefined);

const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { user: adminUser } = useAdminAuth();
  const [items, setItems] = useState<CartItem[]>([]);
  const [savedForLater, setSavedForLater] = useState<SavedForLaterItem[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Use either regular user or admin user for cart storage
  const currentUser = user || adminUser;

  // Load cart from storage on mount and when user changes
  useEffect(() => {
    const stored = loadCartFromStorage(currentUser?.id);
    if (stored.items.length > 0 || stored.savedItems.length > 0) {
      // Convert advanced cart items to basic cart items for compatibility
      const basicItems: CartItem[] = stored.items.map(item => ({
        product: item.product,
        quantity: item.quantity,
        selectedVariants: item.selectedVariants,
        variantKey: item.variantKey
      }));
      setItems(basicItems);
      setSavedForLater(stored.savedItems);
    }
  }, [currentUser?.id]);

  // Save cart to storage when items change
  useEffect(() => {
    if (items.length > 0 || savedForLater.length > 0) {
      // Convert basic cart items to advanced cart items for storage
      const advancedItems: AdvancedCartItem[] = items.map(item => ({
        id: item.variantKey || generateCartItemId(item.product.id, item.selectedVariants),
        product: item.product,
        quantity: item.quantity,
        selectedVariants: item.selectedVariants,
        variantKey: item.variantKey,
        addedAt: new Date(),
        lastModified: new Date(),
        source: 'product_page' as CartItemSource,
        loyaltyPointsEarned: Math.floor((item.product.price * item.quantity) / 10000)
      }));
      saveCartToStorage(advancedItems, savedForLater, currentUser?.id);
    }
  }, [items, savedForLater, currentUser?.id]);

  // Generate unique key for variant combination
  const generateVariantKey = (productId: number, selectedVariants?: SelectedVariants): string => {
    if (!selectedVariants || Object.keys(selectedVariants).length === 0) {
      return `${productId}`;
    }

    const variantKeys = Object.keys(selectedVariants)
      .sort()
      .map(key => `${key}:${selectedVariants[key].id}`)
      .join('|');

    return `${productId}|${variantKeys}`;
  };

  const addItem = (product: Product, quantity = 1, selectedVariants?: SelectedVariants) => {
    setItems(prevItems => {
      const variantKey = generateVariantKey(product.id, selectedVariants);
      const existingItem = prevItems.find(item =>
        item.product.id === product.id && item.variantKey === variantKey
      );

      if (existingItem) {
        return prevItems.map(item =>
          item.product.id === product.id && item.variantKey === variantKey
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }

      return [...prevItems, {
        product,
        quantity,
        selectedVariants,
        variantKey
      }];
    });

    // Show success toast with Persian message
    toast.success(`${product.name} به سبد خرید اضافه شد`);
  };

  const removeItem = (productId: number, variantKey?: string) => {
    setItems(prevItems => {
      if (variantKey) {
        return prevItems.filter(item =>
          !(item.product.id === productId && item.variantKey === variantKey)
        );
      }
      return prevItems.filter(item => item.product.id !== productId);
    });
  };

  const updateQuantity = (productId: number, quantity: number, variantKey?: string) => {
    if (quantity <= 0) {
      removeItem(productId, variantKey);
      return;
    }

    setItems(prevItems =>
      prevItems.map(item => {
        if (variantKey) {
          return item.product.id === productId && item.variantKey === variantKey
            ? { ...item, quantity }
            : item;
        }
        return item.product.id === productId
          ? { ...item, quantity }
          : item;
      })
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const toggleCart = () => {
    setIsOpen(prev => !prev);
  };

  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  
  const totalPrice = items.reduce((total, item) => {
    let basePrice = item.product.discountedPrice || item.product.price;

    // Add variant price adjustments
    if (item.selectedVariants) {
      Object.values(item.selectedVariants).forEach(variant => {
        if (variant.price) {
          basePrice += variant.price;
        }
      });
    }

    return total + (basePrice * item.quantity);
  }, 0);

  const value: CartContextType = {
    items,
    totalItems,
    totalPrice,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    isOpen,
    toggleCart
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

function useCart(): CartContextType {
  const context = useContext(CartContext);

  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }

  return context;
}

export { CartProvider, useCart };