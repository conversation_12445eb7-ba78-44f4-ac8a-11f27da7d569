import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import LoginForm from '../components/auth/LoginForm';
import SEOHead from '../components/seo/SEOHead';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  // Get the intended destination from location state
  const from = (location.state as any)?.from || '/';

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleLoginSuccess = () => {
    navigate(from, { replace: true });
  };

  return (
    <>
      <SEOHead
        title="ورود به حساب کاربری | گلو رویا"
        description="وارد حساب کاربری خود در گلو رویا شوید و از خرید محصولات مراقبت از پوست لذت ببرید."
        keywords="ورود, حساب کاربری, گلو رویا, مراقبت از پوست"
        canonical="/login"
      />
      
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="w-full max-w-md"
        >
          <LoginForm onSuccess={handleLoginSuccess} redirectTo={from} />
        </motion.div>
      </div>
    </>
  );
};

export default LoginPage;
