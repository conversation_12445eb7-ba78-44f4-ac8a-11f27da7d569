import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CategoryDropdownProps } from '../../types/navigation';
import SubCategoryList from './SubCategoryList';

const CategoryDropdown: React.FC<CategoryDropdownProps> = ({
  category,
  isOpen,
  onMouseEnter,
  onMouseLeave
}) => {
  return (
    <div
      className="relative"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {/* Category Header */}
      <Link
        to={`/products?category=${category.slug}`}
        className="flex items-center px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-all duration-200 group"
      >
        <div className="flex-1">
          <h3 className="font-medium text-base mb-1 group-hover:text-primary-600 transition-colors">
            {category.name}
          </h3>
          {category.description && (
            <p className="text-sm text-text-secondary group-hover:text-primary-500 transition-colors">
              {category.description}
            </p>
          )}
        </div>
        <svg 
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 9l-7 7-7-7" 
          />
        </svg>
      </Link>

      {/* Subcategories Dropdown */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10, height: 0 }}
          animate={{ opacity: 1, y: 0, height: 'auto' }}
          exit={{ opacity: 0, y: -10, height: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className="absolute top-full left-0 right-0 bg-white shadow-lg rounded-lg border border-gray-100 mt-1 z-50 overflow-hidden"
        >
          <div className="p-4">
            <SubCategoryList 
              subcategories={category.subcategories} 
              categorySlug={category.slug} 
            />
            
            {/* View All Link */}
            <div className="mt-4 pt-3 border-t border-gray-100">
              <Link
                to={`/products?category=${category.slug}`}
                className="flex items-center justify-center py-2 px-4 bg-primary-50 hover:bg-primary-100 text-primary-600 hover:text-primary-700 rounded-md transition-all duration-200 text-sm font-medium"
              >
                مشاهده همه محصولات {category.name}
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CategoryDropdown;
