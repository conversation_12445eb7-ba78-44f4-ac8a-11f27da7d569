import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Calendar,
  Filter,
  Settings,
  CheckSquare,
  Square
} from 'lucide-react';
import { AdminOrder, OrderExportOptions, OrderFilters } from '../../../types/adminOrder';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import AdminButton from '../common/AdminButton';
import AdminModal from '../common/AdminModal';
import toast from 'react-hot-toast';

interface OrderExportProps {
  orders: AdminOrder[];
  filters?: OrderFilters;
  isOpen: boolean;
  onClose: () => void;
}

const OrderExport: React.FC<OrderExportProps> = ({
  orders,
  filters,
  isOpen,
  onClose
}) => {
  const { checkPermission } = useAdminAuth();
  const [exportOptions, setExportOptions] = useState<OrderExportOptions>({
    format: 'excel',
    fields: [
      'orderNumber',
      'customerName',
      'customerEmail',
      'status',
      'total',
      'createdAt'
    ],
    filters,
    includeItems: false,
    includeCustomerInfo: true,
    includeTimeline: false
  });
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });

  const availableFields = [
    { key: 'orderNumber', label: 'شماره سفارش', category: 'basic' },
    { key: 'customerName', label: 'نام مشتری', category: 'customer' },
    { key: 'customerEmail', label: 'ایمیل مشتری', category: 'customer' },
    { key: 'customerPhone', label: 'تلفن مشتری', category: 'customer' },
    { key: 'status', label: 'وضعیت', category: 'basic' },
    { key: 'priority', label: 'اولویت', category: 'basic' },
    { key: 'total', label: 'مبلغ کل', category: 'financial' },
    { key: 'subtotal', label: 'مبلغ محصولات', category: 'financial' },
    { key: 'shippingCost', label: 'هزینه ارسال', category: 'financial' },
    { key: 'discount', label: 'تخفیف', category: 'financial' },
    { key: 'paymentMethod', label: 'روش پرداخت', category: 'payment' },
    { key: 'shippingMethod', label: 'روش ارسال', category: 'shipping' },
    { key: 'trackingNumber', label: 'کد رهگیری', category: 'shipping' },
    { key: 'createdAt', label: 'تاریخ ایجاد', category: 'basic' },
    { key: 'estimatedDelivery', label: 'تاریخ تحویل تخمینی', category: 'shipping' },
    { key: 'shippingAddress', label: 'آدرس ارسال', category: 'shipping' },
    { key: 'specialInstructions', label: 'توضیحات ویژه', category: 'basic' },
    { key: 'tags', label: 'برچسب‌ها', category: 'basic' },
    { key: 'refundStatus', label: 'وضعیت مرجوعی', category: 'financial' }
  ];

  const fieldCategories = {
    basic: 'اطلاعات پایه',
    customer: 'اطلاعات مشتری',
    financial: 'اطلاعات مالی',
    payment: 'پرداخت',
    shipping: 'ارسال'
  };

  const formatOptions = [
    { 
      value: 'excel', 
      label: 'Excel (.xlsx)', 
      icon: FileSpreadsheet,
      description: 'فایل اکسل با قابلیت فیلتر و فرمول'
    },
    { 
      value: 'csv', 
      label: 'CSV (.csv)', 
      icon: FileText,
      description: 'فایل متنی قابل باز کردن در هر نرم‌افزار'
    },
    { 
      value: 'pdf', 
      label: 'PDF (.pdf)', 
      icon: FileText,
      description: 'گزارش قابل چاپ و ارسال'
    }
  ];

  const handleFieldToggle = (fieldKey: string) => {
    setExportOptions(prev => ({
      ...prev,
      fields: prev.fields.includes(fieldKey)
        ? prev.fields.filter(f => f !== fieldKey)
        : [...prev.fields, fieldKey]
    }));
  };

  const handleSelectAllFields = (category: string) => {
    const categoryFields = availableFields
      .filter(field => field.category === category)
      .map(field => field.key);
    
    const allSelected = categoryFields.every(field => 
      exportOptions.fields.includes(field)
    );

    if (allSelected) {
      // Deselect all category fields
      setExportOptions(prev => ({
        ...prev,
        fields: prev.fields.filter(field => !categoryFields.includes(field))
      }));
    } else {
      // Select all category fields
      setExportOptions(prev => ({
        ...prev,
        fields: [...new Set([...prev.fields, ...categoryFields])]
      }));
    }
  };

  const handleExport = async () => {
    if (!checkPermission('orders', 'export')) {
      toast.error('شما دسترسی لازم برای خروجی گرفتن ندارید');
      return;
    }

    if (exportOptions.fields.length === 0) {
      toast.error('لطفاً حداقل یک فیلد انتخاب کنید');
      return;
    }

    try {
      setLoading(true);

      // Prepare export data
      const exportData = orders.map(order => {
        const row: any = {};
        
        exportOptions.fields.forEach(field => {
          switch (field) {
            case 'orderNumber':
              row['شماره سفارش'] = order.orderNumber;
              break;
            case 'customerName':
              row['نام مشتری'] = order.customerInfo.name;
              break;
            case 'customerEmail':
              row['ایمیل مشتری'] = order.customerInfo.email;
              break;
            case 'customerPhone':
              row['تلفن مشتری'] = order.customerInfo.phone;
              break;
            case 'status':
              row['وضعیت'] = order.status;
              break;
            case 'priority':
              row['اولویت'] = order.priority || 'normal';
              break;
            case 'total':
              row['مبلغ کل'] = order.orderSummary.total;
              break;
            case 'subtotal':
              row['مبلغ محصولات'] = order.orderSummary.subtotal;
              break;
            case 'shippingCost':
              row['هزینه ارسال'] = order.orderSummary.shippingCost;
              break;
            case 'discount':
              row['تخفیف'] = order.orderSummary.discount;
              break;
            case 'paymentMethod':
              row['روش پرداخت'] = order.paymentMethod.title;
              break;
            case 'shippingMethod':
              row['روش ارسال'] = order.shippingMethod.name;
              break;
            case 'trackingNumber':
              row['کد رهگیری'] = order.trackingNumber || '';
              break;
            case 'createdAt':
              row['تاریخ ایجاد'] = new Date(order.createdAt).toLocaleDateString('fa-IR');
              break;
            case 'estimatedDelivery':
              row['تاریخ تحویل تخمینی'] = order.estimatedDelivery 
                ? new Date(order.estimatedDelivery).toLocaleDateString('fa-IR')
                : '';
              break;
            case 'shippingAddress':
              row['آدرس ارسال'] = `${order.shippingAddress.address}, ${order.shippingAddress.city}, ${order.shippingAddress.province}`;
              break;
            case 'specialInstructions':
              row['توضیحات ویژه'] = order.specialInstructions || '';
              break;
            case 'tags':
              row['برچسب‌ها'] = order.tags?.join(', ') || '';
              break;
            case 'refundStatus':
              row['وضعیت مرجوعی'] = order.refundStatus;
              break;
          }
        });

        return row;
      });

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, you would generate and download the file here
      toast.success(`${orders.length} سفارش با موفقیت خروجی گرفته شد`);
      onClose();

    } catch (error) {
      toast.error('خطا در خروجی گرفتن');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryFieldCount = (category: string) => {
    const categoryFields = availableFields.filter(field => field.category === category);
    const selectedCount = categoryFields.filter(field => 
      exportOptions.fields.includes(field.key)
    ).length;
    return { selected: selectedCount, total: categoryFields.length };
  };

  return (
    <AdminModal
      isOpen={isOpen}
      onClose={onClose}
      title="خروجی سفارشات"
      size="xl"
    >
      <div className="space-y-6">
        {/* Export Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-900">
                آماده خروجی {orders.length} سفارش
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                {exportOptions.fields.length} فیلد انتخاب شده
              </p>
            </div>
            <Download className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        {/* Format Selection */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">فرمت خروجی</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {formatOptions.map(format => {
              const Icon = format.icon;
              return (
                <motion.button
                  key={format.value}
                  onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
                  className={`
                    p-4 border-2 rounded-lg text-right transition-all
                    ${exportOptions.format === format.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="w-6 h-6 text-gray-600" />
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900">
                        {format.label}
                      </h5>
                      <p className="text-sm text-gray-600">
                        {format.description}
                      </p>
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>

        {/* Date Range Filter */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">
            <Calendar className="inline w-4 h-4 ml-1" />
            محدوده زمانی (اختیاری)
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-600 mb-1">از تاریخ</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">تا تاریخ</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Field Selection */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">
            <Settings className="inline w-4 h-4 ml-1" />
            انتخاب فیلدها
          </h4>
          
          <div className="space-y-4">
            {Object.entries(fieldCategories).map(([category, categoryLabel]) => {
              const { selected, total } = getCategoryFieldCount(category);
              const allSelected = selected === total;
              
              return (
                <div key={category} className="border border-gray-200 rounded-lg">
                  <div className="p-3 bg-gray-50 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleSelectAllFields(category)}
                          className="flex items-center gap-2"
                        >
                          {allSelected ? (
                            <CheckSquare className="w-4 h-4 text-blue-600" />
                          ) : (
                            <Square className="w-4 h-4 text-gray-400" />
                          )}
                          <span className="font-medium text-gray-900">
                            {categoryLabel}
                          </span>
                        </button>
                      </div>
                      <span className="text-sm text-gray-500">
                        {selected}/{total}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {availableFields
                        .filter(field => field.category === category)
                        .map(field => (
                          <label key={field.key} className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={exportOptions.fields.includes(field.key)}
                              onChange={() => handleFieldToggle(field.key)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">
                              {field.label}
                            </span>
                          </label>
                        ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Additional Options */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">گزینه‌های اضافی</h4>
          <div className="space-y-3">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportOptions.includeItems}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  includeItems: e.target.checked 
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">
                شامل جزئیات محصولات
              </span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportOptions.includeCustomerInfo}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  includeCustomerInfo: e.target.checked 
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">
                شامل اطلاعات کامل مشتری
              </span>
            </label>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={exportOptions.includeTimeline}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  includeTimeline: e.target.checked 
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">
                شامل تاریخچه سفارش
              </span>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <AdminButton
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            انصراف
          </AdminButton>
          <AdminButton
            variant="primary"
            onClick={handleExport}
            loading={loading}
            icon={Download}
          >
            دانلود خروجی
          </AdminButton>
        </div>
      </div>
    </AdminModal>
  );
};

export default OrderExport;
