import { Order } from '../types/checkout';
import { AdminOrder, OrderTimelineEvent, OrderFilters, OrderAnalytics } from '../types/adminOrder';

/**
 * Generate a unique order number
 */
export const generateOrderNumber = (): string => {
  const prefix = 'GR';
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
};

/**
 * Calculate estimated delivery date
 */
export const calculateEstimatedDelivery = (
  shippingMethod: string,
  orderDate: string = new Date().toISOString()
): string => {
  const baseDate = new Date(orderDate);
  let deliveryDays = 3; // Default

  switch (shippingMethod) {
    case 'express':
      deliveryDays = 1;
      break;
    case 'fast':
      deliveryDays = 2;
      break;
    case 'standard':
      deliveryDays = 3;
      break;
    case 'economy':
      deliveryDays = 5;
      break;
    default:
      deliveryDays = 3;
  }

  // Skip weekends
  let currentDate = new Date(baseDate);
  let addedDays = 0;
  
  while (addedDays < deliveryDays) {
    currentDate.setDate(currentDate.getDate() + 1);
    const dayOfWeek = currentDate.getDay();
    
    // Skip Friday (5) and Saturday (6) for Iranian weekend
    if (dayOfWeek !== 5 && dayOfWeek !== 6) {
      addedDays++;
    }
  }

  return currentDate.toISOString();
};

/**
 * Generate tracking number
 */
export const generateTrackingNumber = (carrier: string = 'post'): string => {
  const prefix = carrier.toUpperCase().slice(0, 2);
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${timestamp}${random}`;
};

/**
 * Format order status for display
 */
export const formatOrderStatus = (status: Order['status']): string => {
  const statusMap: Record<Order['status'], string> = {
    pending: 'در انتظار تأیید',
    confirmed: 'تأیید شده',
    processing: 'در حال پردازش',
    shipped: 'ارسال شده',
    delivered: 'تحویل داده شده',
    cancelled: 'لغو شده'
  };
  
  return statusMap[status] || status;
};

/**
 * Get status color for UI
 */
export const getStatusColor = (status: Order['status']): string => {
  const colorMap: Record<Order['status'], string> = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-blue-100 text-blue-800',
    processing: 'bg-purple-100 text-purple-800',
    shipped: 'bg-indigo-100 text-indigo-800',
    delivered: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  };
  
  return colorMap[status] || 'bg-gray-100 text-gray-800';
};

/**
 * Get priority color for UI
 */
export const getPriorityColor = (priority: Order['priority']): string => {
  const colorMap: Record<NonNullable<Order['priority']>, string> = {
    low: 'bg-gray-100 text-gray-800',
    normal: 'bg-blue-100 text-blue-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800'
  };
  
  return colorMap[priority || 'normal'];
};

/**
 * Calculate order processing time in minutes
 */
export const calculateProcessingTime = (order: AdminOrder): number => {
  const createdAt = new Date(order.createdAt);
  const shippedEvent = order.timeline.find(event => 
    event.type === 'status_change' && 
    event.metadata?.to === 'shipped'
  );
  
  if (!shippedEvent) return 0;
  
  const shippedAt = new Date(shippedEvent.timestamp);
  return Math.floor((shippedAt.getTime() - createdAt.getTime()) / (1000 * 60));
};

/**
 * Filter orders based on criteria
 */
export const filterOrders = (orders: AdminOrder[], filters: OrderFilters): AdminOrder[] => {
  return orders.filter(order => {
    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(order.status)) return false;
    }

    // Priority filter
    if (filters.priority && filters.priority.length > 0) {
      if (!filters.priority.includes(order.priority || 'normal')) return false;
    }

    // Date range filter
    if (filters.dateRange) {
      const orderDate = new Date(order.createdAt);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      
      if (orderDate < startDate || orderDate > endDate) return false;
    }

    // Customer search
    if (filters.customerSearch) {
      const searchTerm = filters.customerSearch.toLowerCase();
      const customerMatch = 
        order.customerInfo.name.toLowerCase().includes(searchTerm) ||
        order.customerInfo.email.toLowerCase().includes(searchTerm) ||
        order.customerInfo.phone.includes(searchTerm);
      
      if (!customerMatch) return false;
    }

    // Order number search
    if (filters.orderNumber) {
      if (!order.orderNumber.toLowerCase().includes(filters.orderNumber.toLowerCase())) {
        return false;
      }
    }

    // Amount range
    if (filters.minAmount && order.orderSummary.total < filters.minAmount) return false;
    if (filters.maxAmount && order.orderSummary.total > filters.maxAmount) return false;

    // Payment method
    if (filters.paymentMethod && filters.paymentMethod.length > 0) {
      if (!filters.paymentMethod.includes(order.paymentMethod.type)) return false;
    }

    // Shipping method
    if (filters.shippingMethod && filters.shippingMethod.length > 0) {
      if (!filters.shippingMethod.includes(order.shippingMethod.id)) return false;
    }

    // Tags
    if (filters.tags && filters.tags.length > 0) {
      const orderTags = order.tags || [];
      const hasMatchingTag = filters.tags.some(tag => orderTags.includes(tag));
      if (!hasMatchingTag) return false;
    }

    // Refund filter
    if (filters.hasRefund !== undefined) {
      const hasRefund = order.refundStatus !== 'none';
      if (filters.hasRefund !== hasRefund) return false;
    }

    // Rush orders
    if (filters.isRush !== undefined) {
      if (filters.isRush !== order.flags.isRush) return false;
    }

    // Gift orders
    if (filters.isGift !== undefined) {
      if (filters.isGift !== order.flags.isGift) return false;
    }

    return true;
  });
};

/**
 * Sort orders by specified criteria
 */
export const sortOrders = (
  orders: AdminOrder[], 
  sortBy: string, 
  sortOrder: 'asc' | 'desc' = 'desc'
): AdminOrder[] => {
  return [...orders].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortBy) {
      case 'orderNumber':
        aValue = a.orderNumber;
        bValue = b.orderNumber;
        break;
      case 'customerName':
        aValue = a.customerInfo.name;
        bValue = b.customerInfo.name;
        break;
      case 'total':
        aValue = a.orderSummary.total;
        bValue = b.orderSummary.total;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'priority':
        const priorityOrder = { low: 1, normal: 2, high: 3, urgent: 4 };
        aValue = priorityOrder[a.priority || 'normal'];
        bValue = priorityOrder[b.priority || 'normal'];
        break;
      case 'createdAt':
      default:
        aValue = new Date(a.createdAt);
        bValue = new Date(b.createdAt);
        break;
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

/**
 * Calculate order analytics
 */
export const calculateOrderAnalytics = (orders: AdminOrder[]): OrderAnalytics => {
  const totalOrders = orders.length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  // Orders by status
  const ordersByStatus = orders.reduce((acc, order) => {
    acc[order.status] = (acc[order.status] || 0) + 1;
    return acc;
  }, {} as Record<Order['status'], number>);

  // Orders by priority
  const ordersByPriority = orders.reduce((acc, order) => {
    const priority = order.priority || 'normal';
    acc[priority] = (acc[priority] || 0) + 1;
    return acc;
  }, {} as Record<Order['priority'], number>);

  // Top products
  const productMap = new Map<number, { name: string; quantity: number; revenue: number }>();
  
  orders.forEach(order => {
    order.items.forEach(item => {
      const existing = productMap.get(item.productId);
      if (existing) {
        existing.quantity += item.quantity;
        existing.revenue += item.price * item.quantity;
      } else {
        productMap.set(item.productId, {
          name: item.name,
          quantity: item.quantity,
          revenue: item.price * item.quantity
        });
      }
    });
  });

  const topProducts = Array.from(productMap.entries())
    .map(([productId, data]) => ({
      productId,
      productName: data.name,
      quantity: data.quantity,
      revenue: data.revenue
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10);

  // Customer metrics
  const uniqueCustomers = new Set(orders.map(order => order.customerInfo.email));
  const newCustomers = orders.filter(order => order.customerInfo.totalOrders === 1).length;
  const returningCustomers = orders.filter(order => order.customerInfo.totalOrders > 1).length;
  const vipCustomers = orders.filter(order => order.customerInfo.isVip).length;

  // Fulfillment metrics
  const processedOrders = orders.filter(order => order.metrics.processingTime);
  const averageProcessingTime = processedOrders.length > 0 
    ? processedOrders.reduce((sum, order) => sum + (order.metrics.processingTime || 0), 0) / processedOrders.length
    : 0;

  const shippedOrders = orders.filter(order => order.metrics.shippingTime);
  const averageShippingTime = shippedOrders.length > 0
    ? shippedOrders.reduce((sum, order) => sum + (order.metrics.shippingTime || 0), 0) / shippedOrders.length
    : 0;

  const deliveredOrders = orders.filter(order => order.status === 'delivered');
  const onTimeDeliveryRate = deliveredOrders.length > 0 
    ? (deliveredOrders.filter(order => {
        const estimatedDate = new Date(order.estimatedDelivery || '');
        const deliveredEvent = order.timeline.find(e => e.type === 'status_change' && e.metadata?.to === 'delivered');
        const actualDate = deliveredEvent ? new Date(deliveredEvent.timestamp) : new Date();
        return actualDate <= estimatedDate;
      }).length / deliveredOrders.length) * 100
    : 0;

  // Revenue by day (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const revenueByDay = Array.from({ length: 30 }, (_, i) => {
    const date = new Date(thirtyDaysAgo);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];
    
    const dayOrders = orders.filter(order => 
      order.createdAt.split('T')[0] === dateStr
    );
    
    return {
      date: dateStr,
      revenue: dayOrders.reduce((sum, order) => sum + order.total, 0),
      orders: dayOrders.length
    };
  });

  return {
    totalOrders,
    totalRevenue,
    averageOrderValue,
    ordersByStatus,
    ordersByPriority,
    topProducts,
    customerMetrics: {
      newCustomers,
      returningCustomers,
      vipCustomers
    },
    fulfillmentMetrics: {
      averageProcessingTime,
      averageShippingTime,
      onTimeDeliveryRate
    },
    revenueByDay
  };
};

/**
 * Format Persian currency
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fa-IR', {
    style: 'currency',
    currency: 'IRR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Format Persian date
 */
export const formatPersianDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj);
};

/**
 * Create timeline event
 */
export const createTimelineEvent = (
  type: OrderTimelineEvent['type'],
  title: string,
  description?: string,
  userId?: string,
  userName?: string,
  metadata?: Record<string, any>
): OrderTimelineEvent => {
  return {
    id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    title,
    description,
    timestamp: new Date().toISOString(),
    userId,
    userName,
    metadata,
    isSystemGenerated: !userId
  };
};
