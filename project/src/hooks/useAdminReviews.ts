import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAdminAuth } from './useAdminAuth';
import {
  AdminReview,
  AdminReviewFilters,
  ReviewSortOptions,
  ReviewAnalytics,
  ReviewAnalyticsAPI,
  ProductReviewStats,
  ReviewModerationDecision,
  BulkReviewAction,
  AdminNote,
  ReviewResponse,
  ReviewModerationStatus,
  PERSIAN_REVIEW_ADMIN_MESSAGES
} from '../types/adminReview';
import { Review } from '../types/review';
import {
  filterAdminReviews,
  sortAdminReviews,
  calculateQualityScore,
  calculateSpamScore,
  detectContentFlags,
  calculateSentimentScore,
  determineAutoModerationStatus,
  formatPersianDateTime
} from '../utils/reviewModeration';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1';

// API Helper Functions
const getAuthHeaders = (token: string) => ({
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json',
});

export const useAdminReviews = () => {
  const { user, checkPermission } = useAdminAuth();
  const [reviews, setReviews] = useState<AdminReview[]>([]);
  const [analytics, setAnalytics] = useState<ReviewAnalyticsAPI | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AdminReviewFilters>({});
  const [sortBy, setSortBy] = useState<ReviewSortOptions['field']>('createdAt');
  const [sortOrder, setSortOrder] = useState<ReviewSortOptions['direction']>('desc');

  // Get admin token
  const getToken = useCallback(() => {
    return localStorage.getItem('admin_auth_token');
  }, []);

  // Load reviews from API
  const loadReviews = useCallback(async () => {
    if (!checkPermission('reviews', 'read')) {
      setError(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
      return;
    }

    const token = getToken();
    if (!token) {
      setError('توکن احراز هویت یافت نشد');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters.search) queryParams.append('search', filters.search);
      if (filters.productId) queryParams.append('productId', filters.productId);
      if (filters.userId) queryParams.append('userId', filters.userId);
      if (filters.rating) queryParams.append('rating', filters.rating.toString());
      if (typeof filters.isVerified === 'boolean') queryParams.append('isVerified', filters.isVerified.toString());
      if (typeof filters.isApproved === 'boolean') queryParams.append('isApproved', filters.isApproved.toString());
      if (filters.moderationStatus) queryParams.append('moderationStatus', filters.moderationStatus);
      if (typeof filters.isRecommended === 'boolean') queryParams.append('isRecommended', filters.isRecommended.toString());
      if (filters.skinType) queryParams.append('skinType', filters.skinType);
      if (filters.ageRange) queryParams.append('ageRange', filters.ageRange);
      if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.limit) queryParams.append('limit', filters.limit.toString());

      const response = await fetch(`${API_BASE_URL}/reviews?${queryParams.toString()}`, {
        headers: getAuthHeaders(token),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Transform API data to AdminReview format
        const transformedReviews: AdminReview[] = data.data.reviews.map((review: any) => ({
          ...review,
          // Add legacy fields for backward compatibility
          customer: {
            id: review.user.id,
            name: `${review.user.firstName} ${review.user.lastName}`,
            email: review.user.email || `${review.user.firstName.toLowerCase()}@example.com`,
            totalReviews: 1, // This would come from a separate API call
            averageRating: review.rating,
            isVerifiedCustomer: review.isVerified,
            registrationDate: review.createdAt,
            lastOrderDate: review.createdAt,
            loyaltyTier: 'نقره‌ای'
          },
          // Legacy moderation structure
          moderation: {
            status: review.moderationStatus?.toLowerCase() || 'pending',
            moderatedBy: undefined,
            moderatedByName: undefined,
            moderatedAt: undefined,
            moderationNotes: review.moderationNotes,
            autoModerated: false,
            contentFlags: [],
            qualityScore: 85, // Mock score
            spamScore: 10, // Mock score
            sentimentScore: review.rating > 3 ? 0.5 : review.rating < 3 ? -0.5 : 0
          },
          // Legacy analytics
          analytics: {
            viewCount: 0,
            helpfulnessRatio: review.helpfulCount > 0 ? review.helpfulCount / (review.helpfulCount + review.unhelpfulCount) : 0,
            reportCount: 0,
            engagementScore: review.helpfulCount + review.unhelpfulCount
          },
          adminNotes: [],
          reports: [],
          isHighlighted: false,
          isPinned: false,
          isHidden: false,
          businessResponse: review.responses?.find((r: any) => r.isOfficial) ? {
            id: review.responses[0].id,
            content: review.responses[0].content,
            respondedBy: review.responses[0].user.id,
            respondedByName: `${review.responses[0].user.firstName} ${review.responses[0].user.lastName}`,
            respondedAt: review.responses[0].createdAt,
            isPublic: true
          } : undefined
        }));

        setReviews(transformedReviews);
      } else {
        throw new Error(data.message || 'خطا در دریافت نظرات');
      }
    } catch (err) {
      console.error('Error loading reviews:', err);
      setError(err instanceof Error ? err.message : 'خطا در بارگذاری نظرات');
    } finally {
      setLoading(false);
    }
  }, [checkPermission, getToken, filters]);

  // Load analytics from API
  const loadAnalytics = useCallback(async () => {
    if (!checkPermission('reviews', 'read')) {
      return;
    }

    const token = getToken();
    if (!token) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/reviews/admin/analytics`, {
        headers: getAuthHeaders(token),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setAnalytics(data.data.analytics);
      }
    } catch (err) {
      console.error('Error loading analytics:', err);
    }
  }, [checkPermission, getToken]);

  // Get filtered and sorted reviews
  const filteredReviews = useCallback(() => {
    let result = filterAdminReviews(reviews, filters);
    result = sortAdminReviews(result, { field: sortBy, direction: sortOrder });
    return result;
  }, [reviews, filters, sortBy, sortOrder]);

  // Moderate review using Task 1.7 API
  const moderateReview = useCallback(async (
    reviewId: string,
    decision: ReviewModerationDecision
  ): Promise<void> => {
    if (!checkPermission('reviews', 'moderate')) {
      throw new Error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
    }

    const token = getToken();
    if (!token) {
      throw new Error('توکن احراز هویت یافت نشد');
    }

    try {
      setLoading(true);

      // Map decision action to API moderation status
      const moderationStatus: ReviewModerationStatus =
        decision.action === 'approve' ? 'APPROVED' :
        decision.action === 'reject' ? 'REJECTED' :
        decision.action === 'flag' ? 'FLAGGED' : 'PENDING';

      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}/moderate`, {
        method: 'PATCH',
        headers: getAuthHeaders(token),
        body: JSON.stringify({
          moderationStatus,
          moderationNotes: decision.notes
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update local state with the moderated review
        setReviews(prevReviews =>
          prevReviews.map(review => {
            if (review.id === reviewId) {
              return {
                ...review,
                moderationStatus: data.data.review.moderationStatus,
                moderationNotes: data.data.review.moderationNotes,
                isApproved: data.data.review.isApproved,
                updatedAt: data.data.review.updatedAt,
                // Update legacy moderation structure
                moderation: {
                  ...review.moderation,
                  status: moderationStatus.toLowerCase() as any,
                  moderatedBy: user?.id,
                  moderatedByName: user ? `${user.firstName} ${user.lastName}` : '',
                  moderatedAt: new Date().toISOString(),
                  moderationNotes: decision.notes,
                  autoModerated: false
                }
              };
            }
            return review;
          })
        );

        const actionMessage = decision.action === 'approve' ? PERSIAN_REVIEW_ADMIN_MESSAGES.success.reviewApproved :
                             decision.action === 'reject' ? PERSIAN_REVIEW_ADMIN_MESSAGES.success.reviewRejected :
                             PERSIAN_REVIEW_ADMIN_MESSAGES.success.reviewFlagged;

        toast.success(actionMessage);
      } else {
        throw new Error(data.message || 'خطا در تعدیل نظر');
      }
    } catch (err) {
      console.error('Error moderating review:', err);
      toast.error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.moderationFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission, getToken, user]);

  // Add response to review using Task 1.7 API
  const addReviewResponse = useCallback(async (
    reviewId: string,
    content: string,
    isOfficial: boolean = true
  ): Promise<void> => {
    if (!checkPermission('reviews', 'update')) {
      throw new Error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
    }

    const token = getToken();
    if (!token) {
      throw new Error('توکن احراز هویت یافت نشد');
    }

    try {
      setLoading(true);

      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}/response`, {
        method: 'POST',
        headers: getAuthHeaders(token),
        body: JSON.stringify({
          content,
          isOfficial
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update local state with the new response
        setReviews(prevReviews =>
          prevReviews.map(review => {
            if (review.id === reviewId) {
              return {
                ...review,
                responses: [...review.responses, data.data.response],
                // Update legacy business response
                businessResponse: isOfficial ? {
                  id: data.data.response.id,
                  content: data.data.response.content,
                  respondedBy: data.data.response.userId,
                  respondedByName: `${data.data.response.user.firstName} ${data.data.response.user.lastName}`,
                  respondedAt: data.data.response.createdAt,
                  isPublic: true
                } : review.businessResponse
              };
            }
            return review;
          })
        );

        toast.success(PERSIAN_REVIEW_ADMIN_MESSAGES.success.responseAdded);
      } else {
        throw new Error(data.message || 'خطا در افزودن پاسخ');
      }
    } catch (err) {
      console.error('Error adding review response:', err);
      toast.error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.responseAddFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission, getToken]);

  // Bulk moderate reviews
  const bulkModerateReviews = useCallback(async (
    action: BulkReviewAction
  ): Promise<void> => {
    if (!checkPermission('reviews', 'moderate')) {
      throw new Error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReviews(prevReviews => 
        prevReviews.map(review => {
          if (action.reviewIds.includes(review.id)) {
            const newStatus = action.type === 'approve' ? 'approved' :
                             action.type === 'reject' ? 'rejected' :
                             action.type === 'flag' ? 'flagged' : review.moderation.status;
            
            return {
              ...review,
              moderation: {
                ...review.moderation,
                status: newStatus,
                moderatedBy: user?.id,
                moderatedByName: user ? `${user.firstName} ${user.lastName}` : '',
                moderatedAt: new Date().toISOString(),
                moderationNotes: action.notes,
                autoModerated: false
              },
              isHighlighted: action.type === 'highlight' ? true : review.isHighlighted,
              isPinned: action.type === 'pin' ? true : review.isPinned,
              isHidden: action.type === 'hide' ? true : review.isHidden
            };
          }
          return review;
        })
      );

      toast.success(PERSIAN_REVIEW_ADMIN_MESSAGES.success.bulkActionCompleted);
    } catch (err) {
      toast.error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.bulkActionFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission, user]);

  // Add admin note
  const addAdminNote = useCallback(async (
    reviewId: string,
    note: Omit<AdminNote, 'id' | 'reviewId' | 'createdAt' | 'createdBy' | 'createdByName'>
  ): Promise<void> => {
    if (!checkPermission('reviews', 'update')) {
      throw new Error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
    }

    try {
      const newNote: AdminNote = {
        ...note,
        id: `note_${Date.now()}`,
        reviewId,
        createdBy: user?.id || '',
        createdByName: user ? `${user.firstName} ${user.lastName}` : '',
        createdAt: new Date().toISOString()
      };

      setReviews(prevReviews =>
        prevReviews.map(review => {
          if (review.id === reviewId) {
            return {
              ...review,
              adminNotes: [...review.adminNotes, newNote]
            };
          }
          return review;
        })
      );

      toast.success(PERSIAN_REVIEW_ADMIN_MESSAGES.success.noteAdded);
    } catch (err) {
      throw err;
    }
  }, [checkPermission, user]);

  // Get review by ID
  const getReviewById = useCallback((reviewId: string): AdminReview | undefined => {
    return reviews.find(review => review.id === reviewId);
  }, [reviews]);

  // Respond to review
  const respondToReview = useCallback(async (
    reviewId: string,
    response: { content: string; isPublic: boolean }
  ): Promise<void> => {
    if (!checkPermission('reviews', 'update')) {
      throw new Error(PERSIAN_REVIEW_ADMIN_MESSAGES.errors.insufficientPermissions);
    }

    try {
      const businessResponse = {
        id: `response_${Date.now()}`,
        content: response.content,
        respondedBy: user?.id || '',
        respondedByName: user ? `${user.firstName} ${user.lastName}` : '',
        respondedAt: new Date().toISOString(),
        isPublic: response.isPublic
      };

      setReviews(prevReviews =>
        prevReviews.map(review => {
          if (review.id === reviewId) {
            return {
              ...review,
              businessResponse
            };
          }
          return review;
        })
      );

      toast.success(PERSIAN_REVIEW_ADMIN_MESSAGES.success.responseAdded);
    } catch (err) {
      throw err;
    }
  }, [checkPermission, user]);

  // Get review analytics
  const getAnalytics = useCallback((): ReviewAnalytics => {
    const totalReviews = reviews.length;
    const pendingReviews = reviews.filter(r => r.moderation.status === 'pending').length;
    const approvedReviews = reviews.filter(r => r.moderation.status === 'approved' || r.moderation.status === 'auto_approved').length;
    const rejectedReviews = reviews.filter(r => r.moderation.status === 'rejected' || r.moderation.status === 'auto_rejected').length;
    const flaggedReviews = reviews.filter(r => r.moderation.status === 'flagged').length;

    // Calculate moderation stats
    const moderatedReviews = reviews.filter(r => r.moderation.moderatedAt);
    const autoModeratedReviews = reviews.filter(r => r.moderation.autoModerated);
    const manualReviews = moderatedReviews.filter(r => !r.moderation.autoModerated);

    const averageResponseTime = moderatedReviews.length > 0 
      ? moderatedReviews.reduce((sum, review) => {
          const created = new Date(review.createdAt);
          const moderated = new Date(review.moderation.moderatedAt!);
          return sum + (moderated.getTime() - created.getTime()) / (1000 * 60); // minutes
        }, 0) / moderatedReviews.length
      : 0;

    return {
      totalReviews,
      pendingReviews,
      approvedReviews,
      rejectedReviews,
      flaggedReviews,
      moderationStats: {
        averageResponseTime,
        autoApprovalRate: totalReviews > 0 ? (autoModeratedReviews.length / totalReviews) * 100 : 0,
        manualReviewRate: totalReviews > 0 ? (manualReviews.length / totalReviews) * 100 : 0,
        rejectionRate: totalReviews > 0 ? (rejectedReviews / totalReviews) * 100 : 0
      },
      qualityMetrics: {
        averageQualityScore: reviews.reduce((sum, r) => sum + r.moderation.qualityScore, 0) / totalReviews,
        averageSpamScore: reviews.reduce((sum, r) => sum + r.moderation.spamScore, 0) / totalReviews,
        averageHelpfulnessRatio: reviews.reduce((sum, r) => sum + r.analytics.helpfulnessRatio, 0) / totalReviews,
        verifiedPurchasePercentage: (reviews.filter(r => r.isVerified).length / totalReviews) * 100
      },
      contentAnalysis: {
        flagDistribution: reviews.reduce((acc, review) => {
          review.moderation.contentFlags.forEach(flag => {
            acc[flag.type] = (acc[flag.type] || 0) + 1;
          });
          return acc;
        }, {} as Record<any, number>),
        sentimentDistribution: {
          positive: reviews.filter(r => r.moderation.sentimentScore > 0.2).length,
          neutral: reviews.filter(r => r.moderation.sentimentScore >= -0.2 && r.moderation.sentimentScore <= 0.2).length,
          negative: reviews.filter(r => r.moderation.sentimentScore < -0.2).length
        },
        languageQuality: {
          averageLength: reviews.reduce((sum, r) => sum + r.content.length, 0) / totalReviews,
          readabilityScore: 75 // Mock score
        }
      },
      customerInsights: {
        topReviewers: [],
        newReviewers: reviews.filter(r => r.customer?.totalReviews === 1).length,
        repeatReviewers: reviews.filter(r => r.customer?.totalReviews && r.customer.totalReviews > 1).length
      },
      productInsights: {
        mostReviewedProducts: [],
        categoryDistribution: {},
        brandDistribution: {}
      },
      timeAnalysis: {
        reviewsThisMonth: reviews.filter(r => {
          const reviewDate = new Date(r.createdAt);
          const now = new Date();
          return reviewDate.getMonth() === now.getMonth() && reviewDate.getFullYear() === now.getFullYear();
        }).length,
        reviewsLastMonth: 0,
        growthRate: 0,
        peakHours: [14, 15, 16, 20, 21],
        peakDays: ['شنبه', 'یکشنبه', 'دوشنبه']
      }
    };
  }, [reviews]);

  // Initialize
  useEffect(() => {
    loadReviews();
    loadAnalytics();
  }, [loadReviews, loadAnalytics]);

  return {
    // State
    reviews: filteredReviews(),
    allReviews: reviews,
    analytics,
    loading,
    error,
    filters,
    sortBy,
    sortOrder,

    // Actions
    setFilters,
    setSortBy,
    setSortOrder,
    loadReviews,
    loadAnalytics,
    moderateReview,
    addReviewResponse,
    bulkModerateReviews,
    addAdminNote,
    getReviewById,
    respondToReview,
    getAnalytics
  };
};
