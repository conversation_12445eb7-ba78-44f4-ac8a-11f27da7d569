# 🔐 GlowRoya Authentication API Documentation

## Base URL
- **Development:** `http://localhost:3001/api/v1`
- **Production:** `https://yourdomain.com/api/v1`

## Authentication
Most endpoints require authentication via JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All API responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message in Persian",
  "data": { ... },
  "timestamp": "2025-06-06T00:00:00.000Z"
}
```

## Error Responses
```json
{
  "success": false,
  "error": {
    "message": "Error message in Persian",
    "code": "ERROR_CODE",
    "details": { ... }
  },
  "timestamp": "2025-06-06T00:00:00.000Z",
  "path": "/api/v1/endpoint",
  "method": "POST"
}
```

---

## 🔑 Authentication Endpoints

### 1. User Registration
**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "confirmPassword": "SecurePassword123!",
  "firstName": "علی",
  "lastName": "احمدی",
  "phone": "***********"
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "ثبت‌نام با موفقیت انجام شد",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "createdAt": "2025-06-06T00:00:00.000Z",
      "updatedAt": "2025-06-06T00:00:00.000Z"
    },
    "token": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "expiresIn": "7d"
  }
}
```

### 2. User Login
**POST** `/auth/login`

Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "ورود با موفقیت انجام شد",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "lastLoginAt": "2025-06-06T00:00:00.000Z"
    },
    "token": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "expiresIn": "7d"
  }
}
```

### 3. Refresh Token
**POST** `/auth/refresh`

Get a new access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "توکن با موفقیت تازه‌سازی شد",
  "data": {
    "token": "new-jwt-access-token",
    "expiresIn": "7d"
  }
}
```

### 4. User Logout
**POST** `/auth/logout`
🔒 **Requires Authentication**

Logout user and invalidate session.

**Request Body:**
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "خروج با موفقیت انجام شد"
}
```

### 5. Logout from All Devices
**POST** `/auth/logout-all`
🔒 **Requires Authentication**

Logout user from all devices and invalidate all sessions.

**Response (200):**
```json
{
  "success": true,
  "message": "خروج از همه دستگاه‌ها با موفقیت انجام شد"
}
```

### 6. Request Password Reset
**POST** `/auth/forgot-password`

Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "اگر ایمیل معتبر باشد، لینک بازیابی رمز عبور ارسال خواهد شد"
}
```

### 7. Reset Password
**POST** `/auth/reset-password`

Reset password using reset token.

**Request Body:**
```json
{
  "token": "password-reset-token",
  "password": "NewSecurePassword123!",
  "confirmPassword": "NewSecurePassword123!"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "رمز عبور با موفقیت تغییر یافت"
}
```

### 8. Verify Email
**POST** `/auth/verify-email`

Verify user email address.

**Request Body:**
```json
{
  "token": "email-verification-token"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "ایمیل با موفقیت تأیید شد"
}
```

### 9. Get Current User
**GET** `/auth/me`
🔒 **Requires Authentication**

Get current authenticated user information.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "avatar": null,
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "lastLoginAt": "2025-06-06T00:00:00.000Z",
      "createdAt": "2025-06-06T00:00:00.000Z",
      "updatedAt": "2025-06-06T00:00:00.000Z"
    }
  }
}
```

---

## 👤 User Management Endpoints

### 1. Get User Profile
**GET** `/users/profile`
🔒 **Requires Authentication**

Get detailed user profile with addresses.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "avatar": null,
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "lastLoginAt": "2025-06-06T00:00:00.000Z",
      "createdAt": "2025-06-06T00:00:00.000Z",
      "updatedAt": "2025-06-06T00:00:00.000Z",
      "addresses": [
        {
          "id": "address-id",
          "title": "خانه",
          "firstName": "علی",
          "lastName": "احمدی",
          "phone": "***********",
          "province": "تهران",
          "city": "تهران",
          "district": "ونک",
          "street": "خیابان ولیعصر، پلاک ۱۲۳",
          "postalCode": "1234567890",
          "isDefault": true
        }
      ]
    }
  }
}
```

### 2. Update User Profile
**PUT** `/users/profile`
🔒 **Requires Authentication**

Update user profile information.

**Request Body:**
```json
{
  "firstName": "علی",
  "lastName": "احمدی",
  "phone": "***********"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "پروفایل با موفقیت به‌روزرسانی شد",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "role": "CUSTOMER",
      "status": "ACTIVE",
      "isEmailVerified": false,
      "isPhoneVerified": false,
      "lastLoginAt": "2025-06-06T00:00:00.000Z",
      "createdAt": "2025-06-06T00:00:00.000Z",
      "updatedAt": "2025-06-06T00:00:00.000Z"
    }
  }
}
```

### 3. Change Password
**PUT** `/users/change-password`
🔒 **Requires Authentication**

Change user password.

**Request Body:**
```json
{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewSecurePassword123!",
  "confirmNewPassword": "NewSecurePassword123!"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "رمز عبور با موفقیت تغییر یافت"
}
```

---

## 📍 Address Management Endpoints

### 1. Add Address
**POST** `/users/addresses`
🔒 **Requires Authentication**

Add a new address to user profile.

**Request Body:**
```json
{
  "title": "خانه",
  "firstName": "علی",
  "lastName": "احمدی",
  "phone": "***********",
  "province": "تهران",
  "city": "تهران",
  "district": "ونک",
  "street": "خیابان ولیعصر، پلاک ۱۲۳",
  "postalCode": "1234567890",
  "isDefault": true
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "آدرس با موفقیت اضافه شد",
  "data": {
    "address": {
      "id": "address-id",
      "title": "خانه",
      "firstName": "علی",
      "lastName": "احمدی",
      "phone": "***********",
      "province": "تهران",
      "city": "تهران",
      "district": "ونک",
      "street": "خیابان ولیعصر، پلاک ۱۲۳",
      "postalCode": "1234567890",
      "isDefault": true,
      "createdAt": "2025-06-06T00:00:00.000Z",
      "updatedAt": "2025-06-06T00:00:00.000Z"
    }
  }
}
```

### 2. Update Address
**PUT** `/users/addresses/:id`
🔒 **Requires Authentication**

Update an existing address.

### 3. Delete Address
**DELETE** `/users/addresses/:id`
🔒 **Requires Authentication**

Delete an address.

### 4. Set Default Address
**PUT** `/users/addresses/:id/default`
🔒 **Requires Authentication**

Set an address as default.

---

## 👨‍💼 Admin Endpoints

### 1. Get All Users
**GET** `/users?page=1&limit=10&search=query&role=CUSTOMER&status=ACTIVE`
🔒 **Requires Admin Authentication**

Get paginated list of users with filtering.

### 2. Get User by ID
**GET** `/users/:id`
🔒 **Requires Admin Authentication**

Get detailed user information by ID.

### 3. Update User Status
**PUT** `/users/:id/status`
🔒 **Requires Admin Authentication**

Update user status (ACTIVE, INACTIVE, SUSPENDED, BANNED).

---

## 🔒 Security Features

### Password Requirements
- Minimum 8 characters
- Must contain uppercase letter
- Must contain lowercase letter
- Must contain number
- Must contain special character

### JWT Tokens
- **Access Token:** 7 days expiration
- **Refresh Token:** 30 days expiration
- **Reset Token:** 1 hour expiration

### Rate Limiting
- 100 requests per 15 minutes per IP
- Additional rate limiting on sensitive endpoints

### Session Management
- Multiple device support
- Session invalidation on logout
- Automatic cleanup of expired sessions

---

## 📱 Persian/RTL Support

All error messages and responses are in Persian (Farsi) to support the target audience. The API fully supports Persian names, addresses, and content.

## 🚀 Getting Started

1. Register a new user account
2. Login to get access tokens
3. Use the access token in Authorization header
4. Refresh tokens when they expire
5. Logout to invalidate sessions

For more information, visit the interactive API documentation at `/api/v1/docs`.
