import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Crown,
  Shield,
  Save,
  X,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminCustomers } from '../../../hooks/useAdminCustomers';
import { AdminCustomer, PERSIAN_CUSTOMER_MESSAGES } from '../../../types/adminCustomer';
import { formatCustomerName, formatPersianDate } from '../../../utils/customerUtils';
import toast from 'react-hot-toast';

const CustomerEditPage: React.FC = () => {
  const { id: customerId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getCustomerById, updateCustomer, loading, allCustomers } = useAdminCustomers();
  
  const [customer, setCustomer] = useState<AdminCustomer | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    isVip: false,
    status: 'active' as AdminCustomer['status'],
    tags: [] as string[],
    marketingConsent: {
      email: false,
      sms: false,
      push: false,
      phone: false
    }
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (customerId) {
      const customerData = getCustomerById(customerId);
      if (customerData) {
        setCustomer(customerData);
        setFormData({
          firstName: customerData.firstName,
          lastName: customerData.lastName,
          email: customerData.email,
          phone: customerData.phone || '',
          dateOfBirth: customerData.dateOfBirth || '',
          gender: customerData.gender || '',
          isVip: customerData.isVip,
          status: customerData.status,
          tags: customerData.tags,
          marketingConsent: customerData.marketingConsent
        });
      } else {
        // Only show error and navigate if we're not loading AND we have customers data
        // This prevents premature "not found" errors while data is loading
        if (!loading && allCustomers.length > 0) {
          toast.error('مشتری یافت نشد');
          navigate('/admin/customers');
        }
      }
    }
  }, [customerId, getCustomerById, navigate, loading, allCustomers]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'نام الزامی است';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'نام خانوادگی الزامی است';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'ایمیل الزامی است';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'فرمت ایمیل صحیح نیست';
    }

    if (formData.phone && !/^09\d{9}$/.test(formData.phone)) {
      newErrors.phone = 'شماره موبایل باید با 09 شروع شده و 11 رقم باشد';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !customer) return;

    setSaving(true);
    try {
      await updateCustomer(customer.id, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        isVip: formData.isVip,
        status: formData.status,
        tags: formData.tags,
        marketingConsent: formData.marketingConsent
      });
      
      toast.success('اطلاعات مشتری با موفقیت به‌روزرسانی شد');
      navigate(`/admin/customers/${customer.id}`);
    } catch (error) {
      toast.error('خطا در به‌روزرسانی اطلاعات مشتری');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleTagAdd = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }));
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  if (!customer && (loading || allCustomers.length === 0)) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              در حال بارگذاری...
            </h3>
            <p className="text-gray-600">
              لطفاً منتظر بمانید
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // If we have customers data but still no customer found, show error
  if (!customer && !loading && allCustomers.length > 0) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              مشتری یافت نشد
            </h3>
            <p className="text-gray-600 mb-4">
              مشتری مورد نظر در سیستم موجود نیست
            </p>
            <AdminButton
              onClick={() => navigate('/admin/customers')}
              variant="outline"
            >
              بازگشت به لیست مشتریان
            </AdminButton>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={`ویرایش ${formatCustomerName(customer)}`}
      subtitle="ویرایش اطلاعات مشتری"
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <AdminButton
            variant="outline"
            onClick={() => navigate(`/admin/customers/${customer.id}`)}
            icon={ArrowLeft}
          >
            بازگشت به جزئیات
          </AdminButton>
          
          <div className="flex items-center gap-3">
            <AdminButton
              variant="outline"
              onClick={() => navigate('/admin/customers')}
            >
              انصراف
            </AdminButton>
            <AdminButton
              onClick={handleSubmit}
              loading={saving}
              icon={Save}
            >
              ذخیره تغییرات
            </AdminButton>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <AdminCard title="اطلاعات پایه">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نام *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.firstName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="نام مشتری"
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نام خانوادگی *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.lastName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="نام خانوادگی مشتری"
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ایمیل *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="ایمیل مشتری"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  شماره موبایل
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="09123456789"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاریخ تولد
                </label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  جنسیت
                </label>
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">انتخاب کنید</option>
                  <option value="male">مرد</option>
                  <option value="female">زن</option>
                  <option value="other">سایر</option>
                </select>
              </div>
            </div>
          </AdminCard>

          {/* Status and Permissions */}
          <AdminCard title="وضعیت و دسترسی‌ها">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وضعیت حساب
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="active">فعال</option>
                  <option value="inactive">غیرفعال</option>
                  <option value="blocked">مسدود</option>
                  <option value="pending">در انتظار تایید</option>
                </select>
              </div>

              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isVip}
                    onChange={(e) => handleInputChange('isVip', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm font-medium text-gray-700">
                    مشتری ویژه (VIP)
                  </span>
                  <Crown className="w-4 h-4 text-yellow-500 mr-1" />
                </label>
              </div>
            </div>
          </AdminCard>

          {/* Marketing Consent */}
          <AdminCard title="رضایت بازاریابی">
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                تنظیم کنید که مشتری از چه طریق‌هایی می‌تواند پیام‌های بازاریابی دریافت کند.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.marketingConsent.email}
                    onChange={(e) => handleInputChange('marketingConsent', {
                      ...formData.marketingConsent,
                      email: e.target.checked
                    })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">ایمیل</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.marketingConsent.sms}
                    onChange={(e) => handleInputChange('marketingConsent', {
                      ...formData.marketingConsent,
                      sms: e.target.checked
                    })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">پیامک</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.marketingConsent.push}
                    onChange={(e) => handleInputChange('marketingConsent', {
                      ...formData.marketingConsent,
                      push: e.target.checked
                    })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">اعلان‌های اپلیکیشن</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.marketingConsent.phone}
                    onChange={(e) => handleInputChange('marketingConsent', {
                      ...formData.marketingConsent,
                      phone: e.target.checked
                    })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">تماس تلفنی</span>
                </label>
              </div>
            </div>
          </AdminCard>

          {/* Tags */}
          <AdminCard title="برچسب‌ها">
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleTagRemove(tag)}
                      className="mr-1 text-primary-600 hover:text-primary-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>

              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="برچسب جدید اضافه کنید"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleTagAdd(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                />
                <AdminButton
                  type="button"
                  variant="outline"
                  onClick={(e) => {
                    const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                    handleTagAdd(input.value);
                    input.value = '';
                  }}
                >
                  افزودن
                </AdminButton>
              </div>
            </div>
          </AdminCard>

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200">
            <AdminButton
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/customers')}
            >
              انصراف
            </AdminButton>
            <AdminButton
              type="submit"
              loading={saving}
              icon={Save}
            >
              ذخیره تغییرات
            </AdminButton>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default CustomerEditPage;
