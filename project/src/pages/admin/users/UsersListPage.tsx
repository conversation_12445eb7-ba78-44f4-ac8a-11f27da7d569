import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../../hooks/useAdminAuth';

const UsersListPage: React.FC = () => {
  const navigate = useNavigate();
  const { checkPermission } = useAdminAuth();

  useEffect(() => {
    // Check if user has permission to access user management
    if (!checkPermission('users', 'read')) {
      navigate('/admin/dashboard');
      return;
    }

    // Redirect to admin users page by default
    navigate('/admin/users/admins', { replace: true });
  }, [navigate, checkPermission]);

  return null;
};

export default UsersListPage;
