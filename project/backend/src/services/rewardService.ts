import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// Types for reward management
export interface Reward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  type: RewardType;
  value: number;
  isActive: boolean;
  stock?: number;
  image?: string;
  validUntil?: Date;
  terms: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateRewardData {
  title: string;
  description: string;
  pointsCost: number;
  type: RewardType;
  value: number;
  isActive?: boolean;
  stock?: number;
  image?: string;
  validUntil?: Date;
  terms: string[];
}

export interface UpdateRewardData {
  title?: string;
  description?: string;
  pointsCost?: number;
  type?: RewardType;
  value?: number;
  isActive?: boolean;
  stock?: number;
  image?: string;
  validUntil?: Date;
  terms?: string[];
}

export interface RewardFilters {
  type?: RewardType;
  isActive?: boolean;
  minPointsCost?: number;
  maxPointsCost?: number;
  hasStock?: boolean;
  validOnly?: boolean;
  search?: string;
}

export interface RewardListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface RedemptionData {
  userId: string;
  rewardId: string;
  quantity?: number;
}

export interface RedemptionRecord {
  id: string;
  userId: string;
  rewardId: string;
  pointsUsed: number;
  quantity: number;
  status: RedemptionStatus;
  redeemedAt: Date;
  processedAt?: Date;
  notes?: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reward: Reward;
}

export enum RewardType {
  DISCOUNT = 'DISCOUNT',
  PRODUCT = 'PRODUCT',
  SHIPPING = 'SHIPPING',
  EXPERIENCE = 'EXPERIENCE',
}

export enum RedemptionStatus {
  PENDING = 'PENDING',
  PROCESSED = 'PROCESSED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

// Default rewards catalog
export const DEFAULT_REWARDS: CreateRewardData[] = [
  {
    title: 'تخفیف ۵۰ هزار تومانی',
    description: 'کد تخفیف ۵۰ هزار تومانی برای خرید بعدی',
    pointsCost: 500,
    type: RewardType.DISCOUNT,
    value: 50000,
    isActive: true,
    terms: [
      'حداقل خرید ۲۰۰ هزار تومان',
      'قابل استفاده تا ۳۰ روز',
      'غیرقابل تجمیع با سایر تخفیف‌ها'
    ]
  },
  {
    title: 'ارسال رایگان',
    description: 'ارسال رایگان برای سفارش بعدی',
    pointsCost: 200,
    type: RewardType.SHIPPING,
    value: 25000,
    isActive: true,
    terms: [
      'قابل استفاده برای یک سفارش',
      'اعتبار ۱۵ روزه'
    ]
  },
  {
    title: 'کیت نمونه محصولات',
    description: 'کیت نمونه شامل ۵ محصول پرطرفدار',
    pointsCost: 800,
    type: RewardType.PRODUCT,
    value: 150000,
    isActive: true,
    stock: 50,
    terms: [
      'ارسال رایگان',
      'محدود به یک عدد در ماه'
    ]
  },
  {
    title: 'مشاوره تخصصی پوست',
    description: 'جلسه مشاوره آنلاین با متخصص پوست',
    pointsCost: 1000,
    type: RewardType.EXPERIENCE,
    value: 200000,
    isActive: true,
    stock: 10,
    terms: [
      'رزرو قبلی الزامی',
      'مدت جلسه ۳۰ دقیقه',
      'امکان ضبط جلسه'
    ]
  }
];

export class RewardService {
  // Get rewards with filtering and pagination
  static async getRewards(
    filters: RewardFilters = {},
    options: RewardListOptions = {}
  ): Promise<{
    rewards: Reward[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        type,
        isActive,
        minPointsCost,
        maxPointsCost,
        hasStock,
        validOnly,
        search,
      } = filters;

      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (type) {
        where.type = type;
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      if (minPointsCost !== undefined) {
        where.pointsCost = { ...where.pointsCost, gte: minPointsCost };
      }

      if (maxPointsCost !== undefined) {
        where.pointsCost = { ...where.pointsCost, lte: maxPointsCost };
      }

      if (hasStock !== undefined) {
        if (hasStock) {
          where.OR = [
            { stock: { gt: 0 } },
            { stock: null }
          ];
        } else {
          where.stock = { lte: 0 };
        }
      }

      if (validOnly) {
        where.OR = [
          { validUntil: { gte: new Date() } },
          { validUntil: null }
        ];
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Note: Since we don't have a rewards table in the schema yet,
      // we'll simulate this with in-memory data for now
      // In a real implementation, you would create a rewards table

      // For now, return default rewards with filtering applied
      let rewards = DEFAULT_REWARDS.map((reward, index) => ({
        id: `reward_${index + 1}`,
        ...reward,
        createdAt: new Date(),
        updatedAt: new Date(),
      })) as Reward[];

      // Apply filters
      if (type) {
        rewards = rewards.filter(r => r.type === type);
      }

      if (typeof isActive === 'boolean') {
        rewards = rewards.filter(r => r.isActive === isActive);
      }

      if (minPointsCost !== undefined) {
        rewards = rewards.filter(r => r.pointsCost >= minPointsCost);
      }

      if (maxPointsCost !== undefined) {
        rewards = rewards.filter(r => r.pointsCost <= maxPointsCost);
      }

      if (hasStock !== undefined) {
        if (hasStock) {
          rewards = rewards.filter(r => !r.stock || r.stock > 0);
        } else {
          rewards = rewards.filter(r => r.stock !== undefined && r.stock <= 0);
        }
      }

      if (search) {
        const searchLower = search.toLowerCase();
        rewards = rewards.filter(r => 
          r.title.toLowerCase().includes(searchLower) ||
          r.description.toLowerCase().includes(searchLower)
        );
      }

      // Apply pagination
      const total = rewards.length;
      const paginatedRewards = rewards.slice(skip, skip + limit);
      const totalPages = Math.ceil(total / limit);

      return {
        rewards: paginatedRewards,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Error getting rewards:', error);
      throw new AppError('خطا در دریافت لیست جوایز', 500, 'GET_REWARDS_ERROR');
    }
  }

  // Get reward by ID
  static async getRewardById(rewardId: string): Promise<Reward | null> {
    try {
      // For now, simulate with default rewards
      const rewards = DEFAULT_REWARDS.map((reward, index) => ({
        id: `reward_${index + 1}`,
        ...reward,
        createdAt: new Date(),
        updatedAt: new Date(),
      })) as Reward[];

      const reward = rewards.find(r => r.id === rewardId);
      return reward || null;
    } catch (error) {
      logger.error('Error getting reward by ID:', error);
      throw new AppError('خطا در دریافت جایزه', 500, 'GET_REWARD_ERROR');
    }
  }

  // Redeem reward
  static async redeemReward(data: RedemptionData): Promise<RedemptionRecord> {
    try {
      const { userId, rewardId, quantity = 1 } = data;

      // Get reward
      const reward = await this.getRewardById(rewardId);
      if (!reward) {
        throw new AppError('جایزه یافت نشد', 404, 'REWARD_NOT_FOUND');
      }

      if (!reward.isActive) {
        throw new AppError('این جایزه در حال حاضر فعال نیست', 400, 'REWARD_INACTIVE');
      }

      // Check stock
      if (reward.stock !== undefined && reward.stock < quantity) {
        throw new AppError('موجودی این جایزه کافی نیست', 400, 'INSUFFICIENT_STOCK');
      }

      // Check validity
      if (reward.validUntil && reward.validUntil < new Date()) {
        throw new AppError('مهلت استفاده از این جایزه به پایان رسیده است', 400, 'REWARD_EXPIRED');
      }

      const totalPointsCost = reward.pointsCost * quantity;

      // Import LoyaltyService to redeem points
      const { default: LoyaltyService } = await import('./loyaltyService');

      // Redeem points
      await LoyaltyService.redeemPoints({
        userId,
        points: totalPointsCost,
        description: `استفاده از جایزه: ${reward.title}`,
      });

      // Create redemption record (simulated)
      const redemption: RedemptionRecord = {
        id: `redemption_${Date.now()}`,
        userId,
        rewardId,
        pointsUsed: totalPointsCost,
        quantity,
        status: RedemptionStatus.PENDING,
        redeemedAt: new Date(),
        user: {
          id: userId,
          firstName: 'کاربر',
          lastName: 'نمونه',
          email: '<EMAIL>',
        },
        reward,
      };

      logger.info(`Reward redeemed: ${rewardId} by user ${userId}`);

      return redemption;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error redeeming reward:', error);
      throw new AppError('خطا در دریافت جایزه', 500, 'REDEEM_REWARD_ERROR');
    }
  }

  // Get available rewards for user (based on points)
  static async getAvailableRewardsForUser(userId: string): Promise<Reward[]> {
    try {
      // Import LoyaltyService to get user points
      const { default: LoyaltyService } = await import('./loyaltyService');

      const loyaltyAccount = await LoyaltyService.getLoyaltyAccountByUserId(userId);
      const userPoints = loyaltyAccount?.points || 0;

      const { rewards } = await this.getRewards({
        isActive: true,
        validOnly: true,
        hasStock: true,
      });

      // Filter rewards user can afford
      return rewards.filter(reward => reward.pointsCost <= userPoints);
    } catch (error) {
      logger.error('Error getting available rewards for user:', error);
      throw new AppError('خطا در دریافت جوایز قابل دسترس', 500, 'GET_AVAILABLE_REWARDS_ERROR');
    }
  }
}

export default RewardService;
