import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Eye, EyeOff, Mail, Lock, LogIn } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { LoginFormData, PERSIAN_AUTH_MESSAGES } from '../../types/auth';
import { EmailValidator } from '../../utils/authUtils';

// Validation schema
const loginSchema = yup.object({
  email: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.emailRequired)
    .test('email', PERSIAN_AUTH_MESSAGES.errors.emailInvalid, (value) => 
      value ? EmailValidator.validate(value) : false
    ),
  password: yup
    .string()
    .required(PERSIAN_AUTH_MESSAGES.errors.passwordRequired)
    .min(6, 'رمز عبور باید حداقل ۶ کاراکتر باشد'),
  rememberMe: yup.boolean()
});

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, redirectTo }) => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setError
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false
    },
    mode: 'onChange'
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      clearError();
      await login({
        email: EmailValidator.normalize(data.email),
        password: data.password,
        rememberMe: data.rememberMe
      });
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      // Handle specific validation errors
      if (error.message.includes('ایمیل')) {
        setError('email', { message: error.message });
      } else if (error.message.includes('رمز عبور')) {
        setError('password', { message: error.message });
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md mx-auto"
    >
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-primary-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            ورود به حساب کاربری
          </h2>
          <p className="text-gray-600">
            به گلو رویا خوش آمدید
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 inline ml-1" />
              {PERSIAN_AUTH_MESSAGES.labels.email} *
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="email"
                  placeholder={PERSIAN_AUTH_MESSAGES.placeholders.email}
                  className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                    errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
              )}
            />
            {errors.email && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-600"
              >
                {errors.email.message}
              </motion.p>
            )}
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Lock className="w-4 h-4 inline ml-1" />
              {PERSIAN_AUTH_MESSAGES.labels.password} *
            </label>
            <div className="relative">
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type={showPassword ? 'text' : 'password'}
                    placeholder={PERSIAN_AUTH_MESSAGES.placeholders.password}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors pr-12 ${
                      errors.password ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                  />
                )}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-600"
              >
                {errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Remember Me & Forgot Password */}
          <div className="flex items-center justify-between">
            <Controller
              name="rememberMe"
              control={control}
              render={({ field }) => (
                <label className="flex items-center">
                  <input
                    {...field}
                    type="checkbox"
                    checked={field.value}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    disabled={isLoading}
                  />
                  <span className="mr-2 text-sm text-gray-600">
                    {PERSIAN_AUTH_MESSAGES.labels.rememberMe}
                  </span>
                </label>
              )}
            />
            <Link
              to="/forgot-password"
              className="text-sm text-primary-600 hover:text-primary-700 transition-colors"
            >
              {PERSIAN_AUTH_MESSAGES.buttons.forgotPassword}
            </Link>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                در حال ورود...
              </>
            ) : (
              <>
                <LogIn className="w-5 h-5" />
                {PERSIAN_AUTH_MESSAGES.buttons.login}
              </>
            )}
          </button>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-3"
            >
              <p className="text-sm text-red-600 text-center">{error}</p>
            </motion.div>
          )}

          {/* Register Link */}
          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              حساب کاربری ندارید؟{' '}
              <Link
                to="/register"
                className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
              >
                {PERSIAN_AUTH_MESSAGES.buttons.register}
              </Link>
            </p>
          </div>
        </form>
      </div>

      {/* Demo Credentials */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4"
      >
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          🔑 اطلاعات ورود نمونه:
        </h4>
        <div className="text-xs text-blue-700 space-y-1">
          <p><strong>ایمیل:</strong> <EMAIL></p>
          <p><strong>رمز عبور:</strong> password123</p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default LoginForm;
