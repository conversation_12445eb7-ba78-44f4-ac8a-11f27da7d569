import { useState, useEffect, useCallback } from 'react';
import { 
  MobileDetection, 
  DeviceType, 
  ResponsiveBreakpoints,
  MobileViewport,
  DEFAULT_BREAKPOINTS 
} from '../types/mobile';
import { 
  detectMobileDevice, 
  getDeviceType, 
  getViewportInfo,
  debounce 
} from '../utils/mobileUtils';

interface UseMobileDetectionOptions {
  breakpoints?: ResponsiveBreakpoints;
  debounceDelay?: number;
  enableOrientationChange?: boolean;
  enableViewportTracking?: boolean;
}

interface UseMobileDetectionReturn extends MobileDetection {
  viewport: MobileViewport;
  isLandscape: boolean;
  isPortrait: boolean;
  hasNotch: boolean;
  isStandalone: boolean;
  refreshDetection: () => void;
}

/**
 * Hook for mobile device detection and responsive behavior
 */
export const useMobileDetection = (
  options: UseMobileDetectionOptions = {}
): UseMobileDetectionReturn => {
  const {
    breakpoints = DEFAULT_BREAKPOINTS,
    debounceDelay = 250,
    enableOrientationChange = true,
    enableViewportTracking = true
  } = options;

  // State for mobile detection
  const [detection, setDetection] = useState<MobileDetection>(() => detectMobileDevice());
  const [viewport, setViewport] = useState<MobileViewport>(() => getViewportInfo());

  // Refresh detection function
  const refreshDetection = useCallback(() => {
    const newDetection = detectMobileDevice();
    const newViewport = getViewportInfo();
    
    setDetection(newDetection);
    setViewport(newViewport);
  }, []);

  // Debounced refresh function
  const debouncedRefresh = useCallback(
    debounce(refreshDetection, debounceDelay),
    [refreshDetection, debounceDelay]
  );

  // Handle window resize
  useEffect(() => {
    if (!enableViewportTracking) return;

    const handleResize = () => {
      debouncedRefresh();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [debouncedRefresh, enableViewportTracking]);

  // Handle orientation change
  useEffect(() => {
    if (!enableOrientationChange) return;

    const handleOrientationChange = () => {
      // Small delay to ensure viewport dimensions are updated
      setTimeout(() => {
        refreshDetection();
      }, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    return () => window.removeEventListener('orientationchange', handleOrientationChange);
  }, [refreshDetection, enableOrientationChange]);

  // Computed properties
  const isLandscape = detection.orientation === 'landscape';
  const isPortrait = detection.orientation === 'portrait';
  
  // Check for device notch (iPhone X and newer)
  const hasNotch = useCallback(() => {
    const safeAreaTop = parseInt(
      getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top') || '0'
    );
    return safeAreaTop > 0;
  }, []);

  // Check if running as standalone PWA
  const isStandalone = useCallback(() => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }, []);

  return {
    ...detection,
    viewport,
    isLandscape,
    isPortrait,
    hasNotch: hasNotch(),
    isStandalone: isStandalone(),
    refreshDetection
  };
};

/**
 * Hook for responsive breakpoint detection
 */
export const useBreakpoint = (breakpoints: ResponsiveBreakpoints = DEFAULT_BREAKPOINTS) => {
  const { screenWidth } = useMobileDetection({ breakpoints });

  return {
    isMobile: screenWidth < breakpoints.mobile,
    isTablet: screenWidth >= breakpoints.mobile && screenWidth < breakpoints.desktop,
    isDesktop: screenWidth >= breakpoints.desktop && screenWidth < breakpoints.wide,
    isWide: screenWidth >= breakpoints.wide,
    screenWidth
  };
};

/**
 * Hook for device orientation tracking
 */
export const useOrientation = () => {
  const { orientation, screenWidth, screenHeight } = useMobileDetection({
    enableOrientationChange: true
  });

  const [orientationAngle, setOrientationAngle] = useState(0);

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientationAngle(window.orientation || 0);
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    return () => window.removeEventListener('orientationchange', handleOrientationChange);
  }, []);

  return {
    orientation,
    orientationAngle,
    isLandscape: orientation === 'landscape',
    isPortrait: orientation === 'portrait',
    screenWidth,
    screenHeight
  };
};

/**
 * Hook for touch capability detection
 */
export const useTouchDetection = () => {
  const { touchSupported, deviceType } = useMobileDetection();
  
  const [isTouch, setIsTouch] = useState(touchSupported);

  useEffect(() => {
    const handleTouchStart = () => {
      setIsTouch(true);
    };

    const handleMouseDown = () => {
      setIsTouch(false);
    };

    // Listen for first touch or mouse interaction
    document.addEventListener('touchstart', handleTouchStart, { once: true });
    document.addEventListener('mousedown', handleMouseDown, { once: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  return {
    touchSupported,
    isTouch,
    isTouchDevice: deviceType === 'mobile' || deviceType === 'tablet',
    hasHover: !isTouch && deviceType === 'desktop'
  };
};

/**
 * Hook for viewport size tracking
 */
export const useViewportSize = () => {
  const { viewport, refreshDetection } = useMobileDetection({
    enableViewportTracking: true
  });

  const [dimensions, setDimensions] = useState({
    width: viewport.width,
    height: viewport.height
  });

  useEffect(() => {
    setDimensions({
      width: viewport.width,
      height: viewport.height
    });
  }, [viewport.width, viewport.height]);

  return {
    ...dimensions,
    viewport,
    aspectRatio: dimensions.width / dimensions.height,
    isSquare: Math.abs(dimensions.width - dimensions.height) < 50,
    refresh: refreshDetection
  };
};

/**
 * Hook for safe area insets (for devices with notches)
 */
export const useSafeArea = () => {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  });

  useEffect(() => {
    const updateInsets = () => {
      const style = getComputedStyle(document.documentElement);
      setInsets({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0')
      });
    };

    updateInsets();
    
    // Update on orientation change
    window.addEventListener('orientationchange', updateInsets);
    return () => window.removeEventListener('orientationchange', updateInsets);
  }, []);

  return insets;
};

export default useMobileDetection;
