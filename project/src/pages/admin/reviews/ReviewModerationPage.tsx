import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  CheckCircle,
  XCircle,
  Flag,
  AlertTriangle,
  Clock,
  Filter,
  Search,
  Download,
  RefreshCw,
  Star,
  MessageSquare,
  User,
  Calendar,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminReviews } from '../../../hooks/useAdminReviews';
import { AdminReview, ReviewModerationDecision, BulkReviewAction } from '../../../types/adminReview';
import { formatPersianDateTime, formatPersianDate } from '../../../utils/reviewModeration';
import toast from 'react-hot-toast';

const ReviewModerationPage: React.FC = () => {
  const { reviews, loading, moderateReview, bulkModerateReviews } = useAdminReviews();
  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkAction, setBulkAction] = useState<BulkReviewAction>({
    action: 'approve',
    reviewIds: [],
    notes: '',
    notifyCustomers: true
  });

  // Filter reviews based on current filters
  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.title.toLowerCase().includes(searchValue.toLowerCase()) ||
                         review.comment.toLowerCase().includes(searchValue.toLowerCase()) ||
                         review.customer.name.toLowerCase().includes(searchValue.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || review.moderation.status === statusFilter;
    
    const matchesPriority = priorityFilter === 'all' || 
                           (priorityFilter === 'high_spam' && review.moderation.spamScore > 70) ||
                           (priorityFilter === 'low_quality' && review.moderation.qualityScore < 50) ||
                           (priorityFilter === 'flagged' && review.moderation.contentFlags.length > 0);
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const handleSelectReview = (reviewId: string) => {
    setSelectedReviews(prev => 
      prev.includes(reviewId) 
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedReviews(selected ? filteredReviews.map(r => r.id) : []);
  };

  const handleQuickModeration = async (reviewId: string, action: 'approve' | 'reject' | 'flag') => {
    try {
      await moderateReview(reviewId, {
        action,
        notes: '',
        notifyCustomer: true
      });
      toast.success(`نظر ${action === 'approve' ? 'تایید' : action === 'reject' ? 'رد' : 'علامت‌گذاری'} شد`);
    } catch (error) {
      toast.error('خطا در بررسی نظر');
    }
  };

  const handleBulkModeration = async () => {
    if (selectedReviews.length === 0) {
      toast.error('لطفاً حداقل یک نظر انتخاب کنید');
      return;
    }

    try {
      await bulkModerateReviews({
        ...bulkAction,
        reviewIds: selectedReviews
      });
      toast.success(`${selectedReviews.length} نظر با موفقیت بررسی شد`);
      setSelectedReviews([]);
      setShowBulkModal(false);
    } catch (error) {
      toast.error('خطا در بررسی گروهی نظرات');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'flagged':
        return <Flag className="w-4 h-4 text-yellow-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'flagged':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIndicator = (review: AdminReview) => {
    if (review.moderation.spamScore > 70) {
      return <div className="w-2 h-2 bg-red-500 rounded-full" title="اسپم بالا" />;
    }
    if (review.moderation.qualityScore < 50) {
      return <div className="w-2 h-2 bg-orange-500 rounded-full" title="کیفیت پایین" />;
    }
    if (review.moderation.contentFlags.length > 0) {
      return <div className="w-2 h-2 bg-yellow-500 rounded-full" title="دارای علامت" />;
    }
    return null;
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <AdminLayout
      title="بررسی نظرات"
      subtitle="مدیریت و بررسی نظرات مشتریان"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="جستجو در نظرات..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">همه وضعیت‌ها</option>
              <option value="pending">در انتظار بررسی</option>
              <option value="approved">تایید شده</option>
              <option value="rejected">رد شده</option>
              <option value="flagged">علامت‌گذاری شده</option>
            </select>
            
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">همه اولویت‌ها</option>
              <option value="high_spam">اسپم بالا</option>
              <option value="low_quality">کیفیت پایین</option>
              <option value="flagged">دارای علامت</option>
            </select>
          </div>
          
          <div className="flex items-center gap-3">
            {selectedReviews.length > 0 && (
              <AdminButton
                variant="outline"
                onClick={() => setShowBulkModal(true)}
                icon={Shield}
              >
                بررسی گروهی ({selectedReviews.length})
              </AdminButton>
            )}
            <AdminButton
              variant="outline"
              icon={Download}
            >
              خروجی گزارش
            </AdminButton>
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              loading={loading}
            >
              به‌روزرسانی
            </AdminButton>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {reviews.filter(r => r.moderation.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-600">در انتظار بررسی</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {reviews.filter(r => r.moderation.status === 'approved').length}
                </div>
                <div className="text-sm text-gray-600">تایید شده</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <XCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {reviews.filter(r => r.moderation.status === 'rejected').length}
                </div>
                <div className="text-sm text-gray-600">رد شده</div>
              </div>
            </div>
          </AdminCard>

          <AdminCard>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Flag className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {reviews.filter(r => r.moderation.status === 'flagged').length}
                </div>
                <div className="text-sm text-gray-600">علامت‌گذاری شده</div>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Reviews List */}
        <AdminCard>
          <div className="space-y-4">
            {/* Select All */}
            <div className="flex items-center gap-2 pb-4 border-b border-gray-200">
              <input
                type="checkbox"
                checked={filteredReviews.length > 0 && selectedReviews.length === filteredReviews.length}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-600">
                انتخاب همه ({filteredReviews.length} نظر)
              </span>
            </div>

            {/* Reviews */}
            <div className="space-y-4">
              {filteredReviews.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  هیچ نظری برای بررسی یافت نشد
                </div>
              ) : (
                filteredReviews.map((review) => (
                  <motion.div
                    key={review.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start gap-4">
                      <input
                        type="checkbox"
                        checked={selectedReviews.includes(review.id)}
                        onChange={() => handleSelectReview(review.id)}
                        className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              {getPriorityIndicator(review)}
                              <span className="font-medium text-gray-900">
                                {review.customer.name}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              {renderStars(review.rating)}
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(review.moderation.status)}`}>
                              {review.moderation.status === 'pending' ? 'در انتظار' :
                               review.moderation.status === 'approved' ? 'تایید' :
                               review.moderation.status === 'rejected' ? 'رد' : 'علامت'}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">
                              {formatPersianDate(review.createdAt)}
                            </span>
                            <div className="flex items-center gap-1">
                              <AdminButton
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickModeration(review.id, 'approve')}
                                icon={CheckCircle}
                                className="text-green-600 hover:text-green-700"
                              >
                                تایید
                              </AdminButton>
                              <AdminButton
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickModeration(review.id, 'reject')}
                                icon={XCircle}
                                className="text-red-600 hover:text-red-700"
                              >
                                رد
                              </AdminButton>
                              <AdminButton
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuickModeration(review.id, 'flag')}
                                icon={Flag}
                                className="text-yellow-600 hover:text-yellow-700"
                              >
                                علامت
                              </AdminButton>
                              <AdminButton
                                size="sm"
                                variant="outline"
                                icon={Eye}
                              >
                                جزئیات
                              </AdminButton>
                            </div>
                          </div>
                        </div>

                        <div className="mb-3">
                          <h4 className="font-medium text-gray-900 mb-1">{review.title}</h4>
                          <p className="text-gray-700 text-sm line-clamp-2">{review.comment}</p>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <User className="w-4 h-4" />
                              <span>{review.product.name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <MessageSquare className="w-4 h-4" />
                              <span>{review.helpfulVotes} مفید</span>
                            </div>
                            {review.isVerifiedPurchase && (
                              <div className="flex items-center gap-1 text-green-600">
                                <CheckCircle className="w-4 h-4" />
                                <span>خرید تایید شده</span>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm">
                            <span className="text-gray-600">
                              کیفیت: <span className={review.moderation.qualityScore >= 70 ? 'text-green-600' : 
                                                    review.moderation.qualityScore >= 50 ? 'text-yellow-600' : 'text-red-600'}>
                                {review.moderation.qualityScore}
                              </span>
                            </span>
                            <span className="text-gray-600">
                              اسپم: <span className={review.moderation.spamScore <= 30 ? 'text-green-600' : 
                                                  review.moderation.spamScore <= 60 ? 'text-yellow-600' : 'text-red-600'}>
                                {review.moderation.spamScore}
                              </span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Bulk Moderation Modal */}
      <AdminFormModal
        isOpen={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        onSubmit={handleBulkModeration}
        title={`بررسی گروهی ${selectedReviews.length} نظر`}
        submitText="اعمال تصمیم"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عملیات
            </label>
            <select
              value={bulkAction.action}
              onChange={(e) => setBulkAction(prev => ({ 
                ...prev, 
                action: e.target.value as any 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="approve">تایید همه</option>
              <option value="reject">رد همه</option>
              <option value="flag">علامت‌گذاری همه</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              یادداشت (اختیاری)
            </label>
            <textarea
              rows={3}
              value={bulkAction.notes}
              onChange={(e) => setBulkAction(prev => ({ 
                ...prev, 
                notes: e.target.value 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="دلیل تصمیم خود را بنویسید"
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="notifyCustomers"
              checked={bulkAction.notifyCustomers}
              onChange={(e) => setBulkAction(prev => ({ 
                ...prev, 
                notifyCustomers: e.target.checked 
              }))}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <label htmlFor="notifyCustomers" className="mr-2 text-sm text-gray-700">
              اطلاع‌رسانی به مشتریان
            </label>
          </div>
        </div>
      </AdminFormModal>
    </AdminLayout>
  );
};

export default ReviewModerationPage;
