import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Eye, Shuffle, Tag, Settings, Target, Calendar } from 'lucide-react';
import AdminForm, { 
  AdminFormField, 
  AdminInput, 
  AdminTextarea, 
  AdminSelect, 
  AdminCheckbox,
  AdminFormAlert 
} from '../common/AdminForm';
import AdminButton from '../common/AdminButton';
import AdminCard from '../common/AdminCard';
import { PromotionFormData, ContentStatus, PromotionType } from '../../../types/adminContent';
import { validatePromotionForm } from '../../../utils/contentUtils';

interface PromotionEditorProps {
  initialData?: Partial<PromotionFormData>;
  onSave: (data: PromotionFormData) => Promise<void>;
  onCancel: () => void;
  onGenerateCode?: () => string;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

const PromotionEditor: React.FC<PromotionEditorProps> = ({
  initialData,
  onSave,
  onCancel,
  onGenerateCode,
  loading = false,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<PromotionFormData>({
    title: '',
    description: '',
    type: 'percentage',
    discountValue: 0,
    minimumOrderAmount: 0,
    maximumDiscountAmount: 0,
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    usageLimit: 0,
    usagePerCustomer: 1,
    code: '',
    isCodeRequired: true,
    showOnHomepage: false,
    showInCart: true,
    showOnProductPages: false,
    terms: '',
    conditions: [],
    status: 'draft',
    isActive: true,
    startDate: '',
    endDate: '',
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'targeting' | 'limits' | 'display'>('basic');

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  const handleInputChange = (field: keyof PromotionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleGenerateCode = () => {
    if (onGenerateCode) {
      const newCode = onGenerateCode();
      handleInputChange('code', newCode);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validatePromotionForm(formData);
    
    if (!validation.isValid) {
      const errorMap: Record<string, string> = {};
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message;
      });
      setErrors(errorMap);
      
      const warningMap: Record<string, string> = {};
      validation.warnings.forEach(warning => {
        warningMap[warning.field] = warning.message;
      });
      setWarnings(warningMap);
      
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving promotion:', error);
    }
  };

  const promotionTypes: { value: PromotionType; label: string }[] = [
    { value: 'percentage', label: 'درصدی' },
    { value: 'fixed_amount', label: 'مبلغ ثابت' },
    { value: 'buy_one_get_one', label: 'یکی بخر یکی بگیر' },
    { value: 'free_shipping', label: 'ارسال رایگان' }
  ];

  const statusOptions: { value: ContentStatus; label: string }[] = [
    { value: 'draft', label: 'پیش‌نویس' },
    { value: 'published', label: 'منتشر شده' },
    { value: 'scheduled', label: 'زمان‌بندی شده' },
    { value: 'archived', label: 'بایگانی شده' }
  ];

  const tabs = [
    { id: 'basic', label: 'اطلاعات پایه', icon: Settings },
    { id: 'targeting', label: 'هدف‌گیری', icon: Target },
    { id: 'limits', label: 'محدودیت‌ها', icon: Tag },
    { id: 'display', label: 'نمایش', icon: Eye }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <AdminCard>
        <div className="flex space-x-1 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-admin-100 text-admin-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </AdminCard>

      <AdminForm onSubmit={handleSubmit} loading={loading}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information Tab */}
            {activeTab === 'basic' && (
              <AdminCard title="اطلاعات پایه">
                <div className="space-y-4">
                  <AdminFormField label="عنوان تخفیف" required error={errors.title}>
                    <AdminInput
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="عنوان تخفیف را وارد کنید"
                      error={!!errors.title}
                    />
                  </AdminFormField>

                  <AdminFormField label="توضیحات" required error={errors.description}>
                    <AdminTextarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="توضیحات کامل تخفیف"
                      rows={3}
                      error={!!errors.description}
                    />
                  </AdminFormField>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="نوع تخفیف" required error={errors.type}>
                      <AdminSelect
                        value={formData.type}
                        onChange={(e) => handleInputChange('type', e.target.value)}
                        error={!!errors.type}
                      >
                        {promotionTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </AdminSelect>
                    </AdminFormField>

                    <AdminFormField label="مقدار تخفیف" required error={errors.discountValue}>
                      <AdminInput
                        type="number"
                        value={formData.discountValue}
                        onChange={(e) => handleInputChange('discountValue', parseFloat(e.target.value) || 0)}
                        placeholder={formData.type === 'percentage' ? 'درصد' : 'تومان'}
                        min="0"
                        max={formData.type === 'percentage' ? "100" : undefined}
                        error={!!errors.discountValue}
                      />
                    </AdminFormField>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="حداقل مبلغ سفارش" error={errors.minimumOrderAmount}>
                      <AdminInput
                        type="number"
                        value={formData.minimumOrderAmount}
                        onChange={(e) => handleInputChange('minimumOrderAmount', parseFloat(e.target.value) || 0)}
                        placeholder="تومان"
                        min="0"
                        error={!!errors.minimumOrderAmount}
                      />
                    </AdminFormField>

                    {formData.type === 'percentage' && (
                      <AdminFormField label="حداکثر مبلغ تخفیف" error={errors.maximumDiscountAmount}>
                        <AdminInput
                          type="number"
                          value={formData.maximumDiscountAmount}
                          onChange={(e) => handleInputChange('maximumDiscountAmount', parseFloat(e.target.value) || 0)}
                          placeholder="تومان"
                          min="0"
                          error={!!errors.maximumDiscountAmount}
                        />
                      </AdminFormField>
                    )}
                  </div>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.isCodeRequired}
                      onChange={(checked) => handleInputChange('isCodeRequired', checked)}
                      label="نیاز به کد تخفیف"
                      description="آیا مشتری باید کد تخفیف وارد کند؟"
                    />
                  </AdminFormField>

                  {formData.isCodeRequired && (
                    <AdminFormField label="کد تخفیف" required error={errors.code}>
                      <div className="flex gap-2">
                        <AdminInput
                          value={formData.code}
                          onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                          placeholder="کد تخفیف"
                          error={!!errors.code}
                          className="flex-1"
                        />
                        <AdminButton 
                          type="button"
                          variant="outline" 
                          icon={Shuffle}
                          onClick={handleGenerateCode}
                        >
                          تولید
                        </AdminButton>
                      </div>
                    </AdminFormField>
                  )}
                </div>
              </AdminCard>
            )}

            {/* Targeting Tab */}
            {activeTab === 'targeting' && (
              <AdminCard title="هدف‌گیری محصولات">
                <div className="space-y-4">
                  <AdminFormField label="دسته‌بندی‌های قابل اعمال">
                    <AdminInput
                      value={formData.applicableCategories?.join(', ')}
                      onChange={(e) => handleInputChange('applicableCategories', e.target.value.split(',').map(c => c.trim()).filter(c => c))}
                      placeholder="نام دسته‌بندی‌ها را با کاما جدا کنید"
                    />
                  </AdminFormField>

                  <AdminFormField label="محصولات قابل اعمال">
                    <AdminInput
                      value={formData.applicableProducts?.join(', ')}
                      onChange={(e) => handleInputChange('applicableProducts', e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p)))}
                      placeholder="شناسه محصولات را با کاما جدا کنید"
                    />
                  </AdminFormField>

                  <AdminFormField label="محصولات مستثنی">
                    <AdminInput
                      value={formData.excludedProducts?.join(', ')}
                      onChange={(e) => handleInputChange('excludedProducts', e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p)))}
                      placeholder="شناسه محصولات مستثنی را با کاما جدا کنید"
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}

            {/* Limits Tab */}
            {activeTab === 'limits' && (
              <AdminCard title="محدودیت‌های استفاده">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="محدودیت کل استفاده" error={errors.usageLimit}>
                      <AdminInput
                        type="number"
                        value={formData.usageLimit}
                        onChange={(e) => handleInputChange('usageLimit', parseInt(e.target.value) || 0)}
                        placeholder="0 = نامحدود"
                        min="0"
                        error={!!errors.usageLimit}
                      />
                    </AdminFormField>

                    <AdminFormField label="محدودیت هر مشتری" error={errors.usagePerCustomer}>
                      <AdminInput
                        type="number"
                        value={formData.usagePerCustomer}
                        onChange={(e) => handleInputChange('usagePerCustomer', parseInt(e.target.value) || 1)}
                        placeholder="1"
                        min="1"
                        error={!!errors.usagePerCustomer}
                      />
                    </AdminFormField>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AdminFormField label="تاریخ شروع">
                      <AdminInput
                        type="date"
                        value={formData.startDate}
                        onChange={(e) => handleInputChange('startDate', e.target.value)}
                      />
                    </AdminFormField>

                    <AdminFormField label="تاریخ پایان">
                      <AdminInput
                        type="date"
                        value={formData.endDate}
                        onChange={(e) => handleInputChange('endDate', e.target.value)}
                      />
                    </AdminFormField>
                  </div>

                  <AdminFormField label="شرایط و ضوابط">
                    <AdminTextarea
                      value={formData.terms}
                      onChange={(e) => handleInputChange('terms', e.target.value)}
                      placeholder="شرایط و ضوابط استفاده از تخفیف"
                      rows={4}
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}

            {/* Display Tab */}
            {activeTab === 'display' && (
              <AdminCard title="تنظیمات نمایش">
                <div className="space-y-4">
                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.showOnHomepage}
                      onChange={(checked) => handleInputChange('showOnHomepage', checked)}
                      label="نمایش در صفحه اصلی"
                      description="تخفیف در صفحه اصلی نمایش داده شود"
                    />
                  </AdminFormField>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.showInCart}
                      onChange={(checked) => handleInputChange('showInCart', checked)}
                      label="نمایش در سبد خرید"
                      description="تخفیف در صفحه سبد خرید نمایش داده شود"
                    />
                  </AdminFormField>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.showOnProductPages}
                      onChange={(checked) => handleInputChange('showOnProductPages', checked)}
                      label="نمایش در صفحات محصول"
                      description="تخفیف در صفحات محصولات نمایش داده شود"
                    />
                  </AdminFormField>

                  <AdminFormField>
                    <AdminCheckbox
                      checked={formData.isActive}
                      onChange={(checked) => handleInputChange('isActive', checked)}
                      label="فعال"
                      description="تخفیف فعال و قابل استفاده باشد"
                    />
                  </AdminFormField>
                </div>
              </AdminCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status and Actions */}
            <AdminCard title="انتشار">
              <div className="space-y-4">
                <AdminFormField label="وضعیت" required>
                  <AdminSelect
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </AdminSelect>
                </AdminFormField>

                <div className="flex gap-2">
                  <AdminButton 
                    type="submit" 
                    variant="primary" 
                    icon={Save}
                    loading={loading}
                    className="flex-1"
                  >
                    {mode === 'create' ? 'ایجاد تخفیف' : 'به‌روزرسانی'}
                  </AdminButton>
                </div>

                <AdminButton 
                  variant="ghost" 
                  onClick={onCancel}
                  className="w-full"
                >
                  انصراف
                </AdminButton>
              </div>
            </AdminCard>

            {/* Preview */}
            <AdminCard title="پیش‌نمایش">
              <div className="space-y-3">
                <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Tag className="w-4 h-4 text-green-600" />
                    <span className="font-medium text-green-800">{formData.title || 'عنوان تخفیف'}</span>
                  </div>
                  <p className="text-sm text-green-700 mb-2">
                    {formData.description || 'توضیحات تخفیف'}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-green-800">
                      {formData.type === 'percentage' 
                        ? `${formData.discountValue}% تخفیف`
                        : formData.type === 'fixed_amount'
                        ? `${formData.discountValue.toLocaleString('fa-IR')} تومان تخفیف`
                        : formData.type === 'free_shipping'
                        ? 'ارسال رایگان'
                        : 'یکی بخر یکی بگیر'
                      }
                    </span>
                    {formData.isCodeRequired && formData.code && (
                      <span className="px-2 py-1 bg-white text-green-800 rounded text-sm font-mono">
                        {formData.code}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </AdminCard>

            {/* Validation Messages */}
            {Object.keys(errors).length > 0 && (
              <AdminFormAlert
                type="error"
                title="خطاهای فرم"
                message={`لطفاً ${Object.keys(errors).length} خطای موجود را برطرف کنید.`}
              />
            )}

            {Object.keys(warnings).length > 0 && (
              <AdminFormAlert
                type="warning"
                title="هشدارها"
                message={`${Object.keys(warnings).length} هشدار برای بهبود تخفیف وجود دارد.`}
              />
            )}
          </div>
        </div>
      </AdminForm>
    </div>
  );
};

export default PromotionEditor;
