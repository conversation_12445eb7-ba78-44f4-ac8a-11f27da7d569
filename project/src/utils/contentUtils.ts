import { 
  ContentValidationResult, 
  BannerFormData, 
  PromotionFormData, 
  NewsletterCampaignFormData, 
  PageContentFormData,
  ContentStatus,
  Banner,
  Promotion,
  NewsletterCampaign,
  PageContent
} from '../types/adminContent';

// Content Validation Functions
export const validateBannerForm = (data: BannerFormData): ContentValidationResult => {
  const errors: Array<{ field: string; message: string }> = [];
  const warnings: Array<{ field: string; message: string }> = [];

  // Required fields validation
  if (!data.title?.trim()) {
    errors.push({ field: 'title', message: 'عنوان بنر الزامی است' });
  }

  if (!data.image?.trim()) {
    errors.push({ field: 'image', message: 'تصویر بنر الزامی است' });
  }

  if (!data.altText?.trim()) {
    errors.push({ field: 'altText', message: 'متن جایگزین تصویر الزامی است' });
  }

  if (data.position < 0) {
    errors.push({ field: 'position', message: 'موقعیت بنر باید عدد مثبت باشد' });
  }

  // CTA validation
  if (data.ctaText && !data.ctaUrl) {
    errors.push({ field: 'ctaUrl', message: 'لینک دکمه عمل الزامی است' });
  }

  if (data.ctaUrl && !data.ctaText) {
    warnings.push({ field: 'ctaText', message: 'متن دکمه عمل توصیه می‌شود' });
  }

  // URL validation
  if (data.ctaUrl && data.ctaType === 'external' && !isValidUrl(data.ctaUrl)) {
    errors.push({ field: 'ctaUrl', message: 'لینک خارجی معتبر نیست' });
  }

  // Duration validation
  if (data.autoplay && (!data.duration || data.duration < 1)) {
    errors.push({ field: 'duration', message: 'مدت زمان نمایش باید حداقل ۱ ثانیه باشد' });
  }

  // Date validation
  if (data.startDate && data.endDate && new Date(data.startDate) >= new Date(data.endDate)) {
    errors.push({ field: 'endDate', message: 'تاریخ پایان باید بعد از تاریخ شروع باشد' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export const validatePromotionForm = (data: PromotionFormData): ContentValidationResult => {
  const errors: Array<{ field: string; message: string }> = [];
  const warnings: Array<{ field: string; message: string }> = [];

  // Required fields
  if (!data.title?.trim()) {
    errors.push({ field: 'title', message: 'عنوان تخفیف الزامی است' });
  }

  if (!data.description?.trim()) {
    errors.push({ field: 'description', message: 'توضیحات تخفیف الزامی است' });
  }

  if (!data.discountValue || data.discountValue <= 0) {
    errors.push({ field: 'discountValue', message: 'مقدار تخفیف باید بیشتر از صفر باشد' });
  }

  // Percentage validation
  if (data.type === 'percentage' && data.discountValue > 100) {
    errors.push({ field: 'discountValue', message: 'درصد تخفیف نمی‌تواند بیشتر از ۱۰۰ باشد' });
  }

  // Code validation
  if (data.isCodeRequired && !data.code?.trim()) {
    errors.push({ field: 'code', message: 'کد تخفیف الزامی است' });
  }

  if (data.code && data.code.length < 3) {
    errors.push({ field: 'code', message: 'کد تخفیف باید حداقل ۳ کاراکتر باشد' });
  }

  // Usage limits validation
  if (data.usageLimit && data.usageLimit < 1) {
    errors.push({ field: 'usageLimit', message: 'محدودیت استفاده باید حداقل ۱ باشد' });
  }

  if (data.usagePerCustomer && data.usagePerCustomer < 1) {
    errors.push({ field: 'usagePerCustomer', message: 'محدودیت استفاده هر مشتری باید حداقل ۱ باشد' });
  }

  // Date validation
  if (data.startDate && data.endDate && new Date(data.startDate) >= new Date(data.endDate)) {
    errors.push({ field: 'endDate', message: 'تاریخ پایان باید بعد از تاریخ شروع باشد' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export const validateNewsletterForm = (data: NewsletterCampaignFormData): ContentValidationResult => {
  const errors: Array<{ field: string; message: string }> = [];
  const warnings: Array<{ field: string; message: string }> = [];

  // Required fields
  if (!data.title?.trim()) {
    errors.push({ field: 'title', message: 'عنوان کمپین الزامی است' });
  }

  if (!data.subject?.trim()) {
    errors.push({ field: 'subject', message: 'موضوع ایمیل الزامی است' });
  }

  if (!data.content?.trim()) {
    errors.push({ field: 'content', message: 'محتوای ایمیل الزامی است' });
  }

  if (!data.recipientSegments || data.recipientSegments.length === 0) {
    errors.push({ field: 'recipientSegments', message: 'حداقل یک گروه مخاطب انتخاب کنید' });
  }

  // Subject line validation
  if (data.subject && data.subject.length > 50) {
    warnings.push({ field: 'subject', message: 'موضوع ایمیل بهتر است کمتر از ۵۰ کاراکتر باشد' });
  }

  // Preheader validation
  if (data.preheader && data.preheader.length > 90) {
    warnings.push({ field: 'preheader', message: 'متن پیش‌نمایش بهتر است کمتر از ۹۰ کاراکتر باشد' });
  }

  // Send date validation
  if (data.sendAt && new Date(data.sendAt) <= new Date()) {
    errors.push({ field: 'sendAt', message: 'زمان ارسال باید در آینده باشد' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export const validatePageForm = (data: PageContentFormData): ContentValidationResult => {
  const errors: Array<{ field: string; message: string }> = [];
  const warnings: Array<{ field: string; message: string }> = [];

  // Required fields
  if (!data.title?.trim()) {
    errors.push({ field: 'title', message: 'عنوان صفحه الزامی است' });
  }

  if (!data.content?.trim()) {
    errors.push({ field: 'content', message: 'محتوای صفحه الزامی است' });
  }

  // Slug validation
  if (data.slug && !isValidSlug(data.slug)) {
    errors.push({ field: 'slug', message: 'نامک صفحه معتبر نیست (فقط حروف انگلیسی، اعداد و خط تیره)' });
  }

  // Menu order validation
  if (data.menuOrder < 0) {
    errors.push({ field: 'menuOrder', message: 'ترتیب منو باید عدد مثبت باشد' });
  }

  // SEO validation
  if (data.metaDescription && data.metaDescription.length > 160) {
    warnings.push({ field: 'metaDescription', message: 'توضیحات متا بهتر است کمتر از ۱۶۰ کاراکتر باشد' });
  }

  if (data.metaTitle && data.metaTitle.length > 60) {
    warnings.push({ field: 'metaTitle', message: 'عنوان متا بهتر است کمتر از ۶۰ کاراکتر باشد' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Utility Functions
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isValidSlug = (slug: string): boolean => {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
};

export const formatContentStatus = (status: ContentStatus): string => {
  const statusMap: Record<ContentStatus, string> = {
    draft: 'پیش‌نویس',
    published: 'منتشر شده',
    scheduled: 'زمان‌بندی شده',
    archived: 'بایگانی شده'
  };
  return statusMap[status] || status;
};

export const getStatusColor = (status: ContentStatus): string => {
  const colorMap: Record<ContentStatus, string> = {
    draft: 'warning',
    published: 'success',
    scheduled: 'info',
    archived: 'default'
  };
  return colorMap[status] || 'default';
};

// Content Processing
export const sanitizeHtml = (html: string): string => {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

export const extractTextFromHtml = (html: string): string => {
  return html.replace(/<[^>]*>/g, '').trim();
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

// Date and Time Utilities
export const formatPersianDate = (date: string): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};

export const formatPersianDateTime = (date: string): string => {
  return new Intl.DateTimeFormat('fa-IR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};

export const isContentScheduled = (content: Banner | Promotion | NewsletterCampaign | PageContent): boolean => {
  return content.status === 'scheduled' && !!content.scheduledAt;
};

export const isContentActive = (content: Banner | Promotion | NewsletterCampaign | PageContent): boolean => {
  if (!content.isActive) return false;
  
  const now = new Date();
  const startDate = content.startDate ? new Date(content.startDate) : null;
  const endDate = content.endDate ? new Date(content.endDate) : null;
  
  if (startDate && now < startDate) return false;
  if (endDate && now > endDate) return false;
  
  return content.status === 'published';
};
