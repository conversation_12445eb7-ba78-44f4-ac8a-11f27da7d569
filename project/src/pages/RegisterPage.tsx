import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import RegisterForm from '../components/auth/RegisterForm';
import SEOHead from '../components/seo/SEOHead';

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleRegisterSuccess = () => {
    navigate('/', { replace: true });
  };

  return (
    <>
      <SEOHead
        title="ثبت‌نام در گلو رویا | عضویت رایگان"
        description="در گلو رویا ثبت‌نام کنید و از تخفیف‌های ویژه، باشگاه مشتریان و خدمات منحصر به فرد بهره‌مند شوید."
        keywords="ثبت نام, عضویت, گلو رویا, مراقبت از پوست, باشگاه مشتریان"
        canonical="/register"
      />
      
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="w-full max-w-md"
        >
          <RegisterForm onSuccess={handleRegisterSuccess} />
        </motion.div>
      </div>
    </>
  );
};

export default RegisterPage;
