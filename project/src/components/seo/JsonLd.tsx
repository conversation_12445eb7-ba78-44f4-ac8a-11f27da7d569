import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Product } from '../../types';

interface JsonLdProps {
  type: 'website' | 'product' | 'organization' | 'breadcrumb' | 'faq' | 'review' | 'offer' | 'article';
  data: any;
}

const JsonLd: React.FC<JsonLdProps> = ({ type, data }) => {
  const generateSchema = () => {
    const baseUrl = window.location.origin;
    
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "آرامش پوست",
          "alternateName": "GlowRoya Skincare",
          "url": baseUrl,
          "description": "فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی با بهترین برندهای ایرانی و خارجی",
          "inLanguage": "fa",
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/products?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "@id": `${baseUrl}#organization`
          }
        };

      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "@id": `${baseUrl}#organization`,
          "name": "آرامش پوست",
          "alternateName": "GlowRoya Skincare",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/logo.png`,
            "width": 200,
            "height": 200
          },
          "description": "فروشگاه آنلاین محصولات مراقبت از پوست و زیبایی",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": data.address || "تهران، ایران",
            "addressLocality": "تهران",
            "addressRegion": "تهران",
            "postalCode": data.postalCode || "1234567890",
            "addressCountry": "IR"
          },
          "contactPoint": [
            {
              "@type": "ContactPoint",
              "telephone": data.phone || "+98-21-12345678",
              "contactType": "customer service",
              "availableLanguage": ["Persian", "English"],
              "areaServed": "IR"
            }
          ],
          "sameAs": [
            data.instagram || "https://instagram.com/glowroya",
            data.telegram || "https://t.me/glowroya",
            data.whatsapp || "https://wa.me/989123456789"
          ],
          "foundingDate": "2023",
          "numberOfEmployees": "10-50",
          "slogan": "پوست سالم، زندگی شاد"
        };

      case 'product':
        const product: Product = data.product;
        return {
          "@context": "https://schema.org",
          "@type": "Product",
          "name": product.name,
          "description": product.description,
          "image": [
            product.imageSrc,
            ...(product.gallery || [])
          ],
          "brand": {
            "@type": "Brand",
            "name": product.brand || "آرامش پوست"
          },
          "category": product.category,
          "sku": product.id,
          "gtin": data.gtin || product.id,
          "offers": {
            "@type": "Offer",
            "price": product.discountedPrice || product.price,
            "priceCurrency": "IRR",
            "availability": product.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            "seller": {
              "@type": "Organization",
              "name": "آرامش پوست",
              "@id": `${baseUrl}#organization`
            },
            "url": `${baseUrl}/product/${product.id}`,
            "priceValidUntil": data.priceValidUntil || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            "shippingDetails": {
              "@type": "OfferShippingDetails",
              "shippingRate": {
                "@type": "MonetaryAmount",
                "value": data.shippingCost || 0,
                "currency": "IRR"
              },
              "deliveryTime": {
                "@type": "ShippingDeliveryTime",
                "handlingTime": {
                  "@type": "QuantitativeValue",
                  "minValue": 1,
                  "maxValue": 2,
                  "unitCode": "DAY"
                },
                "transitTime": {
                  "@type": "QuantitativeValue",
                  "minValue": 1,
                  "maxValue": 3,
                  "unitCode": "DAY"
                }
              }
            }
          },
          "aggregateRating": product.rating && product.reviewCount ? {
            "@type": "AggregateRating",
            "ratingValue": product.rating,
            "reviewCount": product.reviewCount,
            "bestRating": 5,
            "worstRating": 1
          } : undefined,
          "review": data.reviews?.map((review: any) => ({
            "@type": "Review",
            "reviewRating": {
              "@type": "Rating",
              "ratingValue": review.rating,
              "bestRating": 5,
              "worstRating": 1
            },
            "author": {
              "@type": "Person",
              "name": review.userName
            },
            "reviewBody": review.comment,
            "datePublished": review.date
          })) || []
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data.items.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": `${baseUrl}${item.url}`
          }))
        };

      case 'faq':
        return {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": data.faqs.map((faq: any) => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": faq.answer
            }
          }))
        };

      case 'review':
        return {
          "@context": "https://schema.org",
          "@type": "Review",
          "itemReviewed": {
            "@type": "Product",
            "name": data.productName,
            "image": data.productImage,
            "brand": {
              "@type": "Brand",
              "name": data.productBrand || "آرامش پوست"
            }
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": data.rating,
            "bestRating": 5,
            "worstRating": 1
          },
          "author": {
            "@type": "Person",
            "name": data.authorName
          },
          "reviewBody": data.reviewText,
          "datePublished": data.datePublished,
          "publisher": {
            "@type": "Organization",
            "name": "آرامش پوست",
            "@id": `${baseUrl}#organization`
          }
        };

      case 'offer':
        return {
          "@context": "https://schema.org",
          "@type": "Offer",
          "name": data.name,
          "description": data.description,
          "price": data.price,
          "priceCurrency": "IRR",
          "availability": "https://schema.org/InStock",
          "validFrom": data.validFrom,
          "validThrough": data.validThrough,
          "seller": {
            "@type": "Organization",
            "name": "آرامش پوست",
            "@id": `${baseUrl}#organization`
          },
          "url": `${baseUrl}${data.url}`
        };

      case 'article':
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data.title,
          "description": data.description,
          "image": data.image,
          "author": {
            "@type": "Person",
            "name": data.author || "تیم آرامش پوست"
          },
          "publisher": {
            "@type": "Organization",
            "name": "آرامش پوست",
            "@id": `${baseUrl}#organization`,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          },
          "datePublished": data.datePublished,
          "dateModified": data.dateModified || data.datePublished,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `${baseUrl}${data.url}`
          },
          "articleSection": data.category,
          "keywords": data.keywords?.join(', '),
          "wordCount": data.wordCount,
          "inLanguage": "fa"
        };

      default:
        return null;
    }
  };

  const schema = generateSchema();

  if (!schema) {
    return null;
  }

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema, null, 2)}
      </script>
    </Helmet>
  );
};

export default JsonLd;
