import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bookmark, 
  ShoppingCart, 
  X, 
  Clock, 
  AlertCircle,
  Eye,
  TrendingDown,
  Gift,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { SavedForLaterItem, SaveReason } from '../../types/advancedCart';
import { formatPrice, formatNumber } from '../../utils/formatters';
import { PERSIAN_ADVANCED_CART_MESSAGES } from '../../types/advancedCart';

interface SaveForLaterProps {
  savedItems: SavedForLaterItem[];
  onMoveToCart: (savedItemId: string) => void;
  onRemoveItem: (savedItemId: string) => void;
  className?: string;
}

const SaveForLater: React.FC<SaveForLaterProps> = ({
  savedItems,
  onMoveToCart,
  onRemoveItem,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (savedItems.length === 0) {
    return null;
  }

  const getReasonIcon = (reason?: SaveReason) => {
    switch (reason) {
      case 'out_of_stock':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'price_watch':
        return <TrendingDown className="w-4 h-4 text-blue-500" />;
      case 'later_purchase':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'gift_planning':
        return <Gift className="w-4 h-4 text-purple-500" />;
      case 'comparison':
        return <Eye className="w-4 h-4 text-green-500" />;
      default:
        return <Bookmark className="w-4 h-4 text-gray-500" />;
    }
  };

  const getReasonText = (reason?: SaveReason) => {
    switch (reason) {
      case 'out_of_stock':
        return 'ناموجود';
      case 'price_watch':
        return 'رصد قیمت';
      case 'later_purchase':
        return 'خرید بعدی';
      case 'gift_planning':
        return 'برای هدیه';
      case 'comparison':
        return 'مقایسه';
      default:
        return 'ذخیره شده';
    }
  };

  const formatSavedDate = (date: Date) => {
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'امروز';
    } else if (diffDays === 1) {
      return 'دیروز';
    } else if (diffDays < 7) {
      return `${formatNumber(diffDays)} روز پیش`;
    } else {
      return date.toLocaleDateString('fa-IR');
    }
  };

  return (
    <div className={`bg-gray-50 rounded-lg ${className}`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <Bookmark className="w-5 h-5 ml-2 text-gray-600" />
          <h3 className="font-medium text-gray-900">
            ذخیره شده برای بعد
          </h3>
          <span className="bg-gray-200 text-gray-700 text-sm rounded-full px-2 py-0.5 mr-2">
            {formatNumber(savedItems.length)}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        )}
      </div>

      {/* Saved Items */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-200"
          >
            <div className="p-4 space-y-4">
              {savedItems.map((item, index) => (
                <div
                  key={item.id}
                  className="bg-white rounded-lg p-3 shadow-sm border border-gray-100"
                >
                  <div className="flex items-start space-x-3 space-x-reverse">
                    {/* Product Image */}
                    <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      <img
                        src={item.product.imageSrc}
                        alt={item.product.name}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-grow min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                          {item.product.name}
                        </h4>
                        <button
                          onClick={() => onRemoveItem(item.id)}
                          className="text-gray-400 hover:text-red-500 p-1 -mt-1 -mr-1 transition-colors"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Variants */}
                      {item.selectedVariants && Object.keys(item.selectedVariants).length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {Object.values(item.selectedVariants).map((variant, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-700"
                            >
                              {variant.name}
                            </span>
                          ))}
                        </div>
                      )}

                      {/* Reason and Date */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center text-xs text-gray-500">
                          {getReasonIcon(item.reason)}
                          <span className="mr-1">{getReasonText(item.reason)}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {formatSavedDate(item.savedAt)}
                        </span>
                      </div>

                      {/* Price and Quantity */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-sm font-semibold text-gray-900">
                            {formatPrice(item.product.discountedPrice || item.product.price)}
                          </span>
                          {item.product.discountedPrice && (
                            <span className="text-xs text-gray-500 line-through">
                              {formatPrice(item.product.price)}
                            </span>
                          )}
                          {item.quantity > 1 && (
                            <span className="text-xs text-gray-500">
                              × {formatNumber(item.quantity)}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Stock Status */}
                      <div className="mb-3">
                        {item.product.stock === 0 ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                            <AlertCircle className="w-3 h-3 ml-1" />
                            ناموجود
                          </span>
                        ) : item.product.stock < item.quantity ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                            <AlertCircle className="w-3 h-3 ml-1" />
                            موجودی کافی نیست
                          </span>
                        ) : item.product.stock <= 5 ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                            <Clock className="w-3 h-3 ml-1" />
                            موجودی محدود
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                            موجود
                          </span>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => onMoveToCart(item.id)}
                          disabled={item.product.stock === 0 || item.product.stock < item.quantity}
                          className="flex items-center px-3 py-1.5 bg-primary-600 text-white text-xs font-medium rounded-md hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                        >
                          <ShoppingCart className="w-3 h-3 ml-1" />
                          {PERSIAN_ADVANCED_CART_MESSAGES.actions.moveToCart}
                        </button>

                        <button
                          onClick={() => onRemoveItem(item.id)}
                          className="flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 text-xs font-medium rounded-md hover:bg-gray-200 transition-colors"
                        >
                          <X className="w-3 h-3 ml-1" />
                          حذف
                        </button>
                      </div>

                      {/* Reminder Date */}
                      {item.reminderDate && (
                        <div className="mt-2 pt-2 border-t border-gray-100">
                          <div className="flex items-center text-xs text-gray-500">
                            <Clock className="w-3 h-3 ml-1" />
                            <span>یادآوری: {item.reminderDate.toLocaleDateString('fa-IR')}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Bulk Actions */}
              {savedItems.length > 1 && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <span className="text-sm text-gray-600">
                    {formatNumber(savedItems.length)} محصول ذخیره شده
                  </span>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => {
                        savedItems.forEach(item => {
                          if (item.product.stock >= item.quantity) {
                            onMoveToCart(item.id);
                          }
                        });
                      }}
                      className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                    >
                      انتقال همه به سبد
                    </button>
                    <button
                      onClick={() => {
                        savedItems.forEach(item => onRemoveItem(item.id));
                      }}
                      className="text-sm text-red-600 hover:text-red-700 font-medium"
                    >
                      حذف همه
                    </button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SaveForLater;
