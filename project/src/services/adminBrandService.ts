// API Configuration
import { API_BASE_URL } from '../config/api';
const API_BASE_URL_V1 = `${API_BASE_URL}/api/v1`;

// Helper function to get auth token from admin auth storage
const getAuthToken = (): string | null => {
  // Use the correct admin auth storage keys
  return sessionStorage.getItem('admin_auth_token') ||
         localStorage.getItem('admin_auth_token');
};

// Helper function to make authenticated API requests
const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<any> => {
  const token = getAuthToken();

  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  };

  try {
    const response = await fetch(`${API_BASE_URL_V1}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Types
export interface Brand {
  id: string;
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive: boolean;
  productsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBrandData {
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive?: boolean;
}

export interface UpdateBrandData {
  name?: string;
  nameEn?: string;
  slug?: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive?: boolean;
}

export interface BrandStats {
  total: number;
  active: number;
  inactive: number;
  totalProducts: number;
}

export interface BrandQueryParams {
  includeInactive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}



export class AdminBrandService {

  /**
   * Get all brands
   */
  static async getBrands(params?: BrandQueryParams): Promise<{
    brands: Brand[];
    pagination: {
      page: number;
      limit: number;
      total: number;
    };
  }> {
    const searchParams = new URLSearchParams();

    if (params) {
      if (params.includeInactive !== undefined) {
        searchParams.append('includeInactive', params.includeInactive.toString());
      }
      if (params.search) {
        searchParams.append('search', params.search);
      }
      if (params.page) {
        searchParams.append('page', params.page.toString());
      }
      if (params.limit) {
        searchParams.append('limit', params.limit.toString());
      }
    }

    const endpoint = `/brands${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await makeAuthenticatedRequest(endpoint);

    if (response.success && response.data) {
      return {
        brands: response.data.map((brand: any) => ({
          ...brand,
          productsCount: brand.productsCount || 0,
        })),
        pagination: response.pagination || {
          page: params?.page || 1,
          limit: params?.limit || 50,
          total: response.data.length,
        },
      };
    }

    throw new Error('BRANDS_FETCH_FAILED');
  }

  /**
   * Get brand by ID
   */
  static async getBrandById(id: string): Promise<Brand> {
    const response = await makeAuthenticatedRequest(`/brands/${id}`);

    if (response.success && response.data) {
      return {
        ...response.data,
        productsCount: response.data.productsCount || 0,
      };
    }

    throw new Error('BRAND_NOT_FOUND');
  }

  /**
   * Create new brand
   */
  static async createBrand(data: CreateBrandData): Promise<Brand> {
    try {
      const response = await makeAuthenticatedRequest('/brands', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.success && response.data) {
        return {
          ...response.data,
          productsCount: response.data.productsCount || 0,
        };
      }

      throw new Error('BRAND_CREATION_FAILED');
    } catch (error: any) {
      // If authentication fails, provide a more user-friendly error
      if (error.message?.includes('401') || error.message?.includes('توکن')) {
        throw new Error('برای ایجاد برند، ابتدا وارد حساب کاربری خود شوید');
      }
      throw error;
    }
  }

  /**
   * Update brand
   */
  static async updateBrand(id: string, data: UpdateBrandData): Promise<Brand> {
    try {
      const response = await makeAuthenticatedRequest(`/brands/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });

      if (response.success && response.data) {
        return {
          ...response.data,
          productsCount: response.data.productsCount || 0,
        };
      }

      throw new Error('BRAND_UPDATE_FAILED');
    } catch (error: any) {
      // If authentication fails, provide a more user-friendly error
      if (error.message?.includes('401') || error.message?.includes('توکن')) {
        throw new Error('برای ویرایش برند، ابتدا وارد حساب کاربری خود شوید');
      }
      throw error;
    }
  }

  /**
   * Delete brand
   */
  static async deleteBrand(id: string): Promise<void> {
    try {
      const response = await makeAuthenticatedRequest(`/brands/${id}`, {
        method: 'DELETE',
      });

      if (!response.success) {
        throw new Error('BRAND_DELETE_FAILED');
      }
    } catch (error: any) {
      // If authentication fails, provide a more user-friendly error
      if (error.message?.includes('401') || error.message?.includes('توکن')) {
        throw new Error('برای حذف برند، ابتدا وارد حساب کاربری خود شوید');
      }
      throw error;
    }
  }

  /**
   * Upload brand logo
   */
  static async uploadBrandLogo(brandId: string, file: File): Promise<{
    brand: Brand;
    logo: {
      url: string;
      filename: string;
      originalName: string;
      size: number;
    };
  }> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch(`${API_BASE_URL_V1}/upload/brand-image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status}`);
    }

    const uploadResult = await response.json();

    if (uploadResult.success && uploadResult.data) {
      // Update brand with new logo URL
      const updateData = { logo: uploadResult.data.url };
      const updatedBrand = await this.updateBrand(brandId, updateData);

      return {
        brand: updatedBrand,
        logo: {
          url: uploadResult.data.url,
          filename: uploadResult.data.filename,
          originalName: file.name,
          size: file.size,
        },
      };
    }

    throw new Error('BRAND_LOGO_UPLOAD_FAILED');
  }

  /**
   * Get brand statistics
   */
  static async getBrandStats(): Promise<BrandStats> {
    try {
      const response = await makeAuthenticatedRequest('/brands/admin/stats');

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error('BRAND_STATS_FETCH_FAILED');
    } catch (error: any) {
      // If authentication fails, return default stats
      if (error.message?.includes('401') || error.message?.includes('توکن')) {
        console.warn('Authentication failed for brand stats, returning default values');
        return {
          total: 0,
          active: 0,
          inactive: 0,
          totalProducts: 0
        };
      }
      throw error;
    }
  }

  /**
   * Generate slug from name
   */
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Validate brand data
   */
  static validateBrandData(data: CreateBrandData | UpdateBrandData): {
    isValid: boolean;
    errors: Record<string, string>;
  } {
    const errors: Record<string, string> = {};

    // Validate name
    if ('name' in data && data.name !== undefined) {
      if (!data.name || data.name.trim().length < 2) {
        errors.name = 'نام برند باید حداقل ۲ کاراکتر باشد';
      }
    }

    // Validate nameEn
    if ('nameEn' in data && data.nameEn !== undefined) {
      if (data.nameEn && data.nameEn.trim().length < 2) {
        errors.nameEn = 'نام انگلیسی برند باید حداقل ۲ کاراکتر باشد';
      }
    }

    // Validate slug
    if ('slug' in data && data.slug !== undefined) {
      if (!data.slug || data.slug.trim().length < 2) {
        errors.slug = 'نام انگلیسی برند الزامی است';
      } else if (!/^[a-z0-9-]+$/.test(data.slug)) {
        errors.slug = 'نام انگلیسی برند فقط می‌تواند شامل حروف انگلیسی، اعداد و خط تیره باشد';
      }
    }

    // Validate website
    if ('website' in data && data.website !== undefined) {
      if (data.website && !data.website.match(/^https?:\/\/.+/)) {
        errors.website = 'آدرس وب‌سایت باید با http:// یا https:// شروع شود';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Format brand data for display
   */
  static formatBrandForDisplay(brand: Brand): Brand & {
    displayName: string;
    statusText: string;
    statusColor: string;
  } {
    return {
      ...brand,
      displayName: brand.nameEn ? `${brand.name} (${brand.nameEn})` : brand.name,
      statusText: brand.isActive ? 'فعال' : 'غیرفعال',
      statusColor: brand.isActive ? 'green' : 'red',
    };
  }
}

export default AdminBrandService;
