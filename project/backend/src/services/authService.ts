import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { User, UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../config/database';
import { config } from '../config';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

// Types for authentication
export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresIn: string;
}

export interface TokenPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  userId: string;
  sessionId: string;
  iat?: number;
  exp?: number;
}

export class AuthService {
  // Password hashing
  static async hashPassword(password: string): Promise<string> {
    try {
      const saltRounds = config.security.bcryptRounds;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      logger.error('Password hashing failed:', error);
      throw new AppError('خطا در پردازش رمز عبور', 500, 'PASSWORD_HASH_ERROR');
    }
  }

  // Password verification
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      logger.error('Password verification failed:', error);
      throw new AppError('خطا در بررسی رمز عبور', 500, 'PASSWORD_VERIFY_ERROR');
    }
  }

  // Generate access token
  static generateAccessToken(user: User): string {
    const payload: TokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'glowroya-api',
      audience: 'glowroya-app',
    } as jwt.SignOptions);
  }

  // Generate refresh token
  static generateRefreshToken(userId: string, sessionId: string): string {
    const payload: RefreshTokenPayload = {
      userId,
      sessionId,
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'glowroya-api',
      audience: 'glowroya-app',
    } as jwt.SignOptions);
  }

  // Verify access token
  static verifyAccessToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'glowroya-api',
        audience: 'glowroya-app',
      }) as TokenPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AppError('توکن منقضی شده است', 401, 'TOKEN_EXPIRED');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('توکن نامعتبر است', 401, 'INVALID_TOKEN');
      }
      throw new AppError('خطای احراز هویت', 401, 'AUTH_ERROR');
    }
  }

  // Verify refresh token
  static verifyRefreshToken(token: string): RefreshTokenPayload {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'glowroya-api',
        audience: 'glowroya-app',
      }) as RefreshTokenPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AppError('توکن تازه‌سازی منقضی شده است', 401, 'REFRESH_TOKEN_EXPIRED');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('توکن تازه‌سازی نامعتبر است', 401, 'INVALID_REFRESH_TOKEN');
      }
      throw new AppError('خطای تازه‌سازی توکن', 401, 'REFRESH_TOKEN_ERROR');
    }
  }

  // Create user session
  static async createSession(
    userId: string,
    refreshToken: string,
    userAgent?: string,
    ipAddress?: string
  ): Promise<string> {
    try {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30); // 30 days

      const session = await prisma.userSession.create({
        data: {
          userId,
          token: refreshToken,
          refreshToken,
          userAgent,
          ipAddress,
          expiresAt,
          isActive: true,
        },
      });

      return session.id;
    } catch (error) {
      logger.error('Session creation failed:', error);
      throw new AppError('خطا در ایجاد جلسه کاربری', 500, 'SESSION_CREATE_ERROR');
    }
  }

  // Validate session
  static async validateSession(sessionId: string, refreshToken: string): Promise<boolean> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { id: sessionId },
      });

      if (!session || !session.isActive || session.refreshToken !== refreshToken) {
        return false;
      }

      if (session.expiresAt < new Date()) {
        // Mark session as inactive
        await prisma.userSession.update({
          where: { id: sessionId },
          data: { isActive: false },
        });
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Session validation failed:', error);
      return false;
    }
  }

  // Invalidate session
  static async invalidateSession(sessionId: string): Promise<void> {
    try {
      await prisma.userSession.update({
        where: { id: sessionId },
        data: { isActive: false },
      });
    } catch (error) {
      logger.error('Session invalidation failed:', error);
      // Don't throw error for session invalidation
    }
  }

  // Invalidate all user sessions
  static async invalidateAllUserSessions(userId: string): Promise<void> {
    try {
      await prisma.userSession.updateMany({
        where: { userId, isActive: true },
        data: { isActive: false },
      });
    } catch (error) {
      logger.error('All sessions invalidation failed:', error);
      // Don't throw error for session invalidation
    }
  }

  // Clean expired sessions
  static async cleanExpiredSessions(): Promise<void> {
    try {
      await prisma.userSession.updateMany({
        where: {
          expiresAt: { lt: new Date() },
          isActive: true,
        },
        data: { isActive: false },
      });
    } catch (error) {
      logger.error('Expired sessions cleanup failed:', error);
    }
  }

  // Validate email format
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Validate password strength
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('رمز عبور باید حداقل ۸ کاراکتر باشد');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('رمز عبور باید شامل حروف کوچک انگلیسی باشد');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('رمز عبور باید شامل حروف بزرگ انگلیسی باشد');
    }

    if (!/\d/.test(password)) {
      errors.push('رمز عبور باید شامل عدد باشد');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('رمز عبور باید شامل کاراکتر خاص باشد');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Generate password reset token
  static async generatePasswordResetToken(userId: string): Promise<string> {
    try {
      // Invalidate existing reset tokens
      await prisma.passwordReset.updateMany({
        where: { userId, usedAt: null },
        data: { usedAt: new Date() },
      });

      // Generate new token
      const resetToken = jwt.sign(
        { userId, type: 'password_reset' },
        config.jwt.secret,
        { expiresIn: '1h' }
      );

      // Store in database
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);

      await prisma.passwordReset.create({
        data: {
          userId,
          token: resetToken,
          expiresAt,
        },
      });

      return resetToken;
    } catch (error) {
      logger.error('Password reset token generation failed:', error);
      throw new AppError('خطا در ایجاد توکن بازیابی رمز عبور', 500, 'RESET_TOKEN_ERROR');
    }
  }

  // Verify password reset token
  static async verifyPasswordResetToken(token: string): Promise<string> {
    try {
      // Verify JWT token
      const payload = jwt.verify(token, config.jwt.secret) as any;
      
      if (payload.type !== 'password_reset') {
        throw new AppError('توکن نامعتبر است', 400, 'INVALID_RESET_TOKEN');
      }

      // Check database record
      const resetRecord = await prisma.passwordReset.findUnique({
        where: { token },
      });

      if (!resetRecord || resetRecord.usedAt || resetRecord.expiresAt < new Date()) {
        throw new AppError('توکن بازیابی منقضی یا استفاده شده است', 400, 'EXPIRED_RESET_TOKEN');
      }

      return payload.userId;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('توکن بازیابی نامعتبر است', 400, 'INVALID_RESET_TOKEN');
    }
  }

  // Mark password reset token as used
  static async markPasswordResetTokenUsed(token: string): Promise<void> {
    try {
      await prisma.passwordReset.update({
        where: { token },
        data: { usedAt: new Date() },
      });
    } catch (error) {
      logger.error('Password reset token marking failed:', error);
    }
  }
}

export default AuthService;
