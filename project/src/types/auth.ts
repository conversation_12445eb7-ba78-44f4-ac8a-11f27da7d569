export interface User {
  id: string;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  addresses: UserAddress[];
  role: UserRole;
}

export interface UserPreferences {
  language: 'fa' | 'en';
  newsletter: boolean;
  smsNotifications: boolean;
  emailNotifications: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface UserAddress {
  id: string;
  title: string; // e.g., "خانه", "محل کار"
  firstName: string;
  lastName: string;
  phone: string;
  province: string;
  city: string;
  address: string;
  postalCode: string;
  isDefault: boolean;
  createdAt: string;
}

export type UserRole = 'customer' | 'admin' | 'moderator' | 'super_admin';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  newsletter?: boolean;
}

export interface ResetPasswordData {
  email: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateProfileData {
  firstName: string;
  lastName: string;
  phone?: string;
  preferences: UserPreferences;
}

export interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (data: ResetPasswordData) => Promise<void>;
  changePassword: (data: ChangePasswordData) => Promise<void>;
  updateProfile: (data: UpdateProfileData) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  verifyPhone: (code: string) => Promise<void>;
  resendVerification: (type: 'email' | 'phone') => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  
  // Address management
  addAddress: (address: Omit<UserAddress, 'id' | 'createdAt'>) => Promise<void>;
  updateAddress: (id: string, address: Partial<UserAddress>) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
}

// Form validation schemas
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  newsletter: boolean;
}

// API Response types
export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ApiError {
  message: string;
  code: string;
  field?: string;
}

// Persian error messages
export const PERSIAN_AUTH_MESSAGES = {
  errors: {
    invalidCredentials: 'ایمیل یا رمز عبور اشتباه است',
    emailExists: 'این ایمیل قبلاً ثبت شده است',
    phoneExists: 'این شماره موبایل قبلاً ثبت شده است',
    weakPassword: 'رمز عبور باید حداقل ۸ کاراکتر و شامل حروف و اعداد باشد',
    passwordMismatch: 'تکرار رمز عبور مطابقت ندارد',
    emailRequired: 'ایمیل الزامی است',
    passwordRequired: 'رمز عبور الزامی است',
    firstNameRequired: 'نام الزامی است',
    lastNameRequired: 'نام خانوادگی الزامی است',
    phoneInvalid: 'شماره موبایل معتبر وارد کنید',
    emailInvalid: 'ایمیل معتبر وارد کنید',
    termsRequired: 'پذیرش قوانین الزامی است',
    networkError: 'خطا در اتصال به سرور',
    sessionExpired: 'جلسه کاری شما منقضی شده است',
    unauthorized: 'دسترسی غیرمجاز',
    serverError: 'خطای سرور، لطفاً دوباره تلاش کنید',
    verificationRequired: 'لطفاً ایمیل خود را تأیید کنید',
    phoneVerificationRequired: 'لطفاً شماره موبایل خود را تأیید کنید',
    invalidVerificationCode: 'کد تأیید نامعتبر است',
    verificationExpired: 'کد تأیید منقضی شده است',
    tooManyAttempts: 'تعداد تلاش‌های شما بیش از حد مجاز است',
    accountLocked: 'حساب کاربری شما موقتاً مسدود شده است',
    invalidToken: 'توکن نامعتبر است',
    tokenExpired: 'توکن منقضی شده است'
  },
  success: {
    loginSuccess: 'با موفقیت وارد شدید',
    registerSuccess: 'ثبت‌نام با موفقیت انجام شد',
    logoutSuccess: 'با موفقیت خارج شدید',
    passwordChanged: 'رمز عبور با موفقیت تغییر کرد',
    profileUpdated: 'پروفایل با موفقیت به‌روزرسانی شد',
    emailVerified: 'ایمیل با موفقیت تأیید شد',
    phoneVerified: 'شماره موبایل با موفقیت تأیید شد',
    verificationSent: 'کد تأیید ارسال شد',
    passwordResetSent: 'لینک بازیابی رمز عبور ارسال شد',
    addressAdded: 'آدرس با موفقیت اضافه شد',
    addressUpdated: 'آدرس با موفقیت به‌روزرسانی شد',
    addressDeleted: 'آدرس با موفقیت حذف شد'
  },
  placeholders: {
    email: '<EMAIL>',
    password: 'رمز عبور',
    confirmPassword: 'تکرار رمز عبور',
    firstName: 'نام',
    lastName: 'نام خانوادگی',
    phone: '09123456789',
    verificationCode: 'کد تأیید'
  },
  labels: {
    email: 'ایمیل',
    password: 'رمز عبور',
    confirmPassword: 'تکرار رمز عبور',
    firstName: 'نام',
    lastName: 'نام خانوادگی',
    phone: 'شماره موبایل',
    rememberMe: 'مرا به خاطر بسپار',
    acceptTerms: 'قوانین و مقررات را می‌پذیرم',
    newsletter: 'عضویت در خبرنامه',
    currentPassword: 'رمز عبور فعلی',
    newPassword: 'رمز عبور جدید'
  },
  buttons: {
    login: 'ورود',
    register: 'ثبت‌نام',
    logout: 'خروج',
    forgotPassword: 'فراموشی رمز عبور',
    resetPassword: 'بازیابی رمز عبور',
    changePassword: 'تغییر رمز عبور',
    updateProfile: 'به‌روزرسانی پروفایل',
    verify: 'تأیید',
    resendCode: 'ارسال مجدد کد',
    cancel: 'انصراف',
    save: 'ذخیره',
    edit: 'ویرایش',
    delete: 'حذف',
    addAddress: 'افزودن آدرس',
    setDefault: 'انتخاب به عنوان پیش‌فرض'
  }
};

// Storage keys
export const AUTH_STORAGE_KEYS = {
  TOKEN: 'auth_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  USER: 'auth_user',
  REMEMBER_ME: 'auth_remember_me',
  LAST_LOGIN: 'auth_last_login'
} as const;

// Token expiration times
export const TOKEN_EXPIRY = {
  ACCESS_TOKEN: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
  REMEMBER_ME: 30 * 24 * 60 * 60 * 1000 // 30 days
} as const;
