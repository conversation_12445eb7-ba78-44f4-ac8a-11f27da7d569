const { Client } = require('pg');

// Test database connection
async function testConnection() {
  const client = new Client({
    host: '*************',
    port: 5432,
    user: 'remote_admin',
    password: 'Vahid6636!',
    database: 'postgres', // Try connecting to default postgres database first
  });

  try {
    console.log('Attempting to connect to PostgreSQL...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL successfully!');
    
    // Test query
    const result = await client.query('SELECT version()');
    console.log('PostgreSQL version:', result.rows[0].version);
    
    // Check if glowroya database exists
    const dbCheck = await client.query("SELECT 1 FROM pg_database WHERE datname = 'glowroya'");
    if (dbCheck.rows.length > 0) {
      console.log('✅ Database "glowroya" exists');
    } else {
      console.log('❌ Database "glowroya" does not exist');
      console.log('Creating database "glowroya"...');
      await client.query('CREATE DATABASE glowroya');
      console.log('✅ Database "glowroya" created successfully');
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

testConnection();
