import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Eye,
  Users,
  Clock,
  MousePointer,
  Smartphone,
  Monitor,
  Tablet,
  Globe,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  RefreshCw,
  BarChart3,
  Target,
  ArrowRight
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminAnalytics } from '../../../hooks/useAdminAnalytics';
import { formatPercentage, formatGrowthRate, getDateRangeOptions, formatDuration } from '../../../utils/analyticsUtils';
import { formatNumber } from '../../../utils/formatters';

const TrafficAnalyticsPage: React.FC = () => {
  const { trafficData, loading, error } = useAdminAnalytics();
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');

  const handleExport = async () => {
    console.log('Exporting traffic analytics...');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // Mock traffic data since the hook returns empty arrays
  const mockTrafficData = {
    totalPageViews: 245680,
    uniqueVisitors: 18420,
    bounceRate: 45.2,
    averageSessionDuration: 185,
    conversionRate: 3.2,
    topPages: [
      { path: '/', title: 'صفحه اصلی', views: 45230, uniqueViews: 32100, bounceRate: 35.2, averageTimeOnPage: 120, conversionRate: 4.1 },
      { path: '/products', title: 'محصولات', views: 38420, uniqueViews: 28900, bounceRate: 42.1, averageTimeOnPage: 180, conversionRate: 5.8 },
      { path: '/categories/skincare', title: 'مراقبت از پوست', views: 28350, uniqueViews: 21200, bounceRate: 38.5, averageTimeOnPage: 210, conversionRate: 6.2 },
      { path: '/about', title: 'درباره ما', views: 15680, uniqueViews: 12400, bounceRate: 55.3, averageTimeOnPage: 95, conversionRate: 1.2 },
      { path: '/contact', title: 'تماس با ما', views: 12450, uniqueViews: 9800, bounceRate: 48.7, averageTimeOnPage: 85, conversionRate: 2.1 }
    ],
    trafficSources: [
      { source: 'جستجوی ارگانیک', visitors: 8420, percentage: 45.7, bounceRate: 38.2, conversionRate: 4.1, averageSessionDuration: 220 },
      { source: 'مستقیم', visitors: 5680, percentage: 30.8, bounceRate: 42.1, conversionRate: 5.2, averageSessionDuration: 195 },
      { source: 'شبکه‌های اجتماعی', visitors: 2840, percentage: 15.4, bounceRate: 52.3, conversionRate: 2.8, averageSessionDuration: 145 },
      { source: 'تبلیغات پولی', visitors: 1480, percentage: 8.1, bounceRate: 35.6, conversionRate: 6.8, averageSessionDuration: 180 }
    ],
    deviceStats: [
      { device: 'موبایل', visitors: 11050, percentage: 60.0, bounceRate: 48.2, conversionRate: 2.8 },
      { device: 'دسکتاپ', visitors: 5530, percentage: 30.0, bounceRate: 38.5, conversionRate: 4.2 },
      { device: 'تبلت', visitors: 1840, percentage: 10.0, bounceRate: 45.1, conversionRate: 3.1 }
    ]
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <AdminButton onClick={handleRefresh} className="mt-4">
            تلاش مجدد
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">ترافیک سایت</h1>
            <p className="text-gray-600 mt-1">تحلیل بازدید، منابع ترافیک و رفتار کاربران</p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
            >
              {getDateRangeOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={handleRefresh}
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی Excel
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdminStatsCard
            title="کل بازدید"
            value={formatNumber(mockTrafficData.totalPageViews)}
            change="+12.5%"
            changeType="increase"
            icon={Eye}
            color="bg-blue-500"
          />
          
          <AdminStatsCard
            title="بازدیدکنندگان منحصر"
            value={formatNumber(mockTrafficData.uniqueVisitors)}
            change="+8.3%"
            changeType="increase"
            icon={Users}
            color="bg-green-500"
          />
          
          <AdminStatsCard
            title="نرخ پرش"
            value={formatPercentage(mockTrafficData.bounceRate)}
            change="-2.1%"
            changeType="decrease"
            icon={MousePointer}
            color="bg-orange-500"
          />
          
          <AdminStatsCard
            title="مدت جلسه"
            value={formatDuration(mockTrafficData.averageSessionDuration)}
            change="+15.2%"
            changeType="increase"
            icon={Clock}
            color="bg-purple-500"
          />
        </div>

        {/* Top Pages */}
        <AdminCard title="صفحات پربازدید" icon={BarChart3}>
          <div className="space-y-4">
            {mockTrafficData.topPages.map((page, index) => (
              <motion.div
                key={page.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-admin-600">
                        {formatNumber(index + 1)}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{page.title}</h3>
                      <p className="text-sm text-gray-500">{page.path}</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-gray-900">
                      {formatNumber(page.views)} بازدید
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatNumber(page.uniqueViews)} منحصر
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-xs">
                  <div>
                    <span className="text-gray-500">نرخ پرش:</span>
                    <div className="font-medium">{formatPercentage(page.bounceRate)}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">زمان در صفحه:</span>
                    <div className="font-medium">{formatDuration(page.averageTimeOnPage)}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">نرخ تبدیل:</span>
                    <div className="font-medium text-green-600">{formatPercentage(page.conversionRate)}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </AdminCard>

        {/* Traffic Sources and Device Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Traffic Sources */}
          <AdminCard title="منابع ترافیک" icon={Globe}>
            <div className="space-y-4">
              {mockTrafficData.trafficSources.map((source, index) => (
                <motion.div
                  key={source.source}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">{source.source}</span>
                    <div className="text-left">
                      <span className="font-bold text-gray-900">
                        {formatNumber(source.visitors)}
                      </span>
                      <span className="text-sm text-gray-500 mr-2">
                        ({formatPercentage(source.percentage)})
                      </span>
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${source.percentage}%` }}
                      transition={{ delay: index * 0.2, duration: 0.8 }}
                      className="bg-admin-500 h-2 rounded-full"
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div>
                      <span className="text-gray-500">پرش:</span>
                      <div className="font-medium">{formatPercentage(source.bounceRate)}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">تبدیل:</span>
                      <div className="font-medium">{formatPercentage(source.conversionRate)}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">مدت:</span>
                      <div className="font-medium">{formatDuration(source.averageSessionDuration)}</div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </AdminCard>

          {/* Device Statistics */}
          <AdminCard title="آمار دستگاه‌ها" icon={Smartphone}>
            <div className="space-y-4">
              {mockTrafficData.deviceStats.map((device, index) => {
                const icons = [Smartphone, Monitor, Tablet];
                const IconComponent = icons[index] || Monitor;
                
                return (
                  <motion.div
                    key={device.device}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-admin-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-5 h-5 text-admin-600" />
                        </div>
                        <span className="font-medium text-gray-900">{device.device}</span>
                      </div>
                      <div className="text-left">
                        <div className="font-bold text-gray-900">
                          {formatNumber(device.visitors)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatPercentage(device.percentage)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${device.percentage}%` }}
                        transition={{ delay: index * 0.2, duration: 0.8 }}
                        className="h-2 rounded-full"
                        style={{
                          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B'][index % 3]
                        }}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span className="text-gray-500">نرخ پرش:</span>
                        <div className="font-medium">{formatPercentage(device.bounceRate)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">نرخ تبدیل:</span>
                        <div className="font-medium">{formatPercentage(device.conversionRate)}</div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </AdminCard>
        </div>

        {/* Conversion Funnel */}
        <AdminCard title="قیف تبدیل" icon={Target}>
          <div className="space-y-4">
            {[
              { step: 'بازدید صفحه اصلی', visitors: 18420, percentage: 100, color: 'bg-blue-500' },
              { step: 'مشاهده محصولات', visitors: 12850, percentage: 69.8, color: 'bg-green-500' },
              { step: 'افزودن به سبد', visitors: 3420, percentage: 18.6, color: 'bg-yellow-500' },
              { step: 'شروع پرداخت', visitors: 1680, percentage: 9.1, color: 'bg-orange-500' },
              { step: 'تکمیل خرید', visitors: 590, percentage: 3.2, color: 'bg-red-500' }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="relative"
              >
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={`w-4 h-4 rounded-full ${step.color}`}></div>
                    <span className="font-medium text-gray-900">{step.step}</span>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-gray-900">
                      {formatNumber(step.visitors)} نفر
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatPercentage(step.percentage)}
                    </div>
                  </div>
                </div>
                
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${step.percentage}%` }}
                    transition={{ delay: index * 0.2, duration: 0.8 }}
                    className={`h-2 rounded-full ${step.color}`}
                  />
                </div>
                
                {index < 4 && (
                  <div className="flex justify-center mt-2">
                    <ArrowRight className="w-4 h-4 text-gray-400 rotate-90" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default TrafficAnalyticsPage;
