import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Package,
  CreditCard,
  Users,
  Target
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard, { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminAnalytics } from '../../../hooks/useAdminAnalytics';
import { formatCurrency, formatPercentage, formatGrowthRate, getDateRangeOptions } from '../../../utils/analyticsUtils';
import { formatNumber } from '../../../utils/formatters';

const SalesAnalyticsPage: React.FC = () => {
  const { salesData, overview, loading, error, filters, setFilters } = useAdminAnalytics();
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  const handleExport = async () => {
    // Mock export functionality
    console.log('Exporting sales data...');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-red-600">{error}</p>
          <AdminButton onClick={handleRefresh} className="mt-4">
            تلاش مجدد
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">گزارش فروش</h1>
            <p className="text-gray-600 mt-1">تحلیل درآمد، سفارشات و عملکرد فروش</p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-admin-500"
            >
              {getDateRangeOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <AdminButton
              variant="outline"
              icon={RefreshCw}
              onClick={handleRefresh}
            >
              بروزرسانی
            </AdminButton>
            
            <AdminButton
              variant="outline"
              icon={Download}
              onClick={handleExport}
            >
              خروجی Excel
            </AdminButton>
          </div>
        </div>

        {/* Key Metrics */}
        {overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <AdminStatsCard
              title="کل درآمد"
              value={formatCurrency(overview.totalRevenue)}
              change={formatGrowthRate(overview.revenueGrowth).formatted}
              changeType={overview.revenueGrowth >= 0 ? 'increase' : 'decrease'}
              icon={DollarSign}
              color="bg-green-500"
            />
            
            <AdminStatsCard
              title="کل سفارشات"
              value={formatNumber(overview.totalOrders)}
              change={formatGrowthRate(overview.ordersGrowth).formatted}
              changeType={overview.ordersGrowth >= 0 ? 'increase' : 'decrease'}
              icon={ShoppingCart}
              color="bg-blue-500"
            />
            
            <AdminStatsCard
              title="میانگین ارزش سفارش"
              value={formatCurrency(overview.averageOrderValue)}
              change={formatPercentage(overview.conversionRate)}
              changeType="neutral"
              icon={TrendingUp}
              color="bg-purple-500"
            />
            
            <AdminStatsCard
              title="نرخ تبدیل"
              value={formatPercentage(overview.conversionRate)}
              change={formatGrowthRate(overview.revenueGrowth * 0.3).formatted}
              changeType={overview.revenueGrowth >= 0 ? 'increase' : 'decrease'}
              icon={Target}
              color="bg-orange-500"
            />
          </div>
        )}

        {/* Sales Charts and Analysis */}
        {salesData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Products */}
            <AdminCard title="محصولات پرفروش" icon={Package}>
              <div className="space-y-4">
                {salesData.topProducts.slice(0, 8).map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-admin-600">
                          {formatNumber(index + 1)}
                        </span>
                      </div>
                      {product.image && (
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-10 h-10 object-cover rounded-lg"
                        />
                      )}
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {product.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatNumber(product.sales)} فروش
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900">
                        {formatCurrency(product.revenue)}
                      </div>
                      <div className="text-xs text-gray-500">
                        درآمد
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </AdminCard>

            {/* Revenue by Channel */}
            <AdminCard title="کانال‌های فروش" icon={BarChart3}>
              <div className="space-y-4">
                {salesData.revenueByChannel.map((channel, index) => (
                  <div key={channel.channel} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        {channel.channel}
                      </span>
                      <div className="text-left">
                        <span className="text-sm font-bold text-gray-900">
                          {formatCurrency(channel.revenue)}
                        </span>
                        <span className="text-xs text-gray-500 mr-2">
                          ({formatPercentage(channel.percentage)})
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${channel.percentage}%` }}
                        transition={{ delay: index * 0.2, duration: 0.8 }}
                        className="bg-admin-500 h-2 rounded-full"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </div>
        )}

        {/* Payment Methods and Categories */}
        {salesData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Methods */}
            <AdminCard title="روش‌های پرداخت" icon={CreditCard}>
              <div className="space-y-4">
                {salesData.paymentMethods.map((method, index) => (
                  <div key={method.method} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 rounded-full bg-admin-500" style={{
                        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 4]
                      }}></div>
                      <span className="text-sm font-medium text-gray-700">
                        {method.method}
                      </span>
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-bold text-gray-900">
                        {formatNumber(method.count)} تراکنش
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatPercentage(method.percentage)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>

            {/* Top Categories */}
            <AdminCard title="دسته‌بندی‌های پرفروش" icon={Package}>
              <div className="space-y-4">
                {salesData.topCategories.map((category, index) => (
                  <div key={category.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-admin-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-admin-600">
                          {formatNumber(index + 1)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {category.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatNumber(category.sales)} فروش
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-gray-900">
                        {formatCurrency(category.revenue)}
                      </div>
                      <div className="text-xs text-gray-500">
                        درآمد
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </AdminCard>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default SalesAnalyticsPage;
