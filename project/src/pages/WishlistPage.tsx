import React from 'react';
import { motion } from 'framer-motion';
import { Heart, ShoppingBag, TrendingUp, Users } from 'lucide-react';
import Layout from '../components/layout/Layout';
import Wishlist from '../components/product/Wishlist';
import RecentlyViewed from '../components/product/RecentlyViewed';
import SEOHead from '../components/seo/SEOHead';
import { useWishlist } from '../hooks/useWishlist';
import { useAuth } from '../context/AuthContext';
import { formatNumber } from '../utils/formatters';

const WishlistPage: React.FC = () => {
  const { totalItems, getWishlistStats } = useWishlist();
  const { isAuthenticated, user } = useAuth();
  const stats = getWishlistStats();

  const pageTitle = `علاقه‌مندی‌ها (${totalItems} محصول) - گلو رویا`;
  const pageDescription = `مشاهده و مدیریت ${totalItems} محصول در لیست علاقه‌مندی‌های شما. خرید آسان و مقایسه محصولات مراقبت از پوست.`;

  return (
    <Layout>
      <SEOHead
        title={pageTitle}
        description={pageDescription}
        keywords={[
          'علاقه‌مندی‌ها',
          'لیست خرید',
          'محصولات مراقبت از پوست',
          'گلو رویا',
          'wishlist',
          'skincare favorites'
        ]}
        ogType="website"
        structuredData={{
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          name: pageTitle,
          description: pageDescription,
          url: `${window.location.origin}/wishlist`,
          mainEntity: {
            '@type': 'ItemList',
            numberOfItems: totalItems,
            itemListElement: []
          }
        }}
      />

      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Authentication Check */}
          {!isAuthenticated && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-blue-600" />
                <div>
                  <h3 className="font-medium text-blue-800">
                    برای تجربه بهتر وارد شوید
                  </h3>
                  <p className="text-sm text-blue-600 mt-1">
                    با ورود به حساب کاربری، علاقه‌مندی‌های شما در همه دستگاه‌ها همگام‌سازی می‌شود.
                  </p>
                </div>
                <button
                  onClick={() => window.location.href = '/login'}
                  className="btn-primary btn-sm ml-auto"
                >
                  ورود
                </button>
              </div>
            </motion.div>
          )}

          {/* Stats Cards */}
          {totalItems > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
            >
              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Heart className="w-6 h-6 text-red-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {totalItems}
                </div>
                <div className="text-sm text-text-secondary">
                  محصول علاقه‌مند
                </div>
              </div>

              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ShoppingBag className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {formatNumber(stats.totalValue)}
                </div>
                <div className="text-sm text-text-secondary">
                  تومان ارزش کل
                </div>
              </div>

              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {formatNumber(Math.round(stats.averagePrice))}
                </div>
                <div className="text-sm text-text-secondary">
                  تومان قیمت متوسط
                </div>
              </div>

              <div className="bg-white rounded-lg border p-6 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-text-primary mb-1">
                  {stats.availableItems}
                </div>
                <div className="text-sm text-text-secondary">
                  محصول موجود
                </div>
              </div>
            </motion.div>
          )}

          {/* Main Wishlist Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Wishlist />
          </motion.div>

          {/* Recently Viewed Section */}
          {totalItems > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mt-12"
            >
              <div className="bg-white rounded-lg border p-6">
                <RecentlyViewed 
                  maxItems={8}
                  showTitle={true}
                  compact={false}
                />
              </div>
            </motion.div>
          )}

          {/* Category Breakdown */}
          {totalItems > 0 && Object.keys(stats.categoryCounts).length > 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-8"
            >
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-semibold text-text-primary mb-4">
                  توزیع دسته‌بندی محصولات
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {Object.entries(stats.categoryCounts).map(([category, count]) => (
                    <div key={category} className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-xl font-bold text-primary mb-1">
                        {count}
                      </div>
                      <div className="text-sm text-text-secondary">
                        {category}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{ width: `${(count / totalItems) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Priority Breakdown */}
          {totalItems > 0 && Object.keys(stats.priorityCounts).length > 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="mt-8"
            >
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-semibold text-text-primary mb-4">
                  اولویت‌بندی محصولات
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(stats.priorityCounts).map(([priority, count]) => {
                    const priorityColors = {
                      urgent: 'bg-red-100 text-red-800',
                      high: 'bg-orange-100 text-orange-800',
                      medium: 'bg-blue-100 text-blue-800',
                      low: 'bg-gray-100 text-gray-800'
                    };
                    
                    const priorityLabels = {
                      urgent: 'فوری',
                      high: 'زیاد',
                      medium: 'متوسط',
                      low: 'کم'
                    };

                    return (
                      <div key={priority} className={`text-center p-4 rounded-lg ${priorityColors[priority as keyof typeof priorityColors]}`}>
                        <div className="text-xl font-bold mb-1">
                          {count}
                        </div>
                        <div className="text-sm">
                          {priorityLabels[priority as keyof typeof priorityLabels]}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </motion.div>
          )}

          {/* Tips for Empty Wishlist */}
          {totalItems === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mt-8"
            >
              <div className="bg-white rounded-lg border p-8">
                <h3 className="text-lg font-semibold text-text-primary mb-4 text-center">
                  نکاتی برای استفاده بهتر از علاقه‌مندی‌ها
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Heart className="w-6 h-6 text-blue-600" />
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      ذخیره محصولات
                    </h4>
                    <p className="text-sm text-text-secondary">
                      محصولات مورد علاقه خود را برای خرید آینده ذخیره کنید
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <TrendingUp className="w-6 h-6 text-green-600" />
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      مقایسه قیمت‌ها
                    </h4>
                    <p className="text-sm text-text-secondary">
                      قیمت محصولات را در طول زمان دنبال کنید
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <ShoppingBag className="w-6 h-6 text-purple-600" />
                    </div>
                    <h4 className="font-medium text-text-primary mb-2">
                      خرید سریع
                    </h4>
                    <p className="text-sm text-text-secondary">
                      محصولات را مستقیماً از لیست به سبد خرید اضافه کنید
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default WishlistPage;
