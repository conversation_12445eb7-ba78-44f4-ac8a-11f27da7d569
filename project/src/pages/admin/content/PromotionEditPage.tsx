import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { <PERSON>Left, Save, Eye, Trash2, Copy } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import PromotionEditor from '../../../components/admin/content/PromotionEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { PromotionFormData, Promotion } from '../../../types/adminContent';

const PromotionEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getPromotionById, updatePromotion, deletePromotion, duplicatePromotion, loading: contentLoading } = useAdminContent();
  
  const [promotion, setPromotion] = useState<Promotion | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  useEffect(() => {
    const loadPromotion = async () => {
      if (!id) {
        navigate('/admin/content/promotions');
        return;
      }

      // Wait for content to be loaded first
      if (contentLoading) {
        return;
      }

      try {
        setLoading(true);
        const promotionData = await getPromotionById(id);
        if (promotionData) {
          setPromotion(promotionData);
        } else {
          toast.error('تخفیف مورد نظر یافت نشد');
          navigate('/admin/content/promotions');
        }
      } catch (error) {
        console.error('Error loading promotion:', error);
        toast.error('خطا در بارگذاری تخفیف');
        navigate('/admin/content/promotions');
      } finally {
        setLoading(false);
      }
    };

    loadPromotion();
  }, [id, getPromotionById, navigate, contentLoading]);

  const handleSave = async (formData: PromotionFormData) => {
    if (!id) return;

    try {
      setSaving(true);
      await updatePromotion(id, formData);
      toast.success('تخفیف با موفقیت به‌روزرسانی شد');
      navigate('/admin/content/promotions');
    } catch (error) {
      console.error('Error updating promotion:', error);
      toast.error('خطا در به‌روزرسانی تخفیف');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    try {
      await deletePromotion(id);
      toast.success('تخفیف با موفقیت حذف شد');
      navigate('/admin/content/promotions');
    } catch (error) {
      console.error('Error deleting promotion:', error);
      toast.error('خطا در حذف تخفیف');
    }
  };

  const handleDuplicate = async () => {
    if (!id) return;

    try {
      const duplicatedPromotion = await duplicatePromotion(id);
      toast.success('تخفیف با موفقیت کپی شد');
      navigate(`/admin/content/promotions/${duplicatedPromotion.id}/edit`);
    } catch (error) {
      console.error('Error duplicating promotion:', error);
      toast.error('خطا در کپی کردن تخفیف');
    }
  };

  const handlePreview = () => {
    if (promotion) {
      // Open preview in new tab
      window.open(`/promotions/${promotion.code}`, '_blank');
    }
  };

  if (loading || contentLoading) {
    return (
      <AdminLayout title="ویرایش تخفیف">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (!promotion) {
    return (
      <AdminLayout title="ویرایش تخفیف">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            تخفیف یافت نشد
          </h3>
          <p className="text-gray-500 mb-6">
            تخفیف مورد نظر وجود ندارد یا حذف شده است
          </p>
          <AdminButton 
            variant="primary" 
            onClick={() => navigate('/admin/content/promotions')}
          >
            بازگشت به لیست تخفیف‌ها
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="ویرایش تخفیف"
      subtitle={`ویرایش تخفیف: ${promotion.title}`}
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="ghost" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/promotions')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Copy}
            onClick={handleDuplicate}
          >
            کپی
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Eye}
            onClick={handlePreview}
          >
            پیش‌نمایش
          </AdminButton>
          <AdminButton 
            variant="danger" 
            icon={Trash2}
            onClick={() => setDeleteModalOpen(true)}
          >
            حذف
          </AdminButton>
        </div>
      }
    >
      <PromotionEditor
        initialData={promotion}
        onSave={handleSave}
        onCancel={() => navigate('/admin/content/promotions')}
        loading={saving}
        mode="edit"
      />

      <AdminConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="حذف تخفیف"
        message={`آیا از حذف تخفیف "${promotion.title}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
        confirmText="حذف"
        cancelText="انصراف"
        variant="danger"
      />
    </AdminLayout>
  );
};

export default PromotionEditPage;
