import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, ChevronDown, Search, X, Building2 } from 'lucide-react';
import { FilterOption } from '../../types';
import { getBrandInfo } from '../../utils/brandUtils';

interface BrandFilterProps {
  brands: FilterOption[];
  selectedBrands: string[];
  onToggleBrand: (brand: string) => void;
  onClearBrands: () => void;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
  showSearch?: boolean;
  maxVisible?: number;
}

const BrandFilter: React.FC<BrandFilterProps> = ({
  brands,
  selectedBrands,
  onToggleBrand,
  onClearBrands,
  isCollapsible = false,
  defaultExpanded = true,
  showSearch = true,
  maxVisible = 8
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAll, setShowAll] = useState(false);

  const hasSelection = selectedBrands.length > 0;

  // Filter brands based on search query
  const filteredBrands = brands.filter(brand =>
    brand.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Determine which brands to show
  const visibleBrands = showAll ? filteredBrands : filteredBrands.slice(0, maxVisible);
  const hasMoreBrands = filteredBrands.length > maxVisible;

  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className="border-b border-gray-100 pb-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 font-medium text-text-primary ${
            isCollapsible ? 'hover:text-primary-600 transition-colors' : ''
          }`}
        >
          <span>برندها</span>
          {hasSelection && (
            <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
              {selectedBrands.length}
            </span>
          )}
          {isCollapsible && (
            <ChevronDown 
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            />
          )}
        </button>
        
        {hasSelection && (
          <button
            onClick={onClearBrands}
            className="text-sm text-text-muted hover:text-red-500 transition-colors"
          >
            پاک کردن
          </button>
        )}
      </div>

      {/* Content */}
      {(!isCollapsible || isExpanded) && (
        <motion.div
          initial={isCollapsible ? { height: 0, opacity: 0 } : false}
          animate={isCollapsible ? { height: 'auto', opacity: 1 } : false}
          exit={isCollapsible ? { height: 0, opacity: 0 } : false}
          transition={{ duration: 0.2 }}
          className="space-y-4"
        >
          {/* Search */}
          {showSearch && brands.length > 5 && (
            <div className="relative">
              <div className="flex items-center bg-gray-50 rounded-lg px-3 py-2">
                <Search className="w-4 h-4 text-text-muted" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="جستجوی برند..."
                  className="flex-1 bg-transparent text-sm text-text-primary placeholder-text-muted focus:outline-none mr-2"
                />
                {searchQuery && (
                  <button
                    onClick={clearSearch}
                    className="text-text-muted hover:text-text-primary transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Selected Brands Summary */}
          {hasSelection && (
            <div className="bg-primary-50 rounded-lg p-3">
              <div className="text-sm text-primary-700 mb-2">برندهای انتخاب شده:</div>
              <div className="flex flex-wrap gap-2">
                {selectedBrands.map(brandValue => {
                  const brand = brands.find(b => b.value === brandValue);
                  return brand ? (
                    <span
                      key={brandValue}
                      className="inline-flex items-center gap-1 bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full"
                    >
                      {brand.label}
                      <button
                        onClick={() => onToggleBrand(brandValue)}
                        className="hover:text-primary-900 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Brand Options */}
          <div className="space-y-3 max-h-64 overflow-y-auto custom-scroll">
            <AnimatePresence mode="popLayout">
              {visibleBrands.map((brand, index) => {
                const isSelected = selectedBrands.includes(brand.value);
                
                return (
                  <motion.label
                    key={brand.value}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2, delay: index * 0.03 }}
                    className="flex items-center justify-between cursor-pointer group"
                  >
                    <div className="flex items-center">
                      <div className="relative">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => onToggleBrand(brand.value)}
                          className="sr-only"
                          disabled={brand.disabled}
                        />
                        <div className={`w-5 h-5 rounded border-2 transition-all duration-200 ${
                          isSelected
                            ? 'bg-primary-500 border-primary-500'
                            : brand.disabled
                            ? 'border-gray-200 bg-gray-50'
                            : 'border-gray-300 group-hover:border-primary-400'
                        }`}>
                          {isSelected && (
                            <Check className="w-3 h-3 text-white absolute top-0.5 right-0.5" />
                          )}
                        </div>
                      </div>

                      {/* Brand Logo */}
                      {(() => {
                        const brandInfo = getBrandInfo(brand.value);
                        return brandInfo.logo ? (
                          <img
                            src={brandInfo.logo}
                            alt={`لوگو ${brandInfo.name}`}
                            className="w-6 h-6 rounded object-cover mr-3"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : (
                          <Building2 className="w-4 h-4 text-gray-400 mr-3" />
                        );
                      })()}

                      <span className={`text-sm transition-colors ${
                        brand.disabled
                          ? 'text-gray-400'
                          : isSelected
                          ? 'text-primary-600 font-medium'
                          : 'text-text-secondary group-hover:text-text-primary'
                      }`}>
                        {brand.label}
                      </span>
                    </div>
                    
                    {brand.count !== undefined && (
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        isSelected
                          ? 'bg-primary-100 text-primary-700'
                          : 'bg-gray-100 text-text-muted'
                      }`}>
                        {brand.count}
                      </span>
                    )}
                  </motion.label>
                );
              })}
            </AnimatePresence>
          </div>

          {/* Show More/Less Button */}
          {hasMoreBrands && !searchQuery && (
            <button
              onClick={() => setShowAll(!showAll)}
              className="w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium py-2 border border-primary-200 rounded-lg hover:bg-primary-50 transition-colors"
            >
              {showAll ? `نمایش کمتر` : `نمایش ${filteredBrands.length - maxVisible} برند دیگر`}
            </button>
          )}

          {/* No brands message */}
          {filteredBrands.length === 0 && (
            <div className="text-center py-4 text-text-muted text-sm">
              {searchQuery ? 'برندی با این نام یافت نشد' : 'برندی یافت نشد'}
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default BrandFilter;
