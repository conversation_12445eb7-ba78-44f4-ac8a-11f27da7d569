import React from 'react';
import { Link } from 'react-router-dom';
import { Phone, Mail, MapPin, Instagram, Twitter, Facebook, Sparkles } from 'lucide-react';
import SecurityBadges from '../trust/SecurityBadges';
import TrustBadges from '../trust/TrustBadges';
import NewsletterSignup from '../newsletter/NewsletterSignup';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-br from-primary-50 to-secondary-50 pt-12 pb-6">
      <div className="container-custom">
        {/* Newsletter Section */}
        <div className="mb-12">
          <div className="max-w-2xl mx-auto">
            <NewsletterSignup
              source="footer"
              variant="compact"
              showBenefits={false}
              className="bg-white shadow-lg"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div>
            <Link to="/" className="flex items-center mb-4">
              <Sparkles className="h-6 w-6 text-primary-500 ml-2" />
              <span className="text-2xl font-bold">
                <span className="text-primary-600">Glow</span>
                <span className="text-accent-500">Roya</span>
              </span>
            </Link>
            <p className="text-text-secondary mb-6">
              محصولات مراقبت از پوست با کیفیت و الهام گرفته از طبیعت برای پوستی درخشان و سالم. ما به زیبایی طبیعی شما اهمیت می‌دهیم.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              <a href="#" className="text-text-secondary hover:text-primary-500 transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-text-secondary hover:text-primary-500 transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-text-secondary hover:text-primary-500 transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">دسترسی سریع</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-text-secondary hover:text-primary-500 transition-colors">خانه</Link>
              </li>
              <li>
                <Link to="/products" className="text-text-secondary hover:text-primary-500 transition-colors">فروشگاه</Link>
              </li>
              <li>
                <Link to="/about" className="text-text-secondary hover:text-primary-500 transition-colors">درباره ما</Link>
              </li>
              <li>
                <Link to="/contact" className="text-text-secondary hover:text-primary-500 transition-colors">تماس با ما</Link>
              </li>
              <li>
                <Link to="/faq" className="text-text-secondary hover:text-primary-500 transition-colors">سوالات متداول</Link>
              </li>
              <li>
                <Link to="/blog" className="text-text-secondary hover:text-primary-500 transition-colors">بلاگ</Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">دسته‌بندی محصولات</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/products?category=serums" className="text-text-secondary hover:text-primary-500 transition-colors">سرم‌ها</Link>
              </li>
              <li>
                <Link to="/products?category=creams" className="text-text-secondary hover:text-primary-500 transition-colors">کرم‌ها</Link>
              </li>
              <li>
                <Link to="/products?category=cleansers" className="text-text-secondary hover:text-primary-500 transition-colors">پاک کننده‌ها</Link>
              </li>
              <li>
                <Link to="/products?category=masks" className="text-text-secondary hover:text-primary-500 transition-colors">ماسک‌ها</Link>
              </li>
              <li>
                <Link to="/products?category=toners" className="text-text-secondary hover:text-primary-500 transition-colors">تونرها</Link>
              </li>
              <li>
                <Link to="/products?category=sunscreens" className="text-text-secondary hover:text-primary-500 transition-colors">ضد آفتاب‌ها</Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">اطلاعات تماس</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 text-primary-500 ml-2 mt-1" />
                <span className="text-text-secondary">تهران، خیابان ولیعصر، پلاک ۱۵۶</span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 text-primary-500 ml-2" />
                <span className="text-text-secondary">۰۲۱-۱۲۳۴۵۶۷۸</span>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 text-primary-500 ml-2" />
                <span className="text-text-secondary"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        {/* Trust and Security Badges */}
        <div className="mt-10 pt-8 border-t border-gray-200">
          <div className="mb-8">
            <TrustBadges variant="horizontal" showDescriptions={false} size="small" />
          </div>
          <SecurityBadges layout="compact" showVerificationLinks={false} size="small" />
        </div>

        <div className="border-t border-gray-200 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-text-secondary text-sm mb-4 md:mb-0">
            &copy; ۱۴۰۳ گلورویا. تمامی حقوق محفوظ است.
          </p>
          <div className="flex space-x-4 space-x-reverse">
            <Link to="/terms" className="text-text-secondary hover:text-primary-500 text-sm transition-colors">
              شرایط استفاده
            </Link>
            <Link to="/privacy" className="text-text-secondary hover:text-primary-500 text-sm transition-colors">
              حریم خصوصی
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;