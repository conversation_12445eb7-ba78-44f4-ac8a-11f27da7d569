"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const skincare = await prisma.category.upsert({
        where: { slug: 'skincare' },
        update: {},
        create: {
            name: 'مراقبت از پوست',
            nameEn: 'Skincare',
            slug: 'skincare',
            description: 'محصولات مراقبت از پوست',
            isActive: true,
            sortOrder: 1,
        },
    });
    const cleansers = await prisma.category.upsert({
        where: { slug: 'cleansers' },
        update: {},
        create: {
            name: 'پاک‌کننده‌ها',
            nameEn: 'Cleansers',
            slug: 'cleansers',
            description: 'پاک‌کننده‌های صورت',
            parentId: skincare.id,
            isActive: true,
            sortOrder: 1,
        },
    });
    const cerave = await prisma.brand.upsert({
        where: { slug: 'cerave' },
        update: {},
        create: {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            nameEn: '<PERSON>raVe',
            slug: 'cerave',
            description: 'برند معروف مراقبت از پوست',
            isActive: true,
        },
    });
    const nivea = await prisma.brand.upsert({
        where: { slug: 'nivea' },
        update: {},
        create: {
            name: 'نیوآ',
            nameEn: 'Nivea',
            slug: 'nivea',
            description: 'برند معروف آلمانی',
            isActive: true,
        },
    });
    const adminUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uV8O',
            firstName: 'مدیر',
            lastName: 'سیستم',
            role: 'ADMIN',
            status: 'ACTIVE',
            isEmailVerified: true,
        },
    });
    const customerUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uV8O',
            firstName: 'مشتری',
            lastName: 'نمونه',
            role: 'CUSTOMER',
            status: 'ACTIVE',
            isEmailVerified: true,
        },
    });
    const product1 = await prisma.product.upsert({
        where: { slug: 'cerave-foaming-cleanser' },
        update: {},
        create: {
            name: 'پاک‌کننده فوم‌ساز سراوی',
            nameEn: 'CeraVe Foaming Cleanser',
            slug: 'cerave-foaming-cleanser',
            description: 'پاک‌کننده ملایم برای پوست‌های چرب و مختلط',
            shortDescription: 'پاک‌کننده فوم‌ساز ملایم',
            sku: 'CRV-FC-355',
            brandId: cerave.id,
            price: 450000,
            comparePrice: 500000,
            isActive: true,
            isFeatured: true,
            trackQuantity: true,
            tags: ['پاک‌کننده', 'فوم‌ساز', 'پوست چرب'],
        },
    });
    await prisma.productCategory.upsert({
        where: {
            productId_categoryId: {
                productId: product1.id,
                categoryId: cleansers.id,
            },
        },
        update: {},
        create: {
            productId: product1.id,
            categoryId: cleansers.id,
        },
    });
    await prisma.productInventory.upsert({
        where: { productId: product1.id },
        update: {},
        create: {
            productId: product1.id,
            quantity: 100,
            lowStockThreshold: 10,
        },
    });
    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Created:');
    console.log(`   - Categories: ${skincare.name}, ${cleansers.name}`);
    console.log(`   - Brands: ${cerave.name}, ${nivea.name}`);
    console.log(`   - Users: ${adminUser.email}, ${customerUser.email}`);
    console.log(`   - Products: ${product1.name}`);
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map