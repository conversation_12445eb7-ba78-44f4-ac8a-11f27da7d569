import React from 'react';
import { motion } from 'framer-motion';
import { Search, Clock, TrendingUp, Hash } from 'lucide-react';

interface SearchSuggestionsProps {
  suggestions: string[];
  popularTerms: string[];
  searchHistory: string[];
  query: string;
  activeIndex: number;
  onSelectSuggestion: (suggestion: string) => void;
  showHistory?: boolean;
}

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  suggestions,
  popularTerms,
  searchHistory,
  query,
  activeIndex,
  onSelectSuggestion,
  showHistory = true
}) => {
  const hasQuery = query.length > 0;
  const hasSuggestions = suggestions.length > 0;
  const hasHistory = searchHistory.length > 0 && showHistory;
  const hasPopular = popularTerms.length > 0;

  if (!hasQuery && !hasHistory && !hasPopular) {
    return null;
  }

  let currentIndex = 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
      className="bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden"
    >
      {/* Query-based suggestions */}
      {hasSuggestions && (
        <div className="border-b border-gray-100">
          <div className="px-4 py-2 bg-gray-50">
            <div className="flex items-center gap-2 text-sm font-medium text-text-secondary">
              <Search className="w-4 h-4" />
              پیشنهادات جستجو
            </div>
          </div>
          <div className="py-2">
            {suggestions.map((suggestion, index) => {
              const isActive = currentIndex === activeIndex;
              currentIndex++;
              
              return (
                <button
                  key={suggestion}
                  onClick={() => onSelectSuggestion(suggestion)}
                  className={`w-full text-right px-4 py-2 hover:bg-primary-50 transition-colors ${
                    isActive ? 'bg-primary-50 text-primary-700' : 'text-text-primary'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Search className="w-4 h-4 text-text-muted flex-shrink-0" />
                    <span className="flex-1 text-sm">{suggestion}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Search history */}
      {!hasQuery && hasHistory && (
        <div className="border-b border-gray-100">
          <div className="px-4 py-2 bg-gray-50">
            <div className="flex items-center gap-2 text-sm font-medium text-text-secondary">
              <Clock className="w-4 h-4" />
              جستجوهای اخیر
            </div>
          </div>
          <div className="py-2">
            {searchHistory.map((term, index) => {
              const isActive = currentIndex === activeIndex;
              currentIndex++;
              
              return (
                <button
                  key={`history-${term}`}
                  onClick={() => onSelectSuggestion(term)}
                  className={`w-full text-right px-4 py-2 hover:bg-gray-50 transition-colors ${
                    isActive ? 'bg-gray-50 text-text-primary' : 'text-text-secondary'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Clock className="w-4 h-4 text-text-muted flex-shrink-0" />
                    <span className="flex-1 text-sm">{term}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Popular search terms */}
      {!hasQuery && hasPopular && (
        <div>
          <div className="px-4 py-2 bg-gray-50">
            <div className="flex items-center gap-2 text-sm font-medium text-text-secondary">
              <TrendingUp className="w-4 h-4" />
              جستجوهای محبوب
            </div>
          </div>
          <div className="py-2">
            {popularTerms.slice(0, 6).map((term, index) => {
              const isActive = currentIndex === activeIndex;
              currentIndex++;
              
              return (
                <button
                  key={`popular-${term}`}
                  onClick={() => onSelectSuggestion(term)}
                  className={`w-full text-right px-4 py-2 hover:bg-orange-50 transition-colors ${
                    isActive ? 'bg-orange-50 text-orange-700' : 'text-text-secondary'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Hash className="w-4 h-4 text-orange-500 flex-shrink-0" />
                    <span className="flex-1 text-sm">{term}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Quick search tips */}
      {!hasQuery && (
        <div className="px-4 py-3 bg-blue-50 border-t border-blue-100">
          <div className="text-xs text-blue-700">
            <div className="font-medium mb-1">نکات جستجو:</div>
            <div className="space-y-1">
              <div>• برای جستجوی دقیق‌تر از نام برند استفاده کنید</div>
              <div>• می‌توانید بر اساس نوع پوست جستجو کنید</div>
              <div>• جستجوی ترکیبات مثل "هیالورونیک اسید"</div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SearchSuggestions;
