import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, AlertCircle } from 'lucide-react';
import { ProductVariant } from '../../types';

interface ColorSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant;
  onVariantChange: (variant: ProductVariant) => void;
  disabled?: boolean;
  required?: boolean;
  groupName: string;
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  variants,
  selectedVariant,
  onVariantChange,
  disabled = false,
  required = false,
  groupName
}) => {
  const handleColorSelect = (variant: ProductVariant) => {
    if (disabled || variant.disabled || variant.stock === 0) return;
    onVariantChange(variant);
  };

  return (
    <div className="color-selector">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium text-text-primary">
            {groupName}
            {required && <span className="text-red-500 mr-1">*</span>}
          </h4>
          {selectedVariant && (
            <span className="text-sm text-text-secondary">
              ({selectedVariant.name})
            </span>
          )}
        </div>
        
        {required && !selectedVariant && (
          <div className="flex items-center gap-1 text-red-500 text-xs">
            <AlertCircle className="w-3 h-3" />
            <span>انتخاب اجباری</span>
          </div>
        )}
      </div>

      {/* Color Options */}
      <div className="flex flex-wrap gap-3">
        <AnimatePresence>
          {variants.map((variant, index) => {
            const isSelected = selectedVariant?.id === variant.id;
            const isDisabled = disabled || variant.disabled || variant.stock === 0;
            const isOutOfStock = variant.stock === 0;

            return (
              <motion.button
                key={variant.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                onClick={() => handleColorSelect(variant)}
                disabled={isDisabled}
                className={`relative group ${
                  isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'
                }`}
                title={`${variant.name}${isOutOfStock ? ' - ناموجود' : ''}`}
              >
                {/* Color Swatch */}
                <div
                  className={`w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                    isSelected
                      ? 'border-primary-500 ring-2 ring-primary-200'
                      : isDisabled
                      ? 'border-gray-200'
                      : 'border-gray-300 group-hover:border-primary-400'
                  } ${
                    isDisabled ? 'opacity-50' : ''
                  }`}
                  style={{
                    backgroundColor: variant.colorCode || '#f3f4f6'
                  }}
                >
                  {/* Selected Check */}
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-sm">
                        <Check className="w-4 h-4 text-primary-600" />
                      </div>
                    </motion.div>
                  )}

                  {/* Out of Stock Indicator */}
                  {isOutOfStock && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-8 h-0.5 bg-red-500 rotate-45"></div>
                    </div>
                  )}
                </div>

                {/* Color Name */}
                <div className="mt-2 text-center">
                  <span className={`text-xs ${
                    isSelected 
                      ? 'text-primary-600 font-medium' 
                      : isDisabled
                      ? 'text-gray-400'
                      : 'text-text-secondary group-hover:text-text-primary'
                  }`}>
                    {variant.name}
                  </span>
                  
                  {/* Stock Info */}
                  {variant.stock <= 5 && variant.stock > 0 && (
                    <div className="text-xs text-orange-500 mt-1">
                      {variant.stock} عدد باقی‌مانده
                    </div>
                  )}
                  
                  {/* Price Difference */}
                  {variant.price && variant.price !== 0 && (
                    <div className="text-xs text-primary-600 mt-1">
                      {variant.price > 0 ? '+' : ''}{variant.price.toLocaleString()} تومان
                    </div>
                  )}
                </div>

                {/* Hover Effect */}
                <motion.div
                  className="absolute inset-0 rounded-full bg-primary-500 opacity-0 group-hover:opacity-10 transition-opacity duration-200"
                  whileHover={{ scale: 1.1 }}
                />
              </motion.button>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Selected Variant Info */}
      {selectedVariant && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-4 p-3 bg-primary-50 rounded-lg"
        >
          <div className="flex items-center justify-between text-sm">
            <span className="text-primary-700">
              رنگ انتخاب شده: <strong>{selectedVariant.name}</strong>
            </span>
            <span className="text-primary-600">
              موجودی: {selectedVariant.stock} عدد
            </span>
          </div>
          
          {selectedVariant.price && selectedVariant.price !== 0 && (
            <div className="mt-2 text-sm text-primary-600">
              تغییر قیمت: {selectedVariant.price > 0 ? '+' : ''}{selectedVariant.price.toLocaleString()} تومان
            </div>
          )}
        </motion.div>
      )}

      {/* No Colors Available */}
      {variants.length === 0 && (
        <div className="text-center py-4 text-text-muted text-sm">
          رنگی در دسترس نیست
        </div>
      )}
    </div>
  );
};

export default ColorSelector;
