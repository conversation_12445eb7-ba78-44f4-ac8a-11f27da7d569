import { Request, Response, NextFunction } from 'express';
import { 
  ContentService, 
  CreateBannerData, 
  UpdateBannerData,
  CreatePromotionData,
  UpdatePromotionData,
  CreateNewsletterData,
  UpdateNewsletterData,
  CreatePageData,
  UpdatePageData,
  CreateMediaData,
  UpdateMediaData,
  ContentFilters 
} from '../services/contentService';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';

export class ContentController {
  // Banner Management
  static async getBanners(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        isActive,
        type,
        search,
        startDate,
        endDate,
        createdBy,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters: ContentFilters = {
        status: status as string,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        type: type as string,
        search: search as string,
        startDate: startDate as string,
        endDate: endDate as string,
        createdBy: createdBy as string
      };

      const result = await ContentService.getBanners(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      res.json({
        success: true,
        message: 'بنرها با موفقیت دریافت شد',
        data: result.banners,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  static async getBannerById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const banner = await ContentService.getBannerById(id);

      res.json({
        success: true,
        message: 'بنر با موفقیت دریافت شد',
        data: banner
      });
    } catch (error) {
      next(error);
    }
  }

  static async createBanner(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const bannerData: CreateBannerData = req.body;
      const createdBy = req.user!.id;

      const banner = await ContentService.createBanner(bannerData, createdBy);

      logger.info(`Banner created by admin: ${req.user?.email}, banner: ${banner.title}`);

      res.status(201).json({
        success: true,
        message: 'بنر با موفقیت ایجاد شد',
        data: banner
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateBanner(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const bannerData: UpdateBannerData = req.body;

      const banner = await ContentService.updateBanner(id, bannerData);

      logger.info(`Banner updated by admin: ${req.user?.email}, banner: ${banner.title}`);

      res.json({
        success: true,
        message: 'بنر با موفقیت به‌روزرسانی شد',
        data: banner
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteBanner(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ContentService.deleteBanner(id);

      logger.info(`Banner deleted by admin: ${req.user?.email}, banner ID: ${id}`);

      res.json({
        success: true,
        message: 'بنر با موفقیت حذف شد'
      });
    } catch (error) {
      next(error);
    }
  }

  // Promotion Management
  static async getPromotions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        isActive,
        type,
        search,
        startDate,
        endDate,
        createdBy,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters: ContentFilters = {
        status: status as string,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        type: type as string,
        search: search as string,
        startDate: startDate as string,
        endDate: endDate as string,
        createdBy: createdBy as string
      };

      const result = await ContentService.getPromotions(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      res.json({
        success: true,
        message: 'تخفیف‌ها با موفقیت دریافت شد',
        data: result.promotions,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPromotionById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const promotion = await ContentService.getPromotionById(id);

      res.json({
        success: true,
        message: 'تخفیف با موفقیت دریافت شد',
        data: promotion
      });
    } catch (error) {
      next(error);
    }
  }

  static async createPromotion(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const promotionData: CreatePromotionData = req.body;
      const createdBy = req.user!.id;

      const promotion = await ContentService.createPromotion(promotionData, createdBy);

      logger.info(`Promotion created by admin: ${req.user?.email}, promotion: ${promotion.title}`);

      res.status(201).json({
        success: true,
        message: 'تخفیف با موفقیت ایجاد شد',
        data: promotion
      });
    } catch (error) {
      next(error);
    }
  }

  static async updatePromotion(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const promotionData: UpdatePromotionData = req.body;

      const promotion = await ContentService.updatePromotion(id, promotionData);

      logger.info(`Promotion updated by admin: ${req.user?.email}, promotion: ${promotion.title}`);

      res.json({
        success: true,
        message: 'تخفیف با موفقیت به‌روزرسانی شد',
        data: promotion
      });
    } catch (error) {
      next(error);
    }
  }

  static async deletePromotion(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ContentService.deletePromotion(id);

      logger.info(`Promotion deleted by admin: ${req.user?.email}, promotion ID: ${id}`);

      res.json({
        success: true,
        message: 'تخفیف با موفقیت حذف شد'
      });
    } catch (error) {
      next(error);
    }
  }

  // Newsletter Management
  static async getNewsletters(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        isActive,
        type,
        search,
        startDate,
        endDate,
        createdBy,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters: ContentFilters = {
        status: status as string,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        type: type as string,
        search: search as string,
        startDate: startDate as string,
        endDate: endDate as string,
        createdBy: createdBy as string
      };

      const result = await ContentService.getNewsletters(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      res.json({
        success: true,
        message: 'خبرنامه‌ها با موفقیت دریافت شد',
        data: result.newsletters,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  static async getNewsletterById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const newsletter = await ContentService.getNewsletterById(id);

      res.json({
        success: true,
        message: 'خبرنامه با موفقیت دریافت شد',
        data: newsletter
      });
    } catch (error) {
      next(error);
    }
  }

  static async createNewsletter(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const newsletterData: CreateNewsletterData = req.body;
      const createdBy = req.user!.id;

      const newsletter = await ContentService.createNewsletter(newsletterData, createdBy);

      logger.info(`Newsletter created by admin: ${req.user?.email}, newsletter: ${newsletter.title}`);

      res.status(201).json({
        success: true,
        message: 'خبرنامه با موفقیت ایجاد شد',
        data: newsletter
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateNewsletter(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const newsletterData: UpdateNewsletterData = req.body;

      const newsletter = await ContentService.updateNewsletter(id, newsletterData);

      logger.info(`Newsletter updated by admin: ${req.user?.email}, newsletter: ${newsletter.title}`);

      res.json({
        success: true,
        message: 'خبرنامه با موفقیت به‌روزرسانی شد',
        data: newsletter
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteNewsletter(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ContentService.deleteNewsletter(id);

      logger.info(`Newsletter deleted by admin: ${req.user?.email}, newsletter ID: ${id}`);

      res.json({
        success: true,
        message: 'خبرنامه با موفقیت حذف شد'
      });
    } catch (error) {
      next(error);
    }
  }

  // Page Content Management
  static async getPages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        isActive,
        search,
        createdBy,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters: ContentFilters = {
        status: status as string,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        search: search as string,
        createdBy: createdBy as string
      };

      const result = await ContentService.getPages(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      res.json({
        success: true,
        message: 'صفحات با موفقیت دریافت شد',
        data: result.pages,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPageById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const page = await ContentService.getPageById(id);

      res.json({
        success: true,
        message: 'صفحه با موفقیت دریافت شد',
        data: page
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPageBySlug(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { slug } = req.params;
      const page = await ContentService.getPageBySlug(slug);

      res.json({
        success: true,
        message: 'صفحه با موفقیت دریافت شد',
        data: page
      });
    } catch (error) {
      next(error);
    }
  }

  static async createPage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const pageData: CreatePageData = req.body;
      const createdBy = req.user!.id;

      const page = await ContentService.createPage(pageData, createdBy);

      logger.info(`Page created by admin: ${req.user?.email}, page: ${page.title}`);

      res.status(201).json({
        success: true,
        message: 'صفحه با موفقیت ایجاد شد',
        data: page
      });
    } catch (error) {
      next(error);
    }
  }

  static async updatePage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const pageData: UpdatePageData = req.body;

      const page = await ContentService.updatePage(id, pageData);

      logger.info(`Page updated by admin: ${req.user?.email}, page: ${page.title}`);

      res.json({
        success: true,
        message: 'صفحه با موفقیت به‌روزرسانی شد',
        data: page
      });
    } catch (error) {
      next(error);
    }
  }

  static async deletePage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ContentService.deletePage(id);

      logger.info(`Page deleted by admin: ${req.user?.email}, page ID: ${id}`);

      res.json({
        success: true,
        message: 'صفحه با موفقیت حذف شد'
      });
    } catch (error) {
      next(error);
    }
  }

  // Media Management
  static async getMediaItems(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters: ContentFilters = {
        search: search as string
      };

      const result = await ContentService.getMediaItems(filters, {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      res.json({
        success: true,
        message: 'فایل‌های رسانه با موفقیت دریافت شد',
        data: result.mediaItems,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }

  static async createMediaItem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const mediaData: CreateMediaData = req.body;
      const uploadedBy = req.user!.id;

      const mediaItem = await ContentService.createMediaItem(mediaData, uploadedBy);

      logger.info(`Media item created by admin: ${req.user?.email}, file: ${mediaItem.filename}`);

      res.status(201).json({
        success: true,
        message: 'فایل رسانه با موفقیت ایجاد شد',
        data: mediaItem
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteMediaItem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      await ContentService.deleteMediaItem(id);

      logger.info(`Media item deleted by admin: ${req.user?.email}, media ID: ${id}`);

      res.json({
        success: true,
        message: 'فایل رسانه با موفقیت حذف شد'
      });
    } catch (error) {
      next(error);
    }
  }

  // Analytics
  static async getContentAnalytics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const analytics = await ContentService.getContentAnalytics();

      res.json({
        success: true,
        message: 'آمار محتوا با موفقیت دریافت شد',
        data: analytics
      });
    } catch (error) {
      next(error);
    }
  }
}
