import { Product, CartItem, SelectedVariants } from './index';

// Enhanced cart item with additional metadata
export interface AdvancedCartItem extends CartItem {
  id: string; // Unique identifier for cart item
  addedAt: Date; // When item was added to cart
  lastModified: Date; // When item was last modified
  source: CartItemSource; // How item was added to cart
  isGift?: boolean; // Whether item is marked as gift
  giftMessage?: string; // Gift message if applicable
  estimatedDelivery?: Date; // Estimated delivery date
  loyaltyPointsEarned?: number; // Points that will be earned
  appliedDiscounts?: CartDiscount[]; // Applied discounts
}

// Cart item source tracking
export type CartItemSource = 
  | 'product_page' 
  | 'category_page' 
  | 'search_results' 
  | 'recommendations' 
  | 'wishlist' 
  | 'recently_viewed'
  | 'bulk_add'
  | 'reorder';

// Cart discount information
export interface CartDiscount {
  id: string;
  type: 'percentage' | 'fixed' | 'loyalty' | 'coupon' | 'bulk';
  value: number;
  description: string;
  appliedTo: 'item' | 'cart' | 'shipping';
  conditions?: DiscountCondition[];
}

export interface DiscountCondition {
  type: 'min_quantity' | 'min_amount' | 'category' | 'brand' | 'loyalty_tier';
  value: any;
}

// Save for later item
export interface SavedForLaterItem {
  id: string;
  product: Product;
  quantity: number;
  selectedVariants?: SelectedVariants;
  savedAt: Date;
  reason?: SaveReason;
  reminderDate?: Date;
}

export type SaveReason = 
  | 'out_of_stock' 
  | 'price_watch' 
  | 'later_purchase' 
  | 'gift_planning' 
  | 'comparison';

// Cart recommendations
export interface CartRecommendation {
  id: string;
  product: Product;
  reason: RecommendationReason;
  confidence: number; // 0-1 confidence score
  discount?: CartDiscount;
  bundleWith?: number[]; // Product IDs to bundle with
}

export type RecommendationReason = 
  | 'frequently_bought_together' 
  | 'similar_products' 
  | 'category_popular' 
  | 'brand_collection' 
  | 'price_range_match'
  | 'seasonal_trending'
  | 'loyalty_exclusive';

// Cart analytics data
export interface CartAnalytics {
  sessionId: string;
  userId?: string;
  events: CartEvent[];
  startTime: Date;
  lastActivity: Date;
  totalValue: number;
  itemCount: number;
  abandonmentRisk: number; // 0-1 risk score
}

export interface CartEvent {
  id: string;
  type: CartEventType;
  timestamp: Date;
  productId?: number;
  quantity?: number;
  value?: number;
  metadata?: Record<string, any>;
}

export type CartEventType = 
  | 'item_added' 
  | 'item_removed' 
  | 'quantity_updated' 
  | 'cart_viewed' 
  | 'checkout_started'
  | 'cart_abandoned'
  | 'item_saved_later'
  | 'recommendation_viewed'
  | 'recommendation_added';

// Cart persistence settings
export interface CartPersistenceSettings {
  enabled: boolean;
  storageType: 'localStorage' | 'sessionStorage' | 'indexedDB';
  encryptData: boolean;
  syncWithUser: boolean;
  maxAge: number; // in days
  autoCleanup: boolean;
}

// Cart summary with detailed breakdown
export interface AdvancedCartSummary {
  subtotal: number;
  discounts: CartDiscount[];
  totalDiscount: number;
  shipping: ShippingInfo;
  tax: TaxInfo;
  loyaltyPoints: LoyaltyPointsInfo;
  total: number;
  savings: number;
  estimatedDelivery: Date;
  paymentOptions: PaymentOption[];
}

export interface ShippingInfo {
  cost: number;
  method: string;
  estimatedDays: number;
  isFree: boolean;
  freeShippingThreshold?: number;
  remainingForFreeShipping?: number;
}

export interface TaxInfo {
  rate: number;
  amount: number;
  included: boolean;
}

export interface LoyaltyPointsInfo {
  currentPoints: number;
  pointsToEarn: number;
  pointsToSpend: number;
  availableRewards: string[];
  nextTierProgress?: {
    current: string;
    next: string;
    pointsNeeded: number;
  };
}

export interface PaymentOption {
  id: string;
  name: string;
  type: 'card' | 'wallet' | 'crypto';
  processingFee?: number;
  available: boolean;
}

// Bulk operations
export interface BulkCartOperation {
  type: BulkOperationType;
  items: string[]; // Cart item IDs
  target?: 'wishlist' | 'saved_later' | 'remove';
  filters?: BulkOperationFilter;
}

export type BulkOperationType = 
  | 'move_to_wishlist' 
  | 'save_for_later' 
  | 'remove_items' 
  | 'update_quantities'
  | 'apply_discount'
  | 'clear_category'
  | 'clear_brand';

export interface BulkOperationFilter {
  category?: string;
  brand?: string;
  outOfStock?: boolean;
}

// Cart validation results
export interface CartValidationResult {
  isValid: boolean;
  errors: CartValidationError[];
  warnings: CartValidationWarning[];
  suggestions: CartSuggestion[];
}

export interface CartValidationError {
  itemId: string;
  type: 'out_of_stock' | 'insufficient_stock' | 'price_changed' | 'discontinued';
  message: string;
  suggestedAction: string;
}

export interface CartValidationWarning {
  itemId: string;
  type: 'low_stock' | 'price_increase' | 'shipping_delay';
  message: string;
}

export interface CartSuggestion {
  type: 'bundle_discount' | 'free_shipping' | 'loyalty_upgrade' | 'alternative_product';
  message: string;
  action?: () => void;
}

// Enhanced cart context type
export interface AdvancedCartContextType {
  // Basic cart functionality
  items: AdvancedCartItem[];
  totalItems: number;
  totalPrice: number;
  isOpen: boolean;
  
  // Advanced features
  savedForLater: SavedForLaterItem[];
  recommendations: CartRecommendation[];
  summary: AdvancedCartSummary;
  analytics: CartAnalytics;
  validation: CartValidationResult;
  
  // Actions
  addItem: (product: Product, quantity?: number, selectedVariants?: SelectedVariants, source?: CartItemSource) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  moveToSavedLater: (itemId: string, reason?: SaveReason) => void;
  moveFromSavedLater: (savedItemId: string) => void;
  removeSavedItem: (savedItemId: string) => void;
  clearCart: () => void;
  toggleCart: () => void;
  
  // Bulk operations
  performBulkOperation: (operation: BulkCartOperation) => void;
  
  // Recommendations
  addRecommendation: (recommendationId: string) => void;
  dismissRecommendation: (recommendationId: string) => void;
  
  // Persistence
  saveCart: () => void;
  loadCart: () => void;
  
  // Analytics
  trackEvent: (event: Omit<CartEvent, 'id' | 'timestamp'>) => void;
  
  // Validation
  validateCart: () => Promise<CartValidationResult>;
}

// Persian messages for advanced cart
export const PERSIAN_ADVANCED_CART_MESSAGES = {
  actions: {
    saveForLater: 'ذخیره برای بعد',
    moveToCart: 'انتقال به سبد خرید',
    removeFromSaved: 'حذف از ذخیره شده‌ها',
    clearCategory: 'حذف همه محصولات این دسته',
    clearBrand: 'حذف همه محصولات این برند',
    moveAllToWishlist: 'انتقال همه به علاقه‌مندی‌ها',
    applyBulkDiscount: 'اعمال تخفیف گروهی',
    validateCart: 'بررسی سبد خرید'
  },
  recommendations: {
    title: 'پیشنهادات ویژه',
    frequentlyBoughtTogether: 'معمولاً با هم خریداری می‌شوند',
    similarProducts: 'محصولات مشابه',
    categoryPopular: 'محبوب در این دسته',
    brandCollection: 'از همین برند',
    addToCart: 'افزودن به سبد',
    viewProduct: 'مشاهده محصول'
  },
  summary: {
    subtotal: 'جمع کل',
    discount: 'تخفیف',
    shipping: 'هزینه ارسال',
    freeShipping: 'ارسال رایگان',
    tax: 'مالیات',
    total: 'مبلغ نهایی',
    savings: 'صرفه‌جویی شما',
    loyaltyPoints: 'امتیاز دریافتی',
    estimatedDelivery: 'تاریخ تحویل تقریبی'
  },
  validation: {
    outOfStock: 'این محصول موجود نیست',
    insufficientStock: 'موجودی کافی نیست',
    priceChanged: 'قیمت محصول تغییر کرده است',
    discontinued: 'این محصول دیگر تولید نمی‌شود',
    lowStock: 'موجودی محدود',
    shippingDelay: 'تأخیر در ارسال'
  },
  persistence: {
    cartSaved: 'سبد خرید ذخیره شد',
    cartLoaded: 'سبد خرید بازیابی شد',
    syncFailed: 'خطا در همگام‌سازی سبد خرید'
  }
};
