import { Category } from '../types';
import { MegaMenuCategory, NavigationItem } from '../types/navigation';
import { API_BASE_URL } from '../config/api';

// Helper function to get full image URL
const getFullImageUrl = (imagePath?: string): string => {
  if (!imagePath) return '/images/categories/placeholder.jpg';
  if (imagePath.startsWith('http')) return imagePath;
  return `${API_BASE_URL}${imagePath}`;
};

// Transform backend category to frontend Category
const transformBackendCategory = (backendCategory: any): Category => {
  return {
    id: parseInt(backendCategory.id.replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000000),
    name: backendCategory.name,
    description: backendCategory.description || '',
    imageSrc: getFullImageUrl(backendCategory.image),
    slug: backendCategory.slug
  };
};

// Cache for categories
let categoriesCache: Category[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

// Load categories from backend API
const loadCategoriesFromAPI = async (): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/categories?limit=100`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data?.categories) {
      return data.data.categories.map(transformBackendCategory);
    }

    throw new Error('Invalid API response format');
  } catch (error) {
    console.warn('Failed to load categories from API, using fallback data:', error);
    return getFallbackCategories();
  }
};

// Get categories with caching
export const getCategories = async (): Promise<Category[]> => {
  const now = Date.now();

  // Return cached data if still valid
  if (categoriesCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return categoriesCache;
  }

  // Load fresh data
  categoriesCache = await loadCategoriesFromAPI();
  cacheTimestamp = now;

  return categoriesCache;
};

// Fallback categories for when API is unavailable
const getFallbackCategories = (): Category[] => [
  {
    id: 1,
    name: 'سرم',
    description: 'سرم‌های مراقبت از پوست با فرمول‌های تخصصی',
    imageSrc: getFullImageUrl('/uploads/categories/serums-banner.jpg'),
    slug: 'serums'
  },
  {
    id: 2,
    name: 'کرم',
    description: 'کرم‌های مرطوب کننده و ضد پیری',
    imageSrc: getFullImageUrl('/uploads/categories/creams-banner.jpg'),
    slug: 'creams'
  },
  {
    id: 3,
    name: 'پاک کننده',
    description: 'محصولات پاک کننده آرایش و شوینده صورت',
    imageSrc: getFullImageUrl('/uploads/categories/cleansers-banner.jpg'),
    slug: 'cleansers'
  },
  {
    id: 4,
    name: 'ماسک',
    description: 'ماسک‌های صورت برای تغذیه و درخشندگی پوست',
    imageSrc: getFullImageUrl('/uploads/categories/masks-banner.jpg'),
    slug: 'masks'
  },
  {
    id: 5,
    name: 'تونر',
    description: 'تونر‌های متعادل کننده pH پوست',
    imageSrc: getFullImageUrl('/uploads/categories/toners-banner.jpg'),
    slug: 'toners'
  },
  {
    id: 6,
    name: 'ضد آفتاب',
    description: 'محصولات محافظت از پوست در برابر آفتاب',
    imageSrc: getFullImageUrl('/uploads/categories/sunscreens-banner.jpg'),
    slug: 'sunscreens'
  }
];

// Legacy export for backward compatibility
export const categories = getFallbackCategories();

// RojaShop-inspired mega menu categories with Persian skincare focus
export const megaMenuCategories: MegaMenuCategory[] = [
  {
    id: 1,
    name: 'مراقبت از صورت',
    slug: 'face-care',
    description: 'محصولات تخصصی مراقبت از پوست صورت',
    subcategories: [
      { id: 11, name: 'سرم‌های ضد پیری', slug: 'anti-aging-serums' },
      { id: 12, name: 'سرم‌های آبرسان', slug: 'hydrating-serums' },
      { id: 13, name: 'سرم‌های روشن کننده', slug: 'brightening-serums' },
      { id: 14, name: 'کرم‌های مرطوب کننده', slug: 'moisturizers' },
      { id: 15, name: 'کرم‌های ضد پیری', slug: 'anti-aging-creams' },
      { id: 16, name: 'کرم‌های دور چشم', slug: 'eye-creams' },
      { id: 17, name: 'پاک کننده‌های ملایم', slug: 'gentle-cleansers' },
      { id: 18, name: 'تونر و اسنس', slug: 'toners-essences' }
    ],
    featured: {
      title: 'مجموعه ضد پیری پریمیوم',
      description: 'ترکیب قدرتمند سرم و کرم برای پوستی جوان',
      imageSrc: 'https://images.pexels.com/photos/4465124/pexels-photo-4465124.jpeg?auto=compress&cs=tinysrgb&w=400',
      link: '/products?category=anti-aging'
    }
  },
  {
    id: 2,
    name: 'ضد آفتاب',
    slug: 'sunscreen',
    description: 'محافظت کامل از پوست در برابر اشعه UV',
    subcategories: [
      { id: 21, name: 'ضد آفتاب صورت', slug: 'face-sunscreen' },
      { id: 22, name: 'ضد آفتاب بدن', slug: 'body-sunscreen' },
      { id: 23, name: 'ضد آفتاب کودکان', slug: 'kids-sunscreen' },
      { id: 24, name: 'ضد آفتاب ورزشی', slug: 'sport-sunscreen' },
      { id: 25, name: 'ضد آفتاب رنگی', slug: 'tinted-sunscreen' },
      { id: 26, name: 'اسپری ضد آفتاب', slug: 'sunscreen-spray' }
    ],
    featured: {
      title: 'ضد آفتاب SPF 50+',
      description: 'محافظت طولانی مدت با فرمول ضد آب',
      imageSrc: 'https://images.pexels.com/photos/3786694/pexels-photo-3786694.jpeg?auto=compress&cs=tinysrgb&w=400',
      link: '/products?category=sunscreen'
    }
  },
  {
    id: 3,
    name: 'آرایشی',
    slug: 'makeup',
    description: 'محصولات آرایشی با کیفیت بالا',
    subcategories: [
      { id: 31, name: 'کرم پودر', slug: 'foundation' },
      { id: 32, name: 'کانسیلر', slug: 'concealer' },
      { id: 33, name: 'پودر', slug: 'powder' },
      { id: 34, name: 'رژ لب', slug: 'lipstick' },
      { id: 35, name: 'رژ گونه', slug: 'blush' },
      { id: 36, name: 'سایه چشم', slug: 'eyeshadow' },
      { id: 37, name: 'مداد چشم', slug: 'eyeliner' },
      { id: 38, name: 'ریمل', slug: 'mascara' }
    ],
    featured: {
      title: 'پالت آرایش کامل',
      description: 'همه چیز برای آرایش حرفه‌ای',
      imageSrc: 'https://images.pexels.com/photos/2533266/pexels-photo-2533266.jpeg?auto=compress&cs=tinysrgb&w=400',
      link: '/products?category=makeup'
    }
  },
  {
    id: 4,
    name: 'مراقبت از مو',
    slug: 'hair-care',
    description: 'محصولات تخصصی مراقبت از مو',
    subcategories: [
      { id: 41, name: 'شامپو', slug: 'shampoo' },
      { id: 42, name: 'نرم کننده', slug: 'conditioner' },
      { id: 43, name: 'ماسک مو', slug: 'hair-mask' },
      { id: 44, name: 'سرم مو', slug: 'hair-serum' },
      { id: 45, name: 'روغن مو', slug: 'hair-oil' },
      { id: 46, name: 'اسپری مو', slug: 'hair-spray' },
      { id: 47, name: 'ژل و واکس', slug: 'gel-wax' }
    ],
    featured: {
      title: 'مجموعه ترمیم مو',
      description: 'برای موهای آسیب دیده و خشک',
      imageSrc: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg?auto=compress&cs=tinysrgb&w=400',
      link: '/products?category=hair-care'
    }
  },
  {
    id: 5,
    name: 'بهداشتی',
    slug: 'hygiene',
    description: 'محصولات بهداشت شخصی',
    subcategories: [
      { id: 51, name: 'صابون', slug: 'soap' },
      { id: 52, name: 'ژل دوش', slug: 'shower-gel' },
      { id: 53, name: 'شامپو بدن', slug: 'body-wash' },
      { id: 54, name: 'دئودورانت', slug: 'deodorant' },
      { id: 55, name: 'خمیر دندان', slug: 'toothpaste' },
      { id: 56, name: 'مسواک', slug: 'toothbrush' },
      { id: 57, name: 'دهان شویه', slug: 'mouthwash' }
    ],
    featured: {
      title: 'مجموعه بهداشت کامل',
      description: 'تمام نیازهای بهداشتی روزانه',
      imageSrc: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=400',
      link: '/products?category=hygiene'
    }
  }
];

// Navigation items for the header menu
export const navigationItems: NavigationItem[] = [
  {
    id: 1,
    name: 'خانه',
    path: '/',
    type: 'link'
  },
  {
    id: 2,
    name: 'محصولات',
    type: 'megamenu',
    megaMenuCategories: megaMenuCategories
  },
  {
    id: 3,
    name: 'فروشگاه',
    path: '/products',
    type: 'link'
  },
  {
    id: 4,
    name: 'باشگاه مشتریان',
    path: '/loyalty',
    type: 'link'
  },
  {
    id: 5,
    name: 'درباره ما',
    path: '/about',
    type: 'link'
  },
  {
    id: 6,
    name: 'تماس با ما',
    path: '/contact',
    type: 'link'
  }
];

// Async utility functions that use the API
export const getCategoryBySlug = async (slug: string): Promise<Category | undefined> => {
  const categories = await getCategories();
  return categories.find(category => category.slug === slug);
};

export const getCategoryById = async (id: number): Promise<Category | undefined> => {
  const categories = await getCategories();
  return categories.find(category => category.id === id);
};

// Synchronous fallback functions for immediate use (uses cache if available)
export const getCategoryBySlugSync = (slug: string): Category | undefined => {
  if (!categoriesCache) return getFallbackCategories().find(category => category.slug === slug);
  return categoriesCache.find(category => category.slug === slug);
};

export const getCategoryByIdSync = (id: number): Category | undefined => {
  if (!categoriesCache) return getFallbackCategories().find(category => category.id === id);
  return categoriesCache.find(category => category.id === id);
};

export const getMegaCategoryBySlug = (slug: string): MegaMenuCategory | undefined => {
  return megaMenuCategories.find(category => category.slug === slug);
};

// Initialize categories cache
export const initializeCategories = async (): Promise<void> => {
  try {
    await getCategories();
    console.log('✅ Categories cache initialized successfully');
  } catch (error) {
    console.warn('⚠️ Failed to initialize categories cache:', error);
  }
};