import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  PerformanceMetrics, 
  measurePageLoad, 
  measureComponentRender,
  getMemoryUsage,
  monitorFrameRate,
  formatBytes
} from '../utils/performanceUtils';

export interface PerformanceMonitorOptions {
  enableFrameRateMonitoring?: boolean;
  enableMemoryMonitoring?: boolean;
  enableRenderTimeMonitoring?: boolean;
  monitoringInterval?: number;
  componentName?: string;
}

export interface PerformanceMonitorReturn {
  metrics: PerformanceMetrics;
  frameRate: number;
  memoryUsage: { used: number; total: number; percentage: number } | null;
  renderTime: number;
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
  resetMetrics: () => void;
  measureRender: (renderFn: () => void) => number;
}

const DEFAULT_OPTIONS: Required<PerformanceMonitorOptions> = {
  enableFrameRateMonitoring: true,
  enableMemoryMonitoring: true,
  enableRenderTimeMonitoring: true,
  monitoringInterval: 1000,
  componentName: 'Component'
};

/**
 * Hook for monitoring component and application performance
 */
export const usePerformanceMonitor = (
  options: PerformanceMonitorOptions = {}
): PerformanceMonitorReturn => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0,
    memoryUsage: 0,
    bundleSize: 0,
    imageLoadTime: 0,
    apiResponseTime: 0
  });
  
  const [frameRate, setFrameRate] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState<{ used: number; total: number; percentage: number } | null>(null);
  const [renderTime, setRenderTime] = useState(0);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const frameRateMonitorRef = useRef<(() => void) | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const renderStartTimeRef = useRef<number>(0);

  // Initialize metrics on mount
  useEffect(() => {
    const initialMetrics = measurePageLoad();
    setMetrics(initialMetrics);
  }, []);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;
    
    setIsMonitoring(true);

    // Start frame rate monitoring
    if (opts.enableFrameRateMonitoring) {
      frameRateMonitorRef.current = monitorFrameRate((fps) => {
        setFrameRate(fps);
      });
    }

    // Start periodic monitoring
    intervalRef.current = setInterval(() => {
      // Update memory usage
      if (opts.enableMemoryMonitoring) {
        const memory = getMemoryUsage();
        setMemoryUsage(memory);
        
        if (memory) {
          setMetrics(prev => ({
            ...prev,
            memoryUsage: memory.used
          }));
        }
      }

      // Update other metrics
      const currentMetrics = measurePageLoad();
      setMetrics(prev => ({
        ...prev,
        ...currentMetrics
      }));
    }, opts.monitoringInterval);
  }, [isMonitoring, opts]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;
    
    setIsMonitoring(false);

    // Stop frame rate monitoring
    if (frameRateMonitorRef.current) {
      frameRateMonitorRef.current();
      frameRateMonitorRef.current = null;
    }

    // Stop periodic monitoring
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [isMonitoring]);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    setMetrics({
      loadTime: 0,
      renderTime: 0,
      interactionTime: 0,
      memoryUsage: 0,
      bundleSize: 0,
      imageLoadTime: 0,
      apiResponseTime: 0
    });
    setFrameRate(60);
    setMemoryUsage(null);
    setRenderTime(0);
  }, []);

  // Measure render time
  const measureRender = useCallback((renderFn: () => void): number => {
    if (!opts.enableRenderTimeMonitoring) {
      renderFn();
      return 0;
    }

    const duration = measureComponentRender(opts.componentName, renderFn);
    setRenderTime(duration);
    
    setMetrics(prev => ({
      ...prev,
      renderTime: duration
    }));

    return duration;
  }, [opts.enableRenderTimeMonitoring, opts.componentName]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    metrics,
    frameRate,
    memoryUsage,
    renderTime,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    measureRender
  };
};

/**
 * Hook for monitoring API call performance
 */
export const useApiPerformanceMonitor = () => {
  const [apiMetrics, setApiMetrics] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  const measureApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    apiName: string
  ): Promise<T> => {
    setIsLoading(prev => ({ ...prev, [apiName]: true }));
    
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setApiMetrics(prev => ({ ...prev, [apiName]: duration }));
      
      return result;
    } finally {
      setIsLoading(prev => ({ ...prev, [apiName]: false }));
    }
  }, []);

  return {
    apiMetrics,
    isLoading,
    measureApiCall
  };
};

/**
 * Hook for monitoring component render performance
 */
export const useRenderPerformanceMonitor = (componentName: string) => {
  const [renderMetrics, setRenderMetrics] = useState({
    averageRenderTime: 0,
    maxRenderTime: 0,
    minRenderTime: Infinity,
    renderCount: 0,
    totalRenderTime: 0
  });

  const renderStartTime = useRef<number>(0);

  // Mark render start
  const markRenderStart = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  // Mark render end and calculate metrics
  const markRenderEnd = useCallback(() => {
    if (renderStartTime.current === 0) return;
    
    const renderTime = performance.now() - renderStartTime.current;
    
    setRenderMetrics(prev => {
      const newRenderCount = prev.renderCount + 1;
      const newTotalRenderTime = prev.totalRenderTime + renderTime;
      
      return {
        averageRenderTime: newTotalRenderTime / newRenderCount,
        maxRenderTime: Math.max(prev.maxRenderTime, renderTime),
        minRenderTime: Math.min(prev.minRenderTime, renderTime),
        renderCount: newRenderCount,
        totalRenderTime: newTotalRenderTime
      };
    });

    // Mark performance entry
    performance.mark(`${componentName}-render-end`);
    performance.measure(`${componentName}-render`, `${componentName}-render-start`, `${componentName}-render-end`);
    
    renderStartTime.current = 0;
  }, [componentName]);

  // Auto-mark render start on every render
  useEffect(() => {
    performance.mark(`${componentName}-render-start`);
    markRenderStart();
  }, [componentName, markRenderStart]);

  // Auto-mark render end after render
  useEffect(() => {
    markRenderEnd();
  }, [markRenderEnd]);

  return {
    renderMetrics,
    markRenderStart,
    markRenderEnd
  };
};

/**
 * Hook for monitoring bundle size and loading performance
 */
export const useBundlePerformanceMonitor = () => {
  const [bundleMetrics, setBundleMetrics] = useState({
    totalSize: 0,
    jsSize: 0,
    cssSize: 0,
    imageSize: 0,
    loadTime: 0
  });

  useEffect(() => {
    const calculateBundleMetrics = () => {
      const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      let jsSize = 0;
      let cssSize = 0;
      let imageSize = 0;
      let totalLoadTime = 0;

      resourceEntries.forEach(entry => {
        const size = entry.transferSize || 0;
        
        if (entry.name.includes('.js')) {
          jsSize += size;
        } else if (entry.name.includes('.css')) {
          cssSize += size;
        } else if (entry.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
          imageSize += size;
        }
        
        totalLoadTime += entry.duration;
      });

      setBundleMetrics({
        totalSize: jsSize + cssSize + imageSize,
        jsSize,
        cssSize,
        imageSize,
        loadTime: totalLoadTime
      });
    };

    // Calculate metrics after page load
    if (document.readyState === 'complete') {
      calculateBundleMetrics();
    } else {
      window.addEventListener('load', calculateBundleMetrics);
      return () => window.removeEventListener('load', calculateBundleMetrics);
    }
  }, []);

  return {
    bundleMetrics,
    formattedSizes: {
      total: formatBytes(bundleMetrics.totalSize),
      js: formatBytes(bundleMetrics.jsSize),
      css: formatBytes(bundleMetrics.cssSize),
      images: formatBytes(bundleMetrics.imageSize)
    }
  };
};

export default usePerformanceMonitor;
