import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, X, Image, AlertCircle, Check } from 'lucide-react';
import toast from 'react-hot-toast';

interface AdminFileUploadProps {
  value?: string;
  onChange?: (file: File | null, url?: string) => void;
  accept?: string;
  maxSize?: number; // in MB
  placeholder?: string;
  error?: boolean;
  disabled?: boolean;
  className?: string;
  preview?: boolean;
  aspectRatio?: 'square' | 'landscape' | 'portrait' | 'auto';
  label?: string;
  description?: string;
}

const AdminFileUpload: React.FC<AdminFileUploadProps> = ({
  value,
  onChange,
  accept = 'image/*',
  maxSize = 5,
  placeholder = 'فایل را انتخاب کنید یا اینجا بکشید',
  error = false,
  disabled = false,
  className = '',
  preview = true,
  aspectRatio = 'square',
  label,
  description
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(value || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`حجم فایل نباید بیشتر از ${maxSize} مگابایت باشد`);
      return false;
    }

    // Check file type
    if (accept === 'image/*' && !file.type.startsWith('image/')) {
      toast.error('فقط فایل‌های تصویری مجاز هستند');
      return false;
    }

    // Check specific image formats
    if (accept === 'image/*') {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('فرمت فایل پشتیبانی نمی‌شود. فرمت‌های مجاز: JPG, PNG, WebP, GIF');
        return false;
      }
    }

    return true;
  };

  const handleFileSelect = async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);

    try {
      // Create preview URL for display only
      const previewUrl = URL.createObjectURL(file);
      setPreviewUrl(previewUrl);

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Pass the file and preview URL to parent
      // The parent will handle the actual upload and get the final URL
      onChange?.(file, previewUrl);
      toast.success('فایل انتخاب شد');
    } catch (error) {
      toast.error('خطا در انتخاب فایل');
      console.error('File selection error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemoveFile = () => {
    setPreviewUrl(null);
    onChange?.(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square': return 'aspect-square';
      case 'landscape': return 'aspect-video';
      case 'portrait': return 'aspect-[3/4]';
      default: return '';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}

      <div
        className={`
          relative border-2 border-dashed rounded-lg transition-all duration-200 cursor-pointer
          ${isDragOver ? 'border-admin-500 bg-admin-50' : error ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-admin-400'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${preview && previewUrl ? 'p-2' : 'p-6'}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInputChange}
          disabled={disabled}
          className="hidden"
        />

        <AnimatePresence>
          {preview && previewUrl ? (
            <motion.div
              key="preview"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="relative"
            >
              <div className={`relative ${getAspectRatioClass()} ${aspectRatio === 'auto' ? 'max-h-48' : ''} overflow-hidden rounded-lg bg-gray-100`}>
                <img
                  src={previewUrl}
                  alt="پیش‌نمایش"
                  className="w-full h-full object-cover"
                />
                
                {/* Remove button */}
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFile();
                  }}
                  className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  disabled={disabled}
                >
                  <X className="w-4 h-4" />
                </button>

                {/* Upload overlay */}
                {isUploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                      <p className="text-sm">در حال آپلود...</p>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="upload"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center"
            >
              <div className="flex flex-col items-center">
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-500 mb-4"></div>
                    <p className="text-sm text-gray-600">در حال آپلود...</p>
                  </>
                ) : (
                  <>
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-4 ${
                      error ? 'bg-red-100' : 'bg-gray-100'
                    }`}>
                      {error ? (
                        <AlertCircle className="w-6 h-6 text-red-500" />
                      ) : (
                        <Upload className="w-6 h-6 text-gray-500" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{placeholder}</p>
                    <p className="text-xs text-gray-500">
                      حداکثر {maxSize} مگابایت
                    </p>
                  </>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
    </div>
  );
};

export default AdminFileUpload;
