import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Package, AlertTriangle, TrendingUp, TrendingDown, Download, RefreshCw } from 'lucide-react';
import { AdminListLayout } from '../../../components/admin/layout/AdminLayout';
import { AdminStatsCard } from '../../../components/admin/common/AdminCard';
import SEOHead from '../../../components/seo/SEOHead';
import { formatPrice } from '../../../utils/formatters';
import { InventoryService } from '../../../services/apiService';
import toast from 'react-hot-toast';

// Types
interface InventoryItem {
  id: string;
  productId: string;
  name: string;
  sku: string;
  category: string;
  brand: string;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  lowStockThreshold: number;
  price: number;
  comparePrice?: number;
  totalValue: number;
  lastRestocked: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock';
  allowBackorder: boolean;
  trackQuantity: boolean;
  image?: string;
  productSlug: string;
}

interface InventoryStats {
  totalProducts: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  totalQuantity: number;
  stockHealth: {
    healthy: number;
    warning: number;
    critical: number;
  };
}

const ProductInventoryPage: React.FC = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Load inventory data from API
  const loadInventoryData = async () => {
    try {
      setLoading(true);

      const filters = {
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        page: currentPage,
        limit: 20,
        sortBy: 'name',
        sortOrder: 'asc' as const
      };

      const [inventoryResponse, statsResponse] = await Promise.all([
        InventoryService.getInventory(filters),
        InventoryService.getInventoryStats()
      ]);

      if (inventoryResponse.success && inventoryResponse.data) {
        setInventory(inventoryResponse.data.inventory || []);
        if (inventoryResponse.pagination) {
          setTotalPages(inventoryResponse.pagination.totalPages);
        }
      }

      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
      }

    } catch (error) {
      console.error('Error loading inventory:', error);
      toast.error('خطا در بارگذاری اطلاعات موجودی');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventoryData();
  }, [searchTerm, statusFilter, currentPage]);

  // Calculate display statistics
  const displayStats = stats ? {
    totalProducts: stats.totalProducts,
    inStock: stats.inStock,
    lowStock: stats.lowStock,
    outOfStock: stats.outOfStock,
    totalValue: inventory.reduce((sum, item) => sum + item.totalValue, 0),
    totalStock: stats.totalQuantity
  } : {
    totalProducts: 0,
    inStock: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0,
    totalStock: 0
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'in_stock':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">موجود</span>;
      case 'low_stock':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">موجودی کم</span>;
      case 'out_of_stock':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">ناموجود</span>;
      default:
        return null;
    }
  };

  const handleExportInventory = () => {
    toast.success('گزارش موجودی در حال آماده‌سازی است');
  };

  const handleRefresh = () => {
    loadInventoryData();
    toast.success('اطلاعات موجودی به‌روزرسانی شد');
  };

  const filters = (
    <>
      <select
        value={statusFilter}
        onChange={(e) => setStatusFilter(e.target.value)}
        className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
      >
        <option value="all">همه وضعیت‌ها</option>
        <option value="in_stock">موجود</option>
        <option value="low_stock">موجودی کم</option>
        <option value="out_of_stock">ناموجود</option>
      </select>
      <button
        onClick={handleRefresh}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50"
      >
        <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        به‌روزرسانی
      </button>
      <button
        onClick={handleExportInventory}
        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
      >
        <Download className="w-4 h-4" />
        خروجی Excel
      </button>
    </>
  );

  return (
    <>
      <SEOHead
        title="مدیریت موجودی انبار | پنل مدیریت گلو رویا"
        description="مدیریت موجودی و انبار محصولات"
      />

      <AdminListLayout
        title="مدیریت موجودی انبار"
        subtitle="نظارت و مدیریت موجودی محصولات"
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="جستجو در موجودی..."
        filters={filters}
      >
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <AdminStatsCard
              title="کل محصولات"
              value={displayStats.totalProducts.toLocaleString('fa-IR')}
              icon={Package}
              color="bg-blue-500"
              loading={loading}
            />
            <AdminStatsCard
              title="موجود"
              value={displayStats.inStock.toLocaleString('fa-IR')}
              icon={TrendingUp}
              color="bg-green-500"
              loading={loading}
            />
            <AdminStatsCard
              title="موجودی کم"
              value={displayStats.lowStock.toLocaleString('fa-IR')}
              icon={AlertTriangle}
              color="bg-yellow-500"
              loading={loading}
            />
            <AdminStatsCard
              title="ناموجود"
              value={displayStats.outOfStock.toLocaleString('fa-IR')}
              icon={TrendingDown}
              color="bg-red-500"
              loading={loading}
            />
            <AdminStatsCard
              title="کل موجودی"
              value={displayStats.totalStock.toLocaleString('fa-IR')}
              icon={Package}
              color="bg-purple-500"
              loading={loading}
            />
            <AdminStatsCard
              title="ارزش کل"
              value={formatPrice(displayStats.totalValue)}
              icon={TrendingUp}
              color="bg-indigo-500"
              loading={loading}
            />
          </div>

          {/* Inventory Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      محصول
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SKU
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      موجودی فعلی
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رزرو شده
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      قابل فروش
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      وضعیت
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ارزش کل
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      آخرین تامین
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    // Loading skeleton
                    Array.from({ length: 5 }).map((_, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse">
                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-20"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-16"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-16"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-16"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-6 bg-gray-200 rounded-full w-20"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-24"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse h-4 bg-gray-200 rounded w-20"></div>
                        </td>
                      </tr>
                    ))
                  ) : inventory.map((item) => (
                    <motion.tr
                      key={item.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.name}</div>
                          <div className="text-sm text-gray-500">{item.category}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.sku}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.currentStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.reservedStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.availableStock.toLocaleString('fa-IR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(item.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPrice(item.totalValue)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.lastRestocked).toLocaleDateString('fa-IR')}
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {!loading && inventory.length === 0 && (
              <div className="text-center py-12">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  موجودی یافت نشد
                </h3>
                <p className="text-gray-500">
                  {searchTerm ? 'محصول مورد نظر یافت نشد' : 'هنوز محصولی در انبار ثبت نشده است'}
                </p>
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  صفحه {currentPage.toLocaleString('fa-IR')} از {totalPages.toLocaleString('fa-IR')}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    قبلی
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    بعدی
                  </button>
                </div>
              </div>
            )}
          </div>
        </AdminListLayout>
    </>
  );
};

export default ProductInventoryPage;
