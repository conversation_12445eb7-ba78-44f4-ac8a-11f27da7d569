import React, { useEffect, useState } from 'react';
import { AnalyticsConfig, PrivacySettings, PERSIAN_ANALYTICS_MESSAGES } from '../../types/analytics';
import { hasAnalyticsConsent, setAnalyticsConsent, getPrivacySettings } from '../../utils/analyticsUtils';

interface GoogleAnalyticsProps {
  measurementId: string;
  config?: Partial<AnalyticsConfig>;
  onInitialized?: () => void;
  onError?: (error: Error) => void;
}

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
  }
}

const GoogleAnalytics: React.FC<GoogleAnalyticsProps> = ({
  measurementId,
  config = {},
  onInitialized,
  onError
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [showConsentBanner, setShowConsentBanner] = useState(false);
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>(getPrivacySettings());

  // Initialize Google Analytics
  const initializeGA = async () => {
    try {
      // Check if already initialized
      if (window.gtag && isInitialized) return;

      // Create dataLayer if it doesn't exist
      window.dataLayer = window.dataLayer || [];
      
      // Define gtag function
      window.gtag = function gtag(...args: any[]) {
        window.dataLayer!.push(args);
      };

      // Set initial timestamp
      window.gtag('js', new Date());

      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
      
      script.onload = () => {
        // Configure Google Analytics
        window.gtag!('config', measurementId, {
          // Privacy settings
          anonymize_ip: privacySettings.ip_anonymization,
          allow_google_signals: privacySettings.marketing_consent,
          allow_ad_personalization_signals: privacySettings.marketing_consent,
          
          // Persian/RTL specific settings
          language: 'fa',
          country: 'IR',
          currency: 'IRR',
          
          // Custom dimensions
          custom_map: {
            custom_dimension_1: 'device_category',
            custom_dimension_2: 'traffic_source',
            custom_dimension_3: 'user_type',
            custom_dimension_4: 'language_preference'
          },
          
          // Enhanced ecommerce
          send_page_view: false, // We'll handle this manually
          
          // Debug mode
          debug_mode: config.enableDebugMode || false,
          
          // Additional config
          ...config
        });

        setIsInitialized(true);
        onInitialized?.();
      };

      script.onerror = (error) => {
        const analyticsError = new Error('Failed to load Google Analytics script');
        onError?.(analyticsError);
      };

      document.head.appendChild(script);

    } catch (error) {
      onError?.(error as Error);
    }
  };

  // Check consent and show banner if needed
  useEffect(() => {
    const hasConsent = hasAnalyticsConsent();
    
    if (!hasConsent && config.enableGDPRCompliance !== false) {
      setShowConsentBanner(true);
    } else if (hasConsent) {
      initializeGA();
    }
  }, []);

  // Handle consent acceptance
  const handleAcceptConsent = () => {
    setAnalyticsConsent(true);
    setShowConsentBanner(false);
    initializeGA();
  };

  // Handle consent decline
  const handleDeclineConsent = () => {
    setAnalyticsConsent(false);
    setShowConsentBanner(false);
    
    // Disable Google Analytics
    if (window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied'
      });
    }
  };

  // Handle customize consent
  const handleCustomizeConsent = () => {
    // This would open a detailed consent modal
    // For now, we'll just accept
    handleAcceptConsent();
  };

  // Consent banner component
  const ConsentBanner = () => (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {PERSIAN_ANALYTICS_MESSAGES.consent.title}
            </h3>
            <p className="text-gray-600 text-sm">
              {PERSIAN_ANALYTICS_MESSAGES.consent.description}
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <button
              onClick={handleDeclineConsent}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {PERSIAN_ANALYTICS_MESSAGES.consent.decline}
            </button>
            
            <button
              onClick={handleCustomizeConsent}
              className="px-4 py-2 text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors"
            >
              {PERSIAN_ANALYTICS_MESSAGES.consent.customize}
            </button>
            
            <button
              onClick={handleAcceptConsent}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {PERSIAN_ANALYTICS_MESSAGES.consent.accept}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Enhanced ecommerce tracking functions
  useEffect(() => {
    if (!isInitialized || !window.gtag) return;

    // Set up enhanced ecommerce
    window.gtag('config', measurementId, {
      enhanced_ecommerce: true,
      ecommerce: {
        currency: 'IRR',
        country: 'IR'
      }
    });

    // Set up custom events for Persian e-commerce
    const persianEcommerceEvents = {
      // Persian search tracking
      track_persian_search: (searchTerm: string, resultsCount: number) => {
        window.gtag!('event', 'search', {
          search_term: searchTerm,
          search_results: resultsCount,
          language: 'persian',
          custom_dimension_4: 'persian'
        });
      },

      // Persian product interaction
      track_persian_product_interaction: (productId: string, action: string) => {
        window.gtag!('event', 'product_interaction', {
          product_id: productId,
          interaction_type: action,
          language: 'persian',
          custom_dimension_4: 'persian'
        });
      },

      // RTL interface interaction
      track_rtl_interaction: (elementType: string, interactionType: string) => {
        window.gtag!('event', 'rtl_interaction', {
          element_type: elementType,
          interaction_type: interactionType,
          interface_direction: 'rtl'
        });
      }
    };

    // Make functions globally available
    (window as any).persianAnalytics = persianEcommerceEvents;

  }, [isInitialized, measurementId]);

  // Error boundary for analytics
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (isInitialized && window.gtag) {
        window.gtag('event', 'exception', {
          description: event.error?.message || 'Unknown error',
          fatal: false,
          page_location: window.location.href,
          page_title: document.title
        });
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [isInitialized]);

  // Performance tracking
  useEffect(() => {
    if (!isInitialized) return;

    const trackPerformance = () => {
      if ('performance' in window && window.gtag) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          window.gtag('event', 'timing_complete', {
            name: 'page_load',
            value: Math.round(navigation.loadEventEnd - navigation.navigationStart)
          });

          window.gtag('event', 'timing_complete', {
            name: 'dom_content_loaded',
            value: Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart)
          });
        }
      }
    };

    // Track performance after page load
    if (document.readyState === 'complete') {
      trackPerformance();
    } else {
      window.addEventListener('load', trackPerformance);
    }

    return () => window.removeEventListener('load', trackPerformance);
  }, [isInitialized]);

  return (
    <>
      {/* Consent banner */}
      {showConsentBanner && <ConsentBanner />}
      
      {/* Debug info in development */}
      {config.enableDebugMode && isInitialized && (
        <div className="fixed top-4 right-4 bg-black text-white p-2 rounded text-xs z-50">
          GA4 Initialized: {measurementId}
        </div>
      )}
    </>
  );
};

export default GoogleAnalytics;
