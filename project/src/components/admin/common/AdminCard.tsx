import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface AdminCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  className?: string;
  headerActions?: ReactNode;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

const AdminCard: React.FC<AdminCardProps> = ({
  children,
  title,
  subtitle,
  icon: Icon,
  className = '',
  headerActions,
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
  loading = false,
  onClick
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8'
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };

  const baseClasses = `
    bg-white rounded-lg transition-all duration-200
    ${paddingClasses[padding]}
    ${shadowClasses[shadow]}
    ${border ? 'border border-gray-200' : ''}
    ${hover ? 'hover:shadow-md hover:border-gray-300 cursor-pointer' : ''}
    ${onClick ? 'cursor-pointer' : ''}
    ${loading ? 'opacity-50 pointer-events-none' : ''}
    ${className}
  `.trim();

  const cardContent = (
    <>
      {/* Header */}
      {(title || subtitle || Icon || headerActions) && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="p-2 bg-admin-100 rounded-lg">
                <Icon className="w-5 h-5 text-admin-600" />
              </div>
            )}
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className="relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-admin-600"></div>
          </div>
        )}
        {children}
      </div>
    </>
  );

  if (onClick) {
    return (
      <motion.div
        className={baseClasses}
        onClick={onClick}
        whileHover={hover ? { y: -2 } : undefined}
        whileTap={{ scale: 0.98 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        {cardContent}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={baseClasses}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {cardContent}
    </motion.div>
  );
};

// Specialized card variants
export const AdminStatsCard: React.FC<{
  title: string;
  value: string | number;
  unit?: string;
  change?: string;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon: LucideIcon;
  color?: string;
  loading?: boolean;
}> = ({
  title,
  value,
  unit,
  change,
  changeType = 'neutral',
  icon: Icon,
  color = 'bg-admin-500',
  loading = false
}) => {
  const changeColors = {
    increase: 'text-green-600',
    decrease: 'text-red-600',
    neutral: 'text-gray-600'
  };

  return (
    <AdminCard loading={loading} hover>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-2">
            {title}
          </p>
          <div className="flex items-baseline gap-2">
            <p className="text-2xl font-bold text-gray-900">
              {value}
            </p>
            {unit && (
              <p className="text-sm text-gray-500">
                {unit}
              </p>
            )}
          </div>
          {change && (
            <div className="flex items-center mt-2">
              <span className={`text-sm font-medium ${changeColors[changeType]}`}>
                {change}
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </AdminCard>
  );
};

export const AdminActionCard: React.FC<{
  title: string;
  description?: string;
  icon: LucideIcon;
  onClick: () => void;
  disabled?: boolean;
  badge?: string;
}> = ({
  title,
  description,
  icon: Icon,
  onClick,
  disabled = false,
  badge
}) => {
  return (
    <AdminCard
      onClick={disabled ? undefined : onClick}
      hover={!disabled}
      className={`text-center relative ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {badge && (
        <div className="absolute top-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
          {badge}
        </div>
      )}
      <div className="flex flex-col items-center gap-3">
        <div className="p-4 bg-admin-100 rounded-lg">
          <Icon className="w-8 h-8 text-admin-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 mb-1">
            {title}
          </h3>
          {description && (
            <p className="text-sm text-gray-600">
              {description}
            </p>
          )}
        </div>
      </div>
    </AdminCard>
  );
};

export default AdminCard;
