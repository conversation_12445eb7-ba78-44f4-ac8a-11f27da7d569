import {
  AdminUser,
  AdminLoginCredentials,
  AdminAuthResponse,
  AdminRole,
  AdminResource,
  AdminAction,
  AdminPermission,
  ADMIN_ROLE_PERMISSIONS,
  ADMIN_STORAGE_KEYS,
  ADMIN_SESSION_CONFIG,
  PERSIAN_ADMIN_MESSAGES
} from '../types/admin';
import { TokenUtils } from './authUtils';

/**
 * Admin storage utilities
 */
export class AdminStorage {
  static setToken(token: string, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);

    // Store session expiry in the same storage as the token
    const expiryTime = Date.now() + (rememberMe ?
      ADMIN_SESSION_CONFIG.REMEMBER_ME_EXPIRY :
      ADMIN_SESSION_CONFIG.DEFAULT_EXPIRY
    );
    storage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, expiryTime.toString());
  }

  static getToken(): string | null {
    return sessionStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN) || 
           localStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN);
  }

  static setRefreshToken(refreshToken: string): void {
    localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN);
  }

  static setUser(user: AdminUser): void {
    // Store user in the same storage as the token
    const storage = sessionStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN) ? sessionStorage : localStorage;
    storage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
  }

  static getUser(): AdminUser | null {
    const userStr = sessionStorage.getItem(ADMIN_STORAGE_KEYS.USER) ||
                   localStorage.getItem(ADMIN_STORAGE_KEYS.USER);

    if (!userStr) return null;

    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  static setRememberMe(remember: boolean): void {
    localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, remember.toString());
  }

  static getRememberMe(): boolean {
    return localStorage.getItem(ADMIN_STORAGE_KEYS.REMEMBER_ME) === 'true';
  }

  static getSessionExpiry(): number | null {
    const expiry = sessionStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY) || 
                  localStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY);
    return expiry ? parseInt(expiry) : null;
  }

  static clearAll(): void {
    Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
      sessionStorage.removeItem(key);
      localStorage.removeItem(key);
    });
  }

  static isSessionExpired(): boolean {
    const expiry = this.getSessionExpiry();
    if (!expiry) return true;
    return Date.now() > expiry;
  }

  static getTimeUntilExpiry(): number {
    const expiry = this.getSessionExpiry();
    if (!expiry) return 0;
    return Math.max(0, expiry - Date.now());
  }
}

/**
 * Permission checking utilities
 */
export class AdminPermissionChecker {
  static hasPermission(
    user: AdminUser | null,
    resource: AdminResource,
    action: AdminAction
  ): boolean {
    if (!user || !user.isActive) return false;

    // Super admin has all permissions
    if (user.role === 'super_admin') return true;

    // Check user's specific permissions first (if available)
    if (user.permissions && Array.isArray(user.permissions)) {
      const resourcePermission = user.permissions.find(p => p.resource === resource);
      if (resourcePermission && resourcePermission.actions.includes(action)) {
        return true;
      }
    }

    // Fallback to role-based permissions
    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    if (rolePermissions) {
      const resourcePermission = rolePermissions.find(p => p.resource === resource);
      if (resourcePermission && resourcePermission.actions.includes(action)) {
        return true;
      }
    }

    return false;
  }

  static hasRole(user: AdminUser | null, role: AdminRole): boolean {
    return user?.role === role;
  }

  static hasAnyRole(user: AdminUser | null, roles: AdminRole[]): boolean {
    return user ? roles.includes(user.role) : false;
  }

  static canAccessResource(user: AdminUser | null, resource: AdminResource): boolean {
    if (!user || !user.isActive) return false;
    
    // Super admin can access everything
    if (user.role === 'super_admin') return true;
    
    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    return rolePermissions.some(p => p.resource === resource);
  }

  static getAccessibleResources(user: AdminUser | null): AdminResource[] {
    if (!user || !user.isActive) return [];
    
    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    return rolePermissions.map(p => p.resource);
  }

  static getResourceActions(user: AdminUser | null, resource: AdminResource): AdminAction[] {
    if (!user || !user.isActive) return [];
    
    const rolePermissions = ADMIN_ROLE_PERMISSIONS[user.role];
    const resourcePermission = rolePermissions.find(p => p.resource === resource);
    
    return resourcePermission?.actions || [];
  }
}



/**
 * Admin session management utilities
 */
export class AdminSessionManager {
  private static warningShown = false;
  private static autoLogoutTimer: NodeJS.Timeout | null = null;

  static startSessionMonitoring(
    onWarning: () => void,
    onExpiry: () => void
  ): void {
    this.clearSessionMonitoring();

    const checkSession = () => {
      const timeRemaining = AdminStorage.getTimeUntilExpiry();
      
      if (timeRemaining <= 0) {
        onExpiry();
        return;
      }

      if (timeRemaining <= ADMIN_SESSION_CONFIG.WARNING_THRESHOLD && !this.warningShown) {
        this.warningShown = true;
        onWarning();
      }

      if (timeRemaining <= ADMIN_SESSION_CONFIG.AUTO_LOGOUT_THRESHOLD) {
        onExpiry();
        return;
      }
    };

    // Check immediately and then every minute
    checkSession();
    this.autoLogoutTimer = setInterval(checkSession, 60000);
  }

  static clearSessionMonitoring(): void {
    if (this.autoLogoutTimer) {
      clearInterval(this.autoLogoutTimer);
      this.autoLogoutTimer = null;
    }
    this.warningShown = false;
  }

  static extendSession(): void {
    const rememberMe = AdminStorage.getRememberMe();
    const newExpiry = Date.now() + (rememberMe ? 
      ADMIN_SESSION_CONFIG.REMEMBER_ME_EXPIRY : 
      ADMIN_SESSION_CONFIG.DEFAULT_EXPIRY
    );
    
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, newExpiry.toString());
    
    this.warningShown = false;
  }

  static formatTimeRemaining(milliseconds: number): string {
    const minutes = Math.floor(milliseconds / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours} ساعت و ${minutes % 60} دقیقه`;
    }
    
    return `${minutes} دقیقه`;
  }
}

/**
 * Admin error handling utilities
 */
export class AdminErrorHandler {
  static getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    if (error?.status === 401) {
      return PERSIAN_ADMIN_MESSAGES.auth.accessDenied;
    }

    if (error?.status === 403) {
      return PERSIAN_ADMIN_MESSAGES.errors.forbidden;
    }

    if (error?.status === 404) {
      return PERSIAN_ADMIN_MESSAGES.errors.notFound;
    }

    return PERSIAN_ADMIN_MESSAGES.errors.serverError;
  }

  static isAuthError(error: any): boolean {
    return error?.status === 401 || 
           error?.response?.status === 401 ||
           error?.message?.includes('Unauthorized') ||
           error?.message?.includes(PERSIAN_ADMIN_MESSAGES.auth.accessDenied);
  }

  static isSessionExpiredError(error: any): boolean {
    return error?.message?.includes('session') ||
           error?.message?.includes('expired') ||
           error?.message?.includes(PERSIAN_ADMIN_MESSAGES.auth.sessionExpired);
  }
}
