import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronLeft, Sparkles } from 'lucide-react';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { adminNavigationItems, getFilteredNavigation, AdminNavigationItem } from '../../../data/adminNavigation';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose?: () => void;
  isMobile?: boolean;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  isOpen,
  onClose,
  isMobile = false
}) => {
  const location = useLocation();
  const { user } = useAdminAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Filter navigation items based on user permissions
  const filteredNavigation = user ? getFilteredNavigation(
    user.role,
    user.permissions
  ) : [];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const isParentActive = (item: AdminNavigationItem) => {
    if (isActive(item.path)) return true;
    return item.children?.some(child => isActive(child.path)) || false;
  };

  const sidebarContent = (
    <div className="flex flex-col h-full bg-white border-l border-gray-200">
      {/* Logo */}
      <div className="flex items-center justify-center p-6 border-b border-gray-200">
        <Link to="/admin/dashboard" className="flex items-center">
          <Sparkles className="h-8 w-8 text-primary-500 ml-2" />
          <div className="text-xl font-bold">
            <span className="text-primary-600">Glow</span>
            <span className="text-accent-500">Roya</span>
          </div>
        </Link>
      </div>

      {/* User Info */}
      {user && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-admin-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.role === 'super_admin' ? 'مدیر کل' :
                 user.role === 'admin' ? 'مدیر' :
                 user.role === 'moderator' ? 'ناظر' : 'بیننده'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-4">
        <div className="space-y-1">
          {filteredNavigation.map((item) => {
            const Icon = item.icon;
            const hasChildren = item.children && item.children.length > 0;
            const isExpanded = expandedItems.includes(item.id);
            const isItemActive = isParentActive(item);

            return (
              <div key={item.id}>
                {/* Main Item */}
                <div
                  className={`
                    flex items-center justify-between p-3 rounded-lg transition-all duration-200
                    ${isItemActive 
                      ? 'bg-admin-100 text-admin-700 border-l-4 border-admin-500' 
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                    ${hasChildren ? 'cursor-pointer' : ''}
                  `}
                  onClick={hasChildren ? () => toggleExpanded(item.id) : undefined}
                >
                  <Link
                    to={item.path}
                    className="flex items-center gap-3 flex-1"
                    onClick={isMobile ? onClose : undefined}
                  >
                    <Icon className="w-5 h-5 flex-shrink-0" />
                    <span className="font-medium text-sm">{item.title}</span>
                    {item.badge && (
                      <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                  {hasChildren && (
                    <motion.div
                      animate={{ rotate: isExpanded ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.div>
                  )}
                </div>

                {/* Children */}
                <AnimatePresence>
                  {hasChildren && isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden"
                    >
                      <div className="mr-8 mt-1 space-y-1">
                        {item.children?.map((child) => (
                          <Link
                            key={child.id}
                            to={child.path}
                            onClick={isMobile ? onClose : undefined}
                            className={`
                              flex items-center gap-3 p-2 rounded-md text-sm transition-colors
                              ${isActive(child.path)
                                ? 'bg-admin-50 text-admin-700 font-medium'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                              }
                            `}
                          >
                            <ChevronLeft className="w-3 h-3 flex-shrink-0" />
                            <span>{child.title}</span>
                            {child.badge && (
                              <span className="bg-primary-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                                {child.badge}
                              </span>
                            )}
                          </Link>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-center">
          <p className="text-xs text-gray-500">
            نسخه ۱.۰.۰
          </p>
          <p className="text-xs text-gray-400 mt-1">
            © ۱۴۰۳ گلو رویا
          </p>
        </div>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <>
        {/* Mobile Overlay */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={onClose}
            />
          )}
        </AnimatePresence>

        {/* Mobile Sidebar */}
        <motion.div
          initial={{ x: '100%' }}
          animate={{ x: isOpen ? 0 : '100%' }}
          transition={{ type: 'tween', duration: 0.3 }}
          className="fixed top-0 right-0 h-full w-80 max-w-[90vw] z-50"
        >
          {sidebarContent}
        </motion.div>
      </>
    );
  }

  // Desktop Sidebar
  return (
    <motion.aside
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="w-80 h-full flex-shrink-0"
    >
      {sidebarContent}
    </motion.aside>
  );
};

export default AdminSidebar;
