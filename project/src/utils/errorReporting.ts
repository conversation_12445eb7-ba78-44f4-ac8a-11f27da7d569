/**
 * Error Reporting and Monitoring Utilities
 * Comprehensive error tracking and reporting system
 */

// Types
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string | null;
  sessionId?: string | null;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  errorInfo?: any;
  errorId?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  tags?: string[];
  extra?: Record<string, any>;
}

export interface PerformanceMetrics {
  loadTime?: number;
  renderTime?: number;
  apiResponseTime?: number;
  memoryUsage?: number;
  connectionType?: string;
}

export interface ErrorReport {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context: ErrorContext;
  metrics?: PerformanceMetrics;
  fingerprint: string;
  count: number;
  firstSeen: string;
  lastSeen: string;
}

// Error Reporter Class
class ErrorReporter {
  private reports: Map<string, ErrorReport> = new Map();
  private isEnabled: boolean = true;
  private maxReports: number = 100;
  private reportEndpoint: string = '/api/errors';
  private batchSize: number = 10;
  private flushInterval: number = 30000; // 30 seconds
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.setupGlobalErrorHandlers();
    this.startPeriodicFlush();
  }

  // Setup global error handlers
  private setupGlobalErrorHandlers(): void {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      this.reportError(event.error || new Error(event.message), {
        component: 'Global Error Handler',
        url: event.filename,
        extra: {
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
        component: 'Promise Rejection Handler',
        severity: 'high',
        extra: {
          reason: event.reason
        }
      });
    });

    // Handle resource loading errors (only for critical resources)
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        const src = (event.target as any)?.src || '';
        // Only report critical resource failures
        if (src.includes('.js') || src.includes('.css') || src.includes('manifest')) {
          this.reportWarning(`Critical resource failed to load: ${src}`, {
            component: 'Resource Loader',
            severity: 'high',
            extra: {
              resourceType: (event.target as any)?.tagName,
              src: src
            }
          });
        }
      }
    }, true);
  }

  // Generate error fingerprint for deduplication
  private generateFingerprint(error: Error, context: ErrorContext): string {
    const message = error.message || 'Unknown error';
    const component = context.component || 'Unknown';
    const stack = error.stack?.split('\n')[0] || '';
    
    return btoa(`${message}:${component}:${stack}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);
  }

  // Get current performance metrics
  private getPerformanceMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const connection = (navigator as any).connection;
    
    return {
      loadTime: navigation?.loadEventEnd - navigation?.loadEventStart,
      renderTime: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
      memoryUsage: (performance as any).memory?.usedJSHeapSize,
      connectionType: connection?.effectiveType || 'unknown'
    };
  }

  // Report error
  reportError(error: Error, context: ErrorContext = {}): string {
    if (!this.isEnabled) return '';

    const errorId = context.errorId || `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fingerprint = this.generateFingerprint(error, context);
    const timestamp = new Date().toISOString();

    const enrichedContext: ErrorContext = {
      ...context,
      url: context.url || window.location.href,
      userAgent: context.userAgent || navigator.userAgent,
      timestamp: context.timestamp || timestamp,
      errorId,
      severity: context.severity || 'medium',
      userId: context.userId || this.getCurrentUserId(),
      sessionId: context.sessionId || this.getSessionId()
    };

    // Check if we already have this error
    const existingReport = this.reports.get(fingerprint);
    
    if (existingReport) {
      // Update existing report
      existingReport.count++;
      existingReport.lastSeen = timestamp;
      existingReport.context = { ...existingReport.context, ...enrichedContext };
    } else {
      // Create new report
      const newReport: ErrorReport = {
        id: errorId,
        type: 'error',
        message: error.message || 'Unknown error',
        stack: error.stack,
        context: enrichedContext,
        metrics: this.getPerformanceMetrics(),
        fingerprint,
        count: 1,
        firstSeen: timestamp,
        lastSeen: timestamp
      };

      this.reports.set(fingerprint, newReport);
    }

    // Limit the number of stored reports
    if (this.reports.size > this.maxReports) {
      const oldestKey = this.reports.keys().next().value;
      this.reports.delete(oldestKey);
    }

    // Log to console in development (only for critical errors)
    if (process.env.NODE_ENV === 'development' && enrichedContext.severity === 'critical') {
      console.group(`🚨 Critical Error: ${errorId}`);
      console.error('Error:', error);
      console.log('Context:', enrichedContext);
      console.log('Fingerprint:', fingerprint);
      console.groupEnd();
    }

    return errorId;
  }

  // Report warning
  reportWarning(message: string, context: ErrorContext = {}): string {
    const warningError = new Error(message);
    return this.reportError(warningError, { ...context, severity: 'low' });
  }

  // Report info
  reportInfo(message: string, context: ErrorContext = {}): string {
    const infoError = new Error(message);
    return this.reportError(infoError, { ...context, severity: 'low' });
  }

  // Get current user ID
  private getCurrentUserId(): string | null {
    try {
      const user = JSON.parse(localStorage.getItem('auth_user') || 'null');
      return user?.id || null;
    } catch {
      return null;
    }
  }

  // Get session ID
  private getSessionId(): string | null {
    try {
      let sessionId = sessionStorage.getItem('session_id');
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        sessionStorage.setItem('session_id', sessionId);
      }
      return sessionId;
    } catch {
      return null;
    }
  }

  // Flush reports to server
  private async flushReports(): Promise<void> {
    if (this.reports.size === 0) return;

    const reportsToSend = Array.from(this.reports.values()).slice(0, this.batchSize);
    
    try {
      // In a real application, this would send to your error reporting service
      // For now, we'll just log to console and store in localStorage
      
      // Disable verbose logging in development
      // if (process.env.NODE_ENV === 'development') {
      //   console.log('📊 Flushing error reports:', reportsToSend);
      // }

      // Store in localStorage for development/demo purposes
      const existingReports = JSON.parse(localStorage.getItem('error_reports') || '[]');
      const updatedReports = [...existingReports, ...reportsToSend].slice(-50); // Keep last 50 reports
      localStorage.setItem('error_reports', JSON.stringify(updatedReports));

      // Remove sent reports
      reportsToSend.forEach(report => {
        this.reports.delete(report.fingerprint);
      });

    } catch (error) {
      console.error('Failed to flush error reports:', error);
    }
  }

  // Start periodic flush
  private startPeriodicFlush(): void {
    this.flushTimer = setInterval(() => {
      this.flushReports();
    }, this.flushInterval);
  }

  // Stop periodic flush
  stopPeriodicFlush(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  // Get all reports
  getReports(): ErrorReport[] {
    return Array.from(this.reports.values());
  }

  // Clear all reports
  clearReports(): void {
    this.reports.clear();
  }

  // Enable/disable error reporting
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Configure reporter
  configure(options: {
    maxReports?: number;
    reportEndpoint?: string;
    batchSize?: number;
    flushInterval?: number;
  }): void {
    if (options.maxReports) this.maxReports = options.maxReports;
    if (options.reportEndpoint) this.reportEndpoint = options.reportEndpoint;
    if (options.batchSize) this.batchSize = options.batchSize;
    if (options.flushInterval) {
      this.flushInterval = options.flushInterval;
      this.stopPeriodicFlush();
      this.startPeriodicFlush();
    }
  }

  // Manual flush
  async flush(): Promise<void> {
    await this.flushReports();
  }
}

// Singleton instance
export const errorReporter = new ErrorReporter();

// Utility functions
export const reportError = (error: Error, context?: ErrorContext): string => {
  return errorReporter.reportError(error, context);
};

export const reportWarning = (message: string, context?: ErrorContext): string => {
  return errorReporter.reportWarning(message, context);
};

export const reportInfo = (message: string, context?: ErrorContext): string => {
  return errorReporter.reportInfo(message, context);
};

// Persian error messages
export const PERSIAN_ERROR_MESSAGES = {
  network: 'خطا در اتصال به شبکه',
  timeout: 'زمان انتظار تمام شد',
  unauthorized: 'دسترسی غیرمجاز',
  forbidden: 'دسترسی ممنوع',
  notFound: 'صفحه یافت نشد',
  serverError: 'خطای سرور',
  unknown: 'خطای نامشخص',
  validation: 'خطا در اعتبارسنجی داده‌ها',
  permission: 'عدم دسترسی کافی'
};

export default errorReporter;
