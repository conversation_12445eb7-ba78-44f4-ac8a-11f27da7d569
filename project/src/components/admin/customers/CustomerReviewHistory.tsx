import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Star,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Image as ImageIcon,
  ExternalLink
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import AdminTable, { AdminTableColumn } from '../common/AdminTable';
import { AdminCustomer } from '../../../types/adminCustomer';
import { formatPersianDate } from '../../../utils/customerUtils';

interface CustomerReview {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  isVerifiedPurchase: boolean;
  hasImages: boolean;
  imageCount: number;
  helpfulVotes: number;
  unhelpfulVotes: number;
  reportCount: number;
  adminNotes?: string;
  moderatedBy?: string;
  moderatedAt?: string;
}

interface CustomerReviewHistoryProps {
  customer: AdminCustomer;
  onViewReview: (reviewId: string) => void;
  onViewProduct: (productId: string) => void;
  onModerateReview: (reviewId: string, action: 'approve' | 'reject' | 'flag') => void;
}

const CustomerReviewHistory: React.FC<CustomerReviewHistoryProps> = ({
  customer,
  onViewReview,
  onViewProduct,
  onModerateReview
}) => {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedRating, setSelectedRating] = useState<string>('all');

  // Mock review data - in real app, this would come from API
  const generateMockReviews = (): CustomerReview[] => {
    const statuses: CustomerReview['status'][] = ['pending', 'approved', 'rejected', 'flagged'];
    const products = [
      'سرم ویتامین C',
      'کرم آبرسان هیالورونیک',
      'ماسک صورت طلایی',
      'تونر پاک‌کننده',
      'کرم ضد آفتاب',
      'اسکراب لایه‌بردار'
    ];

    return Array.from({ length: 12 }, (_, i) => ({
      id: `review-${i + 1}`,
      productId: `product-${i + 1}`,
      productName: products[Math.floor(Math.random() * products.length)],
      productImage: '/placeholder-product.jpg',
      rating: Math.floor(Math.random() * 5) + 1,
      title: `نظر شماره ${i + 1}`,
      content: `این محصول واقعاً عالی است و تأثیر مثبتی روی پوست من داشته. کیفیت بسیار خوبی دارد و قیمت مناسبی هم داره. ${Math.random() > 0.5 ? 'پیشنهاد می‌کنم.' : 'راضی هستم از خریدم.'}`,
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      isVerifiedPurchase: Math.random() > 0.3,
      hasImages: Math.random() > 0.6,
      imageCount: Math.random() > 0.6 ? Math.floor(Math.random() * 3) + 1 : 0,
      helpfulVotes: Math.floor(Math.random() * 20),
      unhelpfulVotes: Math.floor(Math.random() * 5),
      reportCount: Math.floor(Math.random() * 3),
      adminNotes: Math.random() > 0.7 ? 'بررسی شده توسط ادمین' : undefined,
      moderatedBy: Math.random() > 0.5 ? 'ادمین سیستم' : undefined,
      moderatedAt: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : undefined
    }));
  };

  const reviews = generateMockReviews();

  const filteredReviews = reviews.filter(review => {
    if (selectedStatus !== 'all' && review.status !== selectedStatus) return false;
    if (selectedRating !== 'all' && review.rating.toString() !== selectedRating) return false;
    return true;
  });

  const getStatusIcon = (status: CustomerReview['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'flagged':
        return <Flag className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: CustomerReview['status']) => {
    switch (status) {
      case 'pending': return 'در انتظار بررسی';
      case 'approved': return 'تأیید شده';
      case 'rejected': return 'رد شده';
      case 'flagged': return 'علامت‌گذاری شده';
      default: return 'نامشخص';
    }
  };

  const getStatusColor = (status: CustomerReview['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'flagged': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const columns: AdminTableColumn<CustomerReview>[] = [
    {
      key: 'product',
      title: 'محصول',
      render: (_, review) => (
        <div className="flex items-center gap-3">
          <img
            src={review.productImage}
            alt={review.productName}
            className="w-10 h-10 rounded-lg object-cover"
          />
          <div>
            <p className="font-medium text-gray-900 text-sm">{review.productName}</p>
            <button
              onClick={() => onViewProduct(review.productId)}
              className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
            >
              <ExternalLink className="w-3 h-3" />
              مشاهده محصول
            </button>
          </div>
        </div>
      )
    },
    {
      key: 'rating',
      title: 'امتیاز',
      render: (_, review) => (
        <div className="flex items-center gap-2">
          {renderStars(review.rating)}
          <span className="text-sm text-gray-600">({review.rating})</span>
        </div>
      )
    },
    {
      key: 'content',
      title: 'نظر',
      render: (_, review) => (
        <div className="max-w-xs">
          <p className="font-medium text-gray-900 text-sm mb-1">{review.title}</p>
          <p className="text-sm text-gray-600 line-clamp-2">{review.content}</p>
          <div className="flex items-center gap-2 mt-2">
            {review.isVerifiedPurchase && (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                خرید تأیید شده
              </span>
            )}
            {review.hasImages && (
              <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                <ImageIcon className="w-3 h-3" />
                {review.imageCount} تصویر
              </span>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      render: (_, review) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(review.status)}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}>
            {getStatusText(review.status)}
          </span>
        </div>
      )
    },
    {
      key: 'engagement',
      title: 'تعامل',
      render: (_, review) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-xs text-gray-600">
            <ThumbsUp className="w-3 h-3 text-green-500" />
            <span>{review.helpfulVotes}</span>
            <ThumbsDown className="w-3 h-3 text-red-500 mr-2" />
            <span>{review.unhelpfulVotes}</span>
          </div>
          {review.reportCount > 0 && (
            <div className="flex items-center gap-1 text-xs text-orange-600">
              <Flag className="w-3 h-3" />
              <span>{review.reportCount} گزارش</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'date',
      title: 'تاریخ',
      sortable: true,
      render: (_, review) => (
        <div className="text-sm text-gray-600">
          {formatPersianDate(review.date)}
        </div>
      )
    }
  ];

  const renderRowActions = (review: CustomerReview) => (
    <div className="flex items-center gap-1">
      <AdminButton
        variant="ghost"
        size="sm"
        icon={Eye}
        onClick={() => onViewReview(review.id)}
      >
        مشاهده
      </AdminButton>
      {review.status === 'pending' && (
        <>
          <AdminButton
            variant="ghost"
            size="sm"
            icon={CheckCircle}
            onClick={() => onModerateReview(review.id, 'approve')}
            className="text-green-600 hover:text-green-700"
          >
            تأیید
          </AdminButton>
          <AdminButton
            variant="ghost"
            size="sm"
            icon={XCircle}
            onClick={() => onModerateReview(review.id, 'reject')}
            className="text-red-600 hover:text-red-700"
          >
            رد
          </AdminButton>
        </>
      )}
    </div>
  );

  // Calculate statistics
  const totalReviews = filteredReviews.length;
  const averageRating = totalReviews > 0 ? 
    filteredReviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews : 0;
  const approvedReviews = filteredReviews.filter(r => r.status === 'approved').length;
  const pendingReviews = filteredReviews.filter(r => r.status === 'pending').length;

  return (
    <div className="space-y-6">
      {/* Review Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">کل نظرات</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalReviews.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">میانگین امتیاز</p>
              <p className="text-2xl font-bold text-gray-900">
                {averageRating.toFixed(1)}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">تأیید شده</p>
              <p className="text-2xl font-bold text-gray-900">
                {approvedReviews.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">در انتظار بررسی</p>
              <p className="text-2xl font-bold text-gray-900">
                {pendingReviews.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Filters */}
      <AdminCard title="فیلترها">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وضعیت نظر
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">همه وضعیت‌ها</option>
              <option value="pending">در انتظار بررسی</option>
              <option value="approved">تأیید شده</option>
              <option value="rejected">رد شده</option>
              <option value="flagged">علامت‌گذاری شده</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              امتیاز
            </label>
            <select
              value={selectedRating}
              onChange={(e) => setSelectedRating(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">همه امتیازها</option>
              <option value="5">5 ستاره</option>
              <option value="4">4 ستاره</option>
              <option value="3">3 ستاره</option>
              <option value="2">2 ستاره</option>
              <option value="1">1 ستاره</option>
            </select>
          </div>
        </div>
      </AdminCard>

      {/* Reviews Table */}
      <AdminCard title="تاریخچه نظرات">
        <AdminTable
          columns={columns}
          data={filteredReviews}
          rowActions={renderRowActions}
          emptyMessage="هیچ نظری یافت نشد"
          hoverable
          striped
        />
      </AdminCard>
    </div>
  );
};

export default CustomerReviewHistory;
