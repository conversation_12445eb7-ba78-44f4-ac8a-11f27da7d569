#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';
import { MockDataMigrator } from './migrate-mock-data';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

class FullMigrationRunner {
  private logFile: string;

  constructor() {
    this.logFile = path.join(__dirname, '../logs/full-migration.log');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    fs.appendFileSync(this.logFile, logMessage + '\n');
  }

  async runFullMigration(): Promise<void> {
    this.log('🚀 Starting Full Mock Data Migration Process...\n');

    try {
      // Step 1: Check database connection
      await this.checkDatabaseConnection();

      // Step 2: Run database migrations
      await this.runDatabaseMigrations();

      // Step 3: Run mock data migration
      await this.runMockDataMigration();

      // Step 4: Verify migration results
      await this.verifyMigration();

      // Step 5: Create additional sample data
      await this.createAdditionalSampleData();

      // Step 6: Generate final report
      await this.generateFinalReport();

      this.log('\n✅ Full Migration Process Completed Successfully!');

    } catch (error) {
      this.log(`\n❌ Migration failed: ${error}`);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async checkDatabaseConnection(): Promise<void> {
    this.log('🔍 Checking database connection...');
    
    try {
      await prisma.$connect();
      this.log('   ✅ Database connection successful');
    } catch (error) {
      this.log(`   ❌ Database connection failed: ${error}`);
      throw error;
    }
  }

  private async runDatabaseMigrations(): Promise<void> {
    this.log('📊 Running database migrations...');
    
    try {
      // Check if migrations are needed
      const { execSync } = require('child_process');
      const migrationOutput = execSync('npx prisma migrate status', { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8' 
      });
      
      this.log(`   Migration status: ${migrationOutput}`);
      
      // Apply any pending migrations
      execSync('npx prisma migrate deploy', { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8' 
      });
      
      this.log('   ✅ Database migrations completed');
    } catch (error) {
      this.log(`   ⚠️ Migration check/apply: ${error}`);
      // Continue anyway as migrations might already be applied
    }
  }

  private async runMockDataMigration(): Promise<void> {
    this.log('📦 Running mock data migration...');
    
    const migrator = new MockDataMigrator();
    await migrator.migrate();
    
    this.log('   ✅ Mock data migration completed');
  }

  private async verifyMigration(): Promise<void> {
    this.log('🔍 Verifying migration results...');

    const counts = {
      users: await prisma.user.count(),
      categories: await prisma.category.count(),
      brands: await prisma.brand.count(),
      products: await prisma.product.count(),
      productImages: await prisma.productImage.count(),
      inventory: await prisma.productInventory.count(),
      testimonials: await prisma.testimonial.count(),
      notifications: await prisma.notification.count(),
      pageContents: await prisma.pageContent.count()
    };

    this.log('   📊 Current database counts:');
    Object.entries(counts).forEach(([table, count]) => {
      this.log(`      ${table}: ${count}`);
    });

    // Verify critical data exists
    if (counts.categories === 0) {
      throw new Error('No categories found after migration');
    }
    if (counts.brands === 0) {
      throw new Error('No brands found after migration');
    }
    if (counts.products === 0) {
      throw new Error('No products found after migration');
    }

    this.log('   ✅ Migration verification passed');
  }

  private async createAdditionalSampleData(): Promise<void> {
    this.log('🎯 Creating additional sample data...');

    // Create sample orders
    await this.createSampleOrders();

    // Create sample reviews
    await this.createSampleReviews();

    // Create sample loyalty accounts
    await this.createSampleLoyaltyAccounts();

    this.log('   ✅ Additional sample data created');
  }

  private async createSampleOrders(): Promise<void> {
    this.log('   📋 Creating sample orders...');

    const customers = await prisma.user.findMany({
      where: { role: 'CUSTOMER' },
      take: 3
    });

    const products = await prisma.product.findMany({
      take: 5,
      include: { inventory: true }
    });

    if (customers.length === 0 || products.length === 0) {
      this.log('   ⚠️ Insufficient data for sample orders');
      return;
    }

    for (let i = 0; i < 10; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const orderProducts = products.slice(0, Math.floor(Math.random() * 3) + 1);
      
      const totalAmount = orderProducts.reduce((sum, product) => sum + Number(product.price), 0);

      const order = await prisma.order.create({
        data: {
          orderNumber: `ORD-${Date.now()}-${i}`,
          userId: customer.id,
          status: ['PENDING', 'CONFIRMED', 'DELIVERED'][Math.floor(Math.random() * 3)] as any,
          totalAmount,
          subtotal: totalAmount,
          shippingAmount: 50000,
          taxAmount: totalAmount * 0.09
        }
      });

      // Create order items
      for (const product of orderProducts) {
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = Number(product.price);
        await prisma.orderItem.create({
          data: {
            orderId: order.id,
            productId: product.id,
            quantity,
            unitPrice,
            totalPrice: unitPrice * quantity
          }
        });
      }
    }

    this.log('   ✅ Created 10 sample orders');
  }

  private async createSampleReviews(): Promise<void> {
    this.log('   ⭐ Creating sample reviews...');

    const customers = await prisma.user.findMany({
      where: { role: 'CUSTOMER' },
      take: 5
    });

    const products = await prisma.product.findMany({
      take: 3
    });

    if (customers.length === 0 || products.length === 0) {
      this.log('   ⚠️ Insufficient data for sample reviews');
      return;
    }

    const sampleReviews = [
      {
        rating: 5,
        title: 'محصول عالی',
        content: 'واقعاً راضی هستم. کیفیت بسیار خوبی دارد و نتیجه فوق‌العاده‌ای داشت.',
        pros: ['کیفیت عالی', 'نتیجه سریع', 'بسته‌بندی مناسب'],
        cons: ['قیمت کمی بالا']
      },
      {
        rating: 4,
        title: 'خوب اما قابل بهبود',
        content: 'محصول خوبی است اما انتظار بیشتری داشتم. در کل راضی هستم.',
        pros: ['مناسب پوست حساس', 'عطر ملایم'],
        cons: ['نتیجه کند', 'حجم کم']
      },
      {
        rating: 5,
        title: 'پیشنهاد می‌کنم',
        content: 'بهترین محصولی که تا حالا استفاده کردم. حتماً دوباره خریداری می‌کنم.',
        pros: ['نتیجه عالی', 'قیمت مناسب', 'ارسال سریع'],
        cons: []
      }
    ];

    // Create unique customer-product combinations
    const combinations: Array<{customer: any, product: any}> = [];
    for (const customer of customers) {
      for (const product of products) {
        combinations.push({ customer, product });
      }
    }

    // Shuffle and take first 15 combinations
    const shuffled = combinations.sort(() => 0.5 - Math.random());
    const selectedCombinations = shuffled.slice(0, Math.min(15, combinations.length));

    for (const { customer, product } of selectedCombinations) {
      const reviewData = sampleReviews[Math.floor(Math.random() * sampleReviews.length)];

      await prisma.review.upsert({
        where: {
          productId_userId: {
            productId: product.id,
            userId: customer.id
          }
        },
        update: {
          rating: reviewData.rating,
          title: reviewData.title,
          content: reviewData.content,
          pros: reviewData.pros,
          cons: reviewData.cons
        },
        create: {
          productId: product.id,
          userId: customer.id,
          rating: reviewData.rating,
          title: reviewData.title,
          content: reviewData.content,
          pros: reviewData.pros,
          cons: reviewData.cons,
          isVerified: Math.random() > 0.3,
          isApproved: true,
          moderationStatus: 'APPROVED' as any,
          skinType: ['خشک', 'چرب', 'مختلط', 'حساس'][Math.floor(Math.random() * 4)],
          ageRange: ['18-25', '26-35', '36-45', '46+'][Math.floor(Math.random() * 4)],
          usageDuration: ['کمتر از یک ماه', '1-3 ماه', '3-6 ماه', 'بیش از 6 ماه'][Math.floor(Math.random() * 4)]
        }
      });
    }

    this.log(`   ✅ Created ${selectedCombinations.length} sample reviews`);
  }

  private async createSampleLoyaltyAccounts(): Promise<void> {
    this.log('   🎁 Creating sample loyalty accounts...');

    const customers = await prisma.user.findMany({
      where: { role: 'CUSTOMER' }
    });

    for (const customer of customers) {
      const points = Math.floor(Math.random() * 10000) + 1000;
      const tier = points > 5000 ? 'GOLD' : points > 2000 ? 'SILVER' : 'BRONZE';

      await prisma.loyaltyAccount.upsert({
        where: { userId: customer.id },
        update: {
          points,
          tier: tier as any,
          totalEarned: points + Math.floor(Math.random() * 5000),
          totalRedeemed: Math.floor(Math.random() * 2000)
        },
        create: {
          userId: customer.id,
          points,
          tier: tier as any,
          totalEarned: points + Math.floor(Math.random() * 5000),
          totalRedeemed: Math.floor(Math.random() * 2000)
        }
      });
    }

    this.log(`   ✅ Created loyalty accounts for ${customers.length} customers`);
  }

  private async generateFinalReport(): Promise<void> {
    this.log('📊 Generating final migration report...');

    const finalCounts = {
      users: await prisma.user.count(),
      categories: await prisma.category.count(),
      brands: await prisma.brand.count(),
      products: await prisma.product.count(),
      productImages: await prisma.productImage.count(),
      inventory: await prisma.productInventory.count(),
      orders: await prisma.order.count(),
      orderItems: await prisma.orderItem.count(),
      reviews: await prisma.review.count(),
      testimonials: await prisma.testimonial.count(),
      loyaltyAccounts: await prisma.loyaltyAccount.count(),
      notifications: await prisma.notification.count(),
      pageContents: await prisma.pageContent.count()
    };

    const report = {
      migrationDate: new Date().toISOString(),
      status: 'completed',
      finalCounts,
      summary: {
        totalRecords: Object.values(finalCounts).reduce((sum, count) => sum + count, 0),
        coreDataMigrated: true,
        sampleDataCreated: true,
        readyForProduction: true
      }
    };

    const reportPath = path.join(__dirname, '../logs/final-migration-report.json');
    await fs.promises.writeFile(reportPath, JSON.stringify(report, null, 2));

    this.log('\n📈 Final Migration Summary:');
    Object.entries(finalCounts).forEach(([table, count]) => {
      this.log(`   ${table}: ${count}`);
    });
    this.log(`\n📄 Final report saved to: ${reportPath}`);
  }
}

// Run migration if called directly
if (require.main === module) {
  const runner = new FullMigrationRunner();
  runner.runFullMigration().catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

export { FullMigrationRunner };
