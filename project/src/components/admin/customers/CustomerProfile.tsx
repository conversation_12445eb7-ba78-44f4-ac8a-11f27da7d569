import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Crown,
  Shield,
  TrendingUp,
  ShoppingBag,
  Star,
  MessageSquare,
  AlertTriangle,
  Edit,
  Save,
  X,
  Plus
} from 'lucide-react';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';
import { AdminFormModal } from '../common/AdminModal';
import { AdminCustomer, CustomerNote, PERSIAN_CUSTOMER_MESSAGES } from '../../../types/adminCustomer';
import { 
  formatCustomerName, 
  formatCustomerPhone, 
  formatPersianCurrency, 
  formatPersianDate,
  getCustomerStatusColor,
  getCustomerSegmentColor,
  getRiskScoreColor
} from '../../../utils/customerUtils';

interface CustomerProfileProps {
  customer: AdminCustomer;
  onUpdateCustomer: (updates: Partial<AdminCustomer>) => Promise<void>;
  onAddNote: (note: Omit<CustomerNote, 'id' | 'customerId' | 'createdAt' | 'createdBy' | 'createdByName'>) => Promise<void>;
  onToggleVip: () => Promise<void>;
  onToggleBlock: () => Promise<void>;
  onSendMessage: () => void;
  onCreateTicket: () => void;
  loading?: boolean;
}

const CustomerProfile: React.FC<CustomerProfileProps> = ({
  customer,
  onUpdateCustomer,
  onAddNote,
  onToggleVip,
  onToggleBlock,
  onSendMessage,
  onCreateTicket,
  loading = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [editedCustomer, setEditedCustomer] = useState(customer);
  const [newNote, setNewNote] = useState({
    content: '',
    type: 'general' as CustomerNote['type'],
    isPrivate: false
  });

  const handleSave = async () => {
    try {
      await onUpdateCustomer(editedCustomer);
      setIsEditing(false);
    } catch (error) {
      // Error handled by parent
    }
  };

  const handleCancel = () => {
    setEditedCustomer(customer);
    setIsEditing(false);
  };

  const handleAddNote = async () => {
    try {
      await onAddNote(newNote);
      setNewNote({ content: '', type: 'general', isPrivate: false });
      setShowNoteModal(false);
    } catch (error) {
      // Error handled by parent
    }
  };

  const getStatusBadge = () => (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerStatusColor(customer.status)}`}>
      {PERSIAN_CUSTOMER_MESSAGES.status[customer.status]}
    </span>
  );

  const getSegmentBadge = () => (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerSegmentColor(customer.segment)}`}>
      {PERSIAN_CUSTOMER_MESSAGES.segment[customer.segment]}
    </span>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <AdminCard>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold">
              {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
            </div>
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">
                  {formatCustomerName(customer)}
                </h1>
                {customer.isVip && (
                  <Crown className="w-6 h-6 text-yellow-500" title="مشتری ویژه" />
                )}
              </div>
              <div className="flex items-center gap-3 mb-2">
                {getStatusBadge()}
                {getSegmentBadge()}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Mail className="w-4 h-4" />
                  <span>{customer.email}</span>
                </div>
                {customer.phone && (
                  <div className="flex items-center gap-1">
                    <Phone className="w-4 h-4" />
                    <span>{formatCustomerPhone(customer.phone)}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>عضو از {formatPersianDate(customer.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <AdminButton
              variant="outline"
              size="sm"
              icon={MessageSquare}
              onClick={onSendMessage}
            >
              ارسال پیام
            </AdminButton>
            <AdminButton
              variant="outline"
              size="sm"
              icon={Plus}
              onClick={onCreateTicket}
            >
              ایجاد تیکت
            </AdminButton>
            <AdminButton
              variant={customer.isVip ? "outline" : "primary"}
              size="sm"
              icon={Crown}
              onClick={onToggleVip}
              loading={loading}
            >
              {customer.isVip ? 'حذف از ویژه' : 'تبدیل به ویژه'}
            </AdminButton>
            <AdminButton
              variant={customer.status === 'blocked' ? "success" : "danger"}
              size="sm"
              icon={Shield}
              onClick={onToggleBlock}
              loading={loading}
            >
              {customer.status === 'blocked' ? 'رفع مسدودی' : 'مسدود کردن'}
            </AdminButton>
          </div>
        </div>
      </AdminCard>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">تعداد سفارشات</p>
              <p className="text-2xl font-bold text-gray-900">
                {customer.analytics.totalOrders.toLocaleString('fa-IR')}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">مجموع خرید</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPersianCurrency(customer.analytics.totalSpent)}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">ارزش مشتری</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPersianCurrency(customer.analytics.lifetimeValue)}
              </p>
            </div>
          </div>
        </AdminCard>

        <AdminCard>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">امتیاز ریسک</p>
              <p className={`text-2xl font-bold ${getRiskScoreColor(customer.riskScore)}`}>
                {customer.riskScore}
              </p>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Customer Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <AdminCard
          title="اطلاعات پایه"
          headerActions={
            <AdminButton
              variant="ghost"
              size="sm"
              icon={isEditing ? X : Edit}
              onClick={isEditing ? handleCancel : () => setIsEditing(true)}
            >
              {isEditing ? 'انصراف' : 'ویرایش'}
            </AdminButton>
          }
        >
          <div className="space-y-4">
            {isEditing ? (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      نام
                    </label>
                    <input
                      type="text"
                      value={editedCustomer.firstName}
                      onChange={(e) => setEditedCustomer(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      نام خانوادگی
                    </label>
                    <input
                      type="text"
                      value={editedCustomer.lastName}
                      onChange={(e) => setEditedCustomer(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ایمیل
                  </label>
                  <input
                    type="email"
                    value={editedCustomer.email}
                    onChange={(e) => setEditedCustomer(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تلفن
                  </label>
                  <input
                    type="tel"
                    value={editedCustomer.phone || ''}
                    onChange={(e) => setEditedCustomer(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex justify-end">
                  <AdminButton
                    variant="primary"
                    size="sm"
                    icon={Save}
                    onClick={handleSave}
                    loading={loading}
                  >
                    ذخیره تغییرات
                  </AdminButton>
                </div>
              </>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900">{formatCustomerName(customer)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900">{customer.email}</span>
                </div>
                {customer.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900">{formatCustomerPhone(customer.phone)}</span>
                  </div>
                )}
                {customer.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900">
                      {customer.location.city}, {customer.location.province}
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </AdminCard>

        {/* Loyalty Information */}
        <AdminCard title="اطلاعات وفاداری">
          {customer.loyaltyMember ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">سطح عضویت</span>
                <span className="font-medium text-gray-900">
                  {customer.loyaltyMember.tier.persianName}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">امتیاز فعلی</span>
                <span className="font-medium text-gray-900">
                  {customer.loyaltyMember.points.toLocaleString('fa-IR')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">کل امتیاز کسب شده</span>
                <span className="font-medium text-gray-900">
                  {customer.loyaltyMember.totalEarned.toLocaleString('fa-IR')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">تاریخ عضویت</span>
                <span className="font-medium text-gray-900">
                  {formatPersianDate(customer.loyaltyMember.joinDate)}
                </span>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500">عضو باشگاه مشتریان نیست</p>
            </div>
          )}
        </AdminCard>
      </div>

      {/* Notes Section */}
      <AdminCard
        title="یادداشت‌ها"
        headerActions={
          <AdminButton
            variant="primary"
            size="sm"
            icon={Plus}
            onClick={() => setShowNoteModal(true)}
          >
            افزودن یادداشت
          </AdminButton>
        }
      >
        {customer.notes.length > 0 ? (
          <div className="space-y-4">
            {customer.notes.map((note) => (
              <div key={note.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900">
                      {note.createdByName}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatPersianDate(note.createdAt)}
                    </span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    note.type === 'support' ? 'bg-blue-100 text-blue-800' :
                    note.type === 'sales' ? 'bg-green-100 text-green-800' :
                    note.type === 'complaint' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {PERSIAN_CUSTOMER_MESSAGES.ticketCategory[note.type] || note.type}
                  </span>
                </div>
                <p className="text-gray-700">{note.content}</p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <MessageSquare className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">هیچ یادداشتی ثبت نشده است</p>
          </div>
        )}
      </AdminCard>

      {/* Add Note Modal */}
      <AdminFormModal
        isOpen={showNoteModal}
        onClose={() => setShowNoteModal(false)}
        onSubmit={handleAddNote}
        title="افزودن یادداشت"
        submitText="ذخیره یادداشت"
        loading={loading}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع یادداشت
            </label>
            <select
              value={newNote.type}
              onChange={(e) => setNewNote(prev => ({ ...prev, type: e.target.value as CustomerNote['type'] }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="general">عمومی</option>
              <option value="support">پشتیبانی</option>
              <option value="sales">فروش</option>
              <option value="billing">مالی</option>
              <option value="complaint">شکایت</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              متن یادداشت
            </label>
            <textarea
              value={newNote.content}
              onChange={(e) => setNewNote(prev => ({ ...prev, content: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="یادداشت خود را وارد کنید..."
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPrivate"
              checked={newNote.isPrivate}
              onChange={(e) => setNewNote(prev => ({ ...prev, isPrivate: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="isPrivate" className="mr-2 text-sm text-gray-700">
              یادداشت خصوصی (فقط برای مدیران قابل مشاهده)
            </label>
          </div>
        </div>
      </AdminFormModal>
    </div>
  );
};

export default CustomerProfile;
