// Analytics data types for admin dashboard
export interface AnalyticsOverview {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  averageOrderValue: number;
  conversionRate: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
}

export interface SalesAnalytics {
  dailySales: DailySalesData[];
  monthlySales: MonthlySalesData[];
  topProducts: ProductPerformance[];
  topCategories: CategoryPerformance[];
  paymentMethods: PaymentMethodStats[];
  revenueByChannel: ChannelRevenue[];
}

export interface DailySalesData {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
  averageOrderValue: number;
}

export interface MonthlySalesData {
  month: string;
  revenue: number;
  orders: number;
  customers: number;
  averageOrderValue: number;
  growth: number;
}

export interface ProductPerformance {
  id: string;
  name: string;
  category: string;
  revenue: number;
  unitsSold: number;
  averageRating: number;
  conversionRate: number;
  profitMargin: number;
  image?: string;
}

export interface CategoryPerformance {
  id: string;
  name: string;
  revenue: number;
  orders: number;
  products: number;
  averageOrderValue: number;
  conversionRate: number;
}

export interface PaymentMethodStats {
  method: string;
  count: number;
  percentage: number;
  revenue: number;
  averageAmount: number;
}

export interface ChannelRevenue {
  channel: string;
  revenue: number;
  orders: number;
  percentage: number;
  conversionRate: number;
}

export interface CustomerAnalytics {
  newCustomers: CustomerGrowthData[];
  customerSegments: CustomerSegment[];
  customerLifetime: CustomerLifetimeData;
  topCustomers: TopCustomer[];
  customerRetention: RetentionData[];
  geographicDistribution: GeographicData[];
}

export interface CustomerGrowthData {
  date: string;
  newCustomers: number;
  returningCustomers: number;
  totalCustomers: number;
}

export interface CustomerSegment {
  segment: string;
  count: number;
  percentage: number;
  averageOrderValue: number;
  totalRevenue: number;
}

export interface CustomerLifetimeData {
  averageLifetimeValue: number;
  averageOrderFrequency: number;
  averageCustomerLifespan: number;
  churnRate: number;
}

export interface TopCustomer {
  id: string;
  name: string;
  email: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: string;
  loyaltyTier: string;
}

export interface RetentionData {
  period: string;
  retentionRate: number;
  cohortSize: number;
  returningCustomers: number;
}

export interface GeographicData {
  province: string;
  customers: number;
  orders: number;
  revenue: number;
  percentage: number;
}

export interface TrafficAnalytics {
  pageViews: PageViewData[];
  topPages: TopPageData[];
  trafficSources: TrafficSourceData[];
  deviceStats: DeviceStatsData[];
  bounceRate: number;
  averageSessionDuration: number;
  conversionFunnel: ConversionFunnelData[];
}

export interface PageViewData {
  date: string;
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  averageTimeOnPage: number;
}

export interface TopPageData {
  path: string;
  title: string;
  views: number;
  uniqueViews: number;
  bounceRate: number;
  averageTimeOnPage: number;
  conversionRate: number;
}

export interface TrafficSourceData {
  source: string;
  visitors: number;
  percentage: number;
  bounceRate: number;
  conversionRate: number;
  averageSessionDuration: number;
}

export interface DeviceStatsData {
  device: string;
  visitors: number;
  percentage: number;
  bounceRate: number;
  conversionRate: number;
}

export interface ConversionFunnelData {
  step: string;
  visitors: number;
  conversionRate: number;
  dropOffRate: number;
}

export interface AnalyticsFilters {
  dateRange: {
    start: string;
    end: string;
  };
  period: 'day' | 'week' | 'month' | 'quarter' | 'year';
  category?: string[];
  channel?: string[];
  customerSegment?: string[];
}

export interface ReportConfig {
  type: 'sales' | 'customers' | 'products' | 'traffic';
  format: 'pdf' | 'excel' | 'csv';
  dateRange: {
    start: string;
    end: string;
  };
  includeCharts: boolean;
  includeDetails: boolean;
  recipients?: string[];
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    enabled: boolean;
  };
}

// Persian labels for analytics
export const ANALYTICS_LABELS = {
  overview: {
    totalRevenue: 'کل درآمد',
    totalOrders: 'کل سفارشات',
    totalCustomers: 'کل مشتریان',
    averageOrderValue: 'میانگین ارزش سفارش',
    conversionRate: 'نرخ تبدیل',
    revenueGrowth: 'رشد درآمد',
    ordersGrowth: 'رشد سفارشات',
    customersGrowth: 'رشد مشتریان'
  },
  periods: {
    day: 'روزانه',
    week: 'هفتگی',
    month: 'ماهانه',
    quarter: 'فصلی',
    year: 'سالانه'
  },
  channels: {
    direct: 'مستقیم',
    organic: 'ارگانیک',
    social: 'شبکه‌های اجتماعی',
    email: 'ایمیل',
    paid: 'تبلیغات پولی',
    referral: 'ارجاع'
  },
  devices: {
    desktop: 'دسکتاپ',
    mobile: 'موبایل',
    tablet: 'تبلت'
  },
  segments: {
    new: 'جدید',
    returning: 'بازگشتی',
    vip: 'ویژه',
    at_risk: 'در معرض خطر'
  }
};
