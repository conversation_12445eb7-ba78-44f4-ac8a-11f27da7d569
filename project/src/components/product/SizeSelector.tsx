import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, AlertCircle, Package } from 'lucide-react';
import { ProductVariant } from '../../types';

interface SizeSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant;
  onVariantChange: (variant: ProductVariant) => void;
  disabled?: boolean;
  required?: boolean;
  groupName: string;
  variantType: 'size' | 'volume' | 'weight';
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  variants,
  selectedVariant,
  onVariantChange,
  disabled = false,
  required = false,
  groupName,
  variantType
}) => {
  const handleSizeSelect = (variant: ProductVariant) => {
    if (disabled || variant.disabled || variant.stock === 0) return;
    onVariantChange(variant);
  };

  const getVariantIcon = () => {
    switch (variantType) {
      case 'volume':
        return '🧴';
      case 'weight':
        return '⚖️';
      case 'size':
      default:
        return '📏';
    }
  };

  const getUnitLabel = () => {
    switch (variantType) {
      case 'volume':
        return 'میلی‌لیتر';
      case 'weight':
        return 'گرم';
      case 'size':
      default:
        return '';
    }
  };

  return (
    <div className="size-selector">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <span className="text-lg">{getVariantIcon()}</span>
          <h4 className="text-sm font-medium text-text-primary">
            {groupName}
            {required && <span className="text-red-500 mr-1">*</span>}
          </h4>
          {selectedVariant && (
            <span className="text-sm text-text-secondary">
              ({selectedVariant.name})
            </span>
          )}
        </div>
        
        {required && !selectedVariant && (
          <div className="flex items-center gap-1 text-red-500 text-xs">
            <AlertCircle className="w-3 h-3" />
            <span>انتخاب اجباری</span>
          </div>
        )}
      </div>

      {/* Size Options */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        <AnimatePresence>
          {variants.map((variant, index) => {
            const isSelected = selectedVariant?.id === variant.id;
            const isDisabled = disabled || variant.disabled || variant.stock === 0;
            const isOutOfStock = variant.stock === 0;

            return (
              <motion.button
                key={variant.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                onClick={() => handleSizeSelect(variant)}
                disabled={isDisabled}
                className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-center ${
                  isSelected
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : isDisabled
                    ? 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                    : 'border-gray-200 bg-white text-text-secondary hover:border-primary-300 hover:bg-primary-25 cursor-pointer'
                } ${
                  isOutOfStock ? 'opacity-60' : ''
                }`}
              >
                {/* Selected Indicator */}
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-2 right-2"
                  >
                    <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                  </motion.div>
                )}

                {/* Out of Stock Indicator */}
                {isOutOfStock && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 rounded-lg">
                    <span className="text-xs text-red-500 font-medium">ناموجود</span>
                  </div>
                )}

                {/* Size Info */}
                <div className="space-y-1">
                  <div className={`text-sm font-medium ${
                    isSelected ? 'text-primary-700' : isDisabled ? 'text-gray-400' : 'text-text-primary'
                  }`}>
                    {variant.value}
                    {getUnitLabel() && (
                      <span className="text-xs text-text-muted mr-1">
                        {getUnitLabel()}
                      </span>
                    )}
                  </div>
                  
                  <div className={`text-xs ${
                    isSelected ? 'text-primary-600' : isDisabled ? 'text-gray-400' : 'text-text-muted'
                  }`}>
                    {variant.name}
                  </div>

                  {/* Stock Info */}
                  {variant.stock <= 5 && variant.stock > 0 && (
                    <div className="text-xs text-orange-500">
                      {variant.stock} عدد
                    </div>
                  )}

                  {/* Price Difference */}
                  {variant.price && variant.price !== 0 && (
                    <div className={`text-xs font-medium ${
                      isSelected ? 'text-primary-600' : 'text-primary-500'
                    }`}>
                      {variant.price > 0 ? '+' : ''}{variant.price.toLocaleString()}
                    </div>
                  )}
                </div>

                {/* Hover Effect */}
                {!isDisabled && (
                  <motion.div
                    className="absolute inset-0 rounded-lg bg-primary-500 opacity-0 hover:opacity-5 transition-opacity duration-200"
                    whileHover={{ scale: 1.02 }}
                  />
                )}
              </motion.button>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Selected Variant Info */}
      {selectedVariant && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-4 p-3 bg-blue-50 rounded-lg"
        >
          <div className="flex items-center gap-2 text-sm">
            <Package className="w-4 h-4 text-blue-600" />
            <span className="text-blue-700">
              {groupName} انتخاب شده: <strong>{selectedVariant.name}</strong>
            </span>
          </div>
          
          <div className="mt-2 flex items-center justify-between text-sm">
            <span className="text-blue-600">
              حجم/اندازه: {selectedVariant.value} {getUnitLabel()}
            </span>
            <span className="text-blue-600">
              موجودی: {selectedVariant.stock} عدد
            </span>
          </div>
          
          {selectedVariant.price && selectedVariant.price !== 0 && (
            <div className="mt-2 text-sm text-blue-600">
              تغییر قیمت: {selectedVariant.price > 0 ? '+' : ''}{selectedVariant.price.toLocaleString()} تومان
            </div>
          )}
        </motion.div>
      )}

      {/* No Sizes Available */}
      {variants.length === 0 && (
        <div className="text-center py-4 text-text-muted text-sm">
          {groupName} در دسترس نیست
        </div>
      )}
    </div>
  );
};

export default SizeSelector;
