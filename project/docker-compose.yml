# Docker Compose for GlowRoya E-commerce Platform
# Updated to use remote PostgreSQL database on VPS
version: '3.8'

services:
  # Frontend (React/Vite)
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: glowroya_frontend
    restart: unless-stopped
    volumes:
      - ./dist:/app/dist:ro
    networks:
      - glowroya_network

  # Backend API (Node.js/Express)
  backend:
    build:
      context: ./backend
      dockerfile: docker/backend.Dockerfile
    container_name: glowroya_backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************************************
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
      - PORT=3001
      - FRONTEND_URL=http://localhost:5173
      - ADMIN_URL=http://localhost:5173/admin
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "3001:3001"
    depends_on:
      - redis
    networks:
      - glowroya_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: glowroya_redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - glowroya_network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: glowroya_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./backend/uploads:/var/www/uploads:ro
    depends_on:
      - frontend
      - backend
    networks:
      - glowroya_network

volumes:
  redis_data:
  # postgres_data removed as using remote VPS database

networks:
  glowroya_network:
    driver: bridge
