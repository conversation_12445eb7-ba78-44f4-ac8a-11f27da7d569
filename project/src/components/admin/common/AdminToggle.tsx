import React from 'react';
import { motion } from 'framer-motion';

interface AdminToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const AdminToggle: React.FC<AdminToggleProps> = ({
  checked,
  onChange,
  label,
  description,
  disabled = false,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      toggle: 'w-8 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-3'
    },
    md: {
      toggle: 'w-11 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-5'
    },
    lg: {
      toggle: 'w-14 h-7',
      thumb: 'w-6 h-6',
      translate: 'translate-x-7'
    }
  };

  const currentSize = sizeClasses[size];

  const handleToggle = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  return (
    <div className={`flex items-start gap-3 ${className}`}>
      {/* Toggle Switch */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={`
          relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-admin-500 focus:ring-offset-2
          ${checked 
            ? 'bg-admin-600' 
            : 'bg-gray-200'
          }
          ${disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'cursor-pointer hover:bg-opacity-80'
          }
          ${currentSize.toggle}
        `}
        role="switch"
        aria-checked={checked}
        aria-disabled={disabled}
      >
        <span className="sr-only">{label || 'Toggle'}</span>
        <motion.span
          className={`
            inline-block rounded-full bg-white shadow-lg transform transition-transform duration-200 ease-in-out
            ${currentSize.thumb}
          `}
          animate={{
            x: checked ? currentSize.translate.replace('translate-x-', '') : '0'
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 30
          }}
        />
      </button>

      {/* Label and Description */}
      {(label || description) && (
        <div className="flex-1">
          {label && (
            <label 
              className={`
                block text-sm font-medium cursor-pointer
                ${disabled ? 'text-gray-400' : 'text-gray-700'}
              `}
              onClick={handleToggle}
            >
              {label}
            </label>
          )}
          {description && (
            <p className={`
              text-sm mt-1
              ${disabled ? 'text-gray-400' : 'text-gray-500'}
            `}>
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminToggle;
