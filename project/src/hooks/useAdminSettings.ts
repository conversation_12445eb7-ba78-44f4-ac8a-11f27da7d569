import { useState, useEffect, useCallback } from 'react';
import {
  GeneralSettings,
  PaymentSettings,
  ShippingSettings,
  NotificationSettings,
  SecuritySettings,
  TaxSettings,
  BackupSettings,
  SettingsFormData,
  PaymentMethod,
  ShippingMethod
} from '../types/adminSettings';

// Mock data for development
const generateMockGeneralSettings = (): GeneralSettings => ({
  siteName: 'فروشگاه زیبایی آرایشی',
  siteDescription: 'فروشگاه آنلاین محصولات زیبایی و آرایشی',
  siteUrl: 'https://beauty-shop.ir',
  adminEmail: '<EMAIL>',
  supportEmail: '<EMAIL>',
  phone: '021-12345678',
  address: 'تهران، خیابان ولیعصر، پلاک 123',
  timezone: 'Asia/Tehran',
  language: 'fa',
  currency: 'IRR',
  dateFormat: 'YYYY/MM/DD',
  timeFormat: '24h',
  maintenanceMode: false,
  maintenanceMessage: 'سایت در حال تعمیر و نگهداری است',
  allowRegistration: true,
  requireEmailVerification: true,
  defaultUserRole: 'customer',
  maxLoginAttempts: 5,
  sessionTimeout: 3600,
  enableTwoFactor: false
});

const generateMockPaymentSettings = (): PaymentSettings => ({
  enabledMethods: [
    {
      id: 'bank-card',
      name: 'کارت بانکی',
      type: 'bank_card',
      enabled: true,
      testMode: false,
      configuration: {
        merchantId: 'MERCHANT123',
        apiKey: 'API_KEY_123'
      },
      fees: { fixed: 0, percentage: 2.5 },
      limits: { minimum: 10000, maximum: ******** },
      supportedCurrencies: ['IRR'],
      description: 'پرداخت با کارت‌های بانکی'
    },
    {
      id: 'wallet',
      name: 'کیف پول',
      type: 'wallet',
      enabled: false,
      testMode: false,
      configuration: {},
      fees: { fixed: 0, percentage: 0 },
      limits: { minimum: 10000, maximum: ******** },
      supportedCurrencies: ['IRR'],
      description: 'پرداخت از موجودی کیف پول'
    },
    {
      id: 'cash-on-delivery',
      name: 'پرداخت در محل',
      type: 'cash_on_delivery',
      enabled: true,
      testMode: false,
      configuration: {},
      fees: { fixed: 15000, percentage: 0 },
      limits: { minimum: 50000, maximum: 2000000 },
      supportedCurrencies: ['IRR'],
      description: 'پرداخت هنگام تحویل کالا'
    }
  ],
  defaultMethod: 'bank-card',
  currency: 'IRR',
  taxRate: 9,
  shippingTaxIncluded: false,
  pricesIncludeTax: true,
  roundingPrecision: 0,
  minimumOrderAmount: 50000,
  maximumOrderAmount: ********
});

const generateMockShippingSettings = (): ShippingSettings => ({
  enabledMethods: [
    {
      id: 'standard',
      name: 'ارسال عادی',
      type: 'flat_rate',
      enabled: true,
      cost: 25000,
      estimatedDays: { min: 2, max: 5 },
      zones: ['tehran', 'karaj'],
      description: 'ارسال عادی با پست'
    },
    {
      id: 'express',
      name: 'ارسال سریع',
      type: 'flat_rate',
      enabled: true,
      cost: 45000,
      estimatedDays: { min: 1, max: 2 },
      zones: ['tehran'],
      description: 'ارسال سریع در روز'
    },
    {
      id: 'free',
      name: 'ارسال رایگان',
      type: 'free',
      enabled: true,
      cost: 0,
      freeThreshold: 500000,
      estimatedDays: { min: 3, max: 7 },
      zones: ['all'],
      description: 'ارسال رایگان برای خریدهای بالای 500 هزار تومان'
    }
  ],
  defaultMethod: 'standard',
  freeShippingThreshold: 500000,
  enableFreeShipping: true,
  weightUnit: 'kg',
  dimensionUnit: 'cm',
  originAddress: {
    street: 'خیابان ولیعصر، پلاک 123',
    city: 'تهران',
    province: 'تهران',
    postalCode: '1234567890',
    country: 'ایران'
  },
  enableShippingZones: true,
  enableShippingCalculator: true,
  maxPackageWeight: 30,
  maxPackageDimensions: {
    length: 100,
    width: 100,
    height: 100
  }
});

const generateMockNotificationSettings = (): NotificationSettings => ({
  emailNotifications: {
    enabled: true,
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '********',
    smtpEncryption: 'tls',
    fromEmail: '<EMAIL>',
    fromName: 'فروشگاه زیبایی',
    replyToEmail: '<EMAIL>',
    enableHtml: true,
    enableTracking: true
  },
  smsNotifications: {
    enabled: true,
    provider: 'kavenegar',
    apiKey: 'SMS_API_KEY',
    apiSecret: 'SMS_API_SECRET',
    fromNumber: '10008663',
    enableDeliveryReports: true
  },
  pushNotifications: {
    enabled: false,
    firebaseServerKey: '',
    vapidPublicKey: '',
    vapidPrivateKey: '',
    enableBrowserNotifications: false,
    enableMobileNotifications: false
  },
  adminNotifications: {
    newOrderNotification: true,
    lowStockNotification: true,
    newReviewNotification: true,
    newCustomerNotification: false,
    systemErrorNotification: true,
    dailyReportNotification: true,
    weeklyReportNotification: true,
    monthlyReportNotification: true
  },
  templates: []
});

export const useAdminSettings = () => {
  const [generalSettings, setGeneralSettings] = useState<GeneralSettings | null>(null);
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings | null>(null);
  const [shippingSettings, setShippingSettings] = useState<ShippingSettings | null>(null);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings | null>(null);
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings | null>(null);
  const [taxSettings, setTaxSettings] = useState<TaxSettings | null>(null);
  const [backupSettings, setBackupSettings] = useState<BackupSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setGeneralSettings(generateMockGeneralSettings());
      setPaymentSettings(generateMockPaymentSettings());
      setShippingSettings(generateMockShippingSettings());
      setNotificationSettings(generateMockNotificationSettings());
      
      // Mock other settings
      setSecuritySettings({
        enableSsl: true,
        forceHttps: true,
        enableCsrfProtection: true,
        enableRateLimiting: true,
        maxRequestsPerMinute: 100,
        enableIpWhitelist: false,
        whitelistedIps: [],
        enableIpBlacklist: false,
        blacklistedIps: [],
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          preventCommonPasswords: true,
          passwordHistoryCount: 5,
          passwordExpiryDays: 90
        },
        sessionSecurity: {
          sessionTimeout: 3600,
          enableRememberMe: true,
          rememberMeDuration: 2592000,
          enableConcurrentSessions: true,
          maxConcurrentSessions: 3,
          enableSessionRotation: true
        },
        enableAuditLog: true,
        auditLogRetentionDays: 365
      });

      setTaxSettings({
        enableTax: true,
        defaultTaxRate: 9,
        taxIncludedInPrices: true,
        enableTaxByLocation: false,
        taxRules: [],
        taxClasses: [],
        enableTaxExemptions: false,
        exemptUserRoles: []
      });

      setBackupSettings({
        enableAutoBackup: true,
        backupFrequency: 'daily',
        backupTime: '02:00',
        retentionDays: 30,
        backupLocation: 'local',
        enableDatabaseBackup: true,
        enableFileBackup: true,
        enableConfigBackup: true,
        compressionEnabled: true,
        encryptionEnabled: false
      });

    } catch (err) {
      setError('خطا در بارگذاری تنظیمات');
      console.error('Settings fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSettings = useCallback(async (section: string, data: SettingsFormData) => {
    try {
      setSaving(true);
      setError(null);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update local state based on section
      switch (section) {
        case 'general':
          if (data.general && generalSettings) {
            setGeneralSettings({ ...generalSettings, ...data.general });
          }
          break;
        case 'payment':
          if (data.payment && paymentSettings) {
            setPaymentSettings({ ...paymentSettings, ...data.payment });
          }
          break;
        case 'shipping':
          if (data.shipping && shippingSettings) {
            setShippingSettings({ ...shippingSettings, ...data.shipping });
          }
          break;
        case 'notifications':
          if (data.notifications && notificationSettings) {
            setNotificationSettings({ ...notificationSettings, ...data.notifications });
          }
          break;
        case 'security':
          if (data.security && securitySettings) {
            setSecuritySettings({ ...securitySettings, ...data.security });
          }
          break;
        case 'tax':
          if (data.tax && taxSettings) {
            setTaxSettings({ ...taxSettings, ...data.tax });
          }
          break;
        case 'backup':
          if (data.backup && backupSettings) {
            setBackupSettings({ ...backupSettings, ...data.backup });
          }
          break;
      }

      return { success: true };
    } catch (err) {
      console.error('Settings update error:', err);
      return { success: false, error: 'خطا در ذخیره تنظیمات' };
    } finally {
      setSaving(false);
    }
  }, [generalSettings, paymentSettings, shippingSettings, notificationSettings, securitySettings, taxSettings, backupSettings]);

  const testConnection = useCallback(async (type: 'email' | 'sms' | 'payment') => {
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Random success/failure for demo
      const success = Math.random() > 0.3;
      
      return {
        success,
        message: success 
          ? 'اتصال با موفقیت برقرار شد' 
          : 'خطا در برقراری اتصال'
      };
    } catch (err) {
      return {
        success: false,
        message: 'خطا در تست اتصال'
      };
    }
  }, []);

  const resetToDefaults = useCallback(async (section: string) => {
    try {
      setSaving(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset to default values
      switch (section) {
        case 'general':
          setGeneralSettings(generateMockGeneralSettings());
          break;
        case 'payment':
          setPaymentSettings(generateMockPaymentSettings());
          break;
        case 'shipping':
          setShippingSettings(generateMockShippingSettings());
          break;
        case 'notifications':
          setNotificationSettings(generateMockNotificationSettings());
          break;
      }
      
      return { success: true };
    } catch (err) {
      return { success: false, error: 'خطا در بازنشانی تنظیمات' };
    } finally {
      setSaving(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    generalSettings,
    paymentSettings,
    shippingSettings,
    notificationSettings,
    securitySettings,
    taxSettings,
    backupSettings,
    loading,
    saving,
    error,
    updateSettings,
    testConnection,
    resetToDefaults,
    refetch: fetchSettings
  };
};
