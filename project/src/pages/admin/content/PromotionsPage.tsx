import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Tag,
  TrendingUp,
  Users,
  DollarSign,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Copy,
  Percent,
  ExternalLink
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../../../components/admin/common/AdminTable';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { Promotion, ContentFilters } from '../../../types/adminContent';
import { formatContentStatus, getStatusColor, formatPersianDate } from '../../../utils/contentUtils';

const PromotionsPage: React.FC = () => {
  const {
    promotions,
    loading,
    error,
    filters,
    setFilters,
    deletePromotion
  } = useAdminContent();

  const [selectedPromotions, setSelectedPromotions] = useState<string[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [promotionToDelete, setPromotionToDelete] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter promotions based on search and filters
  const filteredPromotions = promotions.filter(promotion => {
    const matchesSearch = !searchTerm ||
      promotion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      promotion.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      promotion.code?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status?.length ||
      filters.status.includes(promotion.status);

    return matchesSearch && matchesStatus;
  });

  const handleDeletePromotion = async (id: string) => {
    try {
      await deletePromotion(id);
      setDeleteModalOpen(false);
      setPromotionToDelete(null);
    } catch (error) {
      console.error('Error deleting promotion:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      for (const id of selectedPromotions) {
        await deletePromotion(id);
      }
      setSelectedPromotions([]);
    } catch (error) {
      console.error('Error bulk deleting promotions:', error);
    }
  };

  const columns: AdminTableColumn<Promotion>[] = [
    {
      key: 'title',
      title: 'عنوان',
      sortable: true,
      render: (promotion: Promotion) => (
        <div>
          <div className="font-medium text-gray-900">{promotion.title}</div>
          {promotion.code && (
            <div className="text-sm text-blue-600 font-mono">{promotion.code}</div>
          )}
        </div>
      )
    },
    {
      key: 'type',
      title: 'نوع تخفیف',
      render: (promotion: Promotion) => {
        const typeLabels = {
          percentage: 'درصدی',
          fixed: 'مبلغ ثابت',
          free_shipping: 'ارسال رایگان'
        };
        return (
          <div className="flex items-center gap-2">
            <Percent className="w-4 h-4 text-gray-400" />
            <span>{typeLabels[promotion.type as keyof typeof typeLabels] || promotion.type}</span>
          </div>
        );
      }
    },
    {
      key: 'discountValue',
      title: 'مقدار تخفیف',
      align: 'center',
      render: (promotion: Promotion) => (
        <div className="text-center">
          <div className="font-medium text-green-600">
            {promotion.type === 'percentage'
              ? `${promotion.discountValue}%`
              : `${promotion.discountValue.toLocaleString('fa-IR')} تومان`
            }
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (promotion: Promotion) => (
        <AdminTableBadge variant={getStatusColor(promotion.status as any) as any}>
          {formatContentStatus(promotion.status as any)}
        </AdminTableBadge>
      )
    },
    {
      key: 'currentUsage',
      title: 'استفاده',
      align: 'center',
      sortable: true,
      render: (promotion: Promotion) => (
        <div className="text-center">
          <div className="font-medium">{promotion.currentUsage.toLocaleString('fa-IR')}</div>
          {promotion.usageLimit && (
            <div className="text-xs text-gray-500">
              از {promotion.usageLimit.toLocaleString('fa-IR')}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'conversions',
      title: 'تبدیل',
      align: 'center',
      sortable: true,
      render: (promotion: Promotion) => (
        <div className="text-center">
          <div className="font-medium">{promotion.conversions.toLocaleString('fa-IR')}</div>
          {promotion.currentUsage > 0 && (
            <div className="text-xs text-gray-500">
              {((promotion.conversions / promotion.currentUsage) * 100).toFixed(1)}%
            </div>
          )}
        </div>
      )
    },
    {
      key: 'endDate',
      title: 'تاریخ انقضا',
      sortable: true,
      render: (promotion: Promotion) => (
        <div className="text-sm text-gray-600">
          {promotion.endDate ? formatPersianDate(promotion.endDate) : 'نامحدود'}
        </div>
      )
    }
  ];

  const getRowActions = (promotion: Promotion, index: number) => (
    <AdminActionMenu
      actions={[
        {
          label: 'مشاهده',
          onClick: () => window.open(`/admin/content/promotions/${promotion.id}`, '_blank')
        },
        {
          label: 'ویرایش',
          onClick: () => {
            window.location.href = `/admin/content/promotions/${promotion.id}/edit`;
          }
        },
        {
          label: 'کپی',
          onClick: () => {
            console.log('Duplicate promotion:', promotion.id);
          }
        },
        {
          label: 'حذف',
          onClick: () => {
            setPromotionToDelete(promotion.id);
            setDeleteModalOpen(true);
          },
          variant: 'danger'
        }
      ]}
    />
  );

  const statsCards = [
    {
      title: 'کل تخفیف‌ها',
      value: promotions.length.toLocaleString('fa-IR'),
      icon: Tag,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'فعال',
      value: promotions.filter(p => p.status === 'published' && p.isActive).length.toLocaleString('fa-IR'),
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'کل استفاده',
      value: promotions.reduce((sum, p) => sum + p.currentUsage, 0).toLocaleString('fa-IR'),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'کل تبدیل',
      value: promotions.reduce((sum, p) => sum + p.conversions, 0).toLocaleString('fa-IR'),
      icon: DollarSign,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  if (loading) {
    return (
      <AdminLayout title="مدیریت تخفیف‌ها">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="مدیریت تخفیف‌ها"
      subtitle="مدیریت کمپین‌های تخفیف و کدهای تخفیف"
      actions={
        <AdminButton
          variant="primary"
          icon={Plus}
          onClick={() => window.location.href = '/admin/content/promotions/create'}
        >
          ایجاد تخفیف جدید
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Filters and Search */}
        <AdminCard>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="جستجو در تخفیف‌ها..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <AdminButton variant="outline" icon={Filter} size="sm">
                فیلتر
              </AdminButton>
              {selectedPromotions.length > 0 && (
                <AdminButton
                  variant="danger"
                  icon={Trash2}
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  حذف انتخاب شده ({selectedPromotions.length})
                </AdminButton>
              )}
            </div>
          </div>
        </AdminCard>

        {/* Promotions Table */}
        <AdminCard>
          <AdminTable
            columns={columns}
            data={filteredPromotions}
            loading={loading}
            rowActions={getRowActions}
            emptyMessage="هیچ تخفیفی یافت نشد"
            onRowClick={(promotion, index) => {
              window.location.href = `/admin/content/promotions/${promotion.id}`;
            }}
          />
        </AdminCard>

        {/* Delete Confirmation Modal */}
        <AdminConfirmModal
          isOpen={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setPromotionToDelete(null);
          }}
          onConfirm={() => promotionToDelete && handleDeletePromotion(promotionToDelete)}
          title="حذف تخفیف"
          message="آیا از حذف این تخفیف اطمینان دارید؟ این عمل قابل بازگشت نیست."
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      </div>
    </AdminLayout>
  );
};

export default PromotionsPage;
