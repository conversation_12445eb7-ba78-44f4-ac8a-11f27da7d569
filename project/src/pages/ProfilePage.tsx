import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Phone, MapPin, Settings, LogOut, Edit } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import SEOHead from '../components/seo/SEOHead';

const ProfilePage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'addresses' | 'settings'>('profile');

  if (!user) {
    return null; // This should be handled by ProtectedRoute
  }

  const handleLogout = async () => {
    await logout();
  };

  const tabs = [
    { id: 'profile', label: 'اطلاعات شخصی', icon: User },
    { id: 'addresses', label: 'آدرس‌ها', icon: MapPin },
    { id: 'settings', label: 'تنظیمات', icon: Settings }
  ] as const;

  return (
    <>
      <SEOHead
        title="پروفایل کاربری | گلو رویا"
        description="مدیریت اطلاعات شخصی، آدرس‌ها و تنظیمات حساب کاربری در گلو رویا"
        keywords="پروفایل, حساب کاربری, تنظیمات, آدرس"
        canonical="/profile"
      />

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-sm p-6 mb-6"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <User className="w-8 h-8 text-primary-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                      {user.firstName} {user.lastName}
                    </h1>
                    <p className="text-gray-600">{user.email}</p>
                    <div className="flex items-center gap-4 mt-2">
                      {user.isEmailVerified ? (
                        <span className="text-green-600 text-sm flex items-center gap-1">
                          <Mail className="w-4 h-4" />
                          ایمیل تأیید شده
                        </span>
                      ) : (
                        <span className="text-orange-600 text-sm flex items-center gap-1">
                          <Mail className="w-4 h-4" />
                          ایمیل تأیید نشده
                        </span>
                      )}
                      {user.phone && (
                        <span className="text-gray-600 text-sm flex items-center gap-1">
                          <Phone className="w-4 h-4" />
                          {user.phone}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <LogOut className="w-4 h-4" />
                  خروج
                </button>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Sidebar */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="lg:col-span-1"
              >
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-colors ${
                            activeTab === tab.id
                              ? 'bg-primary-50 text-primary-700 border-r-4 border-primary-600'
                              : 'text-gray-600 hover:bg-gray-50'
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          {tab.label}
                        </button>
                      );
                    })}
                  </nav>
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="lg:col-span-3"
              >
                <div className="bg-white rounded-lg shadow-sm p-6">
                  {activeTab === 'profile' && (
                    <div>
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-semibold text-gray-900">
                          اطلاعات شخصی
                        </h2>
                        <button className="flex items-center gap-2 px-4 py-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">
                          <Edit className="w-4 h-4" />
                          ویرایش
                        </button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نام
                          </label>
                          <div className="bg-gray-50 px-4 py-3 rounded-lg">
                            {user.firstName}
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نام خانوادگی
                          </label>
                          <div className="bg-gray-50 px-4 py-3 rounded-lg">
                            {user.lastName}
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            ایمیل
                          </label>
                          <div className="bg-gray-50 px-4 py-3 rounded-lg">
                            {user.email}
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            شماره موبایل
                          </label>
                          <div className="bg-gray-50 px-4 py-3 rounded-lg">
                            {user.phone || 'وارد نشده'}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'addresses' && (
                    <div>
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-semibold text-gray-900">
                          آدرس‌های من
                        </h2>
                        <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                          <MapPin className="w-4 h-4" />
                          افزودن آدرس جدید
                        </button>
                      </div>
                      
                      {user.addresses.length === 0 ? (
                        <div className="text-center py-12">
                          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600">هنوز آدرسی ثبت نکرده‌اید</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {user.addresses.map((address) => (
                            <div
                              key={address.id}
                              className="border border-gray-200 rounded-lg p-4"
                            >
                              <div className="flex items-start justify-between">
                                <div>
                                  <h3 className="font-medium text-gray-900">
                                    {address.title}
                                  </h3>
                                  <p className="text-sm text-gray-600 mt-1">
                                    {address.firstName} {address.lastName}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    {address.address}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    {address.city}، {address.province}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    کد پستی: {address.postalCode}
                                  </p>
                                </div>
                                {address.isDefault && (
                                  <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                                    پیش‌فرض
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === 'settings' && (
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 mb-6">
                        تنظیمات حساب
                      </h2>
                      
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 mb-4">
                            اعلان‌ها
                          </h3>
                          <div className="space-y-3">
                            <label className="flex items-center justify-between">
                              <span className="text-gray-700">دریافت خبرنامه</span>
                              <input
                                type="checkbox"
                                checked={user.preferences.newsletter}
                                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                readOnly
                              />
                            </label>
                            <label className="flex items-center justify-between">
                              <span className="text-gray-700">اعلان‌های ایمیل</span>
                              <input
                                type="checkbox"
                                checked={user.preferences.emailNotifications}
                                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                readOnly
                              />
                            </label>
                            <label className="flex items-center justify-between">
                              <span className="text-gray-700">اعلان‌های پیامک</span>
                              <input
                                type="checkbox"
                                checked={user.preferences.smsNotifications}
                                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                readOnly
                              />
                            </label>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium text-gray-900 mb-4">
                            امنیت
                          </h3>
                          <button className="text-primary-600 hover:text-primary-700 transition-colors">
                            تغییر رمز عبور
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfilePage;
