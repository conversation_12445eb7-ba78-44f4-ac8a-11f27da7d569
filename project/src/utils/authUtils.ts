import { 
  User, 
  AuthResponse, 
  LoginCredentials, 
  RegisterData,
  AUTH_STORAGE_KEYS,
  TOKEN_EXPIRY,
  PERSIAN_AUTH_MESSAGES
} from '../types/auth';

/**
 * Storage utilities for authentication
 */
export class AuthStorage {
  static setToken(token: string, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(AUTH_STORAGE_KEYS.TOKEN, token);

    // Set expiry time based on remember me setting
    const expiryTime = Date.now() + (rememberMe ? TOKEN_EXPIRY.REMEMBER_ME : TOKEN_EXPIRY.ACCESS_TOKEN);
    storage.setItem(AUTH_STORAGE_KEYS.LAST_LOGIN, expiryTime.toString());

    // Clear token from the other storage to avoid conflicts
    const otherStorage = rememberMe ? sessionStorage : localStorage;
    otherStorage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
    otherStorage.removeItem(AUTH_STORAGE_KEYS.USER);
    otherStorage.removeItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
  }

  static getToken(): string | null {
    // Check if session is expired first
    if (this.isSessionExpired()) {
      this.clearAll();
      return null;
    }

    // Check sessionStorage first, then localStorage
    return sessionStorage.getItem(AUTH_STORAGE_KEYS.TOKEN) ||
           localStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
  }

  static setRefreshToken(refreshToken: string): void {
    // Always store refresh token in localStorage
    localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
  }

  static setUser(user: User, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(user));
  }

  static getUser(): User | null {
    // Check if session is expired first
    if (this.isSessionExpired()) {
      this.clearAll();
      return null;
    }

    const userStr = sessionStorage.getItem(AUTH_STORAGE_KEYS.USER) ||
                   localStorage.getItem(AUTH_STORAGE_KEYS.USER);

    if (!userStr) return null;

    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  static setRememberMe(remember: boolean): void {
    localStorage.setItem(AUTH_STORAGE_KEYS.REMEMBER_ME, remember.toString());
  }

  static getRememberMe(): boolean {
    return localStorage.getItem(AUTH_STORAGE_KEYS.REMEMBER_ME) === 'true';
  }

  static clearAll(): void {
    // Clear from both storages
    Object.values(AUTH_STORAGE_KEYS).forEach(key => {
      sessionStorage.removeItem(key);
      localStorage.removeItem(key);
    });
  }

  static isSessionExpired(): boolean {
    // Check sessionStorage first (for current session)
    const sessionExpiry = sessionStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
    if (sessionExpiry) {
      return Date.now() > parseInt(sessionExpiry);
    }

    // Check localStorage (for remember me sessions)
    const localExpiry = localStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
    if (localExpiry) {
      return Date.now() > parseInt(localExpiry);
    }

    // No expiry found, consider expired
    return true;
  }

  static isTokenExpired(): boolean {
    return this.isSessionExpired();
  }
}

/**
 * JWT token utilities
 */
export class TokenUtils {
  static decodeToken(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch {
      return null;
    }
  }

  static isTokenValid(token: string): boolean {
    if (!token) return false;
    
    const decoded = this.decodeToken(token);
    if (!decoded) return false;
    
    const currentTime = Date.now() / 1000;
    return decoded.exp > currentTime;
  }

  static getTokenExpiry(token: string): number | null {
    const decoded = this.decodeToken(token);
    return decoded?.exp ? decoded.exp * 1000 : null;
  }
}

/**
 * Password validation utilities
 */
export class PasswordValidator {
  static readonly MIN_LENGTH = 8;
  static readonly PATTERNS = {
    hasLetter: /[a-zA-Zآ-ی]/,
    hasNumber: /\d/,
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/,
    hasUpperCase: /[A-Z]/,
    hasLowerCase: /[a-z]/
  };

  static validate(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < this.MIN_LENGTH) {
      errors.push(`رمز عبور باید حداقل ${this.MIN_LENGTH} کاراکتر باشد`);
    }

    if (!this.PATTERNS.hasLetter.test(password)) {
      errors.push('رمز عبور باید شامل حروف باشد');
    }

    if (!this.PATTERNS.hasNumber.test(password)) {
      errors.push('رمز عبور باید شامل اعداد باشد');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static getStrength(password: string): 'weak' | 'medium' | 'strong' {
    let score = 0;

    if (password.length >= this.MIN_LENGTH) score++;
    if (this.PATTERNS.hasLetter.test(password)) score++;
    if (this.PATTERNS.hasNumber.test(password)) score++;
    if (this.PATTERNS.hasSpecial.test(password)) score++;
    if (password.length >= 12) score++;

    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  }
}

/**
 * Email validation utilities
 */
export class EmailValidator {
  static readonly PATTERN = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  static validate(email: string): boolean {
    return this.PATTERN.test(email.trim().toLowerCase());
  }

  static normalize(email: string): string {
    return email.trim().toLowerCase();
  }
}

/**
 * Phone validation utilities for Iranian phone numbers
 */
export class PhoneValidator {
  static readonly PATTERNS = {
    mobile: /^09\d{9}$/,
    landline: /^0\d{2,3}\d{8}$/
  };

  static validateMobile(phone: string): boolean {
    const cleaned = phone.replace(/\s|-/g, '');
    return this.PATTERNS.mobile.test(cleaned);
  }

  static normalizeMobile(phone: string): string {
    return phone.replace(/\s|-/g, '');
  }

  static formatMobile(phone: string): string {
    const cleaned = this.normalizeMobile(phone);
    if (cleaned.length === 11) {
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    return phone;
  }
}

/**
 * Mock API utilities for development
 */
export class MockAuthAPI {
  private static readonly MOCK_USERS: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      phone: '09123456789',
      firstName: 'کاربر',
      lastName: 'نمونه',
      isEmailVerified: true,
      isPhoneVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      role: 'customer',
      preferences: {
        language: 'fa',
        newsletter: true,
        smsNotifications: true,
        emailNotifications: true,
        theme: 'light'
      },
      addresses: []
    }
  ];

  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user = this.MOCK_USERS.find(u => u.email === credentials.email);
    
    if (!user || credentials.password !== 'password123') {
      throw new Error(PERSIAN_AUTH_MESSAGES.errors.invalidCredentials);
    }

    const token = this.generateMockToken(user);
    const refreshToken = this.generateMockRefreshToken(user);

    return {
      user,
      token,
      refreshToken,
      expiresIn: TOKEN_EXPIRY.ACCESS_TOKEN
    };
  }

  static async register(data: RegisterData): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check if email exists
    if (this.MOCK_USERS.some(u => u.email === data.email)) {
      throw new Error(PERSIAN_AUTH_MESSAGES.errors.emailExists);
    }

    // Check if phone exists
    if (data.phone && this.MOCK_USERS.some(u => u.phone === data.phone)) {
      throw new Error(PERSIAN_AUTH_MESSAGES.errors.phoneExists);
    }

    const newUser: User = {
      id: Date.now().toString(),
      email: data.email,
      phone: data.phone,
      firstName: data.firstName,
      lastName: data.lastName,
      isEmailVerified: false,
      isPhoneVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      role: 'customer',
      preferences: {
        language: 'fa',
        newsletter: data.newsletter || false,
        smsNotifications: true,
        emailNotifications: true,
        theme: 'light'
      },
      addresses: []
    };

    this.MOCK_USERS.push(newUser);

    const token = this.generateMockToken(newUser);
    const refreshToken = this.generateMockRefreshToken(newUser);

    return {
      user: newUser,
      token,
      refreshToken,
      expiresIn: TOKEN_EXPIRY.ACCESS_TOKEN
    };
  }

  private static generateMockToken(user: User): string {
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({
      sub: user.id,
      email: user.email,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor((Date.now() + TOKEN_EXPIRY.ACCESS_TOKEN) / 1000)
    }));
    const signature = btoa('mock-signature');
    
    return `${header}.${payload}.${signature}`;
  }

  private static generateMockRefreshToken(user: User): string {
    return btoa(`refresh-${user.id}-${Date.now()}`);
  }
}

/**
 * Error handling utilities
 */
export class AuthErrorHandler {
  static getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    return PERSIAN_AUTH_MESSAGES.errors.serverError;
  }

  static isNetworkError(error: any): boolean {
    return error?.code === 'NETWORK_ERROR' || 
           error?.message?.includes('Network Error') ||
           !navigator.onLine;
  }

  static isAuthError(error: any): boolean {
    return error?.status === 401 || 
           error?.response?.status === 401 ||
           error?.message?.includes('Unauthorized');
  }
}
