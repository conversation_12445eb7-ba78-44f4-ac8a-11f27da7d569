# 🚀 Development Setup Guide
## GlowRoya Persian Skincare E-commerce Platform

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL client tools
- Git

### Quick Start

1. **Install Dependencies**
   ```bash
   # Frontend dependencies
   cd project
   npm install

   # Backend dependencies
   cd backend
   npm install
   cd ..
   ```

2. **Environment Setup**
   ```bash
   # Copy environment templates (if they exist)
   cp .env.example .env 2>/dev/null || echo "No .env.example found"
   cp backend/.env.example backend/.env 2>/dev/null || echo "No backend .env.example found"
   ```

3. **Database Setup**
   ```bash
   cd backend

   # Generate Prisma client
   npx prisma generate

   # Run migrations (if database is accessible)
   npx prisma migrate deploy

   # Seed database
   npx prisma db seed
   ```

4. **Start Development Servers**
   ```bash
   # Terminal 1: Start Backend (from backend directory)
   cd backend
   npm run dev

   # Terminal 2: Start Frontend (from project directory)
   cd ..
   npm run dev
   ```

### Access Points
- **Frontend:** http://localhost:5173/
- **Backend API:** http://localhost:3001/
- **API Documentation:** http://localhost:3001/api/v1/docs
- **Admin Panel:** http://localhost:5173/admin/login

### Admin Credentials
Check `ADMIN_LOGIN_CREDENTIALS.md` for admin access details.

### Troubleshooting
- Ensure PostgreSQL database is accessible
- Check environment variables are properly set
- Verify all dependencies are installed
- Check logs for specific error messages

---

## 🗑️ Price Filtering Removal

**Note:** Price filtering functionality has been completely removed from the project as requested. This includes:
- Frontend price filter components and UI
- Backend API price filtering parameters
- Admin panel price filtering options
- All related state management and utilities
