import { 
  ProductFormData, 
  ProductValidationError, 
  ProductValidationResult,
  PERSIAN_PRODUCT_MESSAGES 
} from '../types/adminProduct';

// Persian text validation - more lenient
export const validatePersianText = (text: string, minLength = 0, maxLength = 1000): boolean => {
  if (!text || typeof text !== 'string') return false;
  if (text.length < minLength || text.length > maxLength) return false;

  // For now, just check length - we can make this more strict later if needed
  // This allows any characters including Persian, Arabic, English, numbers, and punctuation
  return text.trim().length >= minLength;
};

// SKU validation
export const validateSku = (sku: string): boolean => {
  // SKU should be alphanumeric with hyphens and underscores, 3-50 characters
  const skuRegex = /^[A-Za-z0-9\-_]{3,50}$/;
  return skuRegex.test(sku);
};

// Slug validation
export const validateSlug = (slug: string): boolean => {
  // Slug should be lowercase letters, numbers, and hyphens only
  const slugRegex = /^[a-z0-9\-]+$/;
  return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;
};

// Price validation
export const validatePrice = (price: number): boolean => {
  return price > 0 && price <= 999999999 && Number.isFinite(price);
};

// Stock validation
export const validateStock = (stock: number): boolean => {
  return Number.isInteger(stock) && stock >= 0 && stock <= 999999;
};

// Image URL validation
export const validateImageUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);

    // Check if it's a valid HTTP/HTTPS URL
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    // Extract the pathname without query parameters
    const pathname = urlObj.pathname.toLowerCase();

    // Check for image extensions in the pathname
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i;

    // Also allow common image hosting services even without explicit extensions
    const imageHosts = [
      'images.pexels.com',
      'images.unsplash.com',
      'cdn.pixabay.com',
      'images.stockvault.net',
      'i.imgur.com',
      'cloudinary.com',
      'imagekit.io'
    ];

    const isImageHost = imageHosts.some(host => urlObj.hostname.includes(host));
    const hasImageExtension = imageExtensions.test(pathname) || imageExtensions.test(url);

    return hasImageExtension || isImageHost;
  } catch {
    return false;
  }
};

// Generate slug from Persian text
export const generateSlug = (text: string): string => {
  // Persian to English transliteration map
  const persianToEnglish: { [key: string]: string } = {
    'آ': 'a', 'ا': 'a', 'ب': 'b', 'پ': 'p', 'ت': 't', 'ث': 's', 'ج': 'j',
    'چ': 'ch', 'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'z', 'ر': 'r', 'ز': 'z',
    'ژ': 'zh', 'س': 's', 'ش': 'sh', 'ص': 's', 'ض': 'z', 'ط': 't', 'ظ': 'z',
    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'gh', 'ک': 'k', 'گ': 'g', 'ل': 'l',
    'م': 'm', 'ن': 'n', 'و': 'v', 'ه': 'h', 'ی': 'y', 'ئ': 'y', 'ء': ''
  };

  return text
    .toLowerCase()
    .split('')
    .map(char => persianToEnglish[char] || char)
    .join('')
    .replace(/[^a-z0-9\s\-]/g, '') // Remove non-alphanumeric except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, 100); // Limit length
};

// Validate product form data
export const validateProductForm = (data: ProductFormData, existingSkus: string[] = []): ProductValidationResult => {
  const errors: ProductValidationError[] = [];
  const warnings: ProductValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: PERSIAN_PRODUCT_MESSAGES.validation.required,
      code: 'REQUIRED'
    });
  } else if (!validatePersianText(data.name, 2, 200)) {
    errors.push({
      field: 'name',
      message: PERSIAN_PRODUCT_MESSAGES.validation.minLength.replace('{min}', '2'),
      code: 'INVALID_LENGTH'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: PERSIAN_PRODUCT_MESSAGES.validation.required,
      code: 'REQUIRED'
    });
  } else if (!validatePersianText(data.description, 10, 5000)) {
    errors.push({
      field: 'description',
      message: PERSIAN_PRODUCT_MESSAGES.validation.minLength.replace('{min}', '10'),
      code: 'INVALID_LENGTH'
    });
  }

  if (!data.category || data.category.trim().length === 0) {
    errors.push({
      field: 'category',
      message: PERSIAN_PRODUCT_MESSAGES.validation.required,
      code: 'REQUIRED'
    });
  }

  if (!data.sku || data.sku.trim().length === 0) {
    errors.push({
      field: 'sku',
      message: PERSIAN_PRODUCT_MESSAGES.validation.required,
      code: 'REQUIRED'
    });
  } else if (!validateSku(data.sku)) {
    errors.push({
      field: 'sku',
      message: 'کد محصول باید بین 3 تا 50 کاراکتر و شامل حروف، اعداد، خط تیره و زیرخط باشد',
      code: 'INVALID_SKU'
    });
  } else if (existingSkus.includes(data.sku)) {
    errors.push({
      field: 'sku',
      message: PERSIAN_PRODUCT_MESSAGES.validation.duplicateSku,
      code: 'DUPLICATE_SKU'
    });
  }

  // Price validation
  if (!validatePrice(data.price)) {
    errors.push({
      field: 'price',
      message: PERSIAN_PRODUCT_MESSAGES.validation.invalidPrice,
      code: 'INVALID_PRICE'
    });
  }

  if (data.discountedPrice && !validatePrice(data.discountedPrice)) {
    errors.push({
      field: 'discountedPrice',
      message: PERSIAN_PRODUCT_MESSAGES.validation.invalidPrice,
      code: 'INVALID_PRICE'
    });
  }

  if (data.discountedPrice && data.discountedPrice >= data.price) {
    errors.push({
      field: 'discountedPrice',
      message: 'قیمت تخفیف‌دار باید کمتر از قیمت اصلی باشد',
      code: 'INVALID_DISCOUNT_PRICE'
    });
  }

  if (data.costPrice && !validatePrice(data.costPrice)) {
    errors.push({
      field: 'costPrice',
      message: PERSIAN_PRODUCT_MESSAGES.validation.invalidPrice,
      code: 'INVALID_PRICE'
    });
  }

  // Stock validation
  if (data.stock === undefined || data.stock === null || !validateStock(data.stock)) {
    errors.push({
      field: 'stock',
      message: PERSIAN_PRODUCT_MESSAGES.validation.invalidStock,
      code: 'INVALID_STOCK'
    });
  }

  if (data.trackInventory && data.lowStockThreshold < 0) {
    errors.push({
      field: 'lowStockThreshold',
      message: 'حد آستانه موجودی کم نمی‌تواند منفی باشد',
      code: 'INVALID_THRESHOLD'
    });
  }

  // Image validation - main image is required
  if (!data.imageSrc || data.imageSrc.trim().length === 0) {
    errors.push({
      field: 'imageSrc',
      message: 'تصویر اصلی محصول الزامی است',
      code: 'REQUIRED'
    });
  } else if (!validateImageUrl(data.imageSrc)) {
    errors.push({
      field: 'imageSrc',
      message: 'فرمت تصویر اصلی معتبر نیست. فرمت‌های مجاز: JPG, PNG, WebP, SVG',
      code: 'INVALID_IMAGE'
    });
  }

  if (data.images && data.images.length > 10) {
    errors.push({
      field: 'images',
      message: PERSIAN_PRODUCT_MESSAGES.validation.maxImages.replace('{max}', '10'),
      code: 'TOO_MANY_IMAGES'
    });
  }

  // Validate additional images - only if they exist
  if (data.images && Array.isArray(data.images)) {
    data.images.forEach((image, index) => {
      if (image && image.trim().length > 0 && !validateImageUrl(image)) {
        errors.push({
          field: `images.${index}`,
          message: `تصویر اضافی ${index + 1}: فرمت معتبر نیست. فرمت‌های مجاز: JPG, PNG, WebP, SVG`,
          code: 'INVALID_IMAGE'
        });
      }
    });
  }

  // SEO validation
  if (data.seoTitle && data.seoTitle.length > 60) {
    warnings.push({
      field: 'seoTitle',
      message: 'عنوان SEO بهتر است کمتر از 60 کاراکتر باشد',
      code: 'SEO_TITLE_LONG'
    });
  }

  if (data.seoDescription && data.seoDescription.length > 160) {
    warnings.push({
      field: 'seoDescription',
      message: 'توضیحات SEO بهتر است کمتر از 160 کاراکتر باشد',
      code: 'SEO_DESCRIPTION_LONG'
    });
  }

  // Slug validation - make it more lenient, auto-generate if missing
  if (!data.slug || data.slug.trim().length === 0) {
    // Auto-generate slug from name if missing
    if (data.name) {
      const autoSlug = generateSlug(data.name);
      if (autoSlug && autoSlug.length >= 3) {
        // Slug will be auto-generated, no error needed
      } else {
        warnings.push({
          field: 'slug',
          message: 'نامک (slug) خودکار تولید خواهد شد',
          code: 'AUTO_GENERATE_SLUG'
        });
      }
    }
  } else if (!validateSlug(data.slug)) {
    errors.push({
      field: 'slug',
      message: PERSIAN_PRODUCT_MESSAGES.validation.invalidSlug,
      code: 'INVALID_SLUG'
    });
  }

  // Benefits validation - make it optional
  if (data.benefits && Array.isArray(data.benefits)) {
    if (data.benefits.length === 0) {
      warnings.push({
        field: 'benefits',
        message: 'افزودن مزایا به بهبود فروش کمک می‌کند',
        code: 'NO_BENEFITS'
      });
    }

    data.benefits.forEach((benefit, index) => {
      if (benefit && benefit.trim().length > 0 && !validatePersianText(benefit, 5, 200)) {
        errors.push({
          field: `benefits.${index}`,
          message: `مزیت ${index + 1}: حداقل 5 کاراکتر وارد کنید`,
          code: 'INVALID_BENEFIT'
        });
      }
    });
  }

  // Ingredients validation - make it optional
  if (data.ingredients && Array.isArray(data.ingredients)) {
    data.ingredients.forEach((ingredient, index) => {
      if (ingredient && ingredient.trim().length > 0 && !validatePersianText(ingredient, 2, 100)) {
        errors.push({
          field: `ingredients.${index}`,
          message: `ترکیب ${index + 1}: حداقل 2 کاراکتر وارد کنید`,
          code: 'INVALID_INGREDIENT'
        });
      }
    });
  }

  // How to use validation - make it optional
  if (data.howToUse && Array.isArray(data.howToUse)) {
    data.howToUse.forEach((step, index) => {
      if (step && step.trim().length > 0 && !validatePersianText(step, 5, 300)) {
        errors.push({
          field: `howToUse.${index}`,
          message: `مرحله ${index + 1}: حداقل 5 کاراکتر وارد کنید`,
          code: 'INVALID_STEP'
        });
      }
    });
  }

  // Variant validation - make it optional
  if (data.hasVariants && data.variants && Array.isArray(data.variants)) {
    if (data.variants.length === 0) {
      errors.push({
        field: 'variants',
        message: 'حداقل یک نوع متغیر اضافه کنید یا گزینه متغیرها را غیرفعال کنید',
        code: 'NO_VARIANTS'
      });
    }

    data.variants.forEach((variantGroup, groupIndex) => {
      if (variantGroup.variants.length === 0) {
        errors.push({
          field: `variants.${groupIndex}`,
          message: `گروه متغیر ${variantGroup.name}: حداقل یک متغیر اضافه کنید`,
          code: 'EMPTY_VARIANT_GROUP'
        });
      }

      variantGroup.variants.forEach((variant, variantIndex) => {
        if (!variant.name || variant.name.trim().length === 0) {
          errors.push({
            field: `variants.${groupIndex}.${variantIndex}.name`,
            message: 'نام متغیر الزامی است',
            code: 'REQUIRED'
          });
        }

        if (variant.stock !== undefined && !validateStock(variant.stock)) {
          errors.push({
            field: `variants.${groupIndex}.${variantIndex}.stock`,
            message: 'موجودی متغیر معتبر نیست',
            code: 'INVALID_STOCK'
          });
        }
      });
    });
  }

  // Shipping validation
  if (data.requiresShipping) {
    if (data.shippingWeight && data.shippingWeight <= 0) {
      errors.push({
        field: 'shippingWeight',
        message: 'وزن ارسال باید بیشتر از صفر باشد',
        code: 'INVALID_WEIGHT'
      });
    }

    if (data.shippingDimensions) {
      const { length, width, height } = data.shippingDimensions;
      if (length <= 0 || width <= 0 || height <= 0) {
        errors.push({
          field: 'shippingDimensions',
          message: 'ابعاد ارسال باید بیشتر از صفر باشد',
          code: 'INVALID_DIMENSIONS'
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Validate bulk import data
export const validateImportData = (data: any[]): { valid: any[], invalid: any[] } => {
  const valid: any[] = [];
  const invalid: any[] = [];

  data.forEach((row, index) => {
    const errors: string[] = [];

    // Required fields
    if (!row.name) errors.push('نام محصول الزامی است');
    if (!row.sku) errors.push('کد محصول الزامی است');
    if (!row.price || isNaN(Number(row.price))) errors.push('قیمت معتبر نیست');
    if (!row.category) errors.push('دسته‌بندی الزامی است');

    // Validate SKU format
    if (row.sku && !validateSku(row.sku)) {
      errors.push('فرمت کد محصول معتبر نیست');
    }

    // Validate price
    if (row.price && !validatePrice(Number(row.price))) {
      errors.push('قیمت معتبر نیست');
    }

    // Validate stock
    if (row.stock !== undefined && !validateStock(Number(row.stock))) {
      errors.push('موجودی معتبر نیست');
    }

    if (errors.length === 0) {
      valid.push({ ...row, rowIndex: index + 1 });
    } else {
      invalid.push({ ...row, rowIndex: index + 1, errors });
    }
  });

  return { valid, invalid };
};

// Generate unique SKU
export const generateUniqueSku = (productName: string, existingSkus: string[]): string => {
  const baseSlug = generateSlug(productName);
  let sku = baseSlug.toUpperCase().replace(/-/g, '');
  
  // Limit to 20 characters
  if (sku.length > 20) {
    sku = sku.substring(0, 20);
  }

  // Add numbers if SKU exists
  let counter = 1;
  let uniqueSku = sku;
  
  while (existingSkus.includes(uniqueSku)) {
    const suffix = counter.toString();
    uniqueSku = sku.substring(0, 20 - suffix.length) + suffix;
    counter++;
  }

  return uniqueSku;
};
