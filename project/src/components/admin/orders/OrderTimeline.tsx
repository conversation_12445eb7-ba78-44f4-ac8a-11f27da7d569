import React from 'react';
import { motion } from 'framer-motion';
import { 
  Clock, 
  CheckCircle, 
  Package, 
  Truck, 
  MessageSquare, 
  CreditCard,
  User,
  Bot,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { AdminOrder, OrderTimelineEvent } from '../../../types/adminOrder';
import { formatPersianDate } from '../../../utils/orderUtils';

interface OrderTimelineProps {
  order: AdminOrder;
  className?: string;
  showAllEvents?: boolean;
  maxEvents?: number;
}

const OrderTimeline: React.FC<OrderTimelineProps> = ({
  order,
  className = '',
  showAllEvents = true,
  maxEvents = 10
}) => {
  const getEventIcon = (event: OrderTimelineEvent) => {
    switch (event.type) {
      case 'status_change':
        if (event.metadata?.to === 'confirmed') return <CheckCircle className="w-4 h-4 text-blue-500" />;
        if (event.metadata?.to === 'processing') return <Package className="w-4 h-4 text-purple-500" />;
        if (event.metadata?.to === 'shipped') return <Truck className="w-4 h-4 text-indigo-500" />;
        if (event.metadata?.to === 'delivered') return <CheckCircle className="w-4 h-4 text-green-500" />;
        if (event.metadata?.to === 'cancelled') return <AlertCircle className="w-4 h-4 text-red-500" />;
        return <RefreshCw className="w-4 h-4 text-gray-500" />;
      case 'note_added':
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
      case 'payment':
        return <CreditCard className="w-4 h-4 text-green-500" />;
      case 'shipping':
        return <Truck className="w-4 h-4 text-indigo-500" />;
      case 'refund':
        return <RefreshCw className="w-4 h-4 text-orange-500" />;
      case 'communication':
        return <MessageSquare className="w-4 h-4 text-purple-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getEventColor = (event: OrderTimelineEvent) => {
    switch (event.type) {
      case 'status_change':
        if (event.metadata?.to === 'confirmed') return 'border-blue-200 bg-blue-50';
        if (event.metadata?.to === 'processing') return 'border-purple-200 bg-purple-50';
        if (event.metadata?.to === 'shipped') return 'border-indigo-200 bg-indigo-50';
        if (event.metadata?.to === 'delivered') return 'border-green-200 bg-green-50';
        if (event.metadata?.to === 'cancelled') return 'border-red-200 bg-red-50';
        return 'border-gray-200 bg-gray-50';
      case 'note_added':
        return 'border-blue-200 bg-blue-50';
      case 'payment':
        return 'border-green-200 bg-green-50';
      case 'shipping':
        return 'border-indigo-200 bg-indigo-50';
      case 'refund':
        return 'border-orange-200 bg-orange-50';
      case 'communication':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getUserIcon = (event: OrderTimelineEvent) => {
    if (event.isSystemGenerated) {
      return <Bot className="w-3 h-3 text-gray-400" />;
    }
    return <User className="w-3 h-3 text-blue-500" />;
  };

  const formatEventTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'همین الان';
    if (diffInMinutes < 60) return `${diffInMinutes} دقیقه پیش`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ساعت پیش`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)} روز پیش`;
    
    return formatPersianDate(timestamp);
  };

  const eventsToShow = showAllEvents 
    ? order.timeline 
    : order.timeline.slice(0, maxEvents);

  const sortedEvents = [...eventsToShow].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          تاریخچه سفارش
        </h3>
        <span className="text-sm text-gray-500">
          {order.timeline.length} رویداد
        </span>
      </div>

      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute right-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Timeline Events */}
        <div className="space-y-4">
          {sortedEvents.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative flex items-start gap-4"
            >
              {/* Event Icon */}
              <div className={`
                relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2
                ${getEventColor(event)}
              `}>
                {getEventIcon(event)}
              </div>

              {/* Event Content */}
              <div className="flex-1 min-w-0 pb-4">
                <div className={`
                  p-4 rounded-lg border
                  ${getEventColor(event)}
                `}>
                  {/* Event Header */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">
                        {event.title}
                      </h4>
                      {event.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {event.description}
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      {getUserIcon(event)}
                      <span>{formatEventTime(event.timestamp)}</span>
                    </div>
                  </div>

                  {/* Event Metadata */}
                  {event.metadata && (
                    <div className="mt-3 space-y-2">
                      {event.metadata.trackingNumber && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500">کد رهگیری:</span>
                          <span className="text-xs font-mono bg-white px-2 py-1 rounded border">
                            {event.metadata.trackingNumber}
                          </span>
                        </div>
                      )}
                      
                      {event.metadata.from && event.metadata.to && (
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <span>تغییر از</span>
                          <span className="font-medium">
                            {event.metadata.from === 'pending' && 'در انتظار تأیید'}
                            {event.metadata.from === 'confirmed' && 'تأیید شده'}
                            {event.metadata.from === 'processing' && 'در حال پردازش'}
                            {event.metadata.from === 'shipped' && 'ارسال شده'}
                            {event.metadata.from === 'delivered' && 'تحویل داده شده'}
                            {event.metadata.from === 'cancelled' && 'لغو شده'}
                          </span>
                          <span>به</span>
                          <span className="font-medium">
                            {event.metadata.to === 'pending' && 'در انتظار تأیید'}
                            {event.metadata.to === 'confirmed' && 'تأیید شده'}
                            {event.metadata.to === 'processing' && 'در حال پردازش'}
                            {event.metadata.to === 'shipped' && 'ارسال شده'}
                            {event.metadata.to === 'delivered' && 'تحویل داده شده'}
                            {event.metadata.to === 'cancelled' && 'لغو شده'}
                          </span>
                        </div>
                      )}

                      {event.metadata.bulk && (
                        <div className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                          بروزرسانی گروهی
                        </div>
                      )}
                    </div>
                  )}

                  {/* User Info */}
                  {event.userName && (
                    <div className="mt-3 pt-2 border-t border-gray-200">
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <User className="w-3 h-3" />
                        <span>توسط: {event.userName}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Show More Button */}
        {!showAllEvents && order.timeline.length > maxEvents && (
          <div className="text-center pt-4">
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              مشاهده {order.timeline.length - maxEvents} رویداد دیگر
            </button>
          </div>
        )}

        {/* Empty State */}
        {order.timeline.length === 0 && (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">هیچ رویدادی ثبت نشده است</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderTimeline;
