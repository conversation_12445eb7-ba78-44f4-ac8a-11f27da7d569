import React from 'react';
import { SpecialOffersSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';

interface SpecialOffersSectionEditorProps {
  section: SpecialOffersSectionData;
  onChange: (updates: Partial<SpecialOffersSectionData['content']>) => void;
}

const SpecialOffersSectionEditor: React.FC<SpecialOffersSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof SpecialOffersSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <AdminCard title="محتوای بخش پیشنهادات ویژه" icon="Tag">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="پیشنهادات ویژه"
            />
          </AdminFormField>

          <AdminFormField label="نوع نمایش">
            <AdminSelect
              value={section.content.displayType}
              onChange={(value) => handleChange('displayType', value)}
              options={[
                { value: 'carousel', label: 'اسلایدر' },
                { value: 'grid', label: 'شبکه‌ای' },
                { value: 'banner', label: 'بنر' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="پخش خودکار">
            <AdminToggle
              checked={section.content.autoplay}
              onChange={(checked) => handleChange('autoplay', checked)}
              label="اسلایدر به صورت خودکار حرکت کند"
            />
          </AdminFormField>

          <AdminFormField label="نمایش تایمر">
            <AdminToggle
              checked={section.content.showTimer}
              onChange={(checked) => handleChange('showTimer', checked)}
              label="تایمر پایان تخفیف نمایش داده شود"
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default SpecialOffersSectionEditor;
