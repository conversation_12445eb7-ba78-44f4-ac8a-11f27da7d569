import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, Save, Shuffle } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import PromotionEditor from '../../../components/admin/content/PromotionEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { PromotionFormData } from '../../../types/adminContent';

const PromotionCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { createPromotion } = useAdminContent();
  const [loading, setLoading] = useState(false);

  const generatePromotionCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleSave = async (data: PromotionFormData) => {
    try {
      setLoading(true);
      
      // Generate code if required but not provided
      if (data.isCodeRequired && !data.code) {
        data.code = generatePromotionCode();
      }
      
      const newPromotion = await createPromotion(data);
      toast.success('تخفیف با موفقیت ایجاد شد');
      navigate(`/admin/content/promotions/${newPromotion.id}`);
    } catch (error) {
      console.error('Error creating promotion:', error);
      toast.error('خطا در ایجاد تخفیف');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/content/promotions');
  };

  return (
    <AdminLayout 
      title="ایجاد تخفیف جدید"
      subtitle="ایجاد کمپین تخفیف جدید برای مشتریان"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={handleCancel}
          >
            بازگشت
          </AdminButton>
        </div>
      }
    >
      <PromotionEditor
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        mode="create"
        onGenerateCode={generatePromotionCode}
      />
    </AdminLayout>
  );
};

export default PromotionCreatePage;
