// Mobile device detection and responsive design types

export interface MobileDetection {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  orientation: 'portrait' | 'landscape';
  touchSupported: boolean;
  userAgent: string;
  deviceType: DeviceType;
}

export type DeviceType = 'mobile' | 'tablet' | 'desktop';

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  wide: number;
}

export interface TouchGesture {
  type: 'tap' | 'swipe' | 'pinch' | 'long-press';
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration: number;
  direction?: 'left' | 'right' | 'up' | 'down';
}

export interface MobileNavigationItem {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  path: string;
  badge?: number;
  isActive?: boolean;
  children?: MobileNavigationItem[];
}

export interface MobileGridConfig {
  mobile: number; // columns on mobile
  tablet: number; // columns on tablet
  desktop: number; // columns on desktop
  wide: number; // columns on wide screens
  gap: string; // gap between items
  minItemWidth: number; // minimum item width in px
}

export interface MobileFilterState {
  isOpen: boolean;
  activeFilters: Record<string, any>;
  appliedFilters: Record<string, any>;
  hasChanges: boolean;
}

export interface TouchInteraction {
  element: HTMLElement;
  startTime: number;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  isActive: boolean;
  type: 'touch' | 'mouse';
}

export interface MobileOptimization {
  lazyLoading: boolean;
  imageCompression: boolean;
  reducedAnimations: boolean;
  touchOptimized: boolean;
  minTouchTarget: number; // minimum touch target size in px
}

export interface MobileViewport {
  width: number;
  height: number;
  scale: number;
  orientation: number;
  isFullscreen: boolean;
}

// Persian mobile messages
export const PERSIAN_MOBILE_MESSAGES = {
  navigation: {
    menu: 'منو',
    close: 'بستن',
    back: 'بازگشت',
    home: 'خانه',
    search: 'جستجو',
    cart: 'سبد خرید',
    profile: 'پروفایل',
    categories: 'دسته‌بندی‌ها',
    filters: 'فیلترها',
    sort: 'مرتب‌سازی'
  },
  grid: {
    viewMode: 'نمایش',
    listView: 'فهرستی',
    gridView: 'شبکه‌ای',
    columns: 'ستون',
    loading: 'در حال بارگذاری...',
    noResults: 'محصولی یافت نشد',
    loadMore: 'بارگذاری بیشتر'
  },
  filters: {
    apply: 'اعمال فیلتر',
    clear: 'پاک کردن',
    reset: 'بازنشانی',
    showResults: 'نمایش نتایج',
    hideFilters: 'مخفی کردن فیلترها',
    showFilters: 'نمایش فیلترها',
    activeFilters: 'فیلترهای فعال',
    noFilters: 'فیلتری انتخاب نشده'
  },
  touch: {
    tap: 'ضربه',
    swipe: 'کشیدن',
    pinch: 'قیچی کردن',
    longPress: 'نگه داشتن',
    swipeLeft: 'کشیدن به چپ',
    swipeRight: 'کشیدن به راست',
    swipeUp: 'کشیدن به بالا',
    swipeDown: 'کشیدن به پایین'
  },
  optimization: {
    loading: 'بارگذاری...',
    optimizing: 'بهینه‌سازی...',
    compressed: 'فشرده شده',
    cached: 'ذخیره شده',
    offline: 'آفلاین',
    online: 'آنلاین'
  },
  errors: {
    touchNotSupported: 'لمس پشتیبانی نمی‌شود',
    orientationLocked: 'جهت صفحه قفل شده',
    viewportTooSmall: 'صفحه نمایش خیلی کوچک است',
    networkError: 'خطا در اتصال به شبکه',
    loadingError: 'خطا در بارگذاری'
  }
} as const;

// Default responsive breakpoints (following Tailwind CSS)
export const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 640,   // sm
  tablet: 768,   // md
  desktop: 1024, // lg
  wide: 1280     // xl
};

// Default mobile grid configuration
export const DEFAULT_MOBILE_GRID: MobileGridConfig = {
  mobile: 1,
  tablet: 2,
  desktop: 3,
  wide: 4,
  gap: '1rem',
  minItemWidth: 280
};

// Default mobile optimization settings
export const DEFAULT_MOBILE_OPTIMIZATION: MobileOptimization = {
  lazyLoading: true,
  imageCompression: true,
  reducedAnimations: false,
  touchOptimized: true,
  minTouchTarget: 44 // Apple's recommended minimum touch target size
};

// Mobile navigation configuration
export const MOBILE_NAVIGATION_CONFIG = {
  maxItems: 5,
  showLabels: true,
  showBadges: true,
  animationDuration: 300,
  swipeThreshold: 50,
  tapDelay: 150
};

// Touch gesture thresholds
export const TOUCH_THRESHOLDS = {
  tap: {
    maxDuration: 300,
    maxDistance: 10
  },
  swipe: {
    minDistance: 50,
    maxDuration: 1000
  },
  longPress: {
    minDuration: 500,
    maxDistance: 10
  },
  pinch: {
    minScale: 0.5,
    maxScale: 3.0
  }
};

export type MobileNavigationConfig = typeof MOBILE_NAVIGATION_CONFIG;
export type TouchThresholds = typeof TOUCH_THRESHOLDS;
