import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { Plus, FileText, Eye, Edit, Globe, Copy, Trash2 } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminTable, { AdminActionMenu, AdminTableBadge } from '../../../components/admin/common/AdminTable';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { PageContent } from '../../../types/adminContent';
import { formatPersianDate, formatContentStatus } from '../../../utils/contentUtils';

const PagesPage: React.FC = () => {
  const { pages, loading, duplicatePage, deletePage } = useAdminContent();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<string | null>(null);

  const handleDuplicate = async (pageId: string) => {
    try {
      await duplicatePage(pageId);
      toast.success('صفحه با موفقیت کپی شد');
    } catch (error) {
      toast.error('خطا در کپی کردن صفحه');
    }
  };

  const handleDelete = (pageId: string) => {
    setPageToDelete(pageId);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!pageToDelete) return;

    try {
      await deletePage(pageToDelete);
      toast.success('صفحه با موفقیت حذف شد');
      setDeleteModalOpen(false);
      setPageToDelete(null);
    } catch (error) {
      console.error('Error deleting page:', error);
      toast.error('خطا در حذف صفحه');
    }
  };

  const statsCards = [
    {
      title: 'کل صفحات',
      value: pages.length.toLocaleString('fa-IR'),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'منتشر شده',
      value: pages.filter(p => p.status === 'published').length.toLocaleString('fa-IR'),
      icon: Globe,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'در منو',
      value: pages.filter(p => p.showInMenu).length.toLocaleString('fa-IR'),
      icon: Edit,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'کل بازدید',
      value: pages.reduce((sum, p) => sum + p.views, 0).toLocaleString('fa-IR'),
      icon: Eye,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  const templateLabels: Record<PageContent['template'], string> = {
    default: 'پیش‌فرض',
    landing: 'صفحه فرود',
    about: 'درباره ما',
    contact: 'تماس با ما',
    custom: 'سفارشی'
  };

  if (loading) {
    return (
      <AdminLayout title="مدیریت صفحات">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="مدیریت صفحات"
      subtitle="مدیریت صفحات محتوایی و صفحات استاتیک سایت"
      actions={
        <AdminButton 
          variant="primary" 
          icon={Plus}
          onClick={() => window.location.href = '/admin/content/pages/create'}
        >
          ایجاد صفحه جدید
        </AdminButton>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        {/* Pages Table */}
        <AdminCard>
          {pages.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                هیچ صفحه‌ای یافت نشد
              </h3>
              <p className="text-gray-500 mb-6">
                اولین صفحه محتوایی خود را ایجاد کنید
              </p>
              <AdminButton
                variant="primary"
                icon={Plus}
                onClick={() => window.location.href = '/admin/content/pages/create'}
              >
                ایجاد صفحه جدید
              </AdminButton>
            </div>
          ) : (
            <AdminTable
              columns={[
                {
                  key: 'title',
                  title: 'عنوان',
                  sortable: true,
                  render: (page: PageContent) => (
                    <div>
                      <div className="font-medium text-gray-900">{page.title}</div>
                      {page.excerpt && (
                        <div className="text-sm text-gray-500 mt-1">{page.excerpt}</div>
                      )}
                    </div>
                  )
                },
                {
                  key: 'template',
                  title: 'قالب',
                  render: (page: PageContent) => (
                    <span className="text-sm text-gray-600">
                      {templateLabels[page.template] || page.template}
                    </span>
                  )
                },
                {
                  key: 'status',
                  title: 'وضعیت',
                  render: (page: PageContent) => (
                    <AdminTableBadge variant={page.status === 'published' ? 'success' : 'warning'}>
                      {formatContentStatus(page.status)}
                    </AdminTableBadge>
                  )
                },
                {
                  key: 'showInMenu',
                  title: 'منو',
                  render: (page: PageContent) => (
                    <AdminTableBadge variant={page.showInMenu ? 'success' : 'default'}>
                      {page.showInMenu ? 'نمایش' : 'مخفی'}
                    </AdminTableBadge>
                  )
                },
                {
                  key: 'views',
                  title: 'بازدید',
                  sortable: true,
                  render: (page: PageContent) => (
                    <span className="text-sm text-gray-600">
                      {page.views?.toLocaleString('fa-IR') || '0'}
                    </span>
                  )
                },
                {
                  key: 'updatedAt',
                  title: 'آخرین به‌روزرسانی',
                  sortable: true,
                  render: (page: PageContent) => (
                    <span className="text-sm text-gray-500">
                      {formatPersianDate(page.updatedAt)}
                    </span>
                  )
                }
              ]}
              data={pages}
              rowActions={(page: PageContent, index: number) => (
                <AdminActionMenu
                  actions={[
                    {
                      label: 'مشاهده',
                      onClick: () => window.open(`/pages/${page.slug || page.id}`, '_blank')
                    },
                    {
                      label: 'ویرایش',
                      onClick: () => window.location.href = `/admin/content/pages/${page.id}/edit`
                    },
                    {
                      label: 'کپی',
                      onClick: () => handleDuplicate(page.id)
                    },
                    {
                      label: 'حذف',
                      onClick: () => handleDelete(page.id),
                      variant: 'danger'
                    }
                  ]}
                />
              )}
            />
          )}
        </AdminCard>

        {/* Current Pages */}
        {pages.length > 0 && (
          <AdminCard title="صفحات فعلی">
            <div className="space-y-4">
              {pages.map((page, index) => (
                <motion.div
                  key={page.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <FileText className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{page.title}</h4>
                      {page.excerpt && (
                        <p className="text-sm text-gray-500">{page.excerpt}</p>
                      )}
                      <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                        <span>قالب: {templateLabels[page.template] || page.template}</span>
                        <span>ترتیب: {page.menuOrder}</span>
                        {page.showInMenu && <span className="text-green-600">در منو</span>}
                        <span>{page.views.toLocaleString('fa-IR')} بازدید</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="text-sm text-gray-600 mb-1">
                      {formatPersianDate(page.updatedAt)}
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      page.status === 'published' ? 'bg-green-100 text-green-800' :
                      page.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {page.status === 'published' ? 'منتشر شده' :
                       page.status === 'draft' ? 'پیش‌نویس' :
                       page.status}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </AdminCard>
        )}
      </div>

      <AdminConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setPageToDelete(null);
        }}
        onConfirm={confirmDelete}
        title="حذف صفحه"
        message={`آیا از حذف این صفحه اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
        confirmText="حذف"
        cancelText="انصراف"
        variant="danger"
      />
    </AdminLayout>
  );
};

export default PagesPage;
