import { Product, ProductVariant, ProductVariantGroup, Category } from './index';

// Extended product interface for admin management
export interface AdminProduct extends Product {
  // Admin-specific fields
  sku: string;
  barcode?: string;
  status: ProductStatus;
  visibility: ProductVisibility;
  featured: boolean;
  
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  slug: string;
  
  // Inventory management
  trackInventory: boolean;
  allowBackorder: boolean;
  lowStockThreshold: number;
  
  // Pricing
  costPrice?: number;
  compareAtPrice?: number;
  taxable: boolean;
  taxClass?: string;
  
  // Shipping
  requiresShipping: boolean;
  shippingWeight?: number;
  shippingDimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'cm' | 'inch';
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  
  // Admin metadata
  createdBy: string;
  updatedBy: string;
  
  // Additional admin fields
  tags: string[];
  vendor?: string;
  productType?: string;
  collections?: string[];
  
  // Price history
  priceHistory?: PriceHistoryEntry[];
  
  // Inventory history
  inventoryHistory?: InventoryHistoryEntry[];
}

export type ProductStatus = 'active' | 'draft' | 'archived';
export type ProductVisibility = 'visible' | 'hidden' | 'catalog' | 'search';

export interface PriceHistoryEntry {
  id: string;
  price: number;
  discountedPrice?: number;
  costPrice?: number;
  compareAtPrice?: number;
  changedBy: string;
  changedAt: string;
  reason?: string;
}

export interface InventoryHistoryEntry {
  id: string;
  type: 'adjustment' | 'sale' | 'return' | 'damage' | 'restock';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason?: string;
  changedBy: string;
  changedAt: string;
  orderId?: string;
  variantId?: string;
}

// Extended category interface for admin
export interface AdminCategory extends Category {
  parentId?: number;
  level: number;
  sortOrder: number;
  status: 'active' | 'inactive';
  seoTitle?: string;
  seoDescription?: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  productCount: number;
}

// Product form data interface
export interface ProductFormData {
  // Basic info
  name: string;
  nameEn?: string;
  description: string;
  shortDescription?: string;
  category: string;
  brand: string;
  sku: string;
  barcode?: string;

  // Pricing
  price: number;
  discountedPrice?: number;
  costPrice?: number;
  compareAtPrice?: number;
  taxable: boolean;
  taxClass?: string;

  // Inventory
  stock: number;
  trackInventory: boolean;
  allowBackorder: boolean;
  lowStockThreshold: number;

  // Product details
  benefits: string[];
  ingredients: string[];
  howToUse: string[];
  size?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };

  // Images
  imageSrc: string;
  images: string[];

  // Variants
  hasVariants: boolean;
  variants: ProductVariantGroup[];

  // SEO & Meta
  metaTitle?: string;
  metaDescription?: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords: string[];
  slug: string;

  // Status
  status: ProductStatus;
  visibility: ProductVisibility;
  featured: boolean;
  isNew: boolean;
  isBestSeller: boolean;

  // Product Type
  isDigital: boolean;
  requiresShipping: boolean;
  shippingWeight?: number;
  shippingDimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'cm' | 'inch';
  };

  // Additional
  tags: string[];
  vendor?: string;
  productType?: string;
  collections: string[];
}

// Bulk operations
export interface BulkProductOperation {
  type: 'update_status' | 'update_category' | 'update_price' | 'delete' | 'export';
  productIds: number[];
  data?: any;
}

export interface BulkUpdateData {
  status?: ProductStatus;
  visibility?: ProductVisibility;
  category?: string;
  featured?: boolean;
  priceAdjustment?: {
    type: 'percentage' | 'fixed';
    value: number;
    operation: 'increase' | 'decrease';
  };
  tags?: {
    action: 'add' | 'remove' | 'replace';
    tags: string[];
  };
}

// Product import/export
export interface ProductImportData {
  name: string;
  description: string;
  category: string;
  brand: string;
  sku: string;
  price: number;
  discountedPrice?: number;
  stock: number;
  status: string;
  visibility: string;
  featured: boolean;
  isNew: boolean;
  isBestSeller: boolean;
  benefits: string;
  ingredients: string;
  howToUse: string;
  tags: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
}

export interface ProductExportOptions {
  format: 'csv' | 'excel' | 'json';
  fields: string[];
  filters?: {
    status?: ProductStatus[];
    category?: string[];
    featured?: boolean;
    dateRange?: {
      start: string;
      end: string;
    };
  };
}

// Product analytics
export interface ProductAnalytics {
  productId: number;
  views: number;
  sales: number;
  revenue: number;
  conversionRate: number;
  averageRating: number;
  reviewCount: number;
  returnRate: number;
  profitMargin: number;
  period: {
    start: string;
    end: string;
  };
}

// Product search and filtering for admin
export interface AdminProductFilters {
  search?: string;
  status?: ProductStatus[];
  visibility?: ProductVisibility[];
  category?: string[];
  brand?: string[];
  featured?: boolean;
  isNew?: boolean;
  isBestSeller?: boolean;
  lowStock?: boolean;
  outOfStock?: boolean;
  dateRange?: {
    field: 'createdAt' | 'updatedAt' | 'publishedAt';
    start: string;
    end: string;
  };
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Product validation errors
export interface ProductValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ProductValidationResult {
  isValid: boolean;
  errors: ProductValidationError[];
  warnings?: ProductValidationError[];
}

// Persian messages for product management
export const PERSIAN_PRODUCT_MESSAGES = {
  status: {
    active: 'فعال',
    draft: 'پیش‌نویس',
    archived: 'بایگانی شده'
  },
  visibility: {
    visible: 'قابل مشاهده',
    hidden: 'مخفی',
    catalog: 'فقط در کاتالوگ',
    search: 'فقط در جستجو'
  },
  fields: {
    name: 'نام محصول',
    nameEn: 'نام انگلیسی',
    description: 'توضیحات',
    shortDescription: 'توضیحات کوتاه',
    category: 'دسته‌بندی',
    brand: 'برند',
    sku: 'کد محصول',
    barcode: 'بارکد',
    price: 'قیمت',
    discountedPrice: 'قیمت تخفیف‌دار',
    costPrice: 'قیمت تمام شده',
    compareAtPrice: 'قیمت مقایسه',
    stock: 'موجودی',
    lowStockThreshold: 'حد آستانه موجودی کم',
    benefits: 'مزایا',
    ingredients: 'ترکیبات',
    howToUse: 'نحوه استفاده',
    size: 'اندازه',
    weight: 'وزن',
    dimensions: 'ابعاد',
    metaTitle: 'عنوان متا',
    metaDescription: 'توضیحات متا',
    seoTitle: 'عنوان SEO',
    seoDescription: 'توضیحات SEO',
    seoKeywords: 'کلمات کلیدی',
    slug: 'نامک',
    tags: 'برچسب‌ها',
    vendor: 'تأمین‌کننده',
    productType: 'نوع محصول',
    isDigital: 'محصول دیجیتال',
    shippingWeight: 'وزن ارسال',
    shippingDimensions: 'ابعاد ارسال',
    featured: 'ویژه',
    isNew: 'جدید',
    isBestSeller: 'پرفروش',
    trackInventory: 'پیگیری موجودی',
    allowBackorder: 'اجازه سفارش در صورت عدم موجودی',
    requiresShipping: 'نیاز به ارسال',
    taxable: 'مشمول مالیات',
    imageSrc: 'تصویر اصلی',
    images: 'تصاویر اضافی'
  },
  actions: {
    create: 'ایجاد محصول',
    edit: 'ویرایش محصول',
    delete: 'حذف محصول',
    duplicate: 'کپی محصول',
    export: 'خروجی',
    import: 'ورودی',
    bulkEdit: 'ویرایش گروهی',
    viewDetails: 'مشاهده جزئیات',
    manageInventory: 'مدیریت موجودی',
    priceHistory: 'تاریخچه قیمت',
    inventoryHistory: 'تاریخچه موجودی'
  },
  validation: {
    required: 'این فیلد الزامی است',
    minLength: 'حداقل {min} کاراکتر وارد کنید',
    maxLength: 'حداکثر {max} کاراکتر مجاز است',
    invalidPrice: 'قیمت وارد شده معتبر نیست',
    invalidStock: 'موجودی وارد شده معتبر نیست',
    duplicateSku: 'کد محصول تکراری است',
    invalidSlug: 'نامک وارد شده معتبر نیست',
    invalidImage: 'فرمت تصویر معتبر نیست',
    maxImages: 'حداکثر {max} تصویر مجاز است'
  }
};
