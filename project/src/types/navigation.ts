export interface SubCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
}

export interface MegaMenuCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  subcategories: SubCategory[];
  featured?: {
    title: string;
    description: string;
    imageSrc: string;
    link: string;
  };
}

export interface NavigationItem {
  id: number;
  name: string;
  path?: string;
  type: 'link' | 'megamenu' | 'dropdown';
  megaMenuCategories?: MegaMenuCategory[];
  children?: NavigationItem[];
}

export interface MegaMenuProps {
  categories: MegaMenuCategory[];
  isOpen: boolean;
  onClose: () => void;
}

export interface CategoryDropdownProps {
  category: MegaMenuCategory;
  isOpen: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

export interface SubCategoryListProps {
  subcategories: SubCategory[];
  categorySlug: string;
}
