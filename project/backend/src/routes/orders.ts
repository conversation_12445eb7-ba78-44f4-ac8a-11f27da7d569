import { Router } from 'express';
import { authenticate, adminOnly, optionalAuth } from '../middleware/auth';
import OrderController from '../controllers/orderController';
import PaymentController from '../controllers/paymentController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';

const router = Router();

// Public/Customer routes (require authentication for customers)

// Create new order (authenticated users or guests with email)
router.post('/',
  optionalAuth,
  ValidationSets.createOrder(),
  handleValidationErrors,
  OrderController.createOrder
);

// Get customer's own orders (authenticated users only)
router.get('/my-orders',
  authenticate,
  OrderController.getCustomerOrders
);

// Get order by order number (customer or admin)
router.get('/number/:orderNumber',
  authenticate,
  OrderController.getOrderByNumber
);

// Get order by ID (customer or admin)
router.get('/:id',
  authenticate,
  OrderController.getOrderById
);

// Cancel order (customer or admin)
router.patch('/:id/cancel',
  authenticate,
  OrderController.cancelOrder
);

// Admin-only routes (require admin authentication)

// Get all orders with filtering and pagination (admin only)
router.get('/admin/all',
  authenticate,
  adminOnly,
  OrderController.getOrders
);

// Get order statistics (admin only)
router.get('/admin/statistics',
  authenticate,
  adminOnly,
  OrderController.getOrderStatistics
);

// Update order (admin only)
router.put('/admin/:id',
  authenticate,
  adminOnly,
  ValidationSets.updateOrder(),
  handleValidationErrors,
  OrderController.updateOrder
);

// Update order status (admin only)
router.patch('/admin/:id/status',
  authenticate,
  adminOnly,
  ValidationSets.updateOrderStatus(),
  handleValidationErrors,
  OrderController.updateOrder
);

// Payment routes (admin only)

// Get all payments (admin only)
router.get('/admin/payments',
  authenticate,
  adminOnly,
  PaymentController.getPayments
);

// Get payment statistics (admin only)
router.get('/admin/payments/statistics',
  authenticate,
  adminOnly,
  PaymentController.getPaymentStatistics
);

// Get payment by ID (admin only)
router.get('/admin/payments/:id',
  authenticate,
  adminOnly,
  PaymentController.getPaymentById
);

// Create payment (admin only)
router.post('/admin/payments',
  authenticate,
  adminOnly,
  ValidationSets.createPayment(),
  handleValidationErrors,
  PaymentController.createPayment
);

// Update payment (admin only)
router.put('/admin/payments/:id',
  authenticate,
  adminOnly,
  PaymentController.updatePayment
);

// Process payment (simulate gateway callback)
router.post('/admin/payments/:id/process',
  authenticate,
  adminOnly,
  PaymentController.processPayment
);

// Refund payment (admin only)
router.post('/admin/payments/:id/refund',
  authenticate,
  adminOnly,
  PaymentController.refundPayment
);

// Simulate payment gateway (for testing - admin only)
router.post('/admin/payments/simulate',
  authenticate,
  adminOnly,
  PaymentController.simulatePaymentGateway
);

export default router;
