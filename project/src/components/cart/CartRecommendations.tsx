import React from 'react';
import { motion } from 'framer-motion';
import { Plus, Star, Tag, TrendingUp, Award, Sparkles } from 'lucide-react';
import { CartRecommendation, RecommendationReason } from '../../types/advancedCart';
import { formatPrice } from '../../utils/formatters';
import { PERSIAN_ADVANCED_CART_MESSAGES } from '../../types/advancedCart';

interface CartRecommendationsProps {
  recommendations: CartRecommendation[];
  onAddRecommendation: (recommendationId: string) => void;
  onDismissRecommendation: (recommendationId: string) => void;
  className?: string;
}

const CartRecommendations: React.FC<CartRecommendationsProps> = ({
  recommendations,
  onAddRecommendation,
  onDismissRecommendation,
  className = ''
}) => {
  if (recommendations.length === 0) {
    return null;
  }

  const getReasonIcon = (reason: RecommendationReason) => {
    switch (reason) {
      case 'frequently_bought_together':
        return <TrendingUp className="w-4 h-4" />;
      case 'similar_products':
        return <Star className="w-4 h-4" />;
      case 'category_popular':
        return <Award className="w-4 h-4" />;
      case 'brand_collection':
        return <Sparkles className="w-4 h-4" />;
      case 'loyalty_exclusive':
        return <Award className="w-4 h-4 text-yellow-500" />;
      default:
        return <Plus className="w-4 h-4" />;
    }
  };

  const getReasonText = (reason: RecommendationReason) => {
    switch (reason) {
      case 'frequently_bought_together':
        return PERSIAN_ADVANCED_CART_MESSAGES.recommendations.frequentlyBoughtTogether;
      case 'similar_products':
        return PERSIAN_ADVANCED_CART_MESSAGES.recommendations.similarProducts;
      case 'category_popular':
        return PERSIAN_ADVANCED_CART_MESSAGES.recommendations.categoryPopular;
      case 'brand_collection':
        return PERSIAN_ADVANCED_CART_MESSAGES.recommendations.brandCollection;
      case 'loyalty_exclusive':
        return 'ویژه اعضای باشگاه مشتریان';
      default:
        return 'پیشنهاد ویژه';
    }
  };

  const getReasonColor = (reason: RecommendationReason) => {
    switch (reason) {
      case 'frequently_bought_together':
        return 'text-blue-600 bg-blue-50';
      case 'similar_products':
        return 'text-purple-600 bg-purple-50';
      case 'category_popular':
        return 'text-green-600 bg-green-50';
      case 'brand_collection':
        return 'text-pink-600 bg-pink-50';
      case 'loyalty_exclusive':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className={`bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Sparkles className="w-5 h-5 text-purple-500 ml-2" />
          {PERSIAN_ADVANCED_CART_MESSAGES.recommendations.title}
        </h3>
      </div>

      <div className="space-y-3">
        {recommendations.map((recommendation, index) => (
          <motion.div
            key={recommendation.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start space-x-3 space-x-reverse">
              {/* Product Image */}
              <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                <img
                  src={recommendation.product.imageSrc}
                  alt={recommendation.product.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Product Info */}
              <div className="flex-grow min-w-0">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                    {recommendation.product.name}
                  </h4>
                  <button
                    onClick={() => onDismissRecommendation(recommendation.id)}
                    className="text-gray-400 hover:text-gray-600 p-1 -mt-1 -mr-1"
                  >
                    ×
                  </button>
                </div>

                {/* Reason Badge */}
                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mb-2 ${getReasonColor(recommendation.reason)}`}>
                  {getReasonIcon(recommendation.reason)}
                  <span className="mr-1">{getReasonText(recommendation.reason)}</span>
                </div>

                {/* Price and Discount */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-sm font-semibold text-gray-900">
                      {formatPrice(recommendation.product.discountedPrice || recommendation.product.price)}
                    </span>
                    {recommendation.product.discountedPrice && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(recommendation.product.price)}
                      </span>
                    )}
                  </div>

                  {/* Add Button */}
                  <button
                    onClick={() => onAddRecommendation(recommendation.id)}
                    className="inline-flex items-center px-3 py-1.5 bg-primary-600 text-white text-xs font-medium rounded-md hover:bg-primary-700 transition-colors"
                  >
                    <Plus className="w-3 h-3 ml-1" />
                    {PERSIAN_ADVANCED_CART_MESSAGES.recommendations.addToCart}
                  </button>
                </div>

                {/* Bundle Discount */}
                {recommendation.discount && (
                  <div className="mt-2 p-2 bg-green-50 rounded-md">
                    <div className="flex items-center text-green-700">
                      <Tag className="w-3 h-3 ml-1" />
                      <span className="text-xs font-medium">
                        {recommendation.discount.description}
                      </span>
                    </div>
                  </div>
                )}

                {/* Product Rating */}
                {recommendation.product.rating && (
                  <div className="flex items-center mt-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-3 h-3 ${
                            i < Math.floor(recommendation.product.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 mr-1">
                      ({recommendation.product.reviewCount})
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Confidence Indicator */}
            <div className="mt-3 pt-2 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>میزان تطبیق</span>
                <div className="flex items-center">
                  <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden ml-2">
                    <div
                      className="h-full bg-gradient-to-r from-green-400 to-green-600 transition-all duration-300"
                      style={{ width: `${recommendation.confidence * 100}%` }}
                    />
                  </div>
                  <span className="font-medium">
                    {Math.round(recommendation.confidence * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* View More Link */}
      {recommendations.length > 0 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
            مشاهده پیشنهادات بیشتر
          </button>
        </div>
      )}
    </div>
  );
};

export default CartRecommendations;
