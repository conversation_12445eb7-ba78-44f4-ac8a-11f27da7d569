import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { <PERSON>Left, Save, Eye, Trash2, Copy, Send } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import NewsletterEditor from '../../../components/admin/content/NewsletterEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { NewsletterCampaignFormData, NewsletterCampaign } from '../../../types/adminContent';

const NewsletterEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getNewsletterById, updateNewsletter, deleteNewsletter, duplicateNewsletter, sendNewsletter, loading: contentLoading } = useAdminContent();
  
  const [newsletter, setNewsletter] = useState<NewsletterCampaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [sending, setSending] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [sendModalOpen, setSendModalOpen] = useState(false);

  useEffect(() => {
    const loadNewsletter = async () => {
      if (!id) {
        navigate('/admin/content/newsletter');
        return;
      }

      // Wait for content to be loaded first
      if (contentLoading) {
        return;
      }

      try {
        setLoading(true);
        const newsletterData = await getNewsletterById(id);
        if (newsletterData) {
          setNewsletter(newsletterData);
        } else {
          toast.error('کمپین مورد نظر یافت نشد');
          navigate('/admin/content/newsletter');
        }
      } catch (error) {
        console.error('Error loading newsletter:', error);
        toast.error('خطا در بارگذاری کمپین');
        navigate('/admin/content/newsletter');
      } finally {
        setLoading(false);
      }
    };

    loadNewsletter();
  }, [id, getNewsletterById, navigate, contentLoading]);

  const handleSave = async (formData: NewsletterCampaignFormData) => {
    if (!id) return;

    try {
      setSaving(true);
      await updateNewsletter(id, formData);
      toast.success('کمپین با موفقیت به‌روزرسانی شد');
      navigate('/admin/content/newsletter');
    } catch (error) {
      console.error('Error updating newsletter:', error);
      toast.error('خطا در به‌روزرسانی کمپین');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    try {
      await deleteNewsletter(id);
      toast.success('کمپین با موفقیت حذف شد');
      navigate('/admin/content/newsletter');
    } catch (error) {
      console.error('Error deleting newsletter:', error);
      toast.error('خطا در حذف کمپین');
    }
  };

  const handleDuplicate = async () => {
    if (!id) return;

    try {
      const duplicatedNewsletter = await duplicateNewsletter(id);
      toast.success('کمپین با موفقیت کپی شد');
      navigate(`/admin/content/newsletter/${duplicatedNewsletter.id}/edit`);
    } catch (error) {
      console.error('Error duplicating newsletter:', error);
      toast.error('خطا در کپی کردن کمپین');
    }
  };

  const handleSend = async () => {
    if (!id) return;

    try {
      setSending(true);
      await sendNewsletter(id);
      toast.success('کمپین با موفقیت ارسال شد');
      setSendModalOpen(false);
      // Reload newsletter to get updated status
      const updatedNewsletter = await getNewsletterById(id);
      if (updatedNewsletter) {
        setNewsletter(updatedNewsletter);
      }
    } catch (error) {
      console.error('Error sending newsletter:', error);
      toast.error('خطا در ارسال کمپین');
    } finally {
      setSending(false);
    }
  };

  const handlePreview = () => {
    if (newsletter) {
      // Open preview in new tab
      window.open(`/newsletter/preview/${newsletter.id}`, '_blank');
    }
  };

  const canSend = newsletter && 
    newsletter.status === 'draft' && 
    newsletter.subject && 
    newsletter.content;

  if (loading || contentLoading) {
    return (
      <AdminLayout title="ویرایش کمپین">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  if (!newsletter) {
    return (
      <AdminLayout title="ویرایش کمپین">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            کمپین یافت نشد
          </h3>
          <p className="text-gray-500 mb-6">
            کمپین مورد نظر وجود ندارد یا حذف شده است
          </p>
          <AdminButton 
            variant="primary" 
            onClick={() => navigate('/admin/content/newsletter')}
          >
            بازگشت به لیست کمپین‌ها
          </AdminButton>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="ویرایش کمپین"
      subtitle={`ویرایش کمپین: ${newsletter.title}`}
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="ghost" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/newsletter')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Copy}
            onClick={handleDuplicate}
          >
            کپی
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Eye}
            onClick={handlePreview}
          >
            پیش‌نمایش
          </AdminButton>
          {canSend && (
            <AdminButton 
              variant="success" 
              icon={Send}
              onClick={() => setSendModalOpen(true)}
            >
              ارسال
            </AdminButton>
          )}
          <AdminButton 
            variant="danger" 
            icon={Trash2}
            onClick={() => setDeleteModalOpen(true)}
          >
            حذف
          </AdminButton>
        </div>
      }
    >
      <NewsletterEditor
        initialData={newsletter}
        onSave={handleSave}
        onCancel={() => navigate('/admin/content/newsletter')}
        loading={saving}
        mode="edit"
      />

      <AdminConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="حذف کمپین"
        message={`آیا از حذف کمپین "${newsletter.title}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
        confirmText="حذف"
        cancelText="انصراف"
        variant="danger"
      />

      <AdminConfirmModal
        isOpen={sendModalOpen}
        onClose={() => setSendModalOpen(false)}
        onConfirm={handleSend}
        title="ارسال کمپین"
        message={`آیا از ارسال کمپین "${newsletter.title}" به ${newsletter.recipientSegments?.length || 0} گروه مخاطب اطمینان دارید؟`}
        confirmText="ارسال"
        cancelText="انصراف"
        variant="success"
        loading={sending}
      />
    </AdminLayout>
  );
};

export default NewsletterEditPage;
