import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Lock, CreditCard, FileCheck, Globe, Star } from 'lucide-react';

interface SecurityBadge {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  verificationUrl?: string;
  badgeImage?: string;
  isVerified: boolean;
}

interface SecurityBadgesProps {
  layout?: 'horizontal' | 'vertical' | 'compact';
  showVerificationLinks?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const SecurityBadges: React.FC<SecurityBadgesProps> = ({
  layout = 'horizontal',
  showVerificationLinks = true,
  size = 'medium'
}) => {
  const badges: SecurityBadge[] = [
    {
      id: 'enamad',
      name: 'نماد اعتماد الکترونیک',
      description: 'دارای نماد اعتماد الکترونیک از وزارت صنعت',
      icon: <Shield className="w-6 h-6" />,
      verificationUrl: 'https://trustseal.enamad.ir/',
      isVerified: true
    },
    {
      id: 'samandehi',
      name: 'ساماندهی',
      description: 'ثبت شده در سامانه ساماندهی کسب و کارهای مجازی',
      icon: <Globe className="w-6 h-6" />,
      verificationUrl: 'https://samandehi.ir/',
      isVerified: true
    },
    {
      id: 'ssl',
      name: 'گواهی SSL',
      description: 'اتصال امن و رمزگذاری شده',
      icon: <Lock className="w-6 h-6" />,
      isVerified: true
    },
    {
      id: 'payment',
      name: 'درگاه پرداخت معتبر',
      description: 'پرداخت امن از طریق درگاه‌های معتبر بانکی',
      icon: <CreditCard className="w-6 h-6" />,
      isVerified: true
    },
    {
      id: 'license',
      name: 'مجوز فعالیت',
      description: 'دارای مجوز رسمی فروش محصولات آرایشی بهداشتی',
      icon: <FileCheck className="w-6 h-6" />,
      isVerified: true
    },
    {
      id: 'rating',
      name: 'رتبه‌بندی کیفیت',
      description: 'دارای رتبه عالی در ارزیابی کیفیت خدمات',
      icon: <Star className="w-6 h-6" />,
      isVerified: true
    }
  ];

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'p-2',
          icon: 'w-4 h-4',
          title: 'text-xs',
          description: 'text-xs',
          badge: 'w-12 h-12'
        };
      case 'large':
        return {
          container: 'p-6',
          icon: 'w-8 h-8',
          title: 'text-base',
          description: 'text-sm',
          badge: 'w-20 h-20'
        };
      default:
        return {
          container: 'p-4',
          icon: 'w-6 h-6',
          title: 'text-sm',
          description: 'text-xs',
          badge: 'w-16 h-16'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col space-y-3';
      case 'compact':
        return 'flex flex-wrap justify-center gap-2';
      default:
        return 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3
      }
    }
  };

  const BadgeComponent = ({ badge }: { badge: SecurityBadge }) => {
    const content = (
      <motion.div
        variants={itemVariants}
        className={`
          ${sizeClasses.container}
          bg-white rounded-lg border border-gray-200 
          hover:shadow-lg transition-all duration-200
          ${layout === 'compact' ? 'text-center' : 'flex flex-col items-center text-center'}
          ${badge.isVerified ? 'border-green-200 bg-green-50' : 'border-gray-200'}
          relative group cursor-pointer
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {/* Verification Badge */}
        {badge.isVerified && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="w-3 h-3 text-white" />
          </div>
        )}

        {/* Icon */}
        <div className={`
          ${badge.isVerified ? 'text-green-600' : 'text-gray-500'}
          ${layout === 'compact' ? 'mb-1' : 'mb-2'}
        `}>
          {React.cloneElement(badge.icon as React.ReactElement, {
            className: sizeClasses.icon
          })}
        </div>

        {/* Badge Name */}
        <h4 className={`
          ${sizeClasses.title} font-medium text-text-primary mb-1
          ${layout === 'compact' ? 'hidden' : ''}
        `}>
          {badge.name}
        </h4>

        {/* Description */}
        {layout !== 'compact' && (
          <p className={`${sizeClasses.description} text-text-secondary text-center leading-relaxed`}>
            {badge.description}
          </p>
        )}

        {/* Hover Tooltip for Compact Layout */}
        {layout === 'compact' && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
            {badge.name}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
          </div>
        )}

        {/* Verification Link */}
        {showVerificationLinks && badge.verificationUrl && layout !== 'compact' && (
          <div className="mt-2">
            <span className="text-xs text-blue-600 hover:text-blue-800 transition-colors">
              تأیید اعتبار
            </span>
          </div>
        )}
      </motion.div>
    );

    if (badge.verificationUrl && showVerificationLinks) {
      return (
        <a
          href={badge.verificationUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="block"
        >
          {content}
        </a>
      );
    }

    return content;
  };

  return (
    <div className="security-badges">
      {layout !== 'compact' && (
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">
            نمادهای اعتماد و امنیت
          </h3>
          <p className="text-sm text-text-secondary">
            فروشگاه آرامش پوست دارای تمامی مجوزها و گواهی‌های لازم می‌باشد
          </p>
        </div>
      )}

      <motion.div
        className={getLayoutClasses()}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {badges.map((badge) => (
          <BadgeComponent key={badge.id} badge={badge} />
        ))}
      </motion.div>

      {/* Additional Security Info */}
      {layout !== 'compact' && (
        <div className="mt-6 text-center">
          <p className="text-xs text-text-muted">
            تمامی اطلاعات شخصی و مالی شما با بالاترین استانداردهای امنیتی محافظت می‌شود
          </p>
        </div>
      )}
    </div>
  );
};

export default SecurityBadges;
