/**
 * Helper utility to programmatically log in admin users for testing
 * This should only be used in development environment
 */

import { AdminAuthService } from '../services/adminAuthService';
import { ApiService } from '../services/apiService';

export class AdminLoginHelper {
  /**
   * Programmatically log in with admin credentials
   */
  static async loginAsAdmin(): Promise<boolean> {
    try {
      console.log('🔐 Attempting admin login...');
      
      // Try different credential combinations
      const credentials = [
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'password123' },
        { email: '<EMAIL>', password: 'admin123' },
      ];

      for (const cred of credentials) {
        try {
          console.log(`Trying login with: ${cred.email}`);
          
          const response = await AdminAuthService.login({
            email: cred.email,
            password: cred.password,
            rememberMe: true
          });

          if (response.token) {
            console.log('✅ Admin login successful!');
            console.log('Token stored in browser storage');
            return true;
          }
        } catch (error) {
          console.log(`❌ Login failed for ${cred.email}:`, error);
          continue;
        }
      }

      console.log('❌ All login attempts failed');
      return false;
    } catch (error) {
      console.error('❌ Login helper error:', error);
      return false;
    }
  }

  /**
   * Check if user is currently authenticated
   */
  static isAuthenticated(): boolean {
    const token = ApiService.TokenManager.getToken();
    return !!token;
  }

  /**
   * Get current token
   */
  static getToken(): string | null {
    return ApiService.TokenManager.getToken();
  }

  /**
   * Clear authentication
   */
  static logout(): void {
    ApiService.TokenManager.clearTokens();
    console.log('🚪 Logged out successfully');
  }
}

// Make it available globally for console access
if (typeof window !== 'undefined') {
  (window as any).AdminLoginHelper = AdminLoginHelper;
}
