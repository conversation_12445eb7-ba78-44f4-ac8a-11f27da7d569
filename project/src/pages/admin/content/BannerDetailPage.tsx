import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Calendar,
  ExternalLink,
  Copy,
  BarChart3
} from 'lucide-react';
import { motion } from 'framer-motion';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { Banner } from '../../../types/adminContent';
import { formatContentStatus, formatPersianDate, formatPersianDateTime } from '../../../utils/contentUtils';

const BannerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { banners, duplicateBanner } = useAdminContent();
  const [banner, setBanner] = useState<Banner | null>(null);

  useEffect(() => {
    if (id && banners.length > 0) {
      const foundBanner = banners.find(b => b.id === id);
      if (foundBanner) {
        setBanner(foundBanner);
      } else {
        toast.error('بنر یافت نشد');
        navigate('/admin/content/banners');
      }
    }
  }, [id, banners, navigate]);

  const handleEdit = () => {
    navigate(`/admin/content/banners/${id}/edit`);
  };

  const handleDuplicate = () => {
    // TODO: Implement banner duplication
    toast.success('بنر کپی شد');
  };

  const handlePreview = () => {
    if (banner?.ctaUrl) {
      window.open(banner.ctaUrl, '_blank');
    }
  };

  if (!banner) {
    return (
      <AdminLayout title="جزئیات بنر">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  const performanceMetrics = [
    {
      title: 'کل بازدید',
      value: banner.views.toLocaleString('fa-IR'),
      icon: Eye,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'کل کلیک',
      value: banner.clicks.toLocaleString('fa-IR'),
      icon: MousePointer,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'نرخ کلیک',
      value: banner.views > 0 ? `${((banner.clicks / banner.views) * 100).toFixed(1)}%` : '0%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'تبدیل',
      value: banner.conversions.toLocaleString('fa-IR'),
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <AdminLayout 
      title={banner.title}
      subtitle="جزئیات و آمار بنر"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={() => navigate('/admin/content/banners')}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="outline" 
            icon={Copy}
            onClick={handleDuplicate}
          >
            کپی
          </AdminButton>
          {banner.ctaUrl && (
            <AdminButton 
              variant="outline" 
              icon={ExternalLink}
              onClick={handlePreview}
            >
              پیش‌نمایش
            </AdminButton>
          )}
          <AdminButton 
            variant="primary" 
            icon={Edit}
            onClick={handleEdit}
          >
            ویرایش
          </AdminButton>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <motion.div
                key={metric.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AdminCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${metric.bgColor}`}>
                      <Icon className={`w-6 h-6 ${metric.color}`} />
                    </div>
                  </div>
                </AdminCard>
              </motion.div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Banner Preview */}
          <AdminCard title="پیش‌نمایش بنر">
            <div className="space-y-4">
              <div className="relative rounded-lg overflow-hidden bg-gray-100">
                <img 
                  src={banner.image} 
                  alt={banner.altText}
                  className="w-full h-48 object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/api/placeholder/400/192';
                  }}
                />
                {banner.overlayOpacity && banner.overlayOpacity > 0 && (
                  <div 
                    className="absolute inset-0"
                    style={{ 
                      backgroundColor: `rgba(0,0,0,${banner.overlayOpacity})` 
                    }}
                  />
                )}
                <div className="absolute inset-0 p-6 flex flex-col justify-center">
                  <h3 
                    className="text-xl font-bold mb-2"
                    style={{ color: banner.textColor }}
                  >
                    {banner.title}
                  </h3>
                  {banner.subtitle && (
                    <p 
                      className="text-sm mb-3"
                      style={{ color: banner.textColor }}
                    >
                      {banner.subtitle}
                    </p>
                  )}
                  {banner.ctaText && (
                    <button 
                      className="text-sm px-4 py-2 bg-white text-gray-900 rounded self-start hover:bg-gray-100 transition-colors"
                    >
                      {banner.ctaText}
                    </button>
                  )}
                </div>
              </div>
              
              {banner.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">توضیحات</h4>
                  <p className="text-gray-600">{banner.description}</p>
                </div>
              )}
            </div>
          </AdminCard>

          {/* Banner Information */}
          <AdminCard title="اطلاعات بنر">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">وضعیت</label>
                  <div className="mt-1">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      banner.status === 'published' ? 'bg-green-100 text-green-800' :
                      banner.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                      banner.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {formatContentStatus(banner.status)}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">نوع بنر</label>
                  <div className="mt-1">
                    <span className="text-sm text-gray-900">
                      {banner.type === 'hero' ? 'اصلی' :
                       banner.type === 'promotional' ? 'تبلیغاتی' :
                       banner.type === 'announcement' ? 'اعلان' :
                       banner.type === 'category' ? 'دسته‌بندی' : banner.type}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">موقعیت</label>
                  <div className="mt-1">
                    <span className="text-sm text-gray-900">{banner.position}</span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-600">فعال</label>
                  <div className="mt-1">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      banner.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {banner.isActive ? 'فعال' : 'غیرفعال'}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">صفحات نمایش</label>
                <div className="mt-1">
                  <div className="flex flex-wrap gap-1">
                    {banner.showOnPages.map(page => (
                      <span 
                        key={page}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {page === 'home' ? 'صفحه اصلی' :
                         page === 'products' ? 'محصولات' :
                         page === 'categories' ? 'دسته‌بندی‌ها' :
                         page === 'about' ? 'درباره ما' :
                         page === 'contact' ? 'تماس با ما' : page}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {(banner.startDate || banner.endDate) && (
                <div>
                  <label className="text-sm font-medium text-gray-600">بازه زمانی</label>
                  <div className="mt-1 space-y-1">
                    {banner.startDate && (
                      <div className="text-sm text-gray-900">
                        شروع: {formatPersianDate(banner.startDate)}
                      </div>
                    )}
                    {banner.endDate && (
                      <div className="text-sm text-gray-900">
                        پایان: {formatPersianDate(banner.endDate)}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">ایجاد شده:</span>
                    <span className="text-gray-900">{formatPersianDateTime(banner.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">آخرین به‌روزرسانی:</span>
                    <span className="text-gray-900">{formatPersianDateTime(banner.updatedAt)}</span>
                  </div>
                  {banner.publishedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">منتشر شده:</span>
                      <span className="text-gray-900">{formatPersianDateTime(banner.publishedAt)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AdminCard>
        </div>

        {/* Technical Details */}
        <AdminCard title="جزئیات فنی">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-600">تصویر اصلی</label>
              <div className="mt-1">
                <a 
                  href={banner.image} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 break-all"
                >
                  {banner.image}
                </a>
              </div>
            </div>

            {banner.mobileImage && (
              <div>
                <label className="text-sm font-medium text-gray-600">تصویر موبایل</label>
                <div className="mt-1">
                  <a 
                    href={banner.mobileImage} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 break-all"
                  >
                    {banner.mobileImage}
                  </a>
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-600">متن جایگزین</label>
              <div className="mt-1">
                <span className="text-sm text-gray-900">{banner.altText}</span>
              </div>
            </div>

            {banner.ctaUrl && (
              <div>
                <label className="text-sm font-medium text-gray-600">لینک دکمه عمل</label>
                <div className="mt-1">
                  <a 
                    href={banner.ctaUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 break-all"
                  >
                    {banner.ctaUrl}
                  </a>
                </div>
              </div>
            )}

            {banner.backgroundColor && (
              <div>
                <label className="text-sm font-medium text-gray-600">رنگ پس‌زمینه</label>
                <div className="mt-1 flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: banner.backgroundColor }}
                  />
                  <span className="text-sm text-gray-900">{banner.backgroundColor}</span>
                </div>
              </div>
            )}

            {banner.textColor && (
              <div>
                <label className="text-sm font-medium text-gray-600">رنگ متن</label>
                <div className="mt-1 flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: banner.textColor }}
                  />
                  <span className="text-sm text-gray-900">{banner.textColor}</span>
                </div>
              </div>
            )}
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default BannerDetailPage;
