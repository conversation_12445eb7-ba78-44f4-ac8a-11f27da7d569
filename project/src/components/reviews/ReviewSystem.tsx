import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, MessageSquare, Star, TrendingUp } from 'lucide-react';
import { Product } from '../../types';
import { Review, ReviewFormData, ReviewFilters } from '../../types/review';
import ReviewList from './ReviewList';
import ReviewForm from './ReviewForm';
import { useReviews } from '../../hooks/useReviews';
import toast from 'react-hot-toast';

interface ReviewSystemProps {
  product: Product;
  className?: string;
}

const ReviewSystem: React.FC<ReviewSystemProps> = ({ product, className = '' }) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    reviews,
    reviewStats,
    filters,
    isLoading,
    error,
    isAuthenticated,
    hasUserReviewed,
    addReview,
    voteReview,
    setFilters,
    loadReviews
  } = useReviews(product.id);

  const handleSubmitReview = async (reviewData: ReviewFormData) => {
    setIsSubmitting(true);
    try {
      await addReview(product.id, reviewData);
      setShowReviewForm(false);
      toast.success('نظر شما با موفقیت ثبت شد و پس از تأیید نمایش داده خواهد شد');
    } catch (error: any) {
      toast.error(error.message || 'خطا در ثبت نظر. لطفاً دوباره تلاش کنید');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleShowReviewForm = () => {
    if (!isAuthenticated) {
      toast.error('برای ثبت نظر باید وارد حساب کاربری شوید');
      return;
    }

    if (hasUserReviewed) {
      toast.error('شما قبلاً برای این محصول نظر ثبت کرده‌اید');
      return;
    }

    setShowReviewForm(true);
  };

  const handleVoteReview = async (reviewId: string, isHelpful: boolean) => {
    try {
      await voteReview(reviewId, isHelpful);
      toast.success('رأی شما ثبت شد');
    } catch (error) {
      toast.error('خطا در ثبت رأی');
    }
  };

  const handleReportReview = (reviewId: string) => {
    // In a real app, this would open a report modal or send to moderation
    toast.success('گزارش شما ثبت شد و بررسی خواهد شد');
  };

  const handleShareReview = (reviewId: string) => {
    // In a real app, this would open share options
    const review = reviews.find(r => r.id === reviewId);
    if (review) {
      const shareText = `نظر ${review.userName} درباره ${product.name}`;
      const shareUrl = `${window.location.href}#review-${reviewId}`;
      
      if (navigator.share) {
        navigator.share({
          title: shareText,
          url: shareUrl
        });
      } else {
        navigator.clipboard.writeText(shareUrl);
        toast.success('لینک نظر کپی شد');
      }
    }
  };

  const handleFiltersChange = (newFilters: Partial<ReviewFilters>) => {
    setFilters(newFilters);
  };

  const getRecommendationPercentage = () => {
    if (reviewStats.totalReviews === 0) return 0;
    return Math.round(reviewStats.recommendationPercentage);
  };

  const getVerifiedPercentage = () => {
    if (reviewStats.totalReviews === 0) return 0;
    return Math.round(reviewStats.verifiedPurchasePercentage);
  };

  return (
    <div className={`review-system ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              نظرات و امتیازات
            </h2>
            <p className="text-gray-600">
              تجربه سایر کاربران از استفاده از این محصول
            </p>
          </div>

          <button
            onClick={handleShowReviewForm}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
              !isAuthenticated || hasUserReviewed
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
            disabled={!isAuthenticated || hasUserReviewed}
            title={
              !isAuthenticated
                ? 'برای ثبت نظر وارد شوید'
                : hasUserReviewed
                ? 'شما قبلاً نظر ثبت کرده‌اید'
                : 'ثبت نظر جدید'
            }
          >
            <Plus className="w-4 h-4" />
            {hasUserReviewed ? 'نظر ثبت شده' : 'ثبت نظر'}
          </button>
        </div>

        {/* Quick Stats */}
        {reviewStats.totalReviews > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Star className="w-5 h-5 text-white fill-current" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-700">
                    {reviewStats.averageRating.toLocaleString('fa-IR', { 
                      minimumFractionDigits: 1, 
                      maximumFractionDigits: 1 
                    })}
                  </div>
                  <div className="text-sm text-yellow-600">میانگین امتیاز</div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-700">
                    {getRecommendationPercentage()}%
                  </div>
                  <div className="text-sm text-green-600">پیشنهاد می‌کنند</div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-700">
                    {reviewStats.totalReviews.toLocaleString('fa-IR')}
                  </div>
                  <div className="text-sm text-blue-600">نظر ثبت شده</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Review Form Modal */}
      <AnimatePresence>
        {showReviewForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowReviewForm(false);
              }
            }}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <ReviewForm
                productId={product.id}
                productName={product.name}
                onSubmit={handleSubmitReview}
                onCancel={() => setShowReviewForm(false)}
                isLoading={isSubmitting}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 text-red-700">
            <MessageSquare className="w-5 h-5" />
            <span className="font-medium">خطا در بارگذاری نظرات</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <button
            onClick={() => loadReviews(product.id)}
            className="mt-3 text-sm text-red-700 hover:text-red-800 underline"
          >
            تلاش مجدد
          </button>
        </div>
      )}

      {/* Reviews List */}
      <ReviewList
        reviews={reviews}
        totalReviews={reviewStats.totalReviews}
        averageRating={reviewStats.averageRating}
        ratingDistribution={reviewStats.ratingDistribution}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onVote={handleVoteReview}
        onReport={handleReportReview}
        onShare={handleShareReview}
        isLoading={isLoading}
        hasMore={false} // In a real app, this would be based on pagination
        onLoadMore={() => {}} // In a real app, this would load more reviews
      />

      {/* Empty State for New Products */}
      {!isLoading && reviewStats.totalReviews === 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div className="text-gray-400 mb-4">
            <MessageSquare className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            اولین نظر را شما بنویسید
          </h3>
          <p className="text-gray-600 mb-6">
            تجربه خود از استفاده از این محصول را با سایر کاربران به اشتراک بگذارید
          </p>
          <button
            onClick={handleShowReviewForm}
            className={`px-6 py-3 rounded-lg transition-colors ${
              !isAuthenticated
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
            disabled={!isAuthenticated}
            title={!isAuthenticated ? 'برای ثبت نظر وارد شوید' : 'ثبت اولین نظر'}
          >
            {!isAuthenticated ? 'برای ثبت نظر وارد شوید' : 'ثبت اولین نظر'}
          </button>
        </div>
      )}
    </div>
  );
};

export default ReviewSystem;
