import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const Hero: React.FC = () => {
  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Hero Background */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0"
        style={{ backgroundImage: 'url(https://images.pexels.com/photos/7290697/pexels-photo-7290697.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2)' }}
      >
        <div className="absolute inset-0 bg-gradient-to-l from-primary-900/80 to-secondary-900/60 backdrop-blur-sm"></div>
      </div>

      {/* Content */}
      <div className="container-custom relative z-10 h-full flex items-center">
        <div className="w-full max-w-2xl">
          <motion.h1 
            className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            پوستی درخشان با <span className="text-accent-300">محصولات مراقبتی</span> با کیفیت
          </motion.h1>
          
          <motion.p 
            className="text-white/90 text-lg md:text-xl mb-8 max-w-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            مراقبت از پوست با محصولات طبیعی و مؤثر، برای داشتن پوستی سالم، شاداب و درخشان. با گلورویا، زیبایی طبیعی خود را کشف کنید.
          </motion.p>
          
          <motion.div 
            className="flex flex-col sm:flex-row gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Link 
              to="/products" 
              className="btn-primary"
            >
              مشاهده محصولات
              <ArrowLeft className="h-5 w-5" />
            </Link>
            
            <Link 
              to="/about" 
              className="btn-secondary"
            >
              درباره ما
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Decorative Elements */}
      <motion.div 
        className="absolute bottom-10 right-10 md:right-20 w-32 h-32 md:w-48 md:h-48 rounded-full bg-white/10 backdrop-blur-md z-0"
        animate={{ 
          y: [0, -15, 0],
          opacity: [0.4, 0.6, 0.4]
        }}
        transition={{ 
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div 
        className="absolute top-1/4 right-1/3 w-16 h-16 md:w-24 md:h-24 rounded-full bg-accent-500/20 backdrop-blur-md z-0"
        animate={{ 
          y: [0, -10, 0],
          opacity: [0.3, 0.5, 0.3]
        }}
        transition={{ 
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
    </section>
  );
};

export default Hero;