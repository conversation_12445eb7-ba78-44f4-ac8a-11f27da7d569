import React from 'react';
import { AboutBrandSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';

interface AboutBrandSectionEditorProps {
  section: AboutBrandSectionData;
  onChange: (updates: Partial<AboutBrandSectionData['content']>) => void;
}

const AboutBrandSectionEditor: React.FC<AboutBrandSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof AboutBrandSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <AdminCard title="محتوای بخش درباره برند" icon="Info">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="درباره گلورویا"
            />
          </AdminFormField>

          <AdminFormField label="توضیحات کوتاه">
            <AdminTextarea
              value={section.content.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="توضیح کوتاه درباره برند"
              rows={3}
            />
          </AdminFormField>

          <AdminFormField label="محتوای کامل">
            <AdminTextarea
              value={section.content.content}
              onChange={(e) => handleChange('content', e.target.value)}
              placeholder="محتوای کامل درباره برند و ماموریت شرکت"
              rows={6}
            />
          </AdminFormField>

          <AdminFormField label="موقعیت تصویر">
            <AdminSelect
              value={section.content.imagePosition}
              onChange={(value) => handleChange('imagePosition', value)}
              options={[
                { value: 'left', label: 'سمت راست (RTL)' },
                { value: 'right', label: 'سمت چپ (RTL)' },
                { value: 'top', label: 'بالا' },
                { value: 'bottom', label: 'پایین' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="تراز متن">
            <AdminSelect
              value={section.content.textAlignment}
              onChange={(value) => handleChange('textAlignment', value)}
              options={[
                { value: 'left', label: 'راست (RTL)' },
                { value: 'center', label: 'وسط' },
                { value: 'right', label: 'چپ (LTR)' }
              ]}
            />
          </AdminFormField>
        </div>
      </AdminCard>
    </div>
  );
};

export default AboutBrandSectionEditor;
