import React, { useState, useEffect, ReactNode } from 'react';
import { motion } from 'framer-motion';
import AdminHeader from './AdminHeader';
import AdminSidebar from './AdminSidebar';
import AdminBreadcrumb from './AdminBreadcrumb';
import UserDebugInfo from '../debug/UserDebugInfo';
import { AdminNotificationsProvider } from '../../../contexts/AdminNotificationsContext';

interface AdminLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  showBreadcrumb?: boolean;
  className?: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  title,
  subtitle,
  actions,
  showBreadcrumb = true,
  className = ''
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      if (!mobile) {
        setSidebarOpen(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    if (sidebarOpen && isMobile) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [sidebarOpen, isMobile]);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <AdminNotificationsProvider>
      <div className="min-h-screen bg-gray-50 flex">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <AdminSidebar
            isOpen={true}
            onClose={handleSidebarClose}
            isMobile={false}
          />
        )}

        {/* Mobile Sidebar */}
        {isMobile && (
          <AdminSidebar
            isOpen={sidebarOpen}
            onClose={handleSidebarClose}
            isMobile={true}
          />
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Header */}
          <AdminHeader
            onMenuClick={handleSidebarToggle}
            isMobile={isMobile}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto">
            <div className={`p-6 ${className}`}>
              {/* Page Header */}
              {(title || subtitle || actions || showBreadcrumb) && (
                <div className="mb-6">
                  {/* Breadcrumb */}
                  {showBreadcrumb && (
                    <div className="mb-4">
                      <AdminBreadcrumb />
                    </div>
                  )}

                  {/* Title Section */}
                  {(title || subtitle || actions) && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
                    >
                      <div>
                        {title && (
                          <h1 className="text-2xl font-bold text-gray-900">
                            {title}
                          </h1>
                        )}
                        {subtitle && (
                          <p className="text-gray-600 mt-1">
                            {subtitle}
                          </p>
                        )}
                      </div>
                      {actions && (
                        <div className="flex items-center gap-3">
                          {actions}
                        </div>
                      )}
                    </motion.div>
                  )}
                </div>
              )}

              {/* Page Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                {children}
              </motion.div>
            </div>
          </main>
        </div>

        {/* Debug Info Component */}
        <UserDebugInfo />
      </div>
    </AdminNotificationsProvider>
  );
};

// Specialized layout variants
export const AdminPageLayout: React.FC<{
  title: string;
  subtitle?: string;
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
}> = ({ title, subtitle, children, actions, className }) => {
  return (
    <AdminLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      className={className}
    >
      {children}
    </AdminLayout>
  );
};

export const AdminFormLayout: React.FC<{
  title: string;
  subtitle?: string;
  children: ReactNode;
  onSave?: () => void;
  onCancel?: () => void;
  saveText?: string;
  cancelText?: string;
  loading?: boolean;
  className?: string;
}> = ({
  title,
  subtitle,
  children,
  onSave,
  onCancel,
  saveText = 'ذخیره',
  cancelText = 'انصراف',
  loading = false,
  className
}) => {
  const actions = (
    <div className="flex items-center gap-3">
      {onCancel && (
        <button
          type="button"
          onClick={onCancel}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-admin-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {cancelText}
        </button>
      )}
      {onSave && (
        <button
          type="button"
          onClick={onSave}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-admin-600 border border-transparent rounded-lg hover:bg-admin-700 focus:outline-none focus:ring-2 focus:ring-admin-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              در حال ذخیره...
            </div>
          ) : (
            saveText
          )}
        </button>
      )}
    </div>
  );

  return (
    <AdminLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      className={className}
    >
      {children}
    </AdminLayout>
  );
};

export const AdminListLayout: React.FC<{
  title: string;
  subtitle?: string;
  children: ReactNode;
  onAdd?: () => void;
  addText?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  filters?: ReactNode;
  actions?: ReactNode;
  className?: string;
}> = ({
  title,
  subtitle,
  children,
  onAdd,
  addText = 'افزودن',
  searchValue,
  onSearchChange,
  searchPlaceholder = 'جستجو...',
  filters,
  actions: customActions,
  className
}) => {
  const defaultActions = (
    <div className="flex items-center gap-3">
      {onSearchChange && (
        <div className="relative">
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchValue || ''}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500 focus:border-transparent text-sm"
          />
        </div>
      )}
      {filters}
      {onAdd && (
        <button
          type="button"
          onClick={onAdd}
          className="px-4 py-2 text-sm font-medium text-white bg-admin-600 border border-transparent rounded-lg hover:bg-admin-700 focus:outline-none focus:ring-2 focus:ring-admin-500 focus:ring-offset-2"
        >
          {addText}
        </button>
      )}
    </div>
  );

  const finalActions = customActions || defaultActions;

  return (
    <AdminLayout
      title={title}
      subtitle={subtitle}
      actions={finalActions}
      className={className}
    >
      {children}
    </AdminLayout>
  );
};

export default AdminLayout;
