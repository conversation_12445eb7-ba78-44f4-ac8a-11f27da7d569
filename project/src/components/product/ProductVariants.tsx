import React from 'react';
import { motion } from 'framer-motion';
import { ProductVariantGroup, SelectedVariants, ProductVariant } from '../../types';
import ColorSelector from './ColorSelector';
import SizeSelector from './SizeSelector';

interface ProductVariantsProps {
  variantGroups: ProductVariantGroup[];
  selectedVariants: SelectedVariants;
  onVariantChange: (variantType: string, variant: ProductVariant) => void;
  disabled?: boolean;
}

const ProductVariants: React.FC<ProductVariantsProps> = ({
  variantGroups,
  selectedVariants,
  onVariantChange,
  disabled = false
}) => {
  if (!variantGroups || variantGroups.length === 0) {
    return null;
  }

  const renderVariantGroup = (group: ProductVariantGroup) => {
    const selectedVariant = selectedVariants[group.type];

    switch (group.type) {
      case 'color':
        return (
          <ColorSelector
            key={group.type}
            variants={group.variants}
            selectedVariant={selectedVariant}
            onVariantChange={(variant) => onVariantChange(group.type, variant)}
            disabled={disabled}
            required={group.required}
            groupName={group.name}
          />
        );
      
      case 'size':
      case 'volume':
      case 'weight':
        return (
          <SizeSelector
            key={group.type}
            variants={group.variants}
            selectedVariant={selectedVariant}
            onVariantChange={(variant) => onVariantChange(group.type, variant)}
            disabled={disabled}
            required={group.required}
            groupName={group.name}
            variantType={group.type}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {variantGroups.map((group) => (
        <div key={group.type} className="variant-group">
          {renderVariantGroup(group)}
        </div>
      ))}
    </motion.div>
  );
};

export default ProductVariants;
