// Image optimization and lazy loading utilities

export interface ImageOptimizationOptions {
  quality: number; // 0-100
  format: 'webp' | 'jpeg' | 'png' | 'auto';
  width?: number;
  height?: number;
  blur?: number; // for placeholder
  progressive?: boolean;
  lossless?: boolean;
}

export interface ResponsiveImageSizes {
  mobile: number;
  tablet: number;
  desktop: number;
  wide: number;
}

export interface ImageLoadState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  progress: number;
}

// Default optimization settings
export const DEFAULT_IMAGE_OPTIONS: ImageOptimizationOptions = {
  quality: 80,
  format: 'auto',
  progressive: true,
  lossless: false
};

// Default responsive sizes
export const DEFAULT_RESPONSIVE_SIZES: ResponsiveImageSizes = {
  mobile: 400,
  tablet: 600,
  desktop: 800,
  wide: 1200
};

/**
 * Check if WebP is supported
 */
export const isWebPSupported = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

/**
 * Check if AVIF is supported
 */
export const isAVIFSupported = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
};

/**
 * Get optimal image format based on browser support
 */
export const getOptimalImageFormat = async (originalFormat: string): Promise<string> => {
  // Check for modern format support
  const supportsAVIF = await isAVIFSupported();
  const supportsWebP = await isWebPSupported();
  
  // Return best supported format
  if (supportsAVIF) return 'avif';
  if (supportsWebP) return 'webp';
  
  // Fallback to original or JPEG
  return originalFormat === 'png' ? 'png' : 'jpeg';
};

/**
 * Generate responsive image URLs
 */
export const generateResponsiveImageUrls = (
  baseUrl: string,
  sizes: ResponsiveImageSizes = DEFAULT_RESPONSIVE_SIZES,
  options: Partial<ImageOptimizationOptions> = {}
): Record<string, string> => {
  const opts = { ...DEFAULT_IMAGE_OPTIONS, ...options };
  
  return {
    mobile: generateOptimizedImageUrl(baseUrl, { ...opts, width: sizes.mobile }),
    tablet: generateOptimizedImageUrl(baseUrl, { ...opts, width: sizes.tablet }),
    desktop: generateOptimizedImageUrl(baseUrl, { ...opts, width: sizes.desktop }),
    wide: generateOptimizedImageUrl(baseUrl, { ...opts, width: sizes.wide })
  };
};

/**
 * Generate optimized image URL (mock implementation for development)
 */
export const generateOptimizedImageUrl = (
  baseUrl: string,
  options: Partial<ImageOptimizationOptions> = {}
): string => {
  const opts = { ...DEFAULT_IMAGE_OPTIONS, ...options };
  const params = new URLSearchParams();
  
  if (opts.width) params.append('w', opts.width.toString());
  if (opts.height) params.append('h', opts.height.toString());
  if (opts.quality) params.append('q', opts.quality.toString());
  if (opts.format && opts.format !== 'auto') params.append('f', opts.format);
  if (opts.blur) params.append('blur', opts.blur.toString());
  
  // In a real implementation, this would use a service like Cloudinary, ImageKit, etc.
  // For development, we'll just return the original URL with params
  const separator = baseUrl.includes('?') ? '&' : '?';
  return params.toString() ? `${baseUrl}${separator}${params.toString()}` : baseUrl;
};

/**
 * Generate placeholder image (low quality, blurred)
 */
export const generatePlaceholderImage = (
  baseUrl: string,
  width: number = 40,
  height?: number
): string => {
  return generateOptimizedImageUrl(baseUrl, {
    width,
    height,
    quality: 10,
    blur: 5,
    format: 'jpeg'
  });
};

/**
 * Create base64 placeholder
 */
export const createBase64Placeholder = (width: number, height: number, color: string = '#f3f4f6'): string => {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}"/>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Preload critical images
 */
export const preloadImage = (src: string, crossOrigin?: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => resolve(img);
    img.onerror = reject;
    
    if (crossOrigin) img.crossOrigin = crossOrigin;
    img.src = src;
  });
};

/**
 * Preload multiple images
 */
export const preloadImages = (sources: string[]): Promise<HTMLImageElement[]> => {
  return Promise.all(sources.map(src => preloadImage(src)));
};

/**
 * Calculate image aspect ratio
 */
export const calculateAspectRatio = (width: number, height: number): number => {
  return width / height;
};

/**
 * Get responsive image sizes string for srcset
 */
export const getResponsiveImageSizes = (
  sizes: ResponsiveImageSizes = DEFAULT_RESPONSIVE_SIZES
): string => {
  return [
    `(max-width: 640px) ${sizes.mobile}px`,
    `(max-width: 768px) ${sizes.tablet}px`,
    `(max-width: 1024px) ${sizes.desktop}px`,
    `${sizes.wide}px`
  ].join(', ');
};

/**
 * Generate srcset string for responsive images
 */
export const generateSrcSet = (
  baseUrl: string,
  sizes: ResponsiveImageSizes = DEFAULT_RESPONSIVE_SIZES,
  options: Partial<ImageOptimizationOptions> = {}
): string => {
  const urls = generateResponsiveImageUrls(baseUrl, sizes, options);
  
  return [
    `${urls.mobile} ${sizes.mobile}w`,
    `${urls.tablet} ${sizes.tablet}w`,
    `${urls.desktop} ${sizes.desktop}w`,
    `${urls.wide} ${sizes.wide}w`
  ].join(', ');
};

/**
 * Compress image file (client-side)
 */
export const compressImage = (
  file: File,
  options: Partial<ImageOptimizationOptions> = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const opts = { ...DEFAULT_IMAGE_OPTIONS, ...options };
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate dimensions
      let { width, height } = img;
      
      if (opts.width && opts.height) {
        width = opts.width;
        height = opts.height;
      } else if (opts.width) {
        height = (height * opts.width) / width;
        width = opts.width;
      } else if (opts.height) {
        width = (width * opts.height) / height;
        height = opts.height;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${opts.format === 'auto' ? 'jpeg' : opts.format}`,
        opts.quality / 100
      );
    };
    
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Get image dimensions from URL
 */
export const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = reject;
    img.src = src;
  });
};

/**
 * Check if image is in viewport
 */
export const isImageInViewport = (element: HTMLElement, threshold: number = 0.1): boolean => {
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;
  
  return (
    rect.top < windowHeight * (1 + threshold) &&
    rect.bottom > -windowHeight * threshold &&
    rect.left < windowWidth * (1 + threshold) &&
    rect.right > -windowWidth * threshold
  );
};

/**
 * Progressive image loading with fade-in effect
 */
export const loadImageProgressively = (
  element: HTMLImageElement,
  highQualitySrc: string,
  placeholderSrc?: string
): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Set placeholder if provided
    if (placeholderSrc) {
      element.src = placeholderSrc;
      element.style.filter = 'blur(5px)';
      element.style.transition = 'filter 0.3s ease';
    }
    
    // Create high quality image
    const highQualityImg = new Image();
    
    highQualityImg.onload = () => {
      element.src = highQualitySrc;
      element.style.filter = 'none';
      resolve();
    };
    
    highQualityImg.onerror = reject;
    highQualityImg.src = highQualitySrc;
  });
};
