import { Request, Response, NextFunction } from 'express';
import { UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import AuthService, { RegisterData, LoginData } from '../services/authService';
import EmailService from '../services/emailService';
import { config } from '../config';

export class AuthController {
  // User registration
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password, firstName, lastName, phone }: RegisterData = req.body;

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            ...(phone ? [{ phone }] : []),
          ],
        },
      });

      if (existingUser) {
        if (existingUser.email === email) {
          throw new AppError('این ایمیل قبلاً ثبت شده است', 409, 'EMAIL_EXISTS');
        }
        if (existingUser.phone === phone) {
          throw new AppError('این شماره تلفن قبلاً ثبت شده است', 409, 'PHONE_EXISTS');
        }
      }

      // Hash password
      const hashedPassword = await AuthService.hashPassword(password);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          phone,
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Generate tokens
      const accessToken = AuthService.generateAccessToken(user as any);
      const refreshToken = AuthService.generateRefreshToken(user.id, '');

      // Create session
      const sessionId = await AuthService.createSession(
        user.id,
        refreshToken,
        req.get('User-Agent'),
        req.ip
      );

      // Update refresh token with session ID
      const finalRefreshToken = AuthService.generateRefreshToken(user.id, sessionId);
      await prisma.userSession.update({
        where: { id: sessionId },
        data: { refreshToken: finalRefreshToken },
      });

      // Send welcome email (don't wait for it)
      EmailService.sendWelcomeEmail(email, {
        firstName,
        lastName,
      }).catch(error => {
        logger.error('Welcome email sending failed:', error);
      });

      // Send verification email (don't wait for it)
      const verificationToken = await AuthService.generatePasswordResetToken(user.id);
      const verificationUrl = `${config.cors.origin[0]}/verify-email?token=${verificationToken}`;
      
      EmailService.sendVerificationEmail(email, {
        firstName,
        lastName,
        verificationUrl,
      }).catch(error => {
        logger.error('Verification email sending failed:', error);
      });

      logger.info(`User registered successfully: ${email}`);

      res.status(201).json({
        success: true,
        message: 'ثبت‌نام با موفقیت انجام شد',
        data: {
          user,
          token: accessToken,
          refreshToken: finalRefreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // User login
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password }: LoginData = req.body;

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new AppError('ایمیل یا رمز عبور اشتباه است', 401, 'INVALID_CREDENTIALS');
      }

      // Check user status
      if (user.status !== UserStatus.ACTIVE) {
        throw new AppError('حساب کاربری غیرفعال است', 401, 'ACCOUNT_INACTIVE');
      }

      // Verify password
      const isPasswordValid = await AuthService.verifyPassword(password, user.password);
      if (!isPasswordValid) {
        throw new AppError('ایمیل یا رمز عبور اشتباه است', 401, 'INVALID_CREDENTIALS');
      }

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // Generate tokens
      const accessToken = AuthService.generateAccessToken(user);
      const refreshToken = AuthService.generateRefreshToken(user.id, '');

      // Create session
      const sessionId = await AuthService.createSession(
        user.id,
        refreshToken,
        req.get('User-Agent'),
        req.ip
      );

      // Update refresh token with session ID
      const finalRefreshToken = AuthService.generateRefreshToken(user.id, sessionId);
      await prisma.userSession.update({
        where: { id: sessionId },
        data: { refreshToken: finalRefreshToken },
      });

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      logger.info(`User logged in successfully: ${email}`);

      res.json({
        success: true,
        message: 'ورود با موفقیت انجام شد',
        data: {
          user: userWithoutPassword,
          token: accessToken,
          refreshToken: finalRefreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Token refresh
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        throw new AppError('توکن تازه‌سازی الزامی است', 400, 'REFRESH_TOKEN_REQUIRED');
      }

      // Verify refresh token
      const payload = AuthService.verifyRefreshToken(refreshToken);

      // Validate session
      const isSessionValid = await AuthService.validateSession(payload.sessionId, refreshToken);
      if (!isSessionValid) {
        throw new AppError('جلسه کاربری نامعتبر است', 401, 'INVALID_SESSION');
      }

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
        },
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new AppError('کاربر یافت نشد یا غیرفعال است', 401, 'USER_NOT_FOUND');
      }

      // Generate new access token
      const newAccessToken = AuthService.generateAccessToken(user as any);

      res.json({
        success: true,
        message: 'توکن با موفقیت تازه‌سازی شد',
        data: {
          token: newAccessToken,
          expiresIn: config.jwt.expiresIn,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // User logout
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;
      const userId = req.user?.id;

      if (refreshToken) {
        try {
          const payload = AuthService.verifyRefreshToken(refreshToken);
          await AuthService.invalidateSession(payload.sessionId);
        } catch (error) {
          // Continue with logout even if token verification fails
          logger.warn('Refresh token verification failed during logout:', error);
        }
      }

      logger.info(`User logged out: ${userId}`);

      res.json({
        success: true,
        message: 'خروج با موفقیت انجام شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Logout from all devices
  static async logoutAll(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      // Invalidate all user sessions
      await AuthService.invalidateAllUserSessions(userId);

      logger.info(`User logged out from all devices: ${userId}`);

      res.json({
        success: true,
        message: 'خروج از همه دستگاه‌ها با موفقیت انجام شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Request password reset
  static async requestPasswordReset(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          status: true,
        },
      });

      // Always return success to prevent email enumeration
      const successResponse = {
        success: true,
        message: 'اگر ایمیل معتبر باشد، لینک بازیابی رمز عبور ارسال خواهد شد',
      };

      if (!user || user.status !== UserStatus.ACTIVE) {
        res.json(successResponse);
        return;
      }

      // Generate reset token
      const resetToken = await AuthService.generatePasswordResetToken(user.id);
      const resetUrl = `${config.cors.origin[0]}/reset-password?token=${resetToken}`;

      // Send reset email (don't wait for it)
      EmailService.sendPasswordResetEmail(email, {
        firstName: user.firstName,
        lastName: user.lastName,
        resetUrl,
      }).catch(error => {
        logger.error('Password reset email sending failed:', error);
      });

      logger.info(`Password reset requested for: ${email}`);

      res.json(successResponse);
    } catch (error) {
      next(error);
    }
  }

  // Reset password
  static async resetPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token, password } = req.body;

      // Verify reset token
      const userId = await AuthService.verifyPasswordResetToken(token);

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new AppError('کاربر یافت نشد یا غیرفعال است', 400, 'USER_NOT_FOUND');
      }

      // Hash new password
      const hashedPassword = await AuthService.hashPassword(password);

      // Update password
      await prisma.user.update({
        where: { id: userId },
        data: { password: hashedPassword },
      });

      // Mark reset token as used
      await AuthService.markPasswordResetTokenUsed(token);

      // Invalidate all user sessions
      await AuthService.invalidateAllUserSessions(userId);

      logger.info(`Password reset completed for user: ${userId}`);

      res.json({
        success: true,
        message: 'رمز عبور با موفقیت تغییر یافت',
      });
    } catch (error) {
      next(error);
    }
  }

  // Verify email
  static async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.body;

      // Verify token (reusing password reset token logic)
      const userId = await AuthService.verifyPasswordResetToken(token);

      // Update user email verification status
      await prisma.user.update({
        where: { id: userId },
        data: {
          isEmailVerified: true,
          emailVerifiedAt: new Date(),
        },
      });

      // Mark token as used
      await AuthService.markPasswordResetTokenUsed(token);

      logger.info(`Email verified for user: ${userId}`);

      res.json({
        success: true,
        message: 'ایمیل با موفقیت تأیید شد',
      });
    } catch (error) {
      next(error);
    }
  }

  // Get current user
  static async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new AppError('کاربر احراز هویت نشده است', 401, 'UNAUTHENTICATED');
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new AppError('کاربر یافت نشد', 404, 'USER_NOT_FOUND');
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default AuthController;
