import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Trash2,
  UserCog,
  Shield,
  AlertTriangle
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminUserTable from '../../../components/admin/users/AdminUserTable';
import AdminButton from '../../../components/admin/common/AdminButton';
import AdminCard from '../../../components/admin/common/AdminCard';
import { AdminConfirmModal, AdminFormModal } from '../../../components/admin/common/AdminModal';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { AdminUser, ADMIN_ROLE_PERMISSIONS } from '../../../types/admin';

import toast from 'react-hot-toast';
import SEOHead from '../../../components/seo/SEOHead';

const AdminUsersPage: React.FC = () => {
  const { user: currentUser, checkPermission } = useAdminAuth();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToEdit, setUserToEdit] = useState<AdminUser | null>(null);
  const [userToDelete, setUserToDelete] = useState<AdminUser | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: 'viewer' as AdminUser['role'],
    department: '',
    isActive: true
  });
  const [formLoading, setFormLoading] = useState(false);

  // Check permissions
  const canCreate = checkPermission('users', 'create');
  const canUpdate = checkPermission('users', 'update');
  const canDelete = checkPermission('users', 'delete');

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);

      // TODO: Replace with actual API call
      // const response = await AdminUserService.getUsers();
      // setUsers(response.data);

      // For now, set empty array since we removed mock data
      setUsers([]);
      toast.error('API کاربران هنوز پیاده‌سازی نشده است');
    } catch (error) {
      toast.error('خطا در بارگذاری کاربران');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    setSelectedUsers(
      selectedUsers.length === users.length 
        ? [] 
        : users.map(user => user.id)
    );
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      role: 'viewer',
      department: '',
      isActive: true
    });
  };

  const handleViewUser = (user: AdminUser) => {
    // Navigate to user details or open details modal
    console.log('View user:', user);
  };

  const handleEditUser = (user: AdminUser) => {
    if (!canUpdate) {
      toast.error('شما مجوز ویرایش کاربران را ندارید');
      return;
    }
    setUserToEdit(user);
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      department: user.department || '',
      isActive: user.isActive
    });
    setShowEditModal(true);
  };

  const handleToggleStatus = async (user: AdminUser) => {
    if (!canUpdate) {
      toast.error('شما مجوز تغییر وضعیت کاربران را ندارید');
      return;
    }

    if (user.role === 'super_admin' && user.id !== currentUser?.id) {
      toast.error('نمی‌توانید وضعیت مدیر کل را تغییر دهید');
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(prev => prev.map(u => 
        u.id === user.id 
          ? { ...u, isActive: !u.isActive }
          : u
      ));

      toast.success(
        user.isActive 
          ? 'کاربر با موفقیت غیرفعال شد'
          : 'کاربر با موفقیت فعال شد'
      );
    } catch (error) {
      toast.error('خطا در تغییر وضعیت کاربر');
    }
  };

  const handleDeleteUser = (user: AdminUser) => {
    if (!canDelete) {
      toast.error('شما مجوز حذف کاربران را ندارید');
      return;
    }

    if (user.role === 'super_admin') {
      toast.error('نمی‌توانید مدیر کل را حذف کنید');
      return;
    }

    if (user.id === currentUser?.id) {
      toast.error('نمی‌توانید خودتان را حذف کنید');
      return;
    }

    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(prev => prev.filter(u => u.id !== userToDelete.id));
      setShowDeleteModal(false);
      setUserToDelete(null);
      
      toast.success('کاربر با موفقیت حذف شد');
    } catch (error) {
      toast.error('خطا در حذف کاربر');
    }
  };

  const handleCreateUser = async () => {
    if (!canCreate) {
      toast.error('شما مجوز ایجاد کاربر را ندارید');
      return;
    }

    // Validate form
    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      toast.error('لطفاً تمام فیلدهای ضروری را پر کنید');
      return;
    }

    // Check if email already exists
    if (users.some(u => u.email === formData.email)) {
      toast.error('این ایمیل قبلاً استفاده شده است');
      return;
    }

    try {
      setFormLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newUser: AdminUser = {
        id: `admin-${Date.now()}`,
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        permissions: ADMIN_ROLE_PERMISSIONS[formData.role],
        isActive: formData.isActive,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        twoFactorEnabled: false,
        department: formData.department || undefined
      };

      setUsers(prev => [...prev, newUser]);
      setShowCreateModal(false);
      resetForm();

      toast.success('کاربر جدید با موفقیت ایجاد شد');
    } catch (error) {
      toast.error('خطا در ایجاد کاربر');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!canUpdate || !userToEdit) {
      toast.error('شما مجوز ویرایش کاربران را ندارید');
      return;
    }

    // Validate form
    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      toast.error('لطفاً تمام فیلدهای ضروری را پر کنید');
      return;
    }

    // Check if email already exists (excluding current user)
    if (users.some(u => u.email === formData.email && u.id !== userToEdit.id)) {
      toast.error('این ایمیل قبلاً استفاده شده است');
      return;
    }

    try {
      setFormLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setUsers(prev => prev.map(u =>
        u.id === userToEdit.id
          ? {
              ...u,
              firstName: formData.firstName,
              lastName: formData.lastName,
              email: formData.email,
              role: formData.role,
              permissions: ADMIN_ROLE_PERMISSIONS[formData.role],
              isActive: formData.isActive,
              department: formData.department || undefined,
              updatedAt: new Date().toISOString()
            }
          : u
      ));

      setShowEditModal(false);
      setUserToEdit(null);
      resetForm();

      toast.success('کاربر با موفقیت ویرایش شد');
    } catch (error) {
      toast.error('خطا در ویرایش کاربر');
    } finally {
      setFormLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (!canDelete) {
      toast.error('شما مجوز حذف کاربران را ندارید');
      return;
    }

    if (selectedUsers.length === 0) {
      toast.error('هیچ کاربری انتخاب نشده است');
      return;
    }

    // Check if any selected user is super_admin or current user
    const selectedUserObjects = users.filter(u => selectedUsers.includes(u.id));
    const hasSuperAdmin = selectedUserObjects.some(u => u.role === 'super_admin');
    const hasCurrentUser = selectedUsers.includes(currentUser?.id || '');

    if (hasSuperAdmin) {
      toast.error('نمی‌توانید مدیر کل را حذف کنید');
      return;
    }

    if (hasCurrentUser) {
      toast.error('نمی‌توانید خودتان را حذف کنید');
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setUsers(prev => prev.filter(u => !selectedUsers.includes(u.id)));
      setSelectedUsers([]);

      toast.success(`${selectedUsers.length} کاربر با موفقیت حذف شدند`);
    } catch (error) {
      toast.error('خطا در حذف کاربران');
    }
  };

  const handleFilter = () => {
    toast.info('فیلتر در نسخه آینده اضافه خواهد شد');
  };

  const handleExport = () => {
    toast.info('خروجی Excel در نسخه آینده اضافه خواهد شد');
  };

  const filteredUsers = users.filter(user => {
    if (!searchValue) return true;
    
    const searchLower = searchValue.toLowerCase();
    return (
      user.firstName.toLowerCase().includes(searchLower) ||
      user.lastName.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.department?.toLowerCase().includes(searchLower)
    );
  });

  return (
    <>
      <SEOHead 
        title="مدیریت کاربران مدیر - پنل مدیریت"
        description="مدیریت کاربران مدیر سیستم"
      />
      
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <UserCog className="w-8 h-8 text-admin-600" />
                مدیریت کاربران مدیر
              </h1>
              <p className="text-gray-600 mt-1">
                مدیریت کاربران مدیر سیستم و تنظیم دسترسی‌ها
              </p>
            </div>
            
            {canCreate && (
              <AdminButton
                variant="primary"
                icon={Plus}
                onClick={() => setShowCreateModal(true)}
              >
                افزودن کاربر جدید
              </AdminButton>
            )}
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <UserCog className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">کل کاربران</p>
                  <p className="text-2xl font-bold text-gray-900">{users.length}</p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Shield className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">کاربران فعال</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.isActive).length}
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">کاربران غیرفعال</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => !u.isActive).length}
                  </p>
                </div>
              </div>
            </AdminCard>

            <AdminCard className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <UserCog className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">مدیران کل</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.role === 'super_admin').length}
                  </p>
                </div>
              </div>
            </AdminCard>
          </div>

          {/* Filters and Actions */}
          <AdminCard>
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="جستجو در کاربران..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-admin-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <AdminButton variant="outline" icon={Filter} size="sm" onClick={handleFilter}>
                  فیلتر
                </AdminButton>
                <AdminButton variant="outline" icon={Download} size="sm" onClick={handleExport}>
                  خروجی Excel
                </AdminButton>
                {selectedUsers.length > 0 && (
                  <AdminButton
                    variant="danger"
                    icon={Trash2}
                    size="sm"
                    onClick={handleBulkDelete}
                  >
                    حذف انتخاب شده ({selectedUsers.length})
                  </AdminButton>
                )}
              </div>
            </div>
          </AdminCard>

          {/* Users Table */}
          <AdminCard>
            <AdminUserTable
              users={filteredUsers}
              loading={loading}
              selectedUsers={selectedUsers}
              onSelectUser={handleSelectUser}
              onSelectAll={handleSelectAll}
              onViewUser={handleViewUser}
              onEditUser={handleEditUser}
              onToggleStatus={handleToggleStatus}
              onDeleteUser={handleDeleteUser}
            />
          </AdminCard>
        </div>

        {/* Create User Modal */}
        <AdminFormModal
          isOpen={showCreateModal}
          onClose={() => {
            setShowCreateModal(false);
            resetForm();
          }}
          onSubmit={handleCreateUser}
          title="افزودن کاربر جدید"
          subtitle="اطلاعات کاربر مدیر جدید را وارد کنید"
          submitText="ایجاد کاربر"
          loading={formLoading}
          size="lg"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام *
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام کاربر"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام خانوادگی *
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام خانوادگی کاربر"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ایمیل *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نقش *
              </label>
              <select
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as AdminUser['role'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                required
              >
                <option value="viewer">مشاهده‌گر</option>
                <option value="moderator">ناظر</option>
                <option value="admin">مدیر</option>
                {currentUser?.role === 'super_admin' && (
                  <option value="super_admin">مدیر کل</option>
                )}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                بخش
              </label>
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: فروش، مدیریت، محتوا"
              />
            </div>

            <div className="md:col-span-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-admin-600 border-gray-300 rounded focus:ring-admin-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  کاربر فعال باشد
                </span>
              </label>
            </div>
          </div>
        </AdminFormModal>

        {/* Edit User Modal */}
        <AdminFormModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setUserToEdit(null);
            resetForm();
          }}
          onSubmit={handleUpdateUser}
          title="ویرایش کاربر"
          subtitle={`ویرایش اطلاعات ${userToEdit?.firstName} ${userToEdit?.lastName}`}
          submitText="ذخیره تغییرات"
          loading={formLoading}
          size="lg"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام *
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام کاربر"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نام خانوادگی *
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="نام خانوادگی کاربر"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ایمیل *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نقش *
              </label>
              <select
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as AdminUser['role'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                required
                disabled={userToEdit?.role === 'super_admin' && currentUser?.role !== 'super_admin'}
              >
                <option value="viewer">مشاهده‌گر</option>
                <option value="moderator">ناظر</option>
                <option value="admin">مدیر</option>
                {currentUser?.role === 'super_admin' && (
                  <option value="super_admin">مدیر کل</option>
                )}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                بخش
              </label>
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
                placeholder="مثال: فروش، مدیریت، محتوا"
              />
            </div>

            <div className="md:col-span-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-admin-600 border-gray-300 rounded focus:ring-admin-500"
                  disabled={userToEdit?.id === currentUser?.id}
                />
                <span className="text-sm font-medium text-gray-700">
                  کاربر فعال باشد
                  {userToEdit?.id === currentUser?.id && (
                    <span className="text-xs text-gray-500 mr-2">(نمی‌توانید خودتان را غیرفعال کنید)</span>
                  )}
                </span>
              </label>
            </div>
          </div>
        </AdminFormModal>

        {/* Delete Confirmation Modal */}
        <AdminConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDeleteUser}
          title="حذف کاربر"
          message={`آیا از حذف کاربر "${userToDelete?.firstName} ${userToDelete?.lastName}" اطمینان دارید؟`}
          confirmText="حذف"
          cancelText="انصراف"
          variant="danger"
        />
      </AdminLayout>
    </>
  );
};

export default AdminUsersPage;
