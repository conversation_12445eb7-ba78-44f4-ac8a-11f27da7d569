import { 
  Loyalty<PERSON><PERSON>ber, 
  <PERSON><PERSON><PERSON><PERSON>ier, 
  Loyalty<PERSON><PERSON>ard, 
  PointTransaction, 
  LoyaltyBenefit 
} from './loyalty';

// Admin-specific loyalty interfaces
export interface AdminLoyaltyMember extends LoyaltyMember {
  // Enhanced admin fields
  customerInfo: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    registrationDate: string;
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: string;
  };
  adminNotes: AdminLoyaltyNote[];
  flags: LoyaltyMemberFlag[];
  analytics: {
    engagementScore: number; // 0-100
    redemptionRate: number; // percentage
    pointsEarnedLastMonth: number;
    pointsRedeemedLastMonth: number;
    tierUpgradeDate?: string;
    riskScore: number; // 0-100 (fraud detection)
  };
  audit: {
    createdBy?: string;
    lastModifiedBy?: string;
    lastModifiedAt?: string;
    statusHistory: LoyaltyStatusChange[];
  };
}

export interface AdminLoyaltyNote {
  id: string;
  memberId: string;
  adminId: string;
  adminName: string;
  note: string;
  type: 'general' | 'warning' | 'escalation' | 'resolution';
  createdAt: string;
  isPrivate: boolean;
}

export interface LoyaltyMemberFlag {
  id: string;
  type: 'fraud_risk' | 'high_value' | 'churned' | 'vip' | 'problematic' | 'inactive';
  reason: string;
  addedBy: string;
  addedAt: string;
  isActive: boolean;
  expiresAt?: string;
}

export interface LoyaltyStatusChange {
  id: string;
  memberId: string;
  fromStatus: string;
  toStatus: string;
  reason: string;
  changedBy: string;
  changedAt: string;
  metadata?: Record<string, any>;
}

export interface AdminLoyaltyTier extends LoyaltyTier {
  // Enhanced admin fields
  memberCount: number;
  averageSpend: number;
  retentionRate: number;
  upgradeRate: number;
  downgradeRate: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  analytics: {
    totalMembers: number;
    newMembersThisMonth: number;
    totalPointsEarned: number;
    totalPointsRedeemed: number;
    averageEngagement: number;
  };
}

export interface AdminLoyaltyReward extends LoyaltyReward {
  // Enhanced admin fields
  redemptionCount: number;
  totalCost: number;
  profitMargin: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  analytics: {
    totalRedemptions: number;
    redemptionsThisMonth: number;
    averageRating: number;
    customerSatisfaction: number;
    costPerRedemption: number;
  };
  inventory?: {
    totalStock: number;
    availableStock: number;
    reservedStock: number;
    lowStockThreshold: number;
  };
}

export interface AdminPointTransaction extends PointTransaction {
  // Enhanced admin fields
  customerInfo: {
    name: string;
    email: string;
    tier: string;
  };
  adminInfo?: {
    processedBy: string;
    processedAt: string;
    reason?: string;
    notes?: string;
  };
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'reversed';
  metadata: {
    source: 'purchase' | 'manual' | 'bonus' | 'referral' | 'review' | 'birthday' | 'redemption';
    ipAddress?: string;
    userAgent?: string;
    location?: string;
  };
}

// Filter and search interfaces
export interface LoyaltyMemberFilters {
  search?: string;
  tier?: string[];
  status?: ('active' | 'inactive' | 'suspended')[];
  joinDateFrom?: string;
  joinDateTo?: string;
  pointsMin?: number;
  pointsMax?: number;
  totalSpentMin?: number;
  totalSpentMax?: number;
  flags?: LoyaltyMemberFlag['type'][];
  engagementScore?: {
    min: number;
    max: number;
  };
  lastActivityFrom?: string;
  lastActivityTo?: string;
}

export interface LoyaltyRewardFilters {
  search?: string;
  type?: LoyaltyReward['type'][];
  status?: ('active' | 'inactive' | 'out_of_stock')[];
  pointsCostMin?: number;
  pointsCostMax?: number;
  createdFrom?: string;
  createdTo?: string;
  redemptionCountMin?: number;
  redemptionCountMax?: number;
}

export interface PointTransactionFilters {
  search?: string;
  type?: PointTransaction['type'][];
  status?: AdminPointTransaction['status'][];
  dateFrom?: string;
  dateTo?: string;
  pointsMin?: number;
  pointsMax?: number;
  source?: AdminPointTransaction['metadata']['source'][];
  memberId?: string;
}

// Analytics interfaces
export interface LoyaltyProgramAnalytics {
  overview: {
    totalMembers: number;
    activeMembers: number;
    newMembersThisMonth: number;
    memberGrowthRate: number;
    totalPointsIssued: number;
    totalPointsRedeemed: number;
    redemptionRate: number;
    averagePointsPerMember: number;
  };
  tiers: {
    [tierId: string]: {
      memberCount: number;
      percentage: number;
      averageSpend: number;
      retentionRate: number;
    };
  };
  engagement: {
    averageEngagementScore: number;
    activeMembers30Days: number;
    pointsEarnedThisMonth: number;
    pointsRedeemedThisMonth: number;
    topRewards: Array<{
      rewardId: string;
      title: string;
      redemptions: number;
    }>;
  };
  financial: {
    totalLiability: number; // Total points value
    monthlyLiabilityCost: number;
    averageRedemptionValue: number;
    programROI: number;
    costPerMember: number;
  };
  trends: {
    membershipGrowth: Array<{
      month: string;
      newMembers: number;
      totalMembers: number;
    }>;
    pointsActivity: Array<{
      month: string;
      earned: number;
      redeemed: number;
    }>;
    tierDistribution: Array<{
      tier: string;
      count: number;
      percentage: number;
    }>;
  };
}

// Bulk operations
export interface BulkLoyaltyOperation {
  type: 'adjust_points' | 'change_tier' | 'add_flag' | 'remove_flag' | 'suspend' | 'activate';
  memberIds: string[];
  data: {
    points?: number;
    reason?: string;
    tierId?: string;
    flagType?: LoyaltyMemberFlag['type'];
    flagReason?: string;
    notes?: string;
  };
  performedBy: string;
  performedAt: string;
}

// Form data interfaces
export interface LoyaltyTierFormData {
  name: string;
  persianName: string;
  level: number;
  minPoints: number;
  maxPoints?: number;
  color: string;
  icon: string;
  benefits: string[];
  discountPercentage: number;
  freeShippingThreshold: number;
  birthdayBonus: number;
  description: string;
  isActive: boolean;
}

export interface LoyaltyRewardFormData {
  title: string;
  description: string;
  pointsCost: number;
  type: LoyaltyReward['type'];
  value: number;
  isAvailable: boolean;
  stock?: number;
  image?: string;
  validUntil?: string;
  terms: string[];
  targetTiers?: string[];
  maxRedemptionsPerMember?: number;
}

export interface PointAdjustmentFormData {
  memberId: string;
  points: number;
  type: 'add' | 'subtract';
  reason: string;
  description: string;
  expiryDate?: string;
  notifyMember: boolean;
}

// Constants
export const LOYALTY_MEMBER_STATUSES = {
  active: 'فعال',
  inactive: 'غیرفعال',
  suspended: 'معلق'
} as const;

export const LOYALTY_MEMBER_FLAGS = {
  fraud_risk: 'ریسک تقلب',
  high_value: 'مشتری ارزشمند',
  churned: 'ترک کرده',
  vip: 'وی‌آی‌پی',
  problematic: 'مشکل‌دار',
  inactive: 'غیرفعال'
} as const;

export const POINT_TRANSACTION_SOURCES = {
  purchase: 'خرید',
  manual: 'دستی',
  bonus: 'جایزه',
  referral: 'معرفی',
  review: 'نظر',
  birthday: 'تولد',
  redemption: 'استفاده'
} as const;

export const POINT_TRANSACTION_STATUSES = {
  pending: 'در انتظار',
  completed: 'تکمیل شده',
  failed: 'ناموفق',
  cancelled: 'لغو شده',
  reversed: 'برگشت داده شده'
} as const;
