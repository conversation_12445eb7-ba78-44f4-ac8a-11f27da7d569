// Performance monitoring and optimization utilities

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  bundleSize: number;
  imageLoadTime: number;
  apiResponseTime: number;
}

export interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  type: 'navigation' | 'resource' | 'measure' | 'mark';
}

export interface PerformanceBudget {
  maxLoadTime: number; // milliseconds
  maxBundleSize: number; // bytes
  maxImageSize: number; // bytes
  maxApiResponseTime: number; // milliseconds
}

// Default performance budget
export const DEFAULT_PERFORMANCE_BUDGET: PerformanceBudget = {
  maxLoadTime: 3000, // 3 seconds
  maxBundleSize: 1024 * 1024, // 1MB
  maxImageSize: 500 * 1024, // 500KB
  maxApiResponseTime: 1000 // 1 second
};

/**
 * Measure page load performance
 */
export const measurePageLoad = (): PerformanceMetrics => {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  const memory = (performance as any).memory;
  
  return {
    loadTime: navigation.loadEventEnd - navigation.navigationStart,
    renderTime: navigation.domContentLoadedEventEnd - navigation.navigationStart,
    interactionTime: navigation.domInteractive - navigation.navigationStart,
    memoryUsage: memory ? memory.usedJSHeapSize : 0,
    bundleSize: 0, // Will be calculated separately
    imageLoadTime: 0, // Will be calculated separately
    apiResponseTime: 0 // Will be calculated separately
  };
};

/**
 * Measure component render time
 */
export const measureComponentRender = (componentName: string, renderFn: () => void): number => {
  const startTime = performance.now();
  renderFn();
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // Mark the performance entry
  performance.mark(`${componentName}-render-start`);
  performance.mark(`${componentName}-render-end`);
  performance.measure(`${componentName}-render`, `${componentName}-render-start`, `${componentName}-render-end`);
  
  return duration;
};

/**
 * Measure API response time
 */
export const measureApiCall = async <T>(
  apiCall: () => Promise<T>,
  apiName: string
): Promise<{ data: T; duration: number }> => {
  const startTime = performance.now();
  
  try {
    const data = await apiCall();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Mark the performance entry
    performance.mark(`${apiName}-api-start`);
    performance.mark(`${apiName}-api-end`);
    performance.measure(`${apiName}-api`, `${apiName}-api-start`, `${apiName}-api-end`);
    
    return { data, duration };
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Mark failed API call
    performance.mark(`${apiName}-api-error`);
    
    throw error;
  }
};

/**
 * Get performance entries by type
 */
export const getPerformanceEntries = (type?: string): PerformanceEntry[] => {
  const entries = type ? performance.getEntriesByType(type) : performance.getEntries();
  
  return entries.map(entry => ({
    name: entry.name,
    startTime: entry.startTime,
    duration: entry.duration,
    type: entry.entryType as any
  }));
};

/**
 * Calculate bundle size from resource entries
 */
export const calculateBundleSize = (): number => {
  const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  
  return resourceEntries
    .filter(entry => entry.name.includes('.js') || entry.name.includes('.css'))
    .reduce((total, entry) => total + (entry.transferSize || 0), 0);
};

/**
 * Calculate image load performance
 */
export const calculateImageLoadTime = (): number => {
  const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  
  const imageEntries = resourceEntries.filter(entry => 
    entry.name.includes('.jpg') || 
    entry.name.includes('.jpeg') || 
    entry.name.includes('.png') || 
    entry.name.includes('.webp') || 
    entry.name.includes('.svg')
  );
  
  if (imageEntries.length === 0) return 0;
  
  const totalLoadTime = imageEntries.reduce((total, entry) => total + entry.duration, 0);
  return totalLoadTime / imageEntries.length;
};

/**
 * Check if performance budget is met
 */
export const checkPerformanceBudget = (
  metrics: PerformanceMetrics,
  budget: PerformanceBudget = DEFAULT_PERFORMANCE_BUDGET
): { passed: boolean; violations: string[] } => {
  const violations: string[] = [];
  
  if (metrics.loadTime > budget.maxLoadTime) {
    violations.push(`Load time exceeded: ${metrics.loadTime}ms > ${budget.maxLoadTime}ms`);
  }
  
  if (metrics.bundleSize > budget.maxBundleSize) {
    violations.push(`Bundle size exceeded: ${metrics.bundleSize} bytes > ${budget.maxBundleSize} bytes`);
  }
  
  if (metrics.apiResponseTime > budget.maxApiResponseTime) {
    violations.push(`API response time exceeded: ${metrics.apiResponseTime}ms > ${budget.maxApiResponseTime}ms`);
  }
  
  return {
    passed: violations.length === 0,
    violations
  };
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Memoization for expensive calculations
 */
export const memoize = <T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T => {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    
    return result;
  }) as T;
};

/**
 * Lazy load function with intersection observer
 */
export const lazyLoad = (
  element: HTMLElement,
  callback: () => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        callback();
        observer.unobserve(entry.target);
      }
    });
  }, {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  });
  
  observer.observe(element);
  return observer;
};

/**
 * Preload critical resources
 */
export const preloadResource = (href: string, as: string, type?: string): void => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (type) link.type = type;

  document.head.appendChild(link);
};

/**
 * Resource Preloader Class for managing critical resource loading
 */
export class ResourcePreloader {
  private preloadedResources: Set<string> = new Set();
  private preloadQueue: Array<{ href: string; as: string; type?: string; priority: number }> = [];
  private isProcessing = false;

  /**
   * Add resource to preload queue
   */
  addToQueue(href: string, as: string, type?: string, priority: number = 1): void {
    if (this.preloadedResources.has(href)) {
      return;
    }

    this.preloadQueue.push({ href, as, type, priority });
    this.preloadQueue.sort((a, b) => b.priority - a.priority);

    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * Process preload queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.preloadQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.preloadQueue.length > 0) {
      const resource = this.preloadQueue.shift()!;

      try {
        await this.preloadSingleResource(resource);
        this.preloadedResources.add(resource.href);
      } catch (error) {
        // console.warn(`Failed to preload resource: ${resource.href}`, error);
      }

      // Small delay to prevent blocking
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    this.isProcessing = false;
  }

  /**
   * Preload single resource with promise
   */
  private preloadSingleResource(resource: { href: string; as: string; type?: string }): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.type) link.type = resource.type;

      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to preload ${resource.href}`));

      document.head.appendChild(link);
    });
  }

  /**
   * Preload critical fonts
   */
  preloadFonts(fontUrls: string[]): void {
    fontUrls.forEach(url => {
      this.addToQueue(url, 'font', 'font/woff2', 10);
    });
  }

  /**
   * Preload critical images
   */
  preloadImages(imageUrls: string[]): void {
    imageUrls.forEach(url => {
      this.addToQueue(url, 'image', undefined, 8);
    });
  }

  /**
   * Preload critical scripts
   */
  preloadScripts(scriptUrls: string[]): void {
    scriptUrls.forEach(url => {
      this.addToQueue(url, 'script', 'text/javascript', 9);
    });
  }

  /**
   * Preload critical styles
   */
  preloadStyles(styleUrls: string[]): void {
    styleUrls.forEach(url => {
      this.addToQueue(url, 'style', 'text/css', 9);
    });
  }

  /**
   * Get preload statistics
   */
  getStats(): { preloaded: number; queued: number } {
    return {
      preloaded: this.preloadedResources.size,
      queued: this.preloadQueue.length
    };
  }

  /**
   * Clear preload cache
   */
  clear(): void {
    this.preloadedResources.clear();
    this.preloadQueue.length = 0;
  }
}

// Global preloader instance
export const resourcePreloader = new ResourcePreloader();

/**
 * Prefetch resources for future navigation
 */
export const prefetchResource = (href: string): void => {
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  document.head.appendChild(link);
};

/**
 * DNS prefetch for external domains
 */
export const dnsPrefetch = (domain: string): void => {
  const link = document.createElement('link');
  link.rel = 'dns-prefetch';
  link.href = domain;
  document.head.appendChild(link);
};

/**
 * Preconnect to external domains
 */
export const preconnect = (domain: string, crossorigin = false): void => {
  const link = document.createElement('link');
  link.rel = 'preconnect';
  link.href = domain;
  if (crossorigin) link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
};

/**
 * Get memory usage information
 */
export const getMemoryUsage = (): { used: number; total: number; percentage: number } | null => {
  const memory = (performance as any).memory;
  
  if (!memory) return null;
  
  return {
    used: memory.usedJSHeapSize,
    total: memory.totalJSHeapSize,
    percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
  };
};

/**
 * Monitor frame rate
 */
export const monitorFrameRate = (callback: (fps: number) => void, duration: number = 1000): () => void => {
  let frames = 0;
  let lastTime = performance.now();
  let animationId: number;
  
  const tick = (currentTime: number) => {
    frames++;
    
    if (currentTime - lastTime >= duration) {
      const fps = Math.round((frames * 1000) / (currentTime - lastTime));
      callback(fps);
      
      frames = 0;
      lastTime = currentTime;
    }
    
    animationId = requestAnimationFrame(tick);
  };
  
  animationId = requestAnimationFrame(tick);
  
  return () => cancelAnimationFrame(animationId);
};

/**
 * Format bytes to human readable format
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format duration to human readable format
 */
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1) return '< 1ms';
  if (milliseconds < 1000) return `${Math.round(milliseconds)}ms`;
  if (milliseconds < 60000) return `${(milliseconds / 1000).toFixed(1)}s`;

  const minutes = Math.floor(milliseconds / 60000);
  const seconds = ((milliseconds % 60000) / 1000).toFixed(0);
  return `${minutes}:${seconds.padStart(2, '0')}`;
};
