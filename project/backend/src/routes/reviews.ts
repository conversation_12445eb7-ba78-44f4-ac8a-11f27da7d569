import { Router } from 'express';
import { authenticate, adminOnly, optionalAuth } from '../middleware/auth';
import ReviewController from '../controllers/reviewController';
import { ValidationSets, handleValidationErrors } from '../services/validationService';
import { uploadMultipleImages } from '../services/uploadService';

const router = Router();

// Public routes (with optional authentication)

// Get reviews with filtering and pagination
router.get('/',
  optionalAuth,
  ReviewController.getReviews
);

// Get review by ID
router.get('/:id',
  optionalAuth,
  ReviewController.getReviewById
);

// Get product review statistics
router.get('/product/:productId/stats',
  optionalAuth,
  ReviewController.getProductReviewStats
);

// Customer routes (require authentication)

// Create new review
router.post('/',
  authenticate,
  uploadMultipleImages,
  ValidationSets.createReview(),
  handleValidationErrors,
  ReviewController.createReview
);

// Get user's own reviews
router.get('/my-reviews',
  authenticate,
  ReviewController.getUserReviews
);

// Check if user can review product
router.get('/product/:productId/can-review',
  authenticate,
  ReviewController.canUserReviewProduct
);

// Update review (only by owner)
router.put('/:id',
  authenticate,
  ValidationSets.updateReview(),
  handleValidationErrors,
  ReviewController.updateReview
);

// Delete review (only by owner or admin)
router.delete('/:id',
  authenticate,
  ReviewController.deleteReview
);

// Vote on review helpfulness
router.post('/:id/vote',
  authenticate,
  ValidationSets.voteReview(),
  handleValidationErrors,
  ReviewController.voteReview
);

// Admin routes (require admin authentication)

// Add response to review
router.post('/:id/response',
  authenticate,
  adminOnly,
  ValidationSets.addReviewResponse(),
  handleValidationErrors,
  ReviewController.addReviewResponse
);

// Moderate review
router.patch('/:id/moderate',
  authenticate,
  adminOnly,
  ValidationSets.moderateReview(),
  handleValidationErrors,
  ReviewController.moderateReview
);

// Get review analytics
router.get('/admin/analytics',
  authenticate,
  adminOnly,
  ReviewController.getReviewAnalytics
);

export default router;
