// Product filter types for mobile and other components
export interface ProductFilters {
  category: string;
  brand: string;
  rating: number;
  inStock: boolean;
  onSale: boolean;
  sortBy: string;
}

// Product search filters
export interface ProductSearchFilters {
  category?: string;
  brand?: string;
  rating?: number;
  inStock?: boolean;
  onSale?: boolean;
  sortBy?: string;
  page?: number;
  limit?: number;
}

// Product sort options
export type ProductSortOption = 
  | 'newest' 
  | 'oldest' 
  | 'price-low' 
  | 'price-high' 
  | 'rating' 
  | 'popular' 
  | 'name-asc' 
  | 'name-desc';

// Product availability status
export type ProductAvailabilityStatus = 'all' | 'in_stock' | 'out_of_stock' | 'low_stock';

// Product filter state for mobile
export interface MobileProductFilterState {
  category: string;
  brand: string;
  rating: number;
  availability: ProductAvailabilityStatus;
  sortBy: ProductSortOption;
  hasDiscount: boolean;
}
