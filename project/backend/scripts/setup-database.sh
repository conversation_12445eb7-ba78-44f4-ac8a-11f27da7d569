#!/bin/bash

# Database Setup and Migration Script for GlowRoya Backend
# This script sets up the database, runs migrations, and populates with mock data

set -e  # Exit on any error

echo "🚀 Starting GlowRoya Database Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the backend directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the backend directory"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it with database credentials."
    exit 1
fi

print_status "Checking database connection..."

# Load environment variables
source .env

# Test database connection
if ! npx prisma db pull --force > /dev/null 2>&1; then
    print_error "Cannot connect to database. Please check your DATABASE_URL in .env"
    exit 1
fi

print_success "Database connection successful"

print_status "Installing dependencies..."
npm install

print_status "Generating Prisma client..."
npx prisma generate

print_status "Pushing database schema..."
npx prisma db push --force-reset

print_success "Database schema updated"

print_status "Running database migrations..."
if npx prisma migrate deploy > /dev/null 2>&1; then
    print_success "Migrations applied successfully"
else
    print_warning "No migrations to apply or migrations failed"
fi

print_status "Seeding database with initial data..."
if npm run seed > /dev/null 2>&1; then
    print_success "Database seeded successfully"
else
    print_warning "Seeding failed or no seed script found"
fi

print_status "Running mock data migration..."
if npx ts-node scripts/migrate-mock-data.ts; then
    print_success "Mock data migration completed"
else
    print_error "Mock data migration failed"
    exit 1
fi

print_status "Running full migration process..."
if npx ts-node scripts/run-full-migration.ts; then
    print_success "Full migration process completed"
else
    print_error "Full migration process failed"
    exit 1
fi

print_status "Verifying database setup..."

# Check if key tables exist and have data
CATEGORIES_COUNT=$(npx prisma db execute --stdin <<< "SELECT COUNT(*) as count FROM categories;" | grep -o '[0-9]\+' | tail -1)
PRODUCTS_COUNT=$(npx prisma db execute --stdin <<< "SELECT COUNT(*) as count FROM products;" | grep -o '[0-9]\+' | tail -1)
BRANDS_COUNT=$(npx prisma db execute --stdin <<< "SELECT COUNT(*) as count FROM brands;" | grep -o '[0-9]\+' | tail -1)

echo ""
print_success "Database Setup Complete!"
echo "📊 Database Summary:"
echo "   - Categories: $CATEGORIES_COUNT"
echo "   - Brands: $BRANDS_COUNT"  
echo "   - Products: $PRODUCTS_COUNT"
echo ""
echo "🎯 Next Steps:"
echo "   1. Start the backend server: npm run dev"
echo "   2. Test API endpoints: http://localhost:3001/api/v1"
echo "   3. Check admin panel: http://localhost:3000/admin"
echo ""
print_success "Setup completed successfully! 🎉"
