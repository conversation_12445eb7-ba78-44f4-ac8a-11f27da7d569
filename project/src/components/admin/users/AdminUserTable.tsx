import React from 'react';
import { motion } from 'framer-motion';
import {
  Eye,
  Edit,
  Trash2,
  Shield,
  ShieldOff,
  Crown,
  User,
  Mail,
  Phone,
  Calendar,
  CheckCircle,
  XCircle,
  MoreHorizontal
} from 'lucide-react';
import AdminTable, { AdminTableColumn, AdminActionMenu, AdminTableBadge } from '../common/AdminTable';
import { AdminIconButton } from '../common/AdminButton';
import { AdminUser } from '../../../types/admin';
import { formatPersianDate } from '../../../utils/customerUtils';

interface AdminUserTableProps {
  users: AdminUser[];
  loading?: boolean;
  selectedUsers: string[];
  onSelectUser: (userId: string) => void;
  onSelectAll: () => void;
  onViewUser: (user: AdminUser) => void;
  onEditUser: (user: AdminUser) => void;
  onToggleStatus: (user: AdminUser) => void;
  onDeleteUser: (user: AdminUser) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
}

const AdminUserTable: React.FC<AdminUserTableProps> = ({
  users,
  loading = false,
  selectedUsers,
  onSelectUser,
  onSelectAll,
  onViewUser,
  onEditUser,
  onToggleStatus,
  onDeleteUser,
  searchValue = '',
  onSearchChange
}) => {
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'moderator':
        return <ShieldOff className="w-4 h-4 text-green-500" />;
      case 'viewer':
        return <User className="w-4 h-4 text-gray-500" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-yellow-100 text-yellow-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'moderator':
        return 'bg-green-100 text-green-800';
      case 'viewer':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'مدیر کل';
      case 'admin':
        return 'مدیر';
      case 'moderator':
        return 'ناظر';
      case 'viewer':
        return 'بازدیدکننده';
      default:
        return 'نامشخص';
    }
  };

  const getStatusIcon = (isActive: boolean) => {
    return isActive ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <XCircle className="w-4 h-4 text-red-500" />;
  };

  const columns: AdminTableColumn<AdminUser>[] = [
    {
      key: 'select',
      title: (
        <input
          type="checkbox"
          checked={users.length > 0 && selectedUsers.length === users.length}
          onChange={onSelectAll}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      ),
      width: '50px',
      render: (user: AdminUser) => (
        <input
          type="checkbox"
          checked={selectedUsers.includes(user.id)}
          onChange={() => onSelectUser(user.id)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      )
    },
    {
      key: 'user',
      title: 'کاربر',
      sortable: true,
      render: (user: AdminUser) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
          </div>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </span>
              {user.role === 'super_admin' && (
                <Crown className="w-4 h-4 text-yellow-500" title="مدیر کل" />
              )}
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Mail className="w-3 h-3" />
              <span>{user.email}</span>
            </div>
            {user.phone && (
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Phone className="w-3 h-3" />
                <span>{user.phone}</span>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'role',
      title: 'نقش',
      sortable: true,
      render: (user: AdminUser) => (
        <div className="flex items-center gap-2">
          {getRoleIcon(user.role)}
          <AdminTableBadge
            variant="info"
          >
            {getRoleName(user.role)}
          </AdminTableBadge>
        </div>
      )
    },
    {
      key: 'department',
      title: 'بخش',
      render: (user: AdminUser) => (
        <span className="text-sm text-gray-600">
          {user.department || '-'}
        </span>
      )
    },
    {
      key: 'status',
      title: 'وضعیت',
      sortable: true,
      render: (user: AdminUser) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(user.isActive)}
          <AdminTableBadge
            variant={user.isActive ? 'success' : 'danger'}
          >
            {user.isActive ? 'فعال' : 'غیرفعال'}
          </AdminTableBadge>
        </div>
      )
    },
    {
      key: 'lastLogin',
      title: 'آخرین ورود',
      sortable: true,
      render: (user: AdminUser) => (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <Calendar className="w-3 h-3" />
          <span>
            {user.lastLogin ? formatPersianDate(user.lastLogin) : 'هرگز'}
          </span>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'تاریخ ایجاد',
      sortable: true,
      render: (user: AdminUser) => (
        <span className="text-sm text-gray-600">
          {formatPersianDate(user.createdAt)}
        </span>
      )
    }
  ];

  const handleRowClick = (user: AdminUser, index: number) => {
    onViewUser(user);
  };

  const renderRowActions = (user: AdminUser, index: number) => (
    <AdminActionMenu
      actions={[
        {
          label: 'مشاهده جزئیات',
          onClick: () => onViewUser(user)
        },
        {
          label: 'ویرایش',
          onClick: () => onEditUser(user)
        },
        {
          label: user.isActive ? 'غیرفعال کردن' : 'فعال کردن',
          onClick: () => onToggleStatus(user)
        },
        {
          label: 'حذف',
          onClick: () => onDeleteUser(user),
          variant: 'danger',
          disabled: user.role === 'super_admin'
        }
      ]}
    />
  );

  return (
    <AdminTable
      columns={columns}
      data={users}
      loading={loading}
      rowActions={renderRowActions}
      onRowClick={handleRowClick}
      searchable={!!onSearchChange}
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      searchPlaceholder="جستجو در کاربران..."
      emptyMessage="هیچ کاربری یافت نشد"
      className="mt-6"
    />
  );
};

export default AdminUserTable;
