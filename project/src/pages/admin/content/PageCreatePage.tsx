import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import PageEditor from '../../../components/admin/content/PageEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { PageContentFormData } from '../../../types/adminContent';

const PageCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const { createPage } = useAdminContent();
  const [loading, setLoading] = useState(false);

  const handleSave = async (formData: PageContentFormData) => {
    try {
      setLoading(true);
      await createPage(formData);
      toast.success('صفحه با موفقیت ایجاد شد');
      navigate('/admin/content/pages');
    } catch (error) {
      console.error('Error creating page:', error);
      toast.error('خطا در ایجاد صفحه');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/content/pages');
  };

  return (
    <AdminLayout 
      title="ایجاد صفحه جدید"
      subtitle="ایجاد صفحه محتوایی جدید برای سایت"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={handleCancel}
          >
            بازگشت
          </AdminButton>
        </div>
      }
    >
      <PageEditor
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        mode="create"
      />
    </AdminLayout>
  );
};

export default PageCreatePage;
