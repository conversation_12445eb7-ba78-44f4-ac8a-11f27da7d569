import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Shield, Lock, AlertTriangle, Clock } from 'lucide-react';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { AdminRole, AdminResource, AdminAction } from '../../types/admin';
import { NavigationPermissions } from '../../utils/rolePermissions';

interface AdminProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: AdminRole;
  requiredRoles?: AdminRole[];
  requiredResource?: AdminResource;
  requiredAction?: AdminAction;
  redirectTo?: string;
  fallback?: ReactNode;
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  requiredRoles,
  requiredResource,
  requiredAction,
  redirectTo = '/admin/login',
  fallback
}) => {
  const { user, isAuthenticated, isLoading, checkPermission, hasRole, hasAnyRole } = useAdminAuth();
  const location = useLocation();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">در حال بررسی دسترسی...</p>
        </motion.div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // Check specific role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600 mb-6">
            شما مجوز دسترسی به این بخش را ندارید.
            <br />
            نقش مورد نیاز: {requiredRole}
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            بازگشت
          </button>
        </motion.div>
      </div>
    );
  }

  // Check multiple roles requirement
  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600 mb-6">
            شما مجوز دسترسی به این بخش را ندارید.
            <br />
            نقش‌های مورد نیاز: {requiredRoles.join(', ')}
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            بازگشت
          </button>
        </motion.div>
      </div>
    );
  }

  // Check resource and action permission
  if (requiredResource && requiredAction && !checkPermission(requiredResource, requiredAction)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-orange-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            عدم دسترسی
          </h2>
          <p className="text-gray-600 mb-6">
            شما مجوز انجام این عملیات را ندارید.
            <br />
            منبع: {requiredResource}
            <br />
            عملیات: {requiredAction}
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            بازگشت
          </button>
        </motion.div>
      </div>
    );
  }

  // Check route-specific permissions
  if (user && !NavigationPermissions.canAccessRoute(user, location.pathname)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-yellow-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            دسترسی به صفحه محدود
          </h2>
          <p className="text-gray-600 mb-6">
            شما مجوز دسترسی به این صفحه را ندارید.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.history.back()}
              className="w-full bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              بازگشت
            </button>
            <button
              onClick={() => window.location.href = '/admin'}
              className="w-full border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              رفتن به داشبورد
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  // Check if user account is active
  if (user && !user.isActive) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Clock className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            حساب کاربری غیرفعال
          </h2>
          <p className="text-gray-600 mb-6">
            حساب کاربری شما غیرفعال شده است. لطفاً با مدیر سیستم تماس بگیرید.
          </p>
          <button
            onClick={() => window.location.href = '/admin/login'}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            ورود مجدد
          </button>
        </motion.div>
      </div>
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
};

export default AdminProtectedRoute;
