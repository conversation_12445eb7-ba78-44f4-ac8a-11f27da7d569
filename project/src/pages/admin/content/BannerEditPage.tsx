import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, Save, Eye, Trash2 } from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminButton from '../../../components/admin/common/AdminButton';
import { AdminConfirmModal } from '../../../components/admin/common/AdminModal';
import BannerEditor from '../../../components/admin/content/BannerEditor';
import { useAdminContent } from '../../../hooks/useAdminContent';
import { BannerFormData, Banner } from '../../../types/adminContent';

const BannerEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { banners, updateBanner, deleteBanner } = useAdminContent();
  const [banner, setBanner] = useState<Banner | null>(null);
  const [loading, setLoading] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  useEffect(() => {
    if (id && banners.length > 0) {
      const foundBanner = banners.find(b => b.id === id);
      if (foundBanner) {
        setBanner(foundBanner);
      } else {
        toast.error('بنر یافت نشد');
        navigate('/admin/content/banners');
      }
    }
  }, [id, banners, navigate]);

  const handleSave = async (data: BannerFormData) => {
    if (!id) return;
    
    try {
      setLoading(true);
      await updateBanner(id, data);
      toast.success('بنر با موفقیت به‌روزرسانی شد');
      navigate(`/admin/content/banners/${id}`);
    } catch (error) {
      console.error('Error updating banner:', error);
      toast.error('خطا در به‌روزرسانی بنر');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      await deleteBanner(id);
      toast.success('بنر با موفقیت حذف شد');
      navigate('/admin/content/banners');
    } catch (error) {
      console.error('Error deleting banner:', error);
      toast.error('خطا در حذف بنر');
    } finally {
      setLoading(false);
      setDeleteModalOpen(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/content/banners');
  };

  if (!banner) {
    return (
      <AdminLayout title="ویرایش بنر">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری...</span>
        </div>
      </AdminLayout>
    );
  }

  const initialData: Partial<BannerFormData> = {
    title: banner.title,
    subtitle: banner.subtitle,
    description: banner.description,
    type: banner.type,
    image: banner.image,
    mobileImage: banner.mobileImage,
    altText: banner.altText,
    ctaText: banner.ctaText,
    ctaUrl: banner.ctaUrl,
    ctaType: banner.ctaType,
    position: banner.position,
    showOnPages: banner.showOnPages,
    backgroundColor: banner.backgroundColor,
    textColor: banner.textColor,
    overlayOpacity: banner.overlayOpacity,
    animationType: banner.animationType,
    autoplay: banner.autoplay,
    duration: banner.duration,
    status: banner.status,
    isActive: banner.isActive,
    startDate: banner.startDate,
    endDate: banner.endDate,
    seoTitle: banner.seoTitle,
    seoDescription: banner.seoDescription,
    seoKeywords: banner.seoKeywords
  };

  return (
    <AdminLayout 
      title={`ویرایش بنر: ${banner.title}`}
      subtitle="ویرایش اطلاعات و تنظیمات بنر"
      actions={
        <div className="flex gap-2">
          <AdminButton 
            variant="outline" 
            icon={ArrowLeft}
            onClick={handleCancel}
          >
            بازگشت
          </AdminButton>
          <AdminButton 
            variant="danger" 
            icon={Trash2}
            onClick={() => setDeleteModalOpen(true)}
          >
            حذف بنر
          </AdminButton>
        </div>
      }
    >
      <BannerEditor
        initialData={initialData}
        onSave={handleSave}
        onCancel={handleCancel}
        loading={loading}
        mode="edit"
      />

      {/* Delete Confirmation Modal */}
      <AdminConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="حذف بنر"
        message={`آیا از حذف بنر "${banner.title}" اطمینان دارید؟ این عمل قابل بازگشت نیست.`}
        confirmText="حذف"
        cancelText="انصراف"
        variant="danger"
      />
    </AdminLayout>
  );
};

export default BannerEditPage;
