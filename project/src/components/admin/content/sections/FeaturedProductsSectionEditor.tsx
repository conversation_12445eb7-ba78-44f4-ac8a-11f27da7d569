import React from 'react';
import { FeaturedProductsSectionData } from '../../../../types/homePageContent';
import AdminCard from '../../common/AdminCard';
import AdminForm, { AdminFormField, AdminInput, AdminTextarea, AdminSelect } from '../../common/AdminForm';
import AdminToggle from '../../common/AdminToggle';
import AdminSlider from '../../common/AdminSlider';

interface FeaturedProductsSectionEditorProps {
  section: FeaturedProductsSectionData;
  onChange: (updates: Partial<FeaturedProductsSectionData['content']>) => void;
}

const FeaturedProductsSectionEditor: React.FC<FeaturedProductsSectionEditorProps> = ({
  section,
  onChange
}) => {
  const handleChange = (field: keyof FeaturedProductsSectionData['content'], value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      {/* Content Settings */}
      <AdminCard title="محتوای بخش" icon="Star">
        <div className="space-y-4">
          <AdminFormField label="عنوان بخش" required>
            <AdminInput
              value={section.content.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder="محصولات ویژه"
            />
          </AdminFormField>

          <AdminFormField label="زیرعنوان">
            <AdminInput
              value={section.content.subtitle || ''}
              onChange={(e) => handleChange('subtitle', e.target.value)}
              placeholder="بهترین محصولات مراقبت از پوست"
            />
          </AdminFormField>

          <AdminFormField label="توضیحات">
            <AdminTextarea
              value={section.content.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="توضیح کوتاه درباره محصولات ویژه"
              rows={3}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Display Settings */}
      <AdminCard title="تنظیمات نمایش" icon="Grid">
        <div className="space-y-4">
          <AdminFormField label="نوع نمایش">
            <AdminSelect
              value={section.content.displayType}
              onChange={(value) => handleChange('displayType', value)}
              options={[
                { value: 'grid', label: 'شبکه‌ای' },
                { value: 'carousel', label: 'اسلایدر' },
                { value: 'list', label: 'لیستی' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="تعداد محصول در هر ردیف">
            <AdminSelect
              value={section.content.productsPerRow}
              onChange={(value) => handleChange('productsPerRow', parseInt(value))}
              options={[
                { value: '2', label: '2 محصول' },
                { value: '3', label: '3 محصول' },
                { value: '4', label: '4 محصول' },
                { value: '5', label: '5 محصول' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="حداکثر تعداد محصول">
            <AdminSlider
              value={section.content.maxProducts}
              onChange={(value) => handleChange('maxProducts', value)}
              min={4}
              max={20}
              step={2}
              label={`${section.content.maxProducts} محصول`}
            />
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Product Selection */}
      <AdminCard title="انتخاب محصولات" icon="Filter">
        <div className="space-y-4">
          <AdminFormField label="فیلتر محصولات">
            <AdminSelect
              value={section.content.filterBy}
              onChange={(value) => handleChange('filterBy', value)}
              options={[
                { value: 'featured', label: 'محصولات ویژه' },
                { value: 'bestseller', label: 'پرفروش‌ترین' },
                { value: 'new', label: 'جدیدترین' },
                { value: 'discounted', label: 'تخفیف‌دار' },
                { value: 'category', label: 'بر اساس دسته‌بندی' },
                { value: 'manual', label: 'انتخاب دستی' }
              ]}
            />
          </AdminFormField>

          <AdminFormField label="مرتب‌سازی">
            <div className="grid grid-cols-2 gap-4">
              <AdminSelect
                value={section.content.sortBy}
                onChange={(value) => handleChange('sortBy', value)}
                options={[
                  { value: 'name', label: 'نام' },
                  { value: 'price', label: 'قیمت' },
                  { value: 'rating', label: 'امتیاز' },
                  { value: 'date', label: 'تاریخ' },
                  { value: 'popularity', label: 'محبوبیت' }
                ]}
              />
              <AdminSelect
                value={section.content.sortOrder}
                onChange={(value) => handleChange('sortOrder', value)}
                options={[
                  { value: 'asc', label: 'صعودی' },
                  { value: 'desc', label: 'نزولی' }
                ]}
              />
            </div>
          </AdminFormField>
        </div>
      </AdminCard>

      {/* Display Options */}
      <AdminCard title="گزینه‌های نمایش" icon="Eye">
        <div className="space-y-4">
          <AdminFormField label="نمایش قیمت‌ها">
            <AdminToggle
              checked={section.content.showPrices}
              onChange={(checked) => handleChange('showPrices', checked)}
              label="قیمت محصولات نمایش داده شود"
            />
          </AdminFormField>

          <AdminFormField label="نمایش امتیازات">
            <AdminToggle
              checked={section.content.showRatings}
              onChange={(checked) => handleChange('showRatings', checked)}
              label="امتیاز محصولات نمایش داده شود"
            />
          </AdminFormField>

          <AdminFormField label="نمایش دکمه افزودن به سبد">
            <AdminToggle
              checked={section.content.showAddToCart}
              onChange={(checked) => handleChange('showAddToCart', checked)}
              label="دکمه افزودن به سبد نمایش داده شود"
            />
          </AdminFormField>

          <AdminFormField label="نمایش دکمه مشاهده همه">
            <AdminToggle
              checked={section.content.showViewAllButton}
              onChange={(checked) => handleChange('showViewAllButton', checked)}
              label="دکمه مشاهده همه محصولات نمایش داده شود"
            />
          </AdminFormField>

          {section.content.showViewAllButton && (
            <>
              <AdminFormField label="متن دکمه مشاهده همه">
                <AdminInput
                  value={section.content.viewAllButtonText}
                  onChange={(e) => handleChange('viewAllButtonText', e.target.value)}
                  placeholder="مشاهده همه محصولات"
                />
              </AdminFormField>

              <AdminFormField label="لینک دکمه مشاهده همه">
                <AdminInput
                  value={section.content.viewAllButtonUrl}
                  onChange={(e) => handleChange('viewAllButtonUrl', e.target.value)}
                  placeholder="/products"
                />
              </AdminFormField>
            </>
          )}
        </div>
      </AdminCard>
    </div>
  );
};

export default FeaturedProductsSectionEditor;
