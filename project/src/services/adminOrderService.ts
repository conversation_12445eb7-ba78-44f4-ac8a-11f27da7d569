/**
 * Admin Order Service
 * Handles all order-related API operations for admin panel
 */

import { ApiService } from './apiService';
import { AdminOrder, OrderFilters, OrderSortOptions } from '../types/adminOrder';

// Backend order interface (matches Prisma schema)
interface BackendOrder {
  id: string;
  orderNumber: string;
  userId?: string;
  guestEmail?: string;
  status: string;
  paymentStatus: string;
  fulfillmentStatus: string;
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  totalAmount: number;
  shippingAddressId?: string;
  billingAddressId?: string;
  shippingMethod?: string;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  shippingAddress?: {
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
    province: string;
    city: string;
    district?: string;
    street: string;
    postalCode: string;
  };
  billingAddress?: {
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
    province: string;
    city: string;
    district?: string;
    street: string;
    postalCode: string;
  };
  items: {
    id: string;
    productId: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      name: string;
      slug: string;
      sku: string;
      images: string[];
    };
  }[];
  payments?: {
    id: string;
    amount: number;
    method: string;
    status: string;
    transactionId?: string;
    createdAt: string;
  }[];
}

// Transform backend order to admin order format
const transformBackendOrder = (backendOrder: BackendOrder): AdminOrder => {
  const customer = backendOrder.user || {
    id: 'guest',
    email: backendOrder.guestEmail || '',
    firstName: 'مهمان',
    lastName: '',
    phone: ''
  };

  return {
    id: backendOrder.id,
    orderNumber: backendOrder.orderNumber,
    status: backendOrder.status as AdminOrder['status'],
    priority: 'normal', // Default priority, can be enhanced later
    items: backendOrder.items.map(item => ({
      productId: item.productId,
      productName: item.product.name,
      productImage: item.product.images[0] || '',
      quantity: item.quantity,
      price: Number(item.price) || 0
    })),
    orderSummary: {
      subtotal: Number(backendOrder.subtotal) || 0,
      tax: Number(backendOrder.taxAmount) || 0,
      shippingCost: Number(backendOrder.shippingAmount) || 0,
      discount: Number(backendOrder.discountAmount) || 0,
      total: Number(backendOrder.totalAmount) || 0
    },
    shippingAddress: backendOrder.shippingAddress ? {
      firstName: backendOrder.shippingAddress.firstName,
      lastName: backendOrder.shippingAddress.lastName,
      phone: backendOrder.shippingAddress.phone,
      email: backendOrder.shippingAddress.email || customer.email,
      province: backendOrder.shippingAddress.province,
      city: backendOrder.shippingAddress.city,
      address: backendOrder.shippingAddress.street,
      postalCode: backendOrder.shippingAddress.postalCode
    } : {
      firstName: '',
      lastName: '',
      phone: '',
      email: customer.email,
      province: '',
      city: '',
      address: '',
      postalCode: ''
    },
    shippingMethod: {
      id: backendOrder.shippingMethod || 'standard',
      name: getShippingMethodName(backendOrder.shippingMethod || 'standard'),
      description: getShippingMethodDescription(backendOrder.shippingMethod || 'standard'),
      price: Number(backendOrder.shippingAmount) || 0,
      estimatedDays: '3-5 روز کاری'
    },
    paymentMethod: {
      id: backendOrder.payments?.[0]?.method || 'unknown',
      type: backendOrder.payments?.[0]?.method as any || 'card',
      name: getPaymentMethodTitle(backendOrder.payments?.[0]?.method || 'unknown'),
      title: getPaymentMethodTitle(backendOrder.payments?.[0]?.method || 'unknown'),
      description: 'پرداخت',
      icon: '💳',
      isAvailable: true,
      processingFee: 0
    },
    createdAt: backendOrder.createdAt,
    estimatedDelivery: calculateEstimatedDelivery(backendOrder.shippingMethod || 'standard', backendOrder.createdAt),
    trackingNumber: backendOrder.trackingNumber,
    customerId: customer.id,
    customerName: `${customer.firstName} ${customer.lastName}`.trim() || customer.email,
    customerEmail: customer.email,
    customerPhone: customer.phone || '',
    tags: [],
    refundStatus: 'none',
    customerInfo: {
      id: customer.id,
      name: `${customer.firstName} ${customer.lastName}`.trim() || customer.email,
      email: customer.email,
      phone: customer.phone || '',
      totalOrders: 1, // Will be enhanced with actual data
      loyaltyTier: 'bronze',
      isVip: false
    },
    fulfillment: {
      warehouse: 'انبار مرکزی',
      shippingCarrier: getShippingCarrierName(backendOrder.shippingMethod || 'standard'),
      labelPrinted: ['shipped', 'delivered'].includes(backendOrder.status),
      packageWeight: 500 // Default weight
    },
    timeline: generateOrderTimeline(backendOrder),
    metrics: {
      processingTime: undefined,
      shippingTime: undefined,
      profitMargin: 20, // Default margin
      costOfGoods: Math.floor(Number(backendOrder.subtotal) * 0.6)
    },
    flags: {
      isRush: false,
      isGift: false,
      requiresSignature: false,
      isInternational: false,
      hasCustoms: false,
      isFragile: false
    }
  };
};

// Helper functions
const getPaymentMethodTitle = (method: string): string => {
  const titles: Record<string, string> = {
    'card': 'پرداخت آنلاین',
    'wallet': 'کیف پول',
    'cash_on_delivery': 'پرداخت در محل',
    'bank_transfer': 'انتقال بانکی'
  };
  return titles[method] || 'نامشخص';
};

const getShippingCarrierName = (method: string): string => {
  const carriers: Record<string, string> = {
    'standard': 'پست پیشتاز',
    'express': 'پیک موتوری',
    'overnight': 'پست سریع'
  };
  return carriers[method] || 'پست پیشتاز';
};

const getShippingMethodName = (method: string): string => {
  const methods: Record<string, string> = {
    'standard': 'ارسال عادی',
    'express': 'ارسال سریع',
    'overnight': 'ارسال فوری'
  };
  return methods[method] || 'ارسال عادی';
};

const getShippingMethodDescription = (method: string): string => {
  const descriptions: Record<string, string> = {
    'standard': 'ارسال با پست پیشتاز',
    'express': 'ارسال با پیک موتوری',
    'overnight': 'ارسال فوری'
  };
  return descriptions[method] || 'ارسال با پست پیشتاز';
};

const calculateEstimatedDelivery = (shippingMethod: string, orderDate: string): string => {
  const orderTime = new Date(orderDate);
  const deliveryDays = shippingMethod === 'express' ? 1 : shippingMethod === 'overnight' ? 2 : 3;
  const deliveryDate = new Date(orderTime.getTime() + deliveryDays * 24 * 60 * 60 * 1000);
  return deliveryDate.toISOString();
};

const generateOrderTimeline = (order: BackendOrder) => {
  const timeline = [
    {
      id: '1',
      status: 'pending' as const,
      title: 'سفارش ثبت شد',
      description: 'سفارش با موفقیت ثبت شد',
      timestamp: order.createdAt,
      isCompleted: true,
      icon: 'ShoppingCart' as const
    }
  ];

  if (['confirmed', 'processing', 'shipped', 'delivered'].includes(order.status)) {
    timeline.push({
      id: '2',
      status: 'confirmed' as const,
      title: 'سفارش تایید شد',
      description: 'سفارش توسط فروشنده تایید شد',
      timestamp: order.updatedAt,
      isCompleted: true,
      icon: 'CheckCircle' as const
    });
  }

  if (['processing', 'shipped', 'delivered'].includes(order.status)) {
    timeline.push({
      id: '3',
      status: 'processing' as const,
      title: 'در حال آماده‌سازی',
      description: 'سفارش در حال آماده‌سازی است',
      timestamp: order.updatedAt,
      isCompleted: true,
      icon: 'Package' as const
    });
  }

  if (['shipped', 'delivered'].includes(order.status)) {
    timeline.push({
      id: '4',
      status: 'shipped' as const,
      title: 'ارسال شد',
      description: 'سفارش ارسال شد',
      timestamp: order.updatedAt,
      isCompleted: true,
      icon: 'Truck' as const
    });
  }

  if (order.status === 'delivered') {
    timeline.push({
      id: '5',
      status: 'delivered' as const,
      title: 'تحویل داده شد',
      description: 'سفارش با موفقیت تحویل داده شد',
      timestamp: order.updatedAt,
      isCompleted: true,
      icon: 'CheckCircle' as const
    });
  }

  return timeline;
};

export interface OrdersResponse {
  orders: AdminOrder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class AdminOrderService {
  /**
   * Get orders with filtering and pagination
   */
  static async getOrders(params: {
    page?: number;
    limit?: number;
    filters?: OrderFilters;
    sort?: OrderSortOptions;
  }): Promise<OrdersResponse> {
    try {
      const queryParams: Record<string, string | number> = {
        page: params.page || 1,
        limit: params.limit || 20,
      };

      // Add filters
      if (params.filters) {
        if (params.filters.status) queryParams.status = params.filters.status;
        if (params.filters.search) queryParams.search = params.filters.search;
        if (params.filters.dateFrom) queryParams.dateFrom = params.filters.dateFrom;
        if (params.filters.dateTo) queryParams.dateTo = params.filters.dateTo;
        if (params.filters.customerId) queryParams.customerId = params.filters.customerId;
      }

      // Add sorting
      if (params.sort) {
        queryParams.sortBy = params.sort.field;
        queryParams.sortOrder = params.sort.direction;
      }

      const response = await ApiService.Http.get<BackendOrder[]>('/orders/admin/all', queryParams);

      if (response.success && response.data) {
        const orders = response.data.map(transformBackendOrder);
        const pagination = response.pagination || {
          page: 1,
          limit: orders.length,
          total: orders.length,
          totalPages: 1
        };

        return { orders, pagination };
      }

      throw new Error('ORDERS_FETCH_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get order by ID
   */
  static async getOrderById(id: string): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.get<BackendOrder>(`/orders/${id}`);

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('ORDER_NOT_FOUND');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(id: string, status: AdminOrder['status']): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.patch<BackendOrder>(`/orders/admin/${id}/status`, { status });

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('ORDER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add tracking number
   */
  static async addTrackingNumber(id: string, trackingNumber: string): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.patch<BackendOrder>(`/orders/admin/${id}/tracking`, { trackingNumber });

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('TRACKING_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add order notes
   */
  static async addOrderNotes(id: string, notes: string): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.patch<BackendOrder>(`/orders/admin/${id}/notes`, { notes });

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('NOTES_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate shipping label
   */
  static async generateShippingLabel(id: string): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.post<BackendOrder>(`/orders/admin/${id}/shipping-label`);

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('LABEL_GENERATION_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk update order status
   */
  static async bulkUpdateStatus(orderIds: string[], status: AdminOrder['status']): Promise<AdminOrder[]> {
    try {
      const response = await ApiService.Http.patch<BackendOrder[]>('/orders/admin/bulk-status', {
        orderIds,
        status
      });

      if (response.success && response.data) {
        return response.data.map(transformBackendOrder);
      }

      throw new Error('BULK_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update order
   */
  static async updateOrder(id: string, orderData: Partial<AdminOrder>): Promise<AdminOrder> {
    try {
      const response = await ApiService.Http.patch<BackendOrder>(`/orders/admin/${id}`, orderData);

      if (response.success && response.data) {
        return transformBackendOrder(response.data);
      }

      throw new Error('ORDER_UPDATE_FAILED');
    } catch (error) {
      throw error;
    }
  }
}
