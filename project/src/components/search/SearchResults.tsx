import React from 'react';
import { Star, ArrowLeft, Package } from 'lucide-react';
import { Product } from '../../types';
import { highlightSearchTerms } from '../../utils/persianSearch';

interface SearchResultsProps {
  results: Product[];
  query: string;
  activeIndex: number;
  suggestionsCount: number;
  onSelectProduct: (product: Product) => void;
  onViewAllResults: () => void;
  maxResults?: number;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  query,
  activeIndex,
  suggestionsCount,
  onSelectProduct,
  onViewAllResults,
  maxResults = 5
}) => {
  const displayResults = results.slice(0, maxResults);
  const hasMoreResults = results.length > maxResults;

  if (results.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="p-6 text-center"
      >
        <Package className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <h3 className="text-lg font-medium text-text-primary mb-2">
          محصولی یافت نشد
        </h3>
        <p className="text-text-secondary text-sm mb-4">
          متأسفانه محصولی با عبارت "{query}" پیدا نکردیم
        </p>
        <div className="text-xs text-text-muted">
          <div className="mb-2">پیشنهادات:</div>
          <div className="space-y-1">
            <div>• از کلمات کلیدی مختلف استفاده کنید</div>
            <div>• املای کلمات را بررسی کنید</div>
            <div>• جستجوی کلی‌تری انجام دهید</div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <div>
      {/* Results Header */}
      <div className="px-4 py-3 bg-green-50 border-b border-green-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm font-medium text-green-700">
            <Package className="w-4 h-4" />
            {results.length} محصول یافت شد
          </div>
          {hasMoreResults && (
            <button
              onClick={onViewAllResults}
              className="text-sm text-green-600 hover:text-green-700 font-medium"
            >
              مشاهده همه
            </button>
          )}
        </div>
      </div>

      {/* Results List */}
      <div className="py-2">
        {displayResults.map((product, index) => {
          const resultIndex = suggestionsCount + index;
          const isActive = resultIndex === activeIndex;
          
          return (
            <button
              key={product.id}
              onClick={() => onSelectProduct(product)}
              className={`w-full text-right p-4 hover:bg-gray-50 transition-colors border-b border-gray-50 last:border-0 ${
                isActive ? 'bg-primary-50' : ''
              }`}
            >
              <div className="flex items-center gap-4">
                {/* Product Image */}
                <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={product.imageSrc}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Product Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-1">
                    <h4 
                      className="font-medium text-text-primary text-sm leading-tight"
                      dangerouslySetInnerHTML={{
                        __html: highlightSearchTerms(product.name, query)
                      }}
                    />
                    <div className="flex items-center gap-1 text-xs text-text-muted mr-2">
                      <Star className="w-3 h-3 fill-current text-accent-500" />
                      {product.rating}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-text-secondary bg-gray-100 px-2 py-1 rounded-full">
                      {product.category}
                    </span>
                    {product.brand && (
                      <span className="text-xs text-text-muted">
                        {product.brand}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {product.discountedPrice ? (
                        <>
                          <span className="font-bold text-primary-600 text-sm">
                            {product.discountedPrice.toLocaleString()} تومان
                          </span>
                          <span className="line-through text-text-muted text-xs">
                            {product.price.toLocaleString()}
                          </span>
                        </>
                      ) : (
                        <span className="font-bold text-text-primary text-sm">
                          {product.price.toLocaleString()} تومان
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1">
                      {product.isNew && (
                        <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                          جدید
                        </span>
                      )}
                      {product.isBestSeller && (
                        <span className="bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">
                          پرفروش
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Stock Status */}
                  <div className="mt-2">
                    <span className={`text-xs ${
                      product.stock > 10 
                        ? 'text-green-600' 
                        : product.stock > 0 
                        ? 'text-orange-600' 
                        : 'text-red-600'
                    }`}>
                      {product.stock > 0 ? `${product.stock} عدد موجود` : 'ناموجود'}
                    </span>
                  </div>
                </div>

                {/* Arrow Icon */}
                <ArrowLeft className="w-4 h-4 text-text-muted flex-shrink-0" />
              </div>
            </button>
          );
        })}
      </div>

      {/* View All Results Footer */}
      {hasMoreResults && (
        <div className="border-t border-gray-100 p-4">
          <button
            onClick={onViewAllResults}
            className="w-full btn-secondary text-sm"
          >
            مشاهده همه {results.length} محصول
            <ArrowLeft className="w-4 h-4 mr-2" />
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchResults;
