<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Remember Me Functionality</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Remember Me Functionality</h1>
        <p>This page tests the "Remember Me" session persistence functionality.</p>

        <div class="test-section">
            <h3>📝 Simulate Login</h3>
            <button onclick="simulateLogin(false)">Login (Session Only)</button>
            <button onclick="simulateLogin(true)">Login (Remember Me)</button>
            <button class="clear-btn" onclick="clearAllStorage()">Clear All Storage</button>
        </div>

        <div class="test-section">
            <h3>🔍 Check Session Status</h3>
            <button onclick="checkSession()">Check Current Session</button>
            <button onclick="checkExpiry()">Check Session Expiry</button>
            <div id="sessionStatus"></div>
        </div>

        <div class="test-section">
            <h3>📊 Storage Contents</h3>
            <button onclick="showStorageContents()">Show Storage Contents</button>
            <div id="storageContents"></div>
        </div>

        <div class="test-section">
            <h3>⏰ Time Travel Test</h3>
            <p>Simulate time passing to test session expiry:</p>
            <button onclick="simulateTimeTravel(16)">+16 minutes (should expire session)</button>
            <button onclick="simulateTimeTravel(1440)">+24 hours (should expire remember me)</button>
            <button onclick="simulateTimeTravel(43200)">+30 days (should expire remember me)</button>
        </div>
    </div>

    <script>
        // Auth storage keys (matching the app)
        const AUTH_STORAGE_KEYS = {
            TOKEN: 'auth_token',
            REFRESH_TOKEN: 'auth_refresh_token',
            USER: 'auth_user',
            REMEMBER_ME: 'auth_remember_me',
            LAST_LOGIN: 'auth_last_login'
        };

        // Token expiry times (matching the app)
        const TOKEN_EXPIRY = {
            ACCESS_TOKEN: 15 * 60 * 1000, // 15 minutes
            REFRESH_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
            REMEMBER_ME: 30 * 24 * 60 * 60 * 1000 // 30 days
        };

        function simulateLogin(rememberMe) {
            const mockUser = {
                id: '1',
                email: '<EMAIL>',
                firstName: 'کاربر',
                lastName: 'تست'
            };

            const mockToken = btoa(JSON.stringify({
                sub: mockUser.id,
                email: mockUser.email,
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor((Date.now() + TOKEN_EXPIRY.ACCESS_TOKEN) / 1000)
            }));

            // Clear previous session
            clearAllStorage();

            // Store session data
            const storage = rememberMe ? localStorage : sessionStorage;
            const expiryTime = Date.now() + (rememberMe ? TOKEN_EXPIRY.REMEMBER_ME : TOKEN_EXPIRY.ACCESS_TOKEN);

            storage.setItem(AUTH_STORAGE_KEYS.TOKEN, mockToken);
            storage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(mockUser));
            storage.setItem(AUTH_STORAGE_KEYS.LAST_LOGIN, expiryTime.toString());
            
            localStorage.setItem(AUTH_STORAGE_KEYS.REMEMBER_ME, rememberMe.toString());
            localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, btoa(`refresh-${mockUser.id}-${Date.now()}`));

            // Clear from other storage
            const otherStorage = rememberMe ? sessionStorage : localStorage;
            otherStorage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
            otherStorage.removeItem(AUTH_STORAGE_KEYS.USER);
            otherStorage.removeItem(AUTH_STORAGE_KEYS.LAST_LOGIN);

            showStatus(`✅ Login simulated with ${rememberMe ? 'Remember Me' : 'Session Only'}`, 'success');
            checkSession();
        }

        function checkSession() {
            const sessionToken = sessionStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
            const localToken = localStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
            const rememberMe = localStorage.getItem(AUTH_STORAGE_KEYS.REMEMBER_ME) === 'true';
            
            const token = sessionToken || localToken;
            const user = sessionStorage.getItem(AUTH_STORAGE_KEYS.USER) || localStorage.getItem(AUTH_STORAGE_KEYS.USER);

            if (!token || !user) {
                showSessionStatus('❌ No active session found', 'error');
                return;
            }

            const isExpired = isSessionExpired();
            const storageType = sessionToken ? 'Session Storage' : 'Local Storage';
            
            if (isExpired) {
                showSessionStatus('⏰ Session expired', 'error');
            } else {
                showSessionStatus(`✅ Active session found in ${storageType} (Remember Me: ${rememberMe})`, 'success');
            }
        }

        function isSessionExpired() {
            const sessionExpiry = sessionStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            if (sessionExpiry) {
                return Date.now() > parseInt(sessionExpiry);
            }
            
            const localExpiry = localStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            if (localExpiry) {
                return Date.now() > parseInt(localExpiry);
            }
            
            return true;
        }

        function checkExpiry() {
            const sessionExpiry = sessionStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            const localExpiry = localStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            
            const expiry = sessionExpiry || localExpiry;
            if (!expiry) {
                showSessionStatus('❌ No expiry time found', 'error');
                return;
            }

            const expiryTime = new Date(parseInt(expiry));
            const now = new Date();
            const timeLeft = parseInt(expiry) - Date.now();
            
            if (timeLeft > 0) {
                const minutes = Math.floor(timeLeft / (1000 * 60));
                const hours = Math.floor(minutes / 60);
                const days = Math.floor(hours / 24);
                
                let timeLeftStr = '';
                if (days > 0) timeLeftStr = `${days} روز`;
                else if (hours > 0) timeLeftStr = `${hours} ساعت`;
                else timeLeftStr = `${minutes} دقیقه`;
                
                showSessionStatus(`⏰ Session expires in ${timeLeftStr} (${expiryTime.toLocaleString()})`, 'info');
            } else {
                showSessionStatus(`❌ Session expired ${Math.abs(Math.floor(timeLeft / (1000 * 60)))} minutes ago`, 'error');
            }
        }

        function showStorageContents() {
            const sessionData = {};
            const localData = {};

            // Get session storage data
            Object.values(AUTH_STORAGE_KEYS).forEach(key => {
                const value = sessionStorage.getItem(key);
                if (value) sessionData[key] = value;
            });

            // Get local storage data
            Object.values(AUTH_STORAGE_KEYS).forEach(key => {
                const value = localStorage.getItem(key);
                if (value) localData[key] = value;
            });

            const content = `
                <h4>Session Storage:</h4>
                <pre>${JSON.stringify(sessionData, null, 2)}</pre>
                <h4>Local Storage:</h4>
                <pre>${JSON.stringify(localData, null, 2)}</pre>
            `;

            document.getElementById('storageContents').innerHTML = content;
        }

        function simulateTimeTravel(minutes) {
            const sessionExpiry = sessionStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            const localExpiry = localStorage.getItem(AUTH_STORAGE_KEYS.LAST_LOGIN);
            
            if (sessionExpiry) {
                const newExpiry = parseInt(sessionExpiry) - (minutes * 60 * 1000);
                sessionStorage.setItem(AUTH_STORAGE_KEYS.LAST_LOGIN, newExpiry.toString());
            }
            
            if (localExpiry) {
                const newExpiry = parseInt(localExpiry) - (minutes * 60 * 1000);
                localStorage.setItem(AUTH_STORAGE_KEYS.LAST_LOGIN, newExpiry.toString());
            }

            showStatus(`⏰ Time traveled ${minutes} minutes into the future`, 'info');
            checkSession();
        }

        function clearAllStorage() {
            Object.values(AUTH_STORAGE_KEYS).forEach(key => {
                sessionStorage.removeItem(key);
                localStorage.removeItem(key);
            });
            showStatus('🗑️ All storage cleared', 'info');
            document.getElementById('sessionStatus').innerHTML = '';
            document.getElementById('storageContents').innerHTML = '';
        }

        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            document.body.appendChild(statusDiv);
            setTimeout(() => statusDiv.remove(), 5000);
        }

        function showSessionStatus(message, type) {
            const statusDiv = document.getElementById('sessionStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        // Initial check
        window.onload = () => {
            checkSession();
        };
    </script>
</body>
</html>
