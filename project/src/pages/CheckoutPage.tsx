import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import {
  ShippingAddress,
  PaymentMethod,
  ShippingMethod,
  CheckoutFormData,
  OrderSummary as OrderSummaryType,
  CHECKOUT_STEPS
} from '../types/checkout';
import CheckoutSteps, { StepNavigation } from '../components/checkout/CheckoutSteps';
import ShippingForm from '../components/checkout/ShippingForm';
import PaymentForm from '../components/checkout/PaymentForm';
import OrderSummary from '../components/checkout/OrderSummary';
import { ShoppingCart, ArrowRight, CheckCircle, Building2 } from 'lucide-react';
import toast from 'react-hot-toast';
import Confetti from 'react-confetti';
import { getBrandInfo } from '../utils/brandUtils';

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const { items, totalPrice, clearCart } = useCart();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState(CHECKOUT_STEPS);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  
  // Form data
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress | undefined>();
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | undefined>();
  const [shippingMethod, setShippingMethod] = useState<ShippingMethod | undefined>();
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [discountCode, setDiscountCode] = useState('');
  
  // Order summary
  const [orderSummary, setOrderSummary] = useState<OrderSummaryType>({
    subtotal: 0,
    shippingCost: 0,
    tax: 0,
    discount: 0,
    total: 0,
    savings: 0
  });

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      navigate('/products');
      toast.error('سبد خرید شما خالی است');
    }
  }, [items, navigate]);

  // Calculate order summary
  useEffect(() => {
    const subtotal = totalPrice;
    const shippingCost = shippingMethod?.price || 0;
    const tax = 0; // No tax for now
    const discount = discountCode ? Math.round(subtotal * 0.1) : 0; // 10% discount example
    const processingFee = paymentMethod?.processingFee || 0;
    const total = subtotal + shippingCost + tax + processingFee - discount;
    const savings = discount + (shippingMethod?.isFree ? 25000 : 0);

    setOrderSummary({
      subtotal,
      shippingCost,
      tax,
      discount,
      total,
      savings
    });
  }, [totalPrice, shippingMethod, paymentMethod, discountCode]);

  // Update step completion status
  useEffect(() => {
    setSteps(prevSteps => prevSteps.map((step, index) => ({
      ...step,
      isCompleted: index < currentStep,
      isActive: index === currentStep,
      isDisabled: index > currentStep + 1
    })));
  }, [currentStep]);

  const handleStepClick = (stepIndex: number) => {
    if (stepIndex <= currentStep + 1) {
      setCurrentStep(stepIndex);
    }
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0: // Cart review
        return items.length > 0;
      case 1: // Shipping
        return shippingAddress && shippingMethod;
      case 2: // Payment
        return paymentMethod;
      case 3: // Review
        return agreeToTerms;
      default:
        return true;
    }
  };

  const handleApplyDiscount = async (code: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock validation - accept any code starting with "SAVE"
    if (code.toUpperCase().startsWith('SAVE')) {
      setDiscountCode(code);
      toast.success('کد تخفیف اعمال شد');
      return true;
    }
    
    return false;
  };

  const handleRemoveDiscount = () => {
    setDiscountCode('');
    toast.success('کد تخفیف حذف شد');
  };

  const handleSubmitOrder = async () => {
    if (!validateCurrentStep()) {
      toast.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate order submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success
      setShowConfetti(true);
      toast.success('سفارش شما با موفقیت ثبت شد!');
      
      // Clear cart and redirect
      setTimeout(() => {
        clearCart();
        navigate('/order-success', { 
          state: { 
            orderNumber: `GR${Date.now()}`,
            total: orderSummary.total 
          }
        });
      }, 3000);
      
    } catch (error) {
      toast.error('خطا در ثبت سفارش. لطفاً دوباره تلاش کنید');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Cart Review
        return (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <ShoppingCart className="w-5 h-5 text-primary-600" />
              بررسی سبد خرید ({items.length} کالا)
            </h3>
            
            <div className="space-y-4">
              {items.map((item, index) => {
                const itemPrice = item.product.discountedPrice || item.product.price;
                let variantPrice = 0;
                
                if (item.selectedVariants) {
                  Object.values(item.selectedVariants).forEach(variant => {
                    if (variant.price) {
                      variantPrice += variant.price;
                    }
                  });
                }
                
                const totalItemPrice = (itemPrice + variantPrice) * item.quantity;

                return (
                  <div key={`${item.product.id}-${item.variantKey || 'default'}`} className="flex gap-4 p-4 border border-gray-200 rounded-lg">
                    <img
                      src={item.product.imageSrc}
                      alt={item.product.name}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.product.name}</h4>

                      {/* Brand and Category */}
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-sm text-gray-600">{item.product.category}</span>
                        {item.product.brand && (
                          <div className="flex items-center gap-1">
                            {(() => {
                              const brandInfo = getBrandInfo(item.product.brand);
                              return brandInfo.logo ? (
                                <img
                                  src={brandInfo.logo}
                                  alt={`لوگو ${brandInfo.name}`}
                                  className="w-4 h-4 rounded object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : null;
                            })()}
                            <Building2 className={`w-3 h-3 text-gray-400 ${item.product.brand && getBrandInfo(item.product.brand).logo ? 'hidden' : ''}`} />
                            <span className="text-xs text-gray-500">{item.product.brand}</span>
                          </div>
                        )}
                      </div>

                      {item.selectedVariants && Object.keys(item.selectedVariants).length > 0 && (
                        <div className="text-sm text-gray-500 mt-1">
                          {Object.values(item.selectedVariants).map(variant => variant.name).join(', ')}
                        </div>
                      )}
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-gray-600">تعداد: {item.quantity}</span>
                        <span className="font-semibold text-primary-600">
                          {totalItemPrice.toLocaleString()} تومان
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );

      case 1: // Shipping
        return (
          <ShippingForm
            selectedAddress={shippingAddress}
            selectedShipping={shippingMethod}
            onAddressChange={setShippingAddress}
            onShippingChange={setShippingMethod}
            cartTotal={orderSummary.subtotal}
          />
        );

      case 2: // Payment
        return (
          <PaymentForm
            selectedPayment={paymentMethod}
            onPaymentChange={setPaymentMethod}
            orderTotal={orderSummary.total}
          />
        );

      case 3: // Review
        return (
          <div className="space-y-6">
            {/* Order Review */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                بررسی نهایی سفارش
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Shipping Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">آدرس تحویل</h4>
                  {shippingAddress && (
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>{shippingAddress.firstName} {shippingAddress.lastName}</p>
                      <p>{shippingAddress.phone}</p>
                      <p>{shippingAddress.address}</p>
                      <p>{shippingAddress.city}، {shippingAddress.province}</p>
                    </div>
                  )}
                </div>

                {/* Payment Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">روش پرداخت</h4>
                  {paymentMethod && (
                    <div className="text-sm text-gray-600">
                      <p>{paymentMethod.name}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Special Instructions */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                توضیحات ویژه (اختیاری)
              </h3>
              <textarea
                value={specialInstructions}
                onChange={(e) => setSpecialInstructions(e.target.value)}
                placeholder="توضیحات ویژه برای ارسال یا سایر نکات..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
              />
            </div>

            {/* Terms Agreement */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <label className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  className="mt-1 text-primary-600"
                />
                <span className="text-sm text-gray-700">
                  با <a href="/terms" className="text-primary-600 hover:underline">شرایط و قوانین</a> فروشگاه و 
                  <a href="/privacy" className="text-primary-600 hover:underline mr-1">حریم خصوصی</a> موافقم.
                </span>
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (items.length === 0) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      {showConfetti && (
        <Confetti
          width={window.innerWidth}
          height={window.innerHeight}
          recycle={false}
          numberOfPieces={200}
        />
      )}
      
      <div className="container-custom">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/products')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowRight className="w-4 h-4" />
            بازگشت به فروشگاه
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900">تسویه حساب</h1>
          <p className="text-gray-600 mt-2">
            تکمیل اطلاعات و ثبت سفارش
          </p>
        </div>

        {/* Checkout Steps */}
        <CheckoutSteps
          steps={steps}
          currentStep={currentStep}
          onStepClick={handleStepClick}
        />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Step Content */}
          <div className="lg:col-span-2">
            <AnimatePresence>
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStepContent()}
              </motion.div>
            </AnimatePresence>

            {/* Navigation */}
            <div className="mt-8">
              <StepNavigation
                currentStep={currentStep}
                totalSteps={steps.length}
                onPrevious={handlePrevious}
                onNext={handleNext}
                onSubmit={handleSubmitOrder}
                isNextDisabled={!validateCurrentStep()}
                isLoading={isLoading}
                nextButtonText="مرحله بعد"
                submitButtonText="ثبت سفارش"
              />
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <OrderSummary
              items={items}
              orderSummary={orderSummary}
              shippingMethod={shippingMethod}
              paymentMethod={paymentMethod}
              onApplyDiscount={handleApplyDiscount}
              onRemoveDiscount={handleRemoveDiscount}
              discountCode={discountCode}
              isLoading={isLoading}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
