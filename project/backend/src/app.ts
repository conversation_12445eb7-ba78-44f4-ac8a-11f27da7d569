import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import 'express-async-errors';

import { config } from './config';
import { logger, morganStream } from './config/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import customerRoutes from './routes/customers';
import productRoutes from './routes/products';
import categoryRoutes from './routes/categories';
import brandRoutes from './routes/brands';
import orderRoutes from './routes/orders';
import reviewRoutes from './routes/reviews';
import wishlistRoutes from './routes/wishlist';
import loyaltyRoutes from './routes/loyalty';
import uploadRoutes from './routes/upload';
import contentRoutes from './routes/content';
import inventoryRoutes from './routes/inventory';
import analyticsRoutes from './routes/analytics';
import healthRoutes from './routes/health';

// Create Express application
const app: Application = express();

// Trust proxy for rate limiting and IP detection
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors(config.cors));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindow,
  max: config.security.rateLimitMax,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    message: 'درخواست‌های زیادی از این IP ارسال شده، لطفاً بعداً تلاش کنید.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Cookie parser
app.use(cookieParser());

// HTTP request logging
app.use(morgan('combined', { stream: morganStream }));

// Health check endpoint (before API routes)
app.use('/health', healthRoutes);

// API routes
const apiPrefix = config.server.apiPrefix;

app.use(`${apiPrefix}/auth`, authRoutes);
app.use(`${apiPrefix}/users`, userRoutes);
app.use(`${apiPrefix}/customers`, customerRoutes);
app.use(`${apiPrefix}/products`, productRoutes);
app.use(`${apiPrefix}/categories`, categoryRoutes);
app.use(`${apiPrefix}/brands`, brandRoutes);
app.use(`${apiPrefix}/orders`, orderRoutes);
app.use(`${apiPrefix}/reviews`, reviewRoutes);
app.use(`${apiPrefix}/wishlist`, wishlistRoutes);
app.use(`${apiPrefix}/loyalty`, loyaltyRoutes);
app.use(`${apiPrefix}/content`, contentRoutes);
app.use(`${apiPrefix}/inventory`, inventoryRoutes);
app.use(`${apiPrefix}/analytics`, analyticsRoutes);
app.use(`${apiPrefix}/upload`, uploadRoutes);

// Serve static files (uploads)
app.use('/uploads', express.static(config.upload.uploadPath));

// API documentation endpoint
app.get(`${apiPrefix}/docs`, (req: Request, res: Response) => {
  res.json({
    message: 'GlowRoya API Documentation',
    version: config.server.apiVersion,
    endpoints: {
      auth: `${apiPrefix}/auth`,
      users: `${apiPrefix}/users`,
      products: `${apiPrefix}/products`,
      categories: `${apiPrefix}/categories`,
      brands: `${apiPrefix}/brands`,
      orders: `${apiPrefix}/orders`,
      reviews: `${apiPrefix}/reviews`,
      wishlist: `${apiPrefix}/wishlist`,
      loyalty: `${apiPrefix}/loyalty`,
      content: `${apiPrefix}/content`,
      inventory: `${apiPrefix}/inventory`,
      analytics: `${apiPrefix}/analytics`,
      upload: `${apiPrefix}/upload`,
      health: '/health',
    },
    documentation: 'https://docs.glowroya.com',
  });
});

// Root endpoint
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'GlowRoya Backend API',
    version: config.server.apiVersion,
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: config.server.env,
    documentation: `${req.protocol}://${req.get('host')}${apiPrefix}/docs`,
  });
});

// 404 handler
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

export default app;
