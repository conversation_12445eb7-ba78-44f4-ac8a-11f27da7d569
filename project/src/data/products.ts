import { Product } from '../types';

// Backend API base URL
const API_BASE_URL = 'http://localhost:3001';

// Helper function to get full image URL
const getFullImageUrl = (imagePath?: string): string => {
  if (!imagePath) return '/images/products/placeholder.svg';
  if (imagePath.startsWith('http')) return imagePath;
  return `${API_BASE_URL}${imagePath}`;
};

// Transform backend product to frontend Product
const transformBackendProduct = (backendProduct: any): Product => {
  const primaryImage = backendProduct.images?.find((img: any) => img.isPrimary);
  const allImages = backendProduct.images?.map((img: any) => getFullImageUrl(img.url)) || [];

  return {
    id: parseInt(backendProduct.id.replace(/[^0-9]/g, '')) || Math.floor(Math.random() * 1000000),
    name: backendProduct.name,
    category: backendProduct.categories?.[0]?.category?.name || 'عمومی',
    brand: backendProduct.brand?.name || '',
    price: parseFloat(backendProduct.price) || 0,
    discountedPrice: backendProduct.comparePrice ? parseFloat(backendProduct.comparePrice) : undefined,
    imageSrc: primaryImage ? getFullImageUrl(primaryImage.url) : '/images/products/placeholder.svg',
    images: allImages.length > 0 ? allImages : ['/images/products/placeholder.svg'],
    description: backendProduct.description || '',
    ingredients: backendProduct.ingredients
      ? Array.isArray(backendProduct.ingredients)
        ? backendProduct.ingredients
        : backendProduct.ingredients.split(',').map((i: string) => i.trim())
      : [],
    benefits: backendProduct.benefits
      ? Array.isArray(backendProduct.benefits)
        ? backendProduct.benefits
        : backendProduct.benefits.split(',').map((b: string) => b.trim())
      : [],
    howToUse: backendProduct.howToUse
      ? Array.isArray(backendProduct.howToUse)
        ? backendProduct.howToUse
        : typeof backendProduct.howToUse === 'string'
        ? backendProduct.howToUse.split('\n').filter((step: string) => step.trim())
        : []
      : [],
    skinType: backendProduct.skinType
      ? Array.isArray(backendProduct.skinType)
        ? backendProduct.skinType
        : backendProduct.skinType.split(',').map((s: string) => s.trim())
      : [],
    size: backendProduct.size || '',
    inStock: backendProduct.inventory?.quantity > 0 || false,
    stock: backendProduct.inventory?.quantity || 0,
    featured: backendProduct.isFeatured || false,
    isNew: false, // Backend doesn't have this field yet
    isBestSeller: false, // Backend doesn't have this field yet
    rating: 0, // TODO: Calculate from reviews
    reviewCount: 0, // TODO: Get from reviews
    tags: backendProduct.tags
      ? Array.isArray(backendProduct.tags)
        ? backendProduct.tags
        : backendProduct.tags.split(',').map((t: string) => t.trim())
      : []
  };
};

// Cache for products
let productsCache: Product[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Load products from backend API
const loadProductsFromAPI = async (): Promise<Product[]> => {
  try {
    const apiUrl = `${API_BASE_URL}/api/v1/products?include=brand,categories,images,inventory&limit=100`;
    console.log('🔄 Loading products from API:', apiUrl);

    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error('❌ API response not OK:', response.status, response.statusText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📦 API response data:', data);

    // Handle the correct API response format
    if (data.success && Array.isArray(data.data)) {
      console.log(`✅ Successfully loaded ${data.data.length} products from API`);
      return data.data.map(transformBackendProduct);
    }

    console.error('❌ Invalid API response format:', data);
    throw new Error('Invalid API response format');
  } catch (error) {
    console.warn('❌ Failed to load products from API, using fallback data:', error);
    return getFallbackProducts();
  }
};

// Get products with caching
export const getProducts = async (): Promise<Product[]> => {
  const now = Date.now();

  // Return cached data if still valid
  if (productsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return productsCache;
  }

  // Load fresh data
  productsCache = await loadProductsFromAPI();
  cacheTimestamp = now;

  return productsCache;
};

// Fallback products for when API is unavailable
const getFallbackProducts = (): Product[] => [
  {
    id: 1,
    name: 'سرم هیالورونیک اسید',
    category: 'سرم',
    brand: 'گلو رویا',
    price: 320000,
    discountedPrice: 280000,
    rating: 4.8,
    reviewCount: 156,
    imageSrc: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    description: 'سرم هیالورونیک اسید با فرمولاسیون پیشرفته برای آبرسانی عمیق پوست',
    ingredients: ['هیالورونیک اسید', 'ویتامین B5', 'آلوئه ورا', 'گلیسرین'],
    benefits: ['آبرسانی عمیق', 'کاهش خطوط ریز', 'افزایش کشسانی پوست'],
    howToUse: ['صبح و شب روی پوست تمیز و مرطوب اعمال کنید', 'با حرکات دایره‌ای ملایم ماساژ دهید', 'اجازه دهید کاملاً جذب شود'],
    skinType: ['خشک', 'معمولی', 'حساس'],
    size: '30ml',
    inStock: true,
    stock: 25,
    featured: true,
    tags: ['آبرسان', 'ضد پیری', 'طبیعی']
  },
  {
    id: 2,
    name: 'کرم BB با SPF 30',
    category: 'کرم',
    brand: 'لورآل',
    price: 245000,
    rating: 4.5,
    reviewCount: 89,
    imageSrc: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    description: 'کرم BB چندکاره با SPF 30 برای محافظت و پوشش طبیعی پوست',
    ingredients: ['فیلترهای ضد آفتاب', 'هیالورونیک اسید', 'ویتامین E'],
    benefits: ['محافظت در برابر UV', 'پوشش طبیعی', 'مرطوب کنندگی'],
    howToUse: ['صبح‌ها پس از روتین مراقبت از پوست استفاده کنید', 'مقدار کمی روی صورت پخش کنید', 'با انگشتان به آرامی بلند کنید'],
    skinType: ['معمولی', 'خشک', 'مختلط'],
    size: '50ml',
    inStock: true,
    stock: 15,
    featured: false,
    tags: ['ضد آفتاب', 'پوشش', 'مرطوب کننده']
  },
  {
    id: 3,
    name: 'ماسک شب احیاکننده',
    category: 'ماسک',
    brand: 'نیویا',
    price: 195000,
    discountedPrice: 175000,
    rating: 4.7,
    reviewCount: 62,
    imageSrc: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    description: 'ماسک شب احیاکننده برای ترمیم و تغذیه پوست در طول شب',
    ingredients: ['ریتینول', 'پپتیدها', 'اسیدهای آمینه', 'روغن آرگان'],
    benefits: ['ترمیم شبانه', 'کاهش علائم خستگی', 'تغذیه عمیق'],
    howToUse: ['شب‌ها پس از تمیز کردن پوست استفاده کنید', 'لایه نازکی روی صورت و گردن اعمال کنید', 'صبح با آب ولرم بشویید'],
    skinType: ['خشک', 'معمولی', 'بالغ'],
    size: '75ml',
    inStock: true,
    stock: 8,
    featured: false,
    tags: ['شبانه', 'ترمیم', 'ضد پیری']
  }
];

// Async utility functions that use the API
export const getProductById = async (id: number): Promise<Product | undefined> => {
  const products = await getProducts();
  return products.find(product => product.id === id);
};

export const getProductsByCategory = async (category: string): Promise<Product[]> => {
  const products = await getProducts();
  return products.filter(product => product.category === category);
};

export const getNewProducts = async (): Promise<Product[]> => {
  const products = await getProducts();
  return products.filter(product => product.featured);
};

export const getBestSellers = async (): Promise<Product[]> => {
  const products = await getProducts();
  return products.filter(product => product.featured);
};

export const getDiscountedProducts = async (): Promise<Product[]> => {
  const products = await getProducts();
  return products.filter(product => product.discountedPrice);
};

// Synchronous fallback functions for immediate use (uses cache if available)
export const getProductsSync = (): Product[] => {
  if (!productsCache) return getFallbackProducts();
  return productsCache;
};

export const getProductByIdSync = (id: number): Product | undefined => {
  if (!productsCache) return undefined;
  return productsCache.find(product => product.id === id);
};

export const getProductsByCategorySync = (category: string): Product[] => {
  if (!productsCache) return [];
  return productsCache.filter(product => product.category === category);
};

export const getNewProductsSync = (): Product[] => {
  if (!productsCache) return getFallbackProducts().filter(product => product.featured);
  return productsCache.filter(product => product.featured);
};

export const getBestSellersSync = (): Product[] => {
  if (!productsCache) return getFallbackProducts().filter(product => product.featured);
  return productsCache.filter(product => product.featured);
};

export const getDiscountedProductsSync = (): Product[] => {
  if (!productsCache) return getFallbackProducts().filter(product => product.discountedPrice);
  return productsCache.filter(product => product.discountedPrice);
};

// Legacy export for backward compatibility
export const products = getFallbackProducts();

// Initialize products cache
export const initializeProducts = async (): Promise<void> => {
  try {
    await getProducts();
    console.log('✅ Products cache initialized successfully');
  } catch (error) {
    console.warn('⚠️ Failed to initialize products cache:', error);
  }
};