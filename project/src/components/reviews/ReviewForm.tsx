import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { 
  Star, 
  Upload, 
  X, 
  Plus, 
  Minus, 
  Camera,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { ReviewFormData, PERSIAN_REVIEW_TEMPLATES, PERSIAN_REVIEW_MESSAGES } from '../../types/review';
import { QuickRating } from './RatingStars';
import toast from 'react-hot-toast';

interface ReviewFormProps {
  productId: number;
  productName: string;
  onSubmit: (data: ReviewFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Validation schema
const reviewSchema = yup.object({
  rating: yup.number()
    .min(1, 'لطفاً امتیاز خود را انتخاب کنید')
    .max(5)
    .required('امتیاز الزامی است'),
  title: yup.string()
    .min(5, 'عنوان باید حداقل ۵ کاراکتر باشد')
    .max(100, 'عنوان نباید بیش از ۱۰۰ کاراکتر باشد')
    .required('عنوان الزامی است'),
  comment: yup.string()
    .min(20, 'نظر باید حداقل ۲۰ کاراکتر باشد')
    .max(1000, 'نظر نباید بیش از ۱۰۰۰ کاراکتر باشد')
    .required('نظر الزامی است'),
  isRecommended: yup.boolean().required(),
  skinType: yup.string().optional(),
  ageRange: yup.string().optional(),
  usageDuration: yup.string().optional()
});

const ReviewForm: React.FC<ReviewFormProps> = ({
  productId,
  productName,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [pros, setPros] = useState<string[]>(['']);
  const [cons, setCons] = useState<string[]>(['']);
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<ReviewFormData>({
    resolver: yupResolver(reviewSchema),
    defaultValues: {
      rating: 0,
      title: '',
      comment: '',
      isRecommended: true,
      pros: [],
      cons: []
    },
    mode: 'onChange'
  });

  const watchedRating = watch('rating');
  const watchedComment = watch('comment');

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (images.length + files.length > 5) {
      toast.error('حداکثر ۵ تصویر می‌توانید آپلود کنید');
      return;
    }

    const validFiles = files.filter(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`فایل ${file.name} بیش از ۵ مگابایت است`);
        return false;
      }
      if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
        toast.error(`فرمت فایل ${file.name} پشتیبانی نمی‌شود`);
        return false;
      }
      return true;
    });

    setImages(prev => [...prev, ...validFiles]);

    // Create previews
    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const addProsCons = (type: 'pros' | 'cons') => {
    if (type === 'pros' && pros.length < 5) {
      setPros([...pros, '']);
    } else if (type === 'cons' && cons.length < 5) {
      setCons([...cons, '']);
    }
  };

  const removeProsCons = (type: 'pros' | 'cons', index: number) => {
    if (type === 'pros') {
      setPros(pros.filter((_, i) => i !== index));
    } else {
      setCons(cons.filter((_, i) => i !== index));
    }
  };

  const updateProsCons = (type: 'pros' | 'cons', index: number, value: string) => {
    if (type === 'pros') {
      const newPros = [...pros];
      newPros[index] = value;
      setPros(newPros);
    } else {
      const newCons = [...cons];
      newCons[index] = value;
      setCons(newCons);
    }
  };

  const onFormSubmit = async (data: ReviewFormData) => {
    try {
      const formData = {
        ...data,
        pros: pros.filter(p => p.trim()),
        cons: cons.filter(c => c.trim()),
        images
      };
      
      await onSubmit(formData);
      toast.success(PERSIAN_REVIEW_MESSAGES.success.added);
    } catch (error) {
      toast.error(PERSIAN_REVIEW_MESSAGES.errors.networkError);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg border border-gray-200 p-6"
    >
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          نظر خود را درباره {productName} بنویسید
        </h3>
        <p className="text-gray-600 text-sm">
          تجربه خود را با سایر کاربران به اشتراک بگذارید
        </p>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            امتیاز شما *
          </label>
          <Controller
            name="rating"
            control={control}
            render={({ field }) => (
              <QuickRating
                value={field.value}
                onChange={field.onChange}
                error={errors.rating?.message}
              />
            )}
          />
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            عنوان نظر *
          </label>
          <Controller
            name="title"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder={PERSIAN_REVIEW_MESSAGES.placeholders.title}
                className={`
                  w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500
                  ${errors.title ? 'border-red-300' : 'border-gray-300'}
                `}
              />
            )}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        {/* Comment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نظر شما *
          </label>
          <Controller
            name="comment"
            control={control}
            render={({ field }) => (
              <div>
                <textarea
                  {...field}
                  rows={5}
                  placeholder={PERSIAN_REVIEW_MESSAGES.placeholders.comment}
                  className={`
                    w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none
                    ${errors.comment ? 'border-red-300' : 'border-gray-300'}
                  `}
                />
                <div className="flex justify-between mt-1">
                  <div>
                    {errors.comment && (
                      <p className="text-sm text-red-600">{errors.comment.message}</p>
                    )}
                  </div>
                  <span className={`text-xs ${
                    watchedComment?.length > 1000 ? 'text-red-500' : 'text-gray-500'
                  }`}>
                    {watchedComment?.length || 0}/1000
                  </span>
                </div>
              </div>
            )}
          />
        </div>

        {/* Recommendation */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            آیا این محصول را پیشنهاد می‌کنید؟
          </label>
          <Controller
            name="isRecommended"
            control={control}
            render={({ field }) => (
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={field.value === true}
                    onChange={() => field.onChange(true)}
                    className="ml-2 text-primary-600"
                  />
                  <span className="text-green-600">بله، پیشنهاد می‌کنم</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={field.value === false}
                    onChange={() => field.onChange(false)}
                    className="ml-2 text-primary-600"
                  />
                  <span className="text-red-600">خیر، پیشنهاد نمی‌کنم</span>
                </label>
              </div>
            )}
          />
        </div>

        {/* User Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع پوست
            </label>
            <Controller
              name="skinType"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">انتخاب کنید</option>
                  {PERSIAN_REVIEW_TEMPLATES.skinTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رده سنی
            </label>
            <Controller
              name="ageRange"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">انتخاب کنید</option>
                  {PERSIAN_REVIEW_TEMPLATES.ageRanges.map(range => (
                    <option key={range.value} value={range.value}>
                      {range.label}
                    </option>
                  ))}
                </select>
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مدت استفاده
            </label>
            <Controller
              name="usageDuration"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">انتخاب کنید</option>
                  {PERSIAN_REVIEW_TEMPLATES.usageDurations.map(duration => (
                    <option key={duration.value} value={duration.value}>
                      {duration.label}
                    </option>
                  ))}
                </select>
              )}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            disabled={isLoading}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            انصراف
          </button>
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                در حال ارسال...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4" />
                ثبت نظر
              </>
            )}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default ReviewForm;
