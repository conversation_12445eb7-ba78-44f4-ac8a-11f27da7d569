import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Settings,
  Globe,
  CreditCard,
  Truck,
  Bell,
  Shield,
  Receipt,
  Database,
  Save,
  RotateCcw,
  TestTube,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import AdminLayout from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import AdminButton from '../../../components/admin/common/AdminButton';
import { useAdminSettings } from '../../../hooks/useAdminSettings';
import { formatNumber } from '../../../utils/formatters';
import toast from 'react-hot-toast';

const SettingsDashboardPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const {
    generalSettings,
    paymentSettings,
    shippingSettings,
    notificationSettings,
    securitySettings,
    taxSettings,
    backupSettings,
    loading,
    saving,
    error,
    updateSettings,
    testConnection,
    resetToDefaults,
    refetch
  } = useAdminSettings();

  // Determine active tab from URL hash
  const getActiveTabFromHash = () => {
    const hash = location.hash.replace('#', '');
    if (['general', 'payment', 'shipping', 'notifications', 'security', 'tax', 'backup'].includes(hash)) {
      return hash;
    }
    return 'general'; // default
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromHash());
  const [formData, setFormData] = useState<any>({});
  const [testingConnection, setTestingConnection] = useState<string | null>(null);

  // Update active tab when URL hash changes
  useEffect(() => {
    setActiveTab(getActiveTabFromHash());
  }, [location.hash]);

  const handleInputChange = (section: string, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleSaveSettings = async (section: string) => {
    try {
      const result = await updateSettings(section, { [section]: formData[section] });
      if (result.success) {
        toast.success('تنظیمات با موفقیت ذخیره شد');
        setFormData((prev: any) => ({ ...prev, [section]: {} }));
      } else {
        toast.error(result.error || 'خطا در ذخیره تنظیمات');
      }
    } catch (error) {
      toast.error('خطا در ذخیره تنظیمات');
    }
  };

  const handleTestConnection = async (type: 'email' | 'sms' | 'payment') => {
    setTestingConnection(type);
    try {
      const result = await testConnection(type);
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('خطا در تست اتصال');
    } finally {
      setTestingConnection(null);
    }
  };

  const handleResetToDefaults = async (section: string) => {
    if (window.confirm('آیا از بازنشانی تنظیمات به حالت پیش‌فرض اطمینان دارید؟')) {
      try {
        const result = await resetToDefaults(section);
        if (result.success) {
          toast.success('تنظیمات به حالت پیش‌فرض بازنشانی شد');
          setFormData((prev: any) => ({ ...prev, [section]: {} }));
        } else {
          toast.error(result.error || 'خطا در بازنشانی تنظیمات');
        }
      } catch (error) {
        toast.error('خطا در بازنشانی تنظیمات');
      }
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-600"></div>
          <span className="mr-3 text-gray-600">در حال بارگذاری تنظیمات...</span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <AdminCard>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <AdminButton onClick={refetch}>
              تلاش مجدد
            </AdminButton>
          </div>
        </AdminCard>
      </AdminLayout>
    );
  }

  const tabs = [
    { id: 'general', label: 'عمومی', icon: Globe, color: 'text-blue-600' },
    { id: 'payment', label: 'پرداخت', icon: CreditCard, color: 'text-green-600' },
    { id: 'shipping', label: 'ارسال', icon: Truck, color: 'text-orange-600' },
    { id: 'notifications', label: 'اعلان‌ها', icon: Bell, color: 'text-purple-600' },
    { id: 'security', label: 'امنیت', icon: Shield, color: 'text-red-600' },
    { id: 'tax', label: 'مالیات', icon: Receipt, color: 'text-yellow-600' },
    { id: 'backup', label: 'پشتیبان‌گیری', icon: Database, color: 'text-indigo-600' }
  ];

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نام سایت
          </label>
          <input
            type="text"
            value={formData.general?.siteName ?? generalSettings?.siteName ?? ''}
            onChange={(e) => handleInputChange('general', 'siteName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ایمیل مدیر
          </label>
          <input
            type="email"
            value={formData.general?.adminEmail ?? generalSettings?.adminEmail ?? ''}
            onChange={(e) => handleInputChange('general', 'adminEmail', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            شماره تلفن
          </label>
          <input
            type="tel"
            value={formData.general?.phone ?? generalSettings?.phone ?? ''}
            onChange={(e) => handleInputChange('general', 'phone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            واحد پول
          </label>
          <select
            value={formData.general?.currency ?? generalSettings?.currency ?? 'IRR'}
            onChange={(e) => handleInputChange('general', 'currency', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          >
            <option value="IRR">ریال ایران (IRR)</option>
            <option value="USD">دلار آمریکا (USD)</option>
            <option value="EUR">یورو (EUR)</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          توضیحات سایت
        </label>
        <textarea
          rows={3}
          value={formData.general?.siteDescription ?? generalSettings?.siteDescription ?? ''}
          onChange={(e) => handleInputChange('general', 'siteDescription', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          آدرس
        </label>
        <textarea
          rows={2}
          value={formData.general?.address ?? generalSettings?.address ?? ''}
          onChange={(e) => handleInputChange('general', 'address', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
        />
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="maintenanceMode"
            checked={formData.general?.maintenanceMode ?? generalSettings?.maintenanceMode ?? false}
            onChange={(e) => handleInputChange('general', 'maintenanceMode', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="maintenanceMode" className="mr-2 block text-sm text-gray-900">
            حالت تعمیر و نگهداری
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="allowRegistration"
            checked={formData.general?.allowRegistration ?? generalSettings?.allowRegistration ?? true}
            onChange={(e) => handleInputChange('general', 'allowRegistration', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="allowRegistration" className="mr-2 block text-sm text-gray-900">
            امکان ثبت‌نام کاربران جدید
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="requireEmailVerification"
            checked={formData.general?.requireEmailVerification ?? generalSettings?.requireEmailVerification ?? true}
            onChange={(e) => handleInputChange('general', 'requireEmailVerification', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="requireEmailVerification" className="mr-2 block text-sm text-gray-900">
            تأیید ایمیل الزامی
          </label>
        </div>
      </div>
    </div>
  );

  const renderPaymentSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حداقل مبلغ سفارش (تومان)
          </label>
          <input
            type="number"
            value={formData.payment?.minimumOrderAmount ?? paymentSettings?.minimumOrderAmount ?? 0}
            onChange={(e) => handleInputChange('payment', 'minimumOrderAmount', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حداکثر مبلغ سفارش (تومان)
          </label>
          <input
            type="number"
            value={formData.payment?.maximumOrderAmount ?? paymentSettings?.maximumOrderAmount ?? 0}
            onChange={(e) => handleInputChange('payment', 'maximumOrderAmount', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نرخ مالیات (درصد)
          </label>
          <input
            type="number"
            step="0.1"
            value={formData.payment?.taxRate ?? paymentSettings?.taxRate ?? 0}
            onChange={(e) => handleInputChange('payment', 'taxRate', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            کارمزد پرداخت (درصد)
          </label>
          <input
            type="number"
            step="0.1"
            value={formData.payment?.processingFee ?? paymentSettings?.processingFee ?? 0}
            onChange={(e) => handleInputChange('payment', 'processingFee', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="pricesIncludeTax"
            checked={formData.payment?.pricesIncludeTax ?? paymentSettings?.pricesIncludeTax ?? true}
            onChange={(e) => handleInputChange('payment', 'pricesIncludeTax', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="pricesIncludeTax" className="mr-2 block text-sm text-gray-900">
            قیمت‌ها شامل مالیات
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableRefunds"
            checked={formData.payment?.enableRefunds ?? paymentSettings?.enableRefunds ?? true}
            onChange={(e) => handleInputChange('payment', 'enableRefunds', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableRefunds" className="mr-2 block text-sm text-gray-900">
            امکان بازگشت وجه
          </label>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">روش‌های پرداخت فعال</h4>
        <div className="space-y-3">
          {paymentSettings?.enabledMethods.map((method) => (
            <div key={method.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
              <div>
                <h5 className="font-medium text-gray-900">{method.name}</h5>
                <p className="text-sm text-gray-500">{method.description}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  method.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {method.enabled ? 'فعال' : 'غیرفعال'}
                </span>
                <AdminButton
                  size="sm"
                  variant="outline"
                  icon={TestTube}
                  onClick={() => handleTestConnection('payment')}
                  loading={testingConnection === 'payment'}
                >
                  تست
                </AdminButton>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderShippingSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            هزینه ارسال رایگان (تومان)
          </label>
          <input
            type="number"
            value={formData.shipping?.freeShippingThreshold ?? shippingSettings?.freeShippingThreshold ?? 0}
            onChange={(e) => handleInputChange('shipping', 'freeShippingThreshold', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            هزینه ارسال پایه (تومان)
          </label>
          <input
            type="number"
            value={formData.shipping?.baseShippingCost ?? shippingSettings?.baseShippingCost ?? 0}
            onChange={(e) => handleInputChange('shipping', 'baseShippingCost', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حداکثر وزن ارسال (کیلوگرم)
          </label>
          <input
            type="number"
            step="0.1"
            value={formData.shipping?.maxWeight ?? shippingSettings?.maxWeight ?? 0}
            onChange={(e) => handleInputChange('shipping', 'maxWeight', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            زمان پردازش (روز)
          </label>
          <input
            type="number"
            value={formData.shipping?.processingDays ?? shippingSettings?.processingDays ?? 1}
            onChange={(e) => handleInputChange('shipping', 'processingDays', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableExpressShipping"
            checked={formData.shipping?.enableExpressShipping ?? shippingSettings?.enableExpressShipping ?? false}
            onChange={(e) => handleInputChange('shipping', 'enableExpressShipping', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableExpressShipping" className="mr-2 block text-sm text-gray-900">
            ارسال فوری
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableTrackingNumbers"
            checked={formData.shipping?.enableTrackingNumbers ?? shippingSettings?.enableTrackingNumbers ?? true}
            onChange={(e) => handleInputChange('shipping', 'enableTrackingNumbers', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableTrackingNumbers" className="mr-2 block text-sm text-gray-900">
            کد رهگیری
          </label>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">اعلان‌های ایمیل</h4>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="emailNewOrders"
                checked={formData.notifications?.emailNewOrders ?? notificationSettings?.emailNewOrders ?? true}
                onChange={(e) => handleInputChange('notifications', 'emailNewOrders', e.target.checked)}
                className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
              />
              <label htmlFor="emailNewOrders" className="mr-2 block text-sm text-gray-900">
                سفارش جدید
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="emailLowStock"
                checked={formData.notifications?.emailLowStock ?? notificationSettings?.emailLowStock ?? true}
                onChange={(e) => handleInputChange('notifications', 'emailLowStock', e.target.checked)}
                className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
              />
              <label htmlFor="emailLowStock" className="mr-2 block text-sm text-gray-900">
                کمبود موجودی
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="emailNewReviews"
                checked={formData.notifications?.emailNewReviews ?? notificationSettings?.emailNewReviews ?? true}
                onChange={(e) => handleInputChange('notifications', 'emailNewReviews', e.target.checked)}
                className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
              />
              <label htmlFor="emailNewReviews" className="mr-2 block text-sm text-gray-900">
                نظر جدید
              </label>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">اعلان‌های SMS</h4>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="smsOrderStatus"
                checked={formData.notifications?.smsOrderStatus ?? notificationSettings?.smsOrderStatus ?? false}
                onChange={(e) => handleInputChange('notifications', 'smsOrderStatus', e.target.checked)}
                className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
              />
              <label htmlFor="smsOrderStatus" className="mr-2 block text-sm text-gray-900">
                وضعیت سفارش
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="smsPromotions"
                checked={formData.notifications?.smsPromotions ?? notificationSettings?.smsPromotions ?? false}
                onChange={(e) => handleInputChange('notifications', 'smsPromotions', e.target.checked)}
                className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
              />
              <label htmlFor="smsPromotions" className="mr-2 block text-sm text-gray-900">
                تخفیف‌ها و پیشنهادات
              </label>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">تست اتصال</h4>
        <div className="flex gap-3">
          <AdminButton
            size="sm"
            variant="outline"
            icon={TestTube}
            onClick={() => handleTestConnection('email')}
            loading={testingConnection === 'email'}
          >
            تست ایمیل
          </AdminButton>
          <AdminButton
            size="sm"
            variant="outline"
            icon={TestTube}
            onClick={() => handleTestConnection('sms')}
            loading={testingConnection === 'sms'}
          >
            تست SMS
          </AdminButton>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حداکثر تلاش ورود
          </label>
          <input
            type="number"
            value={formData.security?.maxLoginAttempts ?? securitySettings?.maxLoginAttempts ?? 5}
            onChange={(e) => handleInputChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            مدت زمان نشست (ثانیه)
          </label>
          <input
            type="number"
            value={formData.security?.sessionTimeout ?? securitySettings?.sessionTimeout ?? 3600}
            onChange={(e) => handleInputChange('security', 'sessionTimeout', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حداقل طول رمز عبور
          </label>
          <input
            type="number"
            value={formData.security?.passwordMinLength ?? securitySettings?.passwordPolicy?.minLength ?? 8}
            onChange={(e) => handleInputChange('security', 'passwordMinLength', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            انقضای رمز عبور (روز)
          </label>
          <input
            type="number"
            value={formData.security?.passwordExpiryDays ?? securitySettings?.passwordPolicy?.passwordExpiryDays ?? 90}
            onChange={(e) => handleInputChange('security', 'passwordExpiryDays', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableSsl"
            checked={formData.security?.enableSsl ?? securitySettings?.enableSsl ?? true}
            onChange={(e) => handleInputChange('security', 'enableSsl', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableSsl" className="mr-2 block text-sm text-gray-900">
            فعال‌سازی SSL
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableTwoFactor"
            checked={formData.security?.enableTwoFactor ?? securitySettings?.enableTwoFactor ?? false}
            onChange={(e) => handleInputChange('security', 'enableTwoFactor', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableTwoFactor" className="mr-2 block text-sm text-gray-900">
            احراز هویت دو مرحله‌ای
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableAuditLog"
            checked={formData.security?.enableAuditLog ?? securitySettings?.enableAuditLog ?? true}
            onChange={(e) => handleInputChange('security', 'enableAuditLog', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableAuditLog" className="mr-2 block text-sm text-gray-900">
            گزارش عملکرد
          </label>
        </div>
      </div>
    </div>
  );

  const renderTaxSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نرخ مالیات پیش‌فرض (درصد)
          </label>
          <input
            type="number"
            step="0.1"
            value={formData.tax?.defaultTaxRate ?? taxSettings?.defaultTaxRate ?? 9}
            onChange={(e) => handleInputChange('tax', 'defaultTaxRate', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            شماره اقتصادی
          </label>
          <input
            type="text"
            value={formData.tax?.economicCode ?? taxSettings?.economicCode ?? ''}
            onChange={(e) => handleInputChange('tax', 'economicCode', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            شناسه ملی شرکت
          </label>
          <input
            type="text"
            value={formData.tax?.companyNationalId ?? taxSettings?.companyNationalId ?? ''}
            onChange={(e) => handleInputChange('tax', 'companyNationalId', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            کد پستی شرکت
          </label>
          <input
            type="text"
            value={formData.tax?.companyPostalCode ?? taxSettings?.companyPostalCode ?? ''}
            onChange={(e) => handleInputChange('tax', 'companyPostalCode', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableTax"
            checked={formData.tax?.enableTax ?? taxSettings?.enableTax ?? true}
            onChange={(e) => handleInputChange('tax', 'enableTax', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableTax" className="mr-2 block text-sm text-gray-900">
            فعال‌سازی مالیات
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="taxIncludedInPrices"
            checked={formData.tax?.taxIncludedInPrices ?? taxSettings?.taxIncludedInPrices ?? true}
            onChange={(e) => handleInputChange('tax', 'taxIncludedInPrices', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="taxIncludedInPrices" className="mr-2 block text-sm text-gray-900">
            مالیات در قیمت محصولات
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableTaxByLocation"
            checked={formData.tax?.enableTaxByLocation ?? taxSettings?.enableTaxByLocation ?? false}
            onChange={(e) => handleInputChange('tax', 'enableTaxByLocation', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableTaxByLocation" className="mr-2 block text-sm text-gray-900">
            مالیات بر اساس موقعیت
          </label>
        </div>
      </div>
    </div>
  );

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            دوره پشتیبان‌گیری
          </label>
          <select
            value={formData.backup?.backupFrequency ?? backupSettings?.backupFrequency ?? 'daily'}
            onChange={(e) => handleInputChange('backup', 'backupFrequency', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          >
            <option value="hourly">هر ساعت</option>
            <option value="daily">روزانه</option>
            <option value="weekly">هفتگی</option>
            <option value="monthly">ماهانه</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            زمان پشتیبان‌گیری
          </label>
          <input
            type="time"
            value={formData.backup?.backupTime ?? backupSettings?.backupTime ?? '02:00'}
            onChange={(e) => handleInputChange('backup', 'backupTime', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            مدت نگهداری (روز)
          </label>
          <input
            type="number"
            value={formData.backup?.retentionDays ?? backupSettings?.retentionDays ?? 30}
            onChange={(e) => handleInputChange('backup', 'retentionDays', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            محل ذخیره
          </label>
          <select
            value={formData.backup?.backupLocation ?? backupSettings?.backupLocation ?? 'local'}
            onChange={(e) => handleInputChange('backup', 'backupLocation', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-admin-500"
          >
            <option value="local">محلی</option>
            <option value="cloud">ابری</option>
            <option value="ftp">FTP</option>
          </select>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableAutoBackup"
            checked={formData.backup?.enableAutoBackup ?? backupSettings?.enableAutoBackup ?? true}
            onChange={(e) => handleInputChange('backup', 'enableAutoBackup', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableAutoBackup" className="mr-2 block text-sm text-gray-900">
            پشتیبان‌گیری خودکار
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableDatabaseBackup"
            checked={formData.backup?.enableDatabaseBackup ?? backupSettings?.enableDatabaseBackup ?? true}
            onChange={(e) => handleInputChange('backup', 'enableDatabaseBackup', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableDatabaseBackup" className="mr-2 block text-sm text-gray-900">
            پشتیبان‌گیری پایگاه داده
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enableFileBackup"
            checked={formData.backup?.enableFileBackup ?? backupSettings?.enableFileBackup ?? true}
            onChange={(e) => handleInputChange('backup', 'enableFileBackup', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="enableFileBackup" className="mr-2 block text-sm text-gray-900">
            پشتیبان‌گیری فایل‌ها
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="compressionEnabled"
            checked={formData.backup?.compressionEnabled ?? backupSettings?.compressionEnabled ?? true}
            onChange={(e) => handleInputChange('backup', 'compressionEnabled', e.target.checked)}
            className="h-4 w-4 text-admin-600 focus:ring-admin-500 border-gray-300 rounded"
          />
          <label htmlFor="compressionEnabled" className="mr-2 block text-sm text-gray-900">
            فشرده‌سازی
          </label>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">عملیات پشتیبان‌گیری</h4>
        <div className="flex gap-3">
          <AdminButton
            size="sm"
            variant="outline"
            icon={Download}
            onClick={() => toast.success('پشتیبان‌گیری دستی شروع شد')}
          >
            پشتیبان‌گیری دستی
          </AdminButton>
          <AdminButton
            size="sm"
            variant="outline"
            onClick={() => toast.success('بازیابی از پشتیبان شروع شد')}
          >
            بازیابی از پشتیبان
          </AdminButton>
        </div>
      </div>
    </div>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Settings className="w-8 h-8 text-admin-600" />
              تنظیمات سیستم
            </h1>
            <p className="text-gray-600 mt-1">
              مدیریت تنظیمات عمومی و پیکربندی سیستم
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    navigate(`/admin/settings#${tab.id}`);
                  }}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-admin-500 text-admin-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className={`w-4 h-4 ${tab.color}`} />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <AdminCard>
          <div className="space-y-6">
            {activeTab === 'general' && renderGeneralSettings()}
            {activeTab === 'payment' && renderPaymentSettings()}
            {activeTab === 'shipping' && renderShippingSettings()}
            {activeTab === 'notifications' && renderNotificationSettings()}
            {activeTab === 'security' && renderSecuritySettings()}
            {activeTab === 'tax' && renderTaxSettings()}
            {activeTab === 'backup' && renderBackupSettings()}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <AdminButton
              variant="outline"
              icon={RotateCcw}
              onClick={() => handleResetToDefaults(activeTab)}
            >
              بازنشانی به پیش‌فرض
            </AdminButton>
            
            <div className="flex gap-3">
              <AdminButton
                variant="primary"
                icon={Save}
                onClick={() => handleSaveSettings(activeTab)}
                loading={saving}
              >
                ذخیره تغییرات
              </AdminButton>
            </div>
          </div>
        </AdminCard>
      </div>
    </AdminLayout>
  );
};

export default SettingsDashboardPage;
