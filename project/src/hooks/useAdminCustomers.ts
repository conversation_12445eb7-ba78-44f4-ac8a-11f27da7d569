import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAdminAuth } from './useAdminAuth';
import {
  AdminCustomer,
  CustomerFilters,
  CustomerSortOptions,
  CustomerAnalytics,
  CustomerNote,
  SupportTicket,
  PERSIAN_CUSTOMER_MESSAGES
} from '../types/adminCustomer';
import {
  filterCustomers,
  sortCustomers,
  calculateCustomerAnalytics,
  determineCustomerSegment,
  calculateRiskScore,
  formatCustomerName
} from '../utils/customerUtils';
import { LOYALTY_TIERS } from '../types/loyalty';
import { AdminCustomerService } from '../services/adminCustomerService';

export const useAdminCustomers = () => {
  const { user, checkPermission } = useAdminAuth();
  const [customers, setCustomers] = useState<AdminCustomer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<CustomerFilters>({});
  const [sortBy, setSortBy] = useState<CustomerSortOptions['field']>('createdAt');
  const [sortOrder, setSortOrder] = useState<CustomerSortOptions['direction']>('desc');

  // Generate mock customer data
  const generateMockCustomers = useCallback((): AdminCustomer[] => {
    const mockCustomers: AdminCustomer[] = [];

    const firstNames = ['علی', 'فاطمه', 'محمد', 'زهرا', 'حسن', 'مریم', 'رضا', 'سارا', 'احمد', 'نرگس', 'مهدی', 'لیلا', 'امیر', 'نازنین', 'حامد'];
    const lastNames = ['احمدی', 'محمدی', 'حسینی', 'رضایی', 'علوی', 'موسوی', 'کریمی', 'رحمانی', 'نوری', 'صادقی', 'جعفری', 'یوسفی', 'ابراهیمی', 'بهرامی', 'فرهادی'];
    const provinces = ['تهران', 'اصفهان', 'شیراز', 'مشهد', 'تبریز', 'کرج', 'اهواز', 'قم', 'کرمانشاه', 'ارومیه'];
    const cities = ['تهران', 'اصفهان', 'شیراز', 'مشهد', 'تبریز', 'کرج', 'اهواز', 'قم', 'کرمانشاه', 'ارومیه'];

    for (let i = 1; i <= 50; i++) {
      // Use deterministic values based on index for consistent data generation
      const firstName = firstNames[(i - 1) % firstNames.length];
      const lastName = lastNames[(i - 1) % lastNames.length];
      const email = `${firstName.toLowerCase()}${lastName.toLowerCase()}${i}@example.com`;
      const phone = `091${String(i).padStart(8, '0')}`;

      const createdAt = new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString();
      const totalOrders = (i % 20) + 1;
      const totalSpent = totalOrders * (100000 + (i * 10000));
      const lastOrderDate = totalOrders > 0
        ? new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString()
        : undefined;

      const isVip = i % 5 === 0; // Every 5th customer is VIP
      const loyaltyTier = LOYALTY_TIERS[i % LOYALTY_TIERS.length];
      
      const customer: AdminCustomer = {
        id: `customer_${i}`,
        email,
        phone,
        firstName,
        lastName,
        avatar: undefined,
        isEmailVerified: Math.random() > 0.2,
        isPhoneVerified: Math.random() > 0.3,
        createdAt,
        updatedAt: createdAt,
        preferences: {
          language: 'fa',
          newsletter: Math.random() > 0.4,
          smsNotifications: Math.random() > 0.5,
          emailNotifications: Math.random() > 0.3,
          theme: 'light'
        },
        addresses: [],
        role: 'customer',
        
        // Analytics
        analytics: {
          totalOrders,
          totalSpent,
          averageOrderValue: totalOrders > 0 ? totalSpent / totalOrders : 0,
          lastOrderDate,
          firstOrderDate: totalOrders > 0 ? createdAt : undefined,
          lifetimeValue: totalSpent * 1.2,
          orderFrequency: totalOrders / 12, // orders per month
          returnRate: (i % 15),
          cancelationRate: (i % 10),
          favoriteCategories: ['مراقبت از صورت', 'آرایشی'],
          preferredPaymentMethod: i % 2 === 0 ? 'card' : 'cash',
          averageDeliveryTime: (i % 5) + 2,
          customerSatisfactionScore: ((i % 2) + 3) // 3-5
        },

        // Status and segmentation
        status: i % 10 === 0 ? 'blocked' : i % 8 === 0 ? 'inactive' : 'active',
        segment: 'regular', // Will be calculated
        tags: i % 3 === 0 ? ['مشتری ویژه'] : i % 7 === 0 ? ['نیاز به پیگیری'] : [],
        notes: [],

        // VIP and loyalty
        isVip,
        vipSince: isVip ? createdAt : undefined,
        loyaltyMember: i % 3 !== 0 ? {
          id: `loyalty_${i}`,
          userId: `customer_${i}`,
          membershipNumber: `${1000000 + i}`,
          tier: loyaltyTier,
          points: (i * 50) % 2000,
          totalEarned: (i * 100) % 5000,
          totalSpent: totalSpent,
          joinDate: createdAt,
          lastActivity: lastOrderDate || createdAt,
          isActive: true,
          benefits: [],
          nextTierProgress: {
            currentPoints: (i * 50) % 2000,
            requiredPoints: 2500,
            percentage: (i * 10) % 100
          }
        } : undefined,

        // Communication
        communicationHistory: [],
        marketingConsent: {
          email: i % 3 !== 0,
          sms: i % 2 === 0,
          push: i % 4 !== 0,
          phone: i % 5 === 0,
          updatedAt: createdAt
        },
        
        // Support
        supportTickets: [],
        
        // Admin tracking
        createdBy: 'system',
        lastModifiedBy: 'system',
        lastModifiedAt: createdAt,
        
        // Risk and fraud
        riskScore: 0, // Will be calculated
        fraudFlags: [],
        
        // Location
        location: {
          country: 'ایران',
          province: provinces[i % provinces.length],
          city: cities[i % cities.length],
          timezone: 'Asia/Tehran'
        },

        // Behavior
        behavior: {
          loginFrequency: (i % 10) + 1,
          sessionDuration: (i % 60) + 10,
          pageViews: (i % 100) + 10,
          cartAbandonmentRate: (i % 80),
          wishlistItems: i % 20,
          reviewsCount: i % 10,
          referralsCount: i % 5
        }
      };

      // Calculate segment and risk score
      customer.segment = determineCustomerSegment(customer);
      customer.riskScore = calculateRiskScore(customer);

      mockCustomers.push(customer);
    }

    return mockCustomers;
  }, []); // Remove user dependency to keep data stable

  // Load customers
  const loadCustomers = useCallback(async () => {
    if (!checkPermission('customers', 'read')) {
      setError(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Load customers from API
      const response = await AdminCustomerService.getCustomers({
        page: 1,
        limit: 100,
        filters,
        sort: { field: sortBy, direction: sortOrder }
      });

      setCustomers(response.customers);
      toast.success('مشتریان از API بارگذاری شد');
    } catch (err) {
      console.error('Error loading customers:', err);
      setError('خطا در بارگذاری مشتریان');
      toast.error('خطا در بارگذاری مشتریان');
    } finally {
      setLoading(false);
    }
  }, [checkPermission, filters, sortBy, sortOrder]);

  // Get filtered and sorted customers
  const filteredCustomers = useCallback(() => {
    let result = filterCustomers(customers, filters);
    result = sortCustomers(result, { field: sortBy, direction: sortOrder });
    return result;
  }, [customers, filters, sortBy, sortOrder]);

  // Update customer
  const updateCustomer = useCallback(async (
    customerId: string,
    updates: Partial<AdminCustomer>
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Update customer via API
      const updatedCustomer = await AdminCustomerService.updateCustomer(customerId, updates);

      // Update local state
      setCustomers(prevCustomers =>
        prevCustomers.map(customer =>
          customer.id === customerId ? updatedCustomer : customer
        )
      );

      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.customerUpdated);
    } catch (err) {
      console.error('Error updating customer:', err);
      toast.error(PERSIAN_CUSTOMER_MESSAGES.errors.updateFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Block/unblock customer
  const toggleCustomerBlock = useCallback(async (
    customerId: string, 
    block: boolean
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      await updateCustomer(customerId, { 
        status: block ? 'blocked' : 'active' 
      });
      
      toast.success(block 
        ? PERSIAN_CUSTOMER_MESSAGES.success.customerBlocked
        : PERSIAN_CUSTOMER_MESSAGES.success.customerUnblocked
      );
    } catch (err) {
      throw err;
    }
  }, [updateCustomer, checkPermission]);

  // Toggle VIP status
  const toggleVipStatus = useCallback(async (
    customerId: string, 
    isVip: boolean
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      await updateCustomer(customerId, { 
        isVip,
        vipSince: isVip ? new Date().toISOString() : undefined
      });
      
      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.vipStatusUpdated);
    } catch (err) {
      throw err;
    }
  }, [updateCustomer, checkPermission]);

  // Add customer note
  const addCustomerNote = useCallback(async (
    customerId: string,
    note: Omit<CustomerNote, 'id' | 'customerId' | 'createdAt' | 'createdBy' | 'createdByName'>
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      const newNote: CustomerNote = {
        ...note,
        id: `note_${Date.now()}`,
        customerId,
        createdBy: user?.id || '',
        createdByName: user ? `${user.firstName} ${user.lastName}` : '',
        createdAt: new Date().toISOString()
      };

      await updateCustomer(customerId, {
        notes: [...(customers.find(c => c.id === customerId)?.notes || []), newNote]
      });

      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.noteAdded);
    } catch (err) {
      throw err;
    }
  }, [updateCustomer, checkPermission, user, customers]);

  // Create support ticket
  const createSupportTicket = useCallback(async (
    customerId: string,
    ticket: Omit<SupportTicket, 'id' | 'customerId' | 'createdAt' | 'updatedAt' | 'messages'>
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      const newTicket: SupportTicket = {
        ...ticket,
        id: `ticket_${Date.now()}`,
        customerId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messages: []
      };

      const customer = customers.find(c => c.id === customerId);
      if (customer) {
        await updateCustomer(customerId, {
          supportTickets: [...customer.supportTickets, newTicket]
        });
      }

      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.ticketCreated);
    } catch (err) {
      throw err;
    }
  }, [updateCustomer, checkPermission, customers]);

  // Export customers
  const exportCustomers = useCallback(async (
    exportFilters?: CustomerFilters,
    fields: string[] = ['name', 'email', 'phone', 'status', 'totalOrders', 'totalSpent']
  ): Promise<void> => {
    if (!checkPermission('customers', 'export')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      const customersToExport = exportFilters
        ? filterCustomers(customers, exportFilters)
        : customers;

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real implementation, this would generate and download a file
      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.dataExported);
    } catch (err) {
      toast.error(PERSIAN_CUSTOMER_MESSAGES.errors.exportFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission, customers]);

  // Send message to customer
  const sendMessage = useCallback(async (
    customerId: string,
    message: {
      type: 'email' | 'sms' | 'in_app';
      subject?: string;
      content: string;
    }
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // In a real implementation, this would send the actual message
      toast.success(PERSIAN_CUSTOMER_MESSAGES.success.messageSent);
    } catch (err) {
      toast.error(PERSIAN_CUSTOMER_MESSAGES.errors.messageFailed);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission]);

  // Bulk operations
  const bulkUpdateCustomers = useCallback(async (
    customerIds: string[],
    updates: Partial<AdminCustomer>
  ): Promise<void> => {
    if (!checkPermission('customers', 'update')) {
      throw new Error(PERSIAN_CUSTOMER_MESSAGES.errors.insufficientPermissions);
    }

    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCustomers(prevCustomers =>
        prevCustomers.map(customer => {
          if (customerIds.includes(customer.id)) {
            return {
              ...customer,
              ...updates,
              lastModifiedBy: user?.id,
              lastModifiedAt: new Date().toISOString()
            };
          }
          return customer;
        })
      );

      toast.success(`${customerIds.length} مشتری بروزرسانی شد`);
    } catch (err) {
      toast.error('خطا در بروزرسانی گروهی');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [checkPermission, user?.id]);

  // Get customer by ID
  const getCustomerById = useCallback((customerId: string): AdminCustomer | undefined => {
    return customers.find(customer => customer.id === customerId);
  }, [customers]);

  // Search customers
  const searchCustomers = useCallback((query: string): AdminCustomer[] => {
    if (!query.trim()) return customers;

    const searchTerm = query.toLowerCase();
    return customers.filter(customer => {
      const searchableText = `${customer.firstName} ${customer.lastName} ${customer.email} ${customer.phone || ''}`.toLowerCase();
      return searchableText.includes(searchTerm);
    });
  }, [customers]);

  // Get customer analytics
  const getAnalytics = useCallback((): CustomerAnalytics => {
    return calculateCustomerAnalytics(customers);
  }, [customers]);

  // Initialize
  useEffect(() => {
    loadCustomers();
  }, [loadCustomers]);

  return {
    // State
    customers: filteredCustomers(),
    allCustomers: customers,
    loading,
    error,
    filters,
    sortBy,
    sortOrder,

    // Actions
    setFilters,
    setSortBy,
    setSortOrder,
    loadCustomers,
    updateCustomer,
    toggleCustomerBlock,
    toggleVipStatus,
    addCustomerNote,
    createSupportTicket,
    exportCustomers,
    sendMessage,
    bulkUpdateCustomers,
    getCustomerById,
    searchCustomers,
    getAnalytics
  };
};
