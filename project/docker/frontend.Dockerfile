# Multi-stage build for GlowRoya Frontend
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage with Nginx
FROM nginx:alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Create nginx user
RUN addgroup -g 1001 -S nginx_app
RUN adduser -S nginx_app -u 1001

# Set proper permissions
RUN chown -R nginx_app:nginx_app /usr/share/nginx/html
RUN chown -R nginx_app:nginx_app /var/cache/nginx
RUN chown -R nginx_app:nginx_app /var/log/nginx
RUN chown -R nginx_app:nginx_app /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R nginx_app:nginx_app /var/run/nginx.pid

# Switch to non-root user
USER nginx_app

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

# Start nginx with dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["nginx", "-g", "daemon off;"]
