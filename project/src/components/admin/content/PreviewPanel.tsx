import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, RefreshCw, ExternalLink, Eye } from 'lucide-react';
import { HomePageContentFormData } from '../../../types/homePageContent';
import AdminButton from '../common/AdminButton';

interface PreviewPanelProps {
  content: HomePageContentFormData | null;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  onClose: () => void;
}

const PreviewPanel: React.FC<PreviewPanelProps> = ({
  content,
  previewMode,
  onClose
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsRefreshing(false);
  };

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      case 'desktop':
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const renderHeroSection = (section: any) => {
    return (
      <div 
        className="relative h-96 flex items-center justify-center text-white"
        style={{
          backgroundImage: `url(${section.content.backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div 
          className="absolute inset-0 bg-black"
          style={{ opacity: section.content.overlayOpacity }}
        />
        <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
          {section.content.subtitle && (
            <p className="text-lg mb-2 opacity-90">{section.content.subtitle}</p>
          )}
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            {section.content.title}
          </h1>
          <p className="text-xl mb-8 opacity-90">
            {section.content.description}
          </p>
          <div className="flex gap-4 justify-center">
            <button className="px-8 py-3 bg-admin-600 text-white rounded-lg hover:bg-admin-700 transition-colors">
              {section.content.primaryButton.text}
            </button>
            {section.content.secondaryButton && (
              <button className="px-8 py-3 border border-white text-white rounded-lg hover:bg-white hover:text-gray-900 transition-colors">
                {section.content.secondaryButton.text}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderFeaturedProductsSection = (section: any) => {
    return (
      <div className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {section.content.title}
            </h2>
            {section.content.subtitle && (
              <p className="text-lg text-gray-600">
                {section.content.subtitle}
              </p>
            )}
          </div>
          
          <div className={`grid gap-6 ${
            section.content.productsPerRow === 2 ? 'grid-cols-2' :
            section.content.productsPerRow === 3 ? 'grid-cols-3' :
            section.content.productsPerRow === 4 ? 'grid-cols-4' :
            'grid-cols-5'
          }`}>
            {Array.from({ length: Math.min(section.content.maxProducts, 8) }).map((_, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="font-medium text-gray-900 mb-2">محصول نمونه {index + 1}</h3>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-admin-600">۲۵۰,۰۰۰ تومان</span>
                  <div className="flex text-yellow-400">
                    {'★'.repeat(5)}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {section.content.showViewAllButton && (
            <div className="text-center mt-12">
              <button className="px-8 py-3 bg-admin-600 text-white rounded-lg hover:bg-admin-700 transition-colors">
                {section.content.viewAllButtonText}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderCategoriesSection = (section: any) => {
    return (
      <div className="py-16 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {section.content.title}
            </h2>
            {section.content.subtitle && (
              <p className="text-lg text-gray-600">
                {section.content.subtitle}
              </p>
            )}
          </div>
          
          <div className={`grid gap-6 ${
            section.content.categoriesPerRow === 2 ? 'grid-cols-2' :
            section.content.categoriesPerRow === 3 ? 'grid-cols-3' :
            section.content.categoriesPerRow === 4 ? 'grid-cols-4' :
            'grid-cols-6'
          }`}>
            {Array.from({ length: Math.min(section.content.maxCategories, 8) }).map((_, index) => (
              <div key={index} className="text-center">
                <div className={`
                  aspect-square bg-white border border-gray-200 mb-4 mx-auto
                  ${section.content.imageStyle === 'circle' ? 'rounded-full' :
                    section.content.imageStyle === 'rounded' ? 'rounded-lg' : ''}
                `}></div>
                <h3 className="font-medium text-gray-900">دسته‌بندی {index + 1}</h3>
                {section.content.showProductCount && (
                  <p className="text-sm text-gray-600">{Math.floor(Math.random() * 50) + 10} محصول</p>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderTestimonialsSection = (section: any) => {
    return (
      <div className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {section.content.title}
            </h2>
            {section.content.subtitle && (
              <p className="text-lg text-gray-600">
                {section.content.subtitle}
              </p>
            )}
          </div>
          
          <div className={`grid gap-6 ${
            section.content.testimonialsPerView === 1 ? 'grid-cols-1' :
            section.content.testimonialsPerView === 2 ? 'grid-cols-2' :
            'grid-cols-3'
          }`}>
            {section.content.testimonials.slice(0, section.content.testimonialsPerView).map((testimonial: any, index: number) => (
              <div key={index} className={`
                bg-white p-6 border border-gray-200
                ${section.content.cardStyle === 'rounded' ? 'rounded-lg' :
                  section.content.cardStyle === 'shadow' ? 'rounded-lg shadow-md' :
                  section.content.cardStyle === 'bordered' ? 'border-2' : 'rounded-lg'}
              `}>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full mr-4"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">{testimonial.name}</h4>
                    <div className="flex text-yellow-400">
                      {'★'.repeat(testimonial.rating)}
                    </div>
                  </div>
                </div>
                <p className="text-gray-600">{testimonial.content}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderSection = (section: any) => {
    if (!section.isActive) return null;

    switch (section.type) {
      case 'hero':
        return renderHeroSection(section);
      case 'featured-products':
        return renderFeaturedProductsSection(section);
      case 'categories':
        return renderCategoriesSection(section);
      case 'testimonials':
        return renderTestimonialsSection(section);
      default:
        return (
          <div className="py-16 px-6 bg-gray-100">
            <div className="max-w-7xl mx-auto text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {section.title}
              </h2>
              <p className="text-gray-600">
                پیش‌نمایش برای این نوع بخش هنوز آماده نیست
              </p>
            </div>
          </div>
        );
    }
  };

  const dimensions = getPreviewDimensions();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-3">
          <Eye className="w-5 h-5 text-gray-600" />
          <h3 className="font-medium text-gray-900">پیش‌نمایش زنده</h3>
          <span className="text-xs px-2 py-1 bg-gray-200 text-gray-600 rounded-full">
            {previewMode === 'desktop' ? 'دسکتاپ' : 
             previewMode === 'tablet' ? 'تبلت' : 'موبایل'}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <AdminButton
            size="sm"
            variant="outline"
            icon={RefreshCw}
            onClick={handleRefresh}
            loading={isRefreshing}
          >
            بروزرسانی
          </AdminButton>
          
          <AdminButton
            size="sm"
            variant="outline"
            icon={ExternalLink}
            onClick={() => window.open('/', '_blank')}
          >
            نمایش کامل
          </AdminButton>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="p-4">
        <div 
          className={`
            mx-auto bg-white border border-gray-300 rounded-lg overflow-hidden
            ${previewMode !== 'desktop' ? 'shadow-lg' : ''}
          `}
          style={{
            width: dimensions.width,
            height: previewMode !== 'desktop' ? dimensions.height : 'auto',
            maxHeight: previewMode === 'desktop' ? '80vh' : dimensions.height
          }}
        >
          <div className="overflow-y-auto h-full">
            {content?.sections
              .sort((a, b) => a.order - b.order)
              .map((section) => (
                <div key={section.id}>
                  {renderSection(section)}
                </div>
              ))}
            
            {(!content?.sections || content.sections.length === 0) && (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>هیچ بخشی برای نمایش وجود ندارد</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PreviewPanel;
