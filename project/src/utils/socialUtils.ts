/**
 * Social Media and Sharing Utilities
 * Comprehensive utilities for social media integration and sharing
 */

import { Product } from '../types';

// Types
export interface SocialMetaTags {
  title: string;
  description: string;
  image: string;
  url: string;
  type: 'website' | 'product' | 'article';
  siteName: string;
  locale: string;
}

export interface ShareData {
  title: string;
  text: string;
  url: string;
  hashtags?: string[];
}

export interface SocialPlatformConfig {
  name: string;
  baseUrl: string;
  params: Record<string, string>;
  maxLength?: {
    title?: number;
    description?: number;
  };
}

// Social platform configurations
export const SOCIAL_PLATFORMS: Record<string, SocialPlatformConfig> = {
  telegram: {
    name: 'Telegram',
    baseUrl: 'https://t.me/share/url',
    params: {
      url: 'url',
      text: 'text'
    },
    maxLength: {
      title: 100,
      description: 200
    }
  },
  whatsapp: {
    name: 'WhatsApp',
    baseUrl: 'https://wa.me/',
    params: {
      text: 'text'
    },
    maxLength: {
      title: 80,
      description: 150
    }
  },
  twitter: {
    name: 'Twitter',
    baseUrl: 'https://twitter.com/intent/tweet',
    params: {
      text: 'text',
      url: 'url',
      hashtags: 'hashtags'
    },
    maxLength: {
      title: 70,
      description: 200
    }
  },
  facebook: {
    name: 'Facebook',
    baseUrl: 'https://www.facebook.com/sharer/sharer.php',
    params: {
      u: 'url'
    }
  },
  linkedin: {
    name: 'LinkedIn',
    baseUrl: 'https://www.linkedin.com/sharing/share-offsite/',
    params: {
      url: 'url'
    }
  },
  pinterest: {
    name: 'Pinterest',
    baseUrl: 'https://pinterest.com/pin/create/button/',
    params: {
      url: 'url',
      description: 'description',
      media: 'media'
    },
    maxLength: {
      description: 500
    }
  }
};

// Default hashtags for different content types
export const DEFAULT_HASHTAGS = {
  skincare: ['مراقبت_از_پوست', 'زیبایی', 'آرامش_پوست', 'پوست_سالم'],
  product: ['محصول_زیبایی', 'خرید_آنلاین', 'تخفیف'],
  review: ['نظرات', 'تجربه_خرید', 'کیفیت'],
  general: ['آرامش_پوست', 'فروشگاه_آنلاین', 'ایران']
};

/**
 * Generate social media meta tags
 */
export const generateSocialMetaTags = (
  title: string,
  description: string,
  image?: string,
  url?: string,
  type: 'website' | 'product' | 'article' = 'website'
): SocialMetaTags => {
  const baseUrl = window.location.origin;
  const defaultImage = `${baseUrl}/images/og-default.jpg`;
  
  return {
    title: truncateText(title, 60),
    description: truncateText(description, 160),
    image: image || defaultImage,
    url: url || window.location.href,
    type,
    siteName: 'آرامش پوست',
    locale: 'fa_IR'
  };
};

/**
 * Generate product-specific social meta tags
 */
export const generateProductSocialTags = (product: Product): SocialMetaTags => {
  const title = `${product.name} | آرامش پوست`;
  const description = `${product.description} - قیمت: ${formatPrice(product.discountedPrice || product.price)} تومان`;
  
  return generateSocialMetaTags(
    title,
    description,
    product.imageSrc,
    `/product/${product.id}`,
    'product'
  );
};

/**
 * Generate share URL for specific platform
 */
export const generateShareUrl = (
  platform: string,
  data: ShareData
): string => {
  const config = SOCIAL_PLATFORMS[platform];
  if (!config) {
    throw new Error(`Unsupported platform: ${platform}`);
  }

  const url = new URL(config.baseUrl);
  
  // Handle special cases
  if (platform === 'whatsapp') {
    const text = `${data.title}\n${data.text}\n${data.url}`;
    url.searchParams.set('text', encodeURIComponent(text));
  } else {
    // Standard parameter mapping
    Object.entries(config.params).forEach(([paramKey, dataKey]) => {
      let value = '';
      
      switch (dataKey) {
        case 'url':
          value = data.url;
          break;
        case 'text':
          value = data.text || data.title;
          break;
        case 'title':
          value = data.title;
          break;
        case 'hashtags':
          value = data.hashtags?.join(',') || '';
          break;
        case 'description':
          value = data.text;
          break;
        case 'media':
          // For Pinterest, we'd need image URL
          value = ''; // Would be populated with image URL
          break;
      }
      
      if (value) {
        // Apply length limits
        if (config.maxLength) {
          if (dataKey === 'title' && config.maxLength.title) {
            value = truncateText(value, config.maxLength.title);
          }
          if (dataKey === 'description' && config.maxLength.description) {
            value = truncateText(value, config.maxLength.description);
          }
        }
        
        url.searchParams.set(paramKey, encodeURIComponent(value));
      }
    });
  }
  
  return url.toString();
};

/**
 * Generate share data for product
 */
export const generateProductShareData = (
  product: Product,
  hashtags?: string[]
): ShareData => {
  const defaultHashtags = [
    ...DEFAULT_HASHTAGS.product,
    ...DEFAULT_HASHTAGS.skincare,
    product.category.replace(/\s+/g, '_')
  ];
  
  return {
    title: `${product.name} | آرامش پوست`,
    text: `${product.description}\n\nقیمت: ${formatPrice(product.discountedPrice || product.price)} تومان`,
    url: `${window.location.origin}/product/${product.id}`,
    hashtags: hashtags || defaultHashtags
  };
};

/**
 * Generate share data for general content
 */
export const generateGeneralShareData = (
  title: string,
  description: string,
  url?: string,
  hashtags?: string[]
): ShareData => {
  return {
    title,
    text: description,
    url: url || window.location.href,
    hashtags: hashtags || DEFAULT_HASHTAGS.general
  };
};

/**
 * Check if native sharing is supported
 */
export const isNativeShareSupported = (): boolean => {
  return 'share' in navigator && 'canShare' in navigator;
};

/**
 * Share content using native API or fallback
 */
export const shareContent = async (data: ShareData): Promise<boolean> => {
  // Try native sharing first
  if (isNativeShareSupported()) {
    try {
      const shareData: ShareData = {
        title: data.title,
        text: data.text,
        url: data.url
      };
      
      if (navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        return true;
      }
    } catch (error) {
      console.warn('Native sharing failed:', error);
    }
  }
  
  // Fallback to clipboard
  return copyToClipboard(`${data.title}\n${data.text}\n${data.url}`);
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if ('clipboard' in navigator) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      return successful;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Format price for display
 */
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('fa-IR').format(price);
};

/**
 * Generate Open Graph meta tags
 */
export const generateOpenGraphTags = (metaTags: SocialMetaTags) => {
  return {
    'og:title': metaTags.title,
    'og:description': metaTags.description,
    'og:image': metaTags.image,
    'og:url': metaTags.url,
    'og:type': metaTags.type,
    'og:site_name': metaTags.siteName,
    'og:locale': metaTags.locale
  };
};

/**
 * Generate Twitter Card meta tags
 */
export const generateTwitterCardTags = (metaTags: SocialMetaTags) => {
  return {
    'twitter:card': 'summary_large_image',
    'twitter:title': metaTags.title,
    'twitter:description': metaTags.description,
    'twitter:image': metaTags.image,
    'twitter:site': '@glowroya',
    'twitter:creator': '@glowroya'
  };
};

/**
 * Track social sharing events
 */
export const trackSocialShare = (platform: string, contentType: string, contentId?: string) => {
  // Analytics tracking would go here
  if (typeof gtag !== 'undefined') {
    gtag('event', 'share', {
      method: platform,
      content_type: contentType,
      content_id: contentId
    });
  }
  
  console.log(`Social share tracked: ${platform} - ${contentType} - ${contentId}`);
};

/**
 * Get social media profile URLs
 */
export const getSocialProfiles = () => {
  return {
    instagram: 'https://instagram.com/glowroya',
    telegram: 'https://t.me/glowroya',
    whatsapp: 'https://wa.me/989123456789',
    twitter: 'https://twitter.com/glowroya',
    facebook: 'https://facebook.com/glowroya',
    linkedin: 'https://linkedin.com/company/glowroya'
  };
};

export default {
  generateSocialMetaTags,
  generateProductSocialTags,
  generateShareUrl,
  generateProductShareData,
  generateGeneralShareData,
  shareContent,
  copyToClipboard,
  isNativeShareSupported,
  trackSocialShare,
  getSocialProfiles
};
