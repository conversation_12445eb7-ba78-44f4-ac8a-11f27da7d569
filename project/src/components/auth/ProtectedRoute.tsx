import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Shield, Lock } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { UserRole } from '../../types/auth';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: UserRole;
  redirectTo?: string;
  fallback?: ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo = '/login',
  fallback
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { user: adminUser, isAuthenticated: isAdminAuthenticated, isLoading: isAdminLoading } = useAdminAuth();
  const location = useLocation();

  // Check if user is authenticated either as customer or admin
  const isUserAuthenticated = isAuthenticated || isAdminAuthenticated;
  const isAnyLoading = isLoading || isAdminLoading;
  const currentUser = user || (adminUser ? {
    id: adminUser.id,
    firstName: adminUser.firstName,
    lastName: adminUser.lastName,
    email: adminUser.email,
    role: 'admin' as const,
    isEmailVerified: true,
    isPhoneVerified: true,
    createdAt: adminUser.createdAt,
    updatedAt: adminUser.updatedAt,
    preferences: {
      language: 'fa' as const,
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true,
      theme: 'light' as const
    },
    addresses: []
  } : null);

  // Show loading state while checking authentication
  if (isAnyLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">در حال بررسی احراز هویت...</p>
        </motion.div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !isUserAuthenticated) {
    // Store the attempted location for redirect after login
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check role requirement
  if (requiredRole && currentUser && currentUser.role !== requiredRole) {
    // Show unauthorized access message
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            دسترسی محدود
          </h2>
          <p className="text-gray-600 mb-6">
            شما مجوز دسترسی به این بخش را ندارید.
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            بازگشت
          </button>
        </motion.div>
      </div>
    );
  }

  // Check email verification requirement for sensitive operations (only for regular users, not admins)
  if (requireAuth && currentUser && !currentUser.isEmailVerified && location.pathname.includes('/profile') && !isAdminAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center p-8"
        >
          <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-yellow-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            تأیید ایمیل مورد نیاز
          </h2>
          <p className="text-gray-600 mb-6">
            برای دسترسی به این بخش، لطفاً ابتدا ایمیل خود را تأیید کنید.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => {/* Handle resend verification */}}
              className="w-full bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              ارسال مجدد ایمیل تأیید
            </button>
            <button
              onClick={() => window.history.back()}
              className="w-full border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              بازگشت
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
};

export default ProtectedRoute;
