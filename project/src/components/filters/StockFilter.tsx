import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Package, AlertTriangle, XCircle, Check } from 'lucide-react';
import { FilterState } from '../../types';

interface StockFilterProps {
  stockStatus: FilterState['stockStatus'];
  onStockStatusChange: (status: FilterState['stockStatus']) => void;
  productTypes: FilterState['productTypes'];
  onProductTypesChange: (types: Partial<FilterState['productTypes']>) => void;
  onClearStockFilters: () => void;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

const StockFilter: React.FC<StockFilterProps> = ({
  stockStatus,
  onStockStatusChange,
  productTypes,
  onProductTypesChange,
  onClearStockFilters,
  isCollapsible = false,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const hasStockFilter = stockStatus !== 'all';
  const hasProductTypeFilter = productTypes.isNew || productTypes.isBestSeller || productTypes.hasDiscount;
  const hasAnyFilter = hasStockFilter || hasProductTypeFilter;

  const stockOptions = [
    {
      value: 'all' as const,
      label: 'همه محصولات',
      icon: Package,
      description: 'نمایش تمام محصولات'
    },
    {
      value: 'inStock' as const,
      label: 'موجود',
      icon: Check,
      description: 'محصولات موجود در انبار'
    },
    {
      value: 'lowStock' as const,
      label: 'کم موجود',
      icon: AlertTriangle,
      description: 'محصولات با موجودی کم (زیر ۵ عدد)'
    },
    {
      value: 'outOfStock' as const,
      label: 'ناموجود',
      icon: XCircle,
      description: 'محصولات ناموجود'
    }
  ];

  const productTypeOptions = [
    {
      key: 'isNew' as keyof FilterState['productTypes'],
      label: 'محصولات جدید',
      description: 'آخرین محصولات اضافه شده',
      color: 'green'
    },
    {
      key: 'isBestSeller' as keyof FilterState['productTypes'],
      label: 'پرفروش‌ترین‌ها',
      description: 'محصولات با فروش بالا',
      color: 'orange'
    },
    {
      key: 'hasDiscount' as keyof FilterState['productTypes'],
      label: 'تخفیف‌دار',
      description: 'محصولات با تخفیف ویژه',
      color: 'red'
    }
  ];

  return (
    <div className="border-b border-gray-100 pb-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 font-medium text-text-primary ${
            isCollapsible ? 'hover:text-primary-600 transition-colors' : ''
          }`}
        >
          <span>موجودی و نوع محصول</span>
          {hasAnyFilter && (
            <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
              فعال
            </span>
          )}
          {isCollapsible && (
            <ChevronDown 
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            />
          )}
        </button>
        
        {hasAnyFilter && (
          <button
            onClick={onClearStockFilters}
            className="text-sm text-text-muted hover:text-red-500 transition-colors"
          >
            پاک کردن
          </button>
        )}
      </div>

      {/* Content */}
      {(!isCollapsible || isExpanded) && (
        <motion.div
          initial={isCollapsible ? { height: 0, opacity: 0 } : false}
          animate={isCollapsible ? { height: 'auto', opacity: 1 } : false}
          exit={isCollapsible ? { height: 0, opacity: 0 } : false}
          transition={{ duration: 0.2 }}
          className="space-y-6"
        >
          {/* Stock Status */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">وضعیت موجودی</h4>
            <div className="space-y-2">
              {stockOptions.map((option, index) => {
                const Icon = option.icon;
                const isSelected = stockStatus === option.value;
                
                return (
                  <motion.label
                    key={option.value}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    className="flex items-center cursor-pointer group"
                  >
                    <input
                      type="radio"
                      name="stockStatus"
                      value={option.value}
                      checked={isSelected}
                      onChange={() => onStockStatusChange(option.value)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 rounded-full border-2 transition-all duration-200 flex items-center justify-center ${
                      isSelected
                        ? 'bg-primary-500 border-primary-500'
                        : 'border-gray-300 group-hover:border-primary-400'
                    }`}>
                      {isSelected && (
                        <div className="w-2 h-2 bg-white rounded-full" />
                      )}
                    </div>
                    <div className="mr-3 flex-1">
                      <div className="flex items-center gap-2">
                        <Icon className={`w-4 h-4 ${
                          isSelected ? 'text-primary-600' : 'text-text-muted'
                        }`} />
                        <span className={`text-sm transition-colors ${
                          isSelected 
                            ? 'text-primary-600 font-medium' 
                            : 'text-text-secondary group-hover:text-text-primary'
                        }`}>
                          {option.label}
                        </span>
                      </div>
                      <p className="text-xs text-text-muted mt-1">
                        {option.description}
                      </p>
                    </div>
                  </motion.label>
                );
              })}
            </div>
          </div>

          {/* Product Types */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">نوع محصول</h4>
            <div className="space-y-3">
              {productTypeOptions.map((option, index) => {
                const isSelected = productTypes[option.key];
                const colorClasses = {
                  green: isSelected ? 'bg-green-500 border-green-500' : 'border-gray-300 group-hover:border-green-400',
                  orange: isSelected ? 'bg-orange-500 border-orange-500' : 'border-gray-300 group-hover:border-orange-400',
                  red: isSelected ? 'bg-red-500 border-red-500' : 'border-gray-300 group-hover:border-red-400'
                };
                
                return (
                  <motion.label
                    key={option.key}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    className="flex items-center cursor-pointer group"
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => onProductTypesChange({ [option.key]: e.target.checked })}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 rounded border-2 transition-all duration-200 ${
                      colorClasses[option.color]
                    }`}>
                      {isSelected && (
                        <Check className="w-3 h-3 text-white absolute top-0.5 right-0.5" />
                      )}
                    </div>
                    <div className="mr-3 flex-1">
                      <span className={`text-sm transition-colors ${
                        isSelected 
                          ? `text-${option.color}-600 font-medium` 
                          : 'text-text-secondary group-hover:text-text-primary'
                      }`}>
                        {option.label}
                      </span>
                      <p className="text-xs text-text-muted mt-1">
                        {option.description}
                      </p>
                    </div>
                  </motion.label>
                );
              })}
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasAnyFilter && (
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-sm text-blue-700 mb-2">فیلترهای فعال:</div>
              <div className="space-y-1 text-xs text-blue-600">
                {hasStockFilter && (
                  <div>
                    موجودی: {stockOptions.find(opt => opt.value === stockStatus)?.label}
                  </div>
                )}
                {hasProductTypeFilter && (
                  <div>
                    نوع: {productTypeOptions
                      .filter(opt => productTypes[opt.key])
                      .map(opt => opt.label)
                      .join('، ')
                    }
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default StockFilter;
