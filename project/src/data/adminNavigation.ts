import {
  LayoutDashboard,
  Package,
  ShoppingBag,
  Users,
  MessageSquare,
  Gift,
  FileText,
  BarChart3,
  Settings,
  UserCog,
  Shield,
  Bell,
  LucideIcon
} from 'lucide-react';
import { AdminRole, AdminResource, AdminPermission } from '../types/admin';

export interface AdminNavigationItem {
  id: string;
  title: string;
  icon: LucideIcon;
  path: string;
  resource?: AdminResource;
  requiredRoles?: AdminRole[];
  badge?: string;
  children?: AdminNavigationSubItem[];
}

export interface AdminNavigationSubItem {
  id: string;
  title: string;
  path: string;
  resource?: AdminResource;
  requiredRoles?: AdminRole[];
  badge?: string;
}

export const adminNavigationItems: AdminNavigationItem[] = [
  {
    id: 'dashboard',
    title: 'داشبورد',
    icon: LayoutDashboard,
    path: '/admin/dashboard',
  },
  {
    id: 'products',
    title: 'مدیریت محصولات',
    icon: Package,
    path: '/admin/products',
    resource: 'products',
    children: [
      {
        id: 'products-list',
        title: 'لیست محصولات',
        path: '/admin/products',
        resource: 'products'
      },
      {
        id: 'products-add',
        title: 'افزودن محصول',
        path: '/admin/products/create',
        resource: 'products'
      },
      {
        id: 'products-categories',
        title: 'دسته‌بندی‌ها',
        path: '/admin/products/categories',
        resource: 'products'
      },
      {
        id: 'products-brands',
        title: 'برندها',
        path: '/admin/products/brands',
        resource: 'products'
      },
      {
        id: 'products-inventory',
        title: 'موجودی انبار',
        path: '/admin/products/inventory',
        resource: 'products'
      }
    ]
  },
  {
    id: 'orders',
    title: 'مدیریت سفارشات',
    icon: ShoppingBag,
    path: '/admin/orders',
    resource: 'orders',
    children: [
      {
        id: 'orders-list',
        title: 'لیست سفارشات',
        path: '/admin/orders',
        resource: 'orders'
      },
      {
        id: 'orders-pending',
        title: 'سفارشات در انتظار',
        path: '/admin/orders/pending',
        resource: 'orders',
        badge: 'جدید'
      },
      {
        id: 'orders-processing',
        title: 'در حال پردازش',
        path: '/admin/orders/processing',
        resource: 'orders'
      },
      {
        id: 'orders-shipped',
        title: 'ارسال شده',
        path: '/admin/orders/shipped',
        resource: 'orders'
      },
      {
        id: 'orders-returns',
        title: 'مرجوعی‌ها',
        path: '/admin/orders/returns',
        resource: 'orders'
      }
    ]
  },
  {
    id: 'customers',
    title: 'مدیریت مشتریان',
    icon: Users,
    path: '/admin/customers',
    resource: 'customers',
    children: [
      {
        id: 'customers-list',
        title: 'لیست مشتریان',
        path: '/admin/customers',
        resource: 'customers'
      },
      {
        id: 'customers-segments',
        title: 'بخش‌بندی مشتریان',
        path: '/admin/customers/segments',
        resource: 'customers'
      },
      {
        id: 'customers-analytics',
        title: 'تحلیل رفتار',
        path: '/admin/customers/analytics',
        resource: 'analytics'
      }
    ]
  },
  {
    id: 'reviews',
    title: 'مدیریت نظرات',
    icon: MessageSquare,
    path: '/admin/reviews',
    resource: 'reviews',
    children: [
      {
        id: 'reviews-list',
        title: 'لیست نظرات',
        path: '/admin/reviews',
        resource: 'reviews'
      },
      {
        id: 'reviews-moderation',
        title: 'بررسی نظرات',
        path: '/admin/reviews/moderation',
        resource: 'reviews',
        badge: 'جدید'
      },
      {
        id: 'reviews-analytics',
        title: 'تحلیل نظرات',
        path: '/admin/reviews/analytics',
        resource: 'analytics'
      }
    ]
  },
  {
    id: 'loyalty',
    title: 'باشگاه مشتریان',
    icon: Gift,
    path: '/admin/loyalty',
    resource: 'loyalty',
    children: [
      {
        id: 'loyalty-programs',
        title: 'برنامه‌های وفاداری',
        path: '/admin/loyalty/programs',
        resource: 'loyalty'
      },
      {
        id: 'loyalty-points',
        title: 'مدیریت امتیازات',
        path: '/admin/loyalty/points',
        resource: 'loyalty'
      },
      {
        id: 'loyalty-rewards',
        title: 'جوایز و تخفیف‌ها',
        path: '/admin/loyalty/rewards',
        resource: 'loyalty'
      },
      {
        id: 'loyalty-tiers',
        title: 'سطوح عضویت',
        path: '/admin/loyalty/tiers',
        resource: 'loyalty'
      }
    ]
  },
  {
    id: 'content',
    title: 'مدیریت محتوا',
    icon: FileText,
    path: '/admin/content',
    resource: 'content',
    children: [
      {
        id: 'content-dashboard',
        title: 'داشبورد محتوا',
        path: '/admin/content/dashboard',
        resource: 'content'
      },
      {
        id: 'content-homepage',
        title: 'صفحه اصلی',
        path: '/admin/content/homepage',
        resource: 'content'
      },
      {
        id: 'content-banners',
        title: 'بنرها',
        path: '/admin/content/banners',
        resource: 'content'
      },
      {
        id: 'content-promotions',
        title: 'تخفیف‌ها',
        path: '/admin/content/promotions',
        resource: 'content'
      },
      {
        id: 'content-newsletter',
        title: 'خبرنامه',
        path: '/admin/content/newsletter',
        resource: 'content'
      },
      {
        id: 'content-pages',
        title: 'صفحات',
        path: '/admin/content/pages',
        resource: 'content'
      },
      {
        id: 'content-media',
        title: 'کتابخانه رسانه',
        path: '/admin/content/media',
        resource: 'content'
      }
    ]
  },
  {
    id: 'analytics',
    title: 'آمار و گزارشات',
    icon: BarChart3,
    path: '/admin/analytics',
    resource: 'analytics',
    children: [
      {
        id: 'analytics-sales',
        title: 'گزارش فروش',
        path: '/admin/analytics/sales',
        resource: 'analytics'
      },
      {
        id: 'analytics-products',
        title: 'عملکرد محصولات',
        path: '/admin/analytics/products',
        resource: 'analytics'
      },
      {
        id: 'analytics-customers',
        title: 'تحلیل مشتریان',
        path: '/admin/analytics/customers',
        resource: 'analytics'
      },
      {
        id: 'analytics-traffic',
        title: 'ترافیک سایت',
        path: '/admin/analytics/traffic',
        resource: 'analytics'
      }
    ]
  },
  {
    id: 'settings',
    title: 'تنظیمات سیستم',
    icon: Settings,
    path: '/admin/settings',
    resource: 'settings',
    requiredRoles: ['super_admin', 'admin'],
    children: [
      {
        id: 'settings-general',
        title: 'تنظیمات عمومی',
        path: '/admin/settings#general',
        resource: 'settings',
        requiredRoles: ['super_admin', 'admin']
      },
      {
        id: 'settings-payment',
        title: 'درگاه‌های پرداخت',
        path: '/admin/settings#payment',
        resource: 'settings',
        requiredRoles: ['super_admin', 'admin']
      },
      {
        id: 'settings-shipping',
        title: 'روش‌های ارسال',
        path: '/admin/settings#shipping',
        resource: 'settings',
        requiredRoles: ['super_admin', 'admin']
      },
      {
        id: 'settings-notifications',
        title: 'اعلان‌ها',
        path: '/admin/settings#notifications',
        resource: 'settings',
        requiredRoles: ['super_admin', 'admin']
      }
    ]
  },
  {
    id: 'users',
    title: 'مدیریت کاربران',
    icon: UserCog,
    path: '/admin/users',
    resource: 'users',
    requiredRoles: ['super_admin'],
    children: [
      {
        id: 'users-admins',
        title: 'کاربران مدیر',
        path: '/admin/users/admins',
        resource: 'users',
        requiredRoles: ['super_admin']
      },
      {
        id: 'users-roles',
        title: 'نقش‌ها و مجوزها',
        path: '/admin/users/roles',
        resource: 'users',
        requiredRoles: ['super_admin']
      }
    ]
  },
  {
    id: 'audit',
    title: 'گزارش عملکرد',
    icon: Shield,
    path: '/admin/audit',
    resource: 'audit',
    requiredRoles: ['super_admin', 'admin']
  },
  {
    id: 'notifications',
    title: 'اعلان‌ها',
    icon: Bell,
    path: '/admin/notifications',
    resource: 'notifications'
  }
];

// Helper function to filter navigation items based on user permissions
export const getFilteredNavigation = (
  userRole: AdminRole,
  userPermissions: AdminPermission[]
): AdminNavigationItem[] => {
  return adminNavigationItems.filter(item => {
    // Super admin has access to everything
    if (userRole === 'super_admin') {
      return true;
    }

    // Check role requirements
    if (item.requiredRoles && !item.requiredRoles.includes(userRole)) {
      return false;
    }

    // Check resource permissions
    if (item.resource) {
      const hasPermission = userPermissions.some(
        permission => permission.resource === item.resource && permission.actions.includes('read')
      );
      if (!hasPermission) {
        return false;
      }
    }

    // Filter children based on permissions
    if (item.children) {
      item.children = item.children.filter(child => {
        // Super admin has access to all children
        if (userRole === 'super_admin') {
          return true;
        }

        if (child.requiredRoles && !child.requiredRoles.includes(userRole)) {
          return false;
        }
        if (child.resource) {
          const hasChildPermission = userPermissions.some(
            permission => permission.resource === child.resource && permission.actions.includes('read')
          );
          return hasChildPermission;
        }
        return true;
      });
    }

    return true;
  });
};
