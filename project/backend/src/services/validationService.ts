import { body, validationResult, ValidationChain } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middleware/errorHandler';

// Validation result handler
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    const message = errorMessages.join('، ');
    throw new AppError(message, 400, 'VALIDATION_ERROR');
  }
  
  next();
};

// Common validation rules
export class ValidationRules {
  // Email validation
  static email(): ValidationChain {
    return body('email')
      .isEmail()
      .withMessage('فرمت ایمیل صحیح نیست')
      .normalizeEmail()
      .isLength({ min: 5, max: 100 })
      .withMessage('ایمیل باید بین ۵ تا ۱۰۰ کاراکتر باشد');
  }

  // Password validation
  static password(): ValidationChain {
    return body('password')
      .isLength({ min: 8, max: 128 })
      .withMessage('رمز عبور باید بین ۸ تا ۱۲۸ کاراکتر باشد')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
      .withMessage('رمز عبور باید شامل حروف کوچک، بزرگ، عدد و کاراکتر خاص باشد');
  }

  // Confirm password validation
  static confirmPassword(): ValidationChain {
    return body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('تکرار رمز عبور مطابقت ندارد');
        }
        return true;
      });
  }

  // First name validation
  static firstName(): ValidationChain {
    return body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('نام باید بین ۲ تا ۵۰ کاراکتر باشد')
      .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
      .withMessage('نام فقط می‌تواند شامل حروف فارسی و انگلیسی باشد');
  }

  // Last name validation
  static lastName(): ValidationChain {
    return body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('نام خانوادگی باید بین ۲ تا ۵۰ کاراکتر باشد')
      .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
      .withMessage('نام خانوادگی فقط می‌تواند شامل حروف فارسی و انگلیسی باشد');
  }

  // Phone validation (Iranian format)
  static phone(): ValidationChain {
    return body('phone')
      .optional()
      .matches(/^(\+98|0)?9\d{9}$/)
      .withMessage('شماره تلفن همراه صحیح نیست (مثال: 09123456789)');
  }

  // Required phone validation
  static requiredPhone(): ValidationChain {
    return body('phone')
      .notEmpty()
      .withMessage('شماره تلفن همراه الزامی است')
      .matches(/^(\+98|0)?9\d{9}$/)
      .withMessage('شماره تلفن همراه صحیح نیست (مثال: 09123456789)');
  }

  // Current password validation (for password change)
  static currentPassword(): ValidationChain {
    return body('currentPassword')
      .notEmpty()
      .withMessage('رمز عبور فعلی الزامی است')
      .isLength({ min: 1, max: 128 })
      .withMessage('رمز عبور فعلی نامعتبر است');
  }

  // New password validation (for password change)
  static newPassword(): ValidationChain {
    return body('newPassword')
      .isLength({ min: 8, max: 128 })
      .withMessage('رمز عبور جدید باید بین ۸ تا ۱۲۸ کاراکتر باشد')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/)
      .withMessage('رمز عبور جدید باید شامل حروف کوچک، بزرگ، عدد و کاراکتر خاص باشد');
  }

  // Address validation
  static address(): ValidationChain[] {
    return [
      body('title')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('عنوان آدرس باید بین ۲ تا ۵۰ کاراکتر باشد'),
      
      body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام باید بین ۲ تا ۵۰ کاراکتر باشد'),
      
      body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام خانوادگی باید بین ۲ تا ۵۰ کاراکتر باشد'),
      
      body('phone')
        .matches(/^(\+98|0)?9\d{9}$/)
        .withMessage('شماره تلفن همراه صحیح نیست'),
      
      body('province')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('استان باید بین ۲ تا ۵۰ کاراکتر باشد'),
      
      body('city')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('شهر باید بین ۲ تا ۵۰ کاراکتر باشد'),
      
      body('street')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('آدرس باید بین ۵ تا ۲۰۰ کاراکتر باشد'),
      
      body('postalCode')
        .matches(/^\d{10}$/)
        .withMessage('کد پستی باید ۱۰ رقم باشد'),
    ];
  }

  // User ID validation (for params)
  static userId(): ValidationChain {
    return body('userId')
      .optional()
      .isString()
      .withMessage('شناسه کاربر نامعتبر است')
      .isLength({ min: 1, max: 50 })
      .withMessage('شناسه کاربر نامعتبر است');
  }

  // Reset token validation
  static resetToken(): ValidationChain {
    return body('token')
      .notEmpty()
      .withMessage('توکن بازیابی الزامی است')
      .isString()
      .withMessage('توکن بازیابی نامعتبر است');
  }

  // Verification token validation
  static verificationToken(): ValidationChain {
    return body('token')
      .notEmpty()
      .withMessage('توکن تأیید الزامی است')
      .isString()
      .withMessage('توکن تأیید نامعتبر است');
  }

  // Review rating validation
  static reviewRating(): ValidationChain {
    return body('rating')
      .isInt({ min: 1, max: 5 })
      .withMessage('امتیاز باید عددی بین ۱ تا ۵ باشد');
  }

  // Review content validation
  static reviewContent(): ValidationChain {
    return body('content')
      .isLength({ min: 10, max: 2000 })
      .withMessage('متن نظر باید بین ۱۰ تا ۲۰۰۰ کاراکتر باشد')
      .trim();
  }

  // Review title validation
  static reviewTitle(): ValidationChain {
    return body('title')
      .optional()
      .isLength({ min: 3, max: 100 })
      .withMessage('عنوان نظر باید بین ۳ تا ۱۰۰ کاراکتر باشد')
      .trim();
  }

  // Review pros/cons validation
  static reviewProsOrCons(field: 'pros' | 'cons'): ValidationChain {
    return body(field)
      .optional()
      .isArray({ max: 5 })
      .withMessage(`حداکثر ۵ مورد برای ${field === 'pros' ? 'نکات مثبت' : 'نکات منفی'} مجاز است`)
      .custom((value) => {
        if (Array.isArray(value)) {
          for (const item of value) {
            if (typeof item !== 'string' || item.length < 2 || item.length > 100) {
              throw new Error(`هر مورد باید بین ۲ تا ۱۰۰ کاراکتر باشد`);
            }
          }
        }
        return true;
      });
  }

  // Review moderation status validation
  static moderationStatus(): ValidationChain {
    return body('moderationStatus')
      .isIn(['PENDING', 'APPROVED', 'REJECTED', 'FLAGGED'])
      .withMessage('وضعیت تعدیل نامعتبر است');
  }

  // Review vote validation
  static reviewVote(): ValidationChain {
    return body('isHelpful')
      .isBoolean()
      .withMessage('نوع رأی باید true یا false باشد');
  }
}

// Validation rule sets for different endpoints
export class ValidationSets {
  // User registration validation
  static register(): ValidationChain[] {
    return [
      ValidationRules.email(),
      ValidationRules.password(),
      ValidationRules.confirmPassword(),
      ValidationRules.firstName(),
      ValidationRules.lastName(),
      ValidationRules.phone(),
    ];
  }

  // User login validation
  static login(): ValidationChain[] {
    return [
      ValidationRules.email(),
      body('password')
        .notEmpty()
        .withMessage('رمز عبور الزامی است'),
    ];
  }

  // Password change validation
  static changePassword(): ValidationChain[] {
    return [
      ValidationRules.currentPassword(),
      ValidationRules.newPassword(),
      body('confirmNewPassword')
        .custom((value, { req }) => {
          if (value !== req.body.newPassword) {
            throw new Error('تکرار رمز عبور جدید مطابقت ندارد');
          }
          return true;
        }),
    ];
  }

  // Password reset request validation
  static requestPasswordReset(): ValidationChain[] {
    return [ValidationRules.email()];
  }

  // Password reset validation
  static resetPassword(): ValidationChain[] {
    return [
      ValidationRules.resetToken(),
      ValidationRules.password(),
      ValidationRules.confirmPassword(),
    ];
  }

  // Profile update validation
  static updateProfile(): ValidationChain[] {
    return [
      ValidationRules.firstName(),
      ValidationRules.lastName(),
      ValidationRules.phone(),
    ];
  }

  // Email verification validation
  static verifyEmail(): ValidationChain[] {
    return [ValidationRules.verificationToken()];
  }

  // Add address validation
  static addAddress(): ValidationChain[] {
    return ValidationRules.address();
  }

  // Update address validation
  static updateAddress(): ValidationChain[] {
    return [
      ...ValidationRules.address(),
      body('id')
        .notEmpty()
        .withMessage('شناسه آدرس الزامی است'),
    ];
  }

  // Product validation
  static createProduct(): ValidationChain[] {
    return [
      body('name')
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نام محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد'),

      body('nameEn')
        .optional()
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نام انگلیسی محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد'),

      body('slug')
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نامک محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('نامک محصول فقط می‌تواند شامل حروف کوچک انگلیسی، اعداد و خط تیره باشد'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 5000 })
        .withMessage('توضیحات محصول نباید بیش از ۵۰۰۰ کاراکتر باشد'),

      body('shortDescription')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('توضیحات کوتاه محصول نباید بیش از ۵۰۰ کاراکتر باشد'),

      body('sku')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('کد محصول باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('barcode')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('بارکد نباید بیش از ۵۰ کاراکتر باشد'),

      body('brandId')
        .optional()
        .isString()
        .withMessage('شناسه برند نامعتبر است'),

      body('price')
        .isNumeric()
        .withMessage('قیمت باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت نمی‌تواند منفی باشد'),

      body('comparePrice')
        .optional()
        .isNumeric()
        .withMessage('قیمت مقایسه باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت مقایسه نمی‌تواند منفی باشد'),

      body('costPrice')
        .optional()
        .isNumeric()
        .withMessage('قیمت تمام‌شده باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت تمام‌شده نمی‌تواند منفی باشد'),

      body('weight')
        .optional()
        .isNumeric()
        .withMessage('وزن باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('وزن نمی‌تواند منفی باشد'),

      body('isActive')
        .optional()
        .isBoolean()
        .withMessage('وضعیت فعال بودن باید بولین باشد'),

      body('isFeatured')
        .optional()
        .isBoolean()
        .withMessage('وضعیت ویژه بودن باید بولین باشد'),

      body('isDigital')
        .optional()
        .isBoolean()
        .withMessage('وضعیت دیجیتال بودن باید بولین باشد'),

      body('requiresShipping')
        .optional()
        .isBoolean()
        .withMessage('وضعیت نیاز به ارسال باید بولین باشد'),

      body('trackQuantity')
        .optional()
        .isBoolean()
        .withMessage('وضعیت پیگیری موجودی باید بولین باشد'),

      body('allowBackorder')
        .optional()
        .isBoolean()
        .withMessage('وضعیت اجازه پیش‌فروش باید بولین باشد'),

      body('metaTitle')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('عنوان متا نباید بیش از ۲۰۰ کاراکتر باشد'),

      body('metaDescription')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('توضیحات متا نباید بیش از ۵۰۰ کاراکتر باشد'),

      body('tags')
        .optional()
        .isArray()
        .withMessage('برچسب‌ها باید آرایه‌ای از رشته‌ها باشند'),

      body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('هر برچسب باید بین ۱ تا ۵۰ کاراکتر باشد'),

      body('categoryIds')
        .optional()
        .isArray()
        .withMessage('دسته‌بندی‌ها باید آرایه‌ای از شناسه‌ها باشند'),

      body('categoryIds.*')
        .optional()
        .isString()
        .withMessage('شناسه دسته‌بندی نامعتبر است'),
    ];
  }

  // Update product validation
  static updateProduct(): ValidationChain[] {
    return [
      body('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نام محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد'),

      body('nameEn')
        .optional()
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نام انگلیسی محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد'),

      body('slug')
        .optional()
        .trim()
        .isLength({ min: 2, max: 200 })
        .withMessage('نامک محصول باید بین ۲ تا ۲۰۰ کاراکتر باشد')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('نامک محصول فقط می‌تواند شامل حروف کوچک انگلیسی، اعداد و خط تیره باشد'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 5000 })
        .withMessage('توضیحات محصول نباید بیش از ۵۰۰۰ کاراکتر باشد'),

      body('shortDescription')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('توضیحات کوتاه محصول نباید بیش از ۵۰۰ کاراکتر باشد'),

      body('sku')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('کد محصول باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('barcode')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('بارکد نباید بیش از ۵۰ کاراکتر باشد'),

      body('brandId')
        .optional()
        .isString()
        .withMessage('شناسه برند نامعتبر است'),

      body('price')
        .optional()
        .isNumeric()
        .withMessage('قیمت باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت نمی‌تواند منفی باشد'),

      body('comparePrice')
        .optional()
        .isNumeric()
        .withMessage('قیمت مقایسه باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت مقایسه نمی‌تواند منفی باشد'),

      body('costPrice')
        .optional()
        .isNumeric()
        .withMessage('قیمت تمام‌شده باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('قیمت تمام‌شده نمی‌تواند منفی باشد'),

      body('weight')
        .optional()
        .isNumeric()
        .withMessage('وزن باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('وزن نمی‌تواند منفی باشد'),

      body('isActive')
        .optional()
        .isBoolean()
        .withMessage('وضعیت فعال بودن باید بولین باشد'),

      body('isFeatured')
        .optional()
        .isBoolean()
        .withMessage('وضعیت ویژه بودن باید بولین باشد'),

      body('isDigital')
        .optional()
        .isBoolean()
        .withMessage('وضعیت دیجیتال بودن باید بولین باشد'),

      body('requiresShipping')
        .optional()
        .isBoolean()
        .withMessage('وضعیت نیاز به ارسال باید بولین باشد'),

      body('trackQuantity')
        .optional()
        .isBoolean()
        .withMessage('وضعیت پیگیری موجودی باید بولین باشد'),

      body('allowBackorder')
        .optional()
        .isBoolean()
        .withMessage('وضعیت اجازه پیش‌فروش باید بولین باشد'),

      body('metaTitle')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('عنوان متا نباید بیش از ۲۰۰ کاراکتر باشد'),

      body('metaDescription')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('توضیحات متا نباید بیش از ۵۰۰ کاراکتر باشد'),

      body('tags')
        .optional()
        .isArray()
        .withMessage('برچسب‌ها باید آرایه‌ای از رشته‌ها باشند'),

      body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('هر برچسب باید بین ۱ تا ۵۰ کاراکتر باشد'),

      body('categoryIds')
        .optional()
        .isArray()
        .withMessage('دسته‌بندی‌ها باید آرایه‌ای از شناسه‌ها باشند'),

      body('categoryIds.*')
        .optional()
        .isString()
        .withMessage('شناسه دسته‌بندی نامعتبر است'),
    ];
  }

  // Category validation
  static createCategory(): ValidationChain[] {
    return [
      body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نام دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('nameEn')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نام انگلیسی دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('slug')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نامک دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('نامک دسته‌بندی فقط می‌تواند شامل حروف کوچک انگلیسی، اعداد و خط تیره باشد'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('توضیحات دسته‌بندی نباید بیش از ۱۰۰۰ کاراکتر باشد'),

      body('parentId')
        .optional()
        .isString()
        .withMessage('شناسه دسته‌بندی والد نامعتبر است'),

      body('isActive')
        .optional()
        .isBoolean()
        .withMessage('وضعیت فعال بودن باید بولین باشد'),

      body('sortOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('ترتیب نمایش باید عدد صحیح مثبت باشد'),
    ];
  }

  // Update category validation
  static updateCategory(): ValidationChain[] {
    return [
      body('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نام دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('nameEn')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نام انگلیسی دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('slug')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('نامک دسته‌بندی باید بین ۲ تا ۱۰۰ کاراکتر باشد')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('نامک دسته‌بندی فقط می‌تواند شامل حروف کوچک انگلیسی، اعداد و خط تیره باشد'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('توضیحات دسته‌بندی نباید بیش از ۱۰۰۰ کاراکتر باشد'),

      body('parentId')
        .optional()
        .isString()
        .withMessage('شناسه دسته‌بندی والد نامعتبر است'),

      body('isActive')
        .optional()
        .isBoolean()
        .withMessage('وضعیت فعال بودن باید بولین باشد'),

      body('sortOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('ترتیب نمایش باید عدد صحیح مثبت باشد'),
    ];
  }

  // Order validation
  static createOrder(): ValidationChain[] {
    return [
      body('items')
        .isArray({ min: 1 })
        .withMessage('سفارش باید حداقل یک آیتم داشته باشد'),

      body('items.*.productId')
        .isString()
        .withMessage('شناسه محصول الزامی است'),

      body('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('تعداد باید عدد صحیح مثبت باشد'),

      body('items.*.variantId')
        .optional()
        .isString()
        .withMessage('شناسه نوع محصول نامعتبر است'),

      body('shippingAddressId')
        .optional()
        .isString()
        .withMessage('شناسه آدرس ارسال نامعتبر است'),

      body('billingAddressId')
        .optional()
        .isString()
        .withMessage('شناسه آدرس صورتحساب نامعتبر است'),

      body('shippingAddress')
        .optional()
        .isObject()
        .withMessage('آدرس ارسال باید شیء معتبری باشد'),

      body('shippingAddress.firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('shippingAddress.lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام خانوادگی باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('shippingAddress.phone')
        .optional()
        .matches(/^09\d{9}$/)
        .withMessage('شماره تلفن باید ۱۱ رقم و با ۰۹ شروع شود'),

      body('shippingAddress.province')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('استان باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('shippingAddress.city')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('شهر باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('shippingAddress.street')
        .optional()
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('آدرس باید بین ۵ تا ۲۰۰ کاراکتر باشد'),

      body('shippingAddress.postalCode')
        .optional()
        .matches(/^\d{10}$/)
        .withMessage('کد پستی باید ۱۰ رقم باشد'),

      body('guestEmail')
        .optional()
        .isEmail()
        .withMessage('ایمیل نامعتبر است'),

      body('shippingMethod')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('روش ارسال نامعتبر است'),

      body('notes')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('یادداشت نباید بیش از ۱۰۰۰ کاراکتر باشد'),
    ];
  }

  // Update order validation (admin only)
  static updateOrder(): ValidationChain[] {
    return [
      body('status')
        .optional()
        .isIn(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED'])
        .withMessage('وضعیت سفارش نامعتبر است'),

      body('paymentStatus')
        .optional()
        .isIn(['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED'])
        .withMessage('وضعیت پرداخت نامعتبر است'),

      body('fulfillmentStatus')
        .optional()
        .isIn(['UNFULFILLED', 'PARTIAL', 'FULFILLED', 'SHIPPED', 'DELIVERED', 'RETURNED'])
        .withMessage('وضعیت تحویل نامعتبر است'),

      body('shippingMethod')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('روش ارسال نامعتبر است'),

      body('trackingNumber')
        .optional()
        .trim()
        .isLength({ min: 5, max: 100 })
        .withMessage('شماره پیگیری باید بین ۵ تا ۱۰۰ کاراکتر باشد'),

      body('internalNotes')
        .optional()
        .trim()
        .isLength({ max: 2000 })
        .withMessage('یادداشت داخلی نباید بیش از ۲۰۰۰ کاراکتر باشد'),

      body('tags')
        .optional()
        .isArray()
        .withMessage('برچسب‌ها باید آرایه‌ای از رشته‌ها باشند'),

      body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('هر برچسب باید بین ۱ تا ۵۰ کاراکتر باشد'),
    ];
  }

  // Payment validation
  static createPayment(): ValidationChain[] {
    return [
      body('orderId')
        .isString()
        .withMessage('شناسه سفارش الزامی است'),

      body('amount')
        .isNumeric()
        .withMessage('مبلغ باید عددی باشد')
        .isFloat({ min: 0 })
        .withMessage('مبلغ نمی‌تواند منفی باشد'),

      body('method')
        .isIn(['CARD', 'BANK_TRANSFER', 'WALLET', 'CASH_ON_DELIVERY'])
        .withMessage('روش پرداخت نامعتبر است'),

      body('gateway')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('درگاه پرداخت نامعتبر است'),
    ];
  }

  // Order status update validation
  static updateOrderStatus(): ValidationChain[] {
    return [
      body('status')
        .isIn(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED'])
        .withMessage('وضعیت سفارش نامعتبر است'),

      body('reason')
        .optional()
        .trim()
        .isLength({ min: 5, max: 500 })
        .withMessage('دلیل تغییر وضعیت باید بین ۵ تا ۵۰۰ کاراکتر باشد'),

      body('notifyCustomer')
        .optional()
        .isBoolean()
        .withMessage('اطلاع‌رسانی به مشتری باید بولین باشد'),
    ];
  }

  // Customer creation validation (admin only)
  static createCustomer(): ValidationChain[] {
    return [
      ValidationRules.email(),
      ValidationRules.password(),
      ValidationRules.firstName(),
      ValidationRules.lastName(),
      ValidationRules.phone(),

      body('role')
        .optional()
        .isIn(['CUSTOMER', 'ADMIN', 'SUPER_ADMIN'])
        .withMessage('نقش کاربر نامعتبر است'),

      body('status')
        .optional()
        .isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED'])
        .withMessage('وضعیت کاربر نامعتبر است'),

      body('isEmailVerified')
        .optional()
        .isBoolean()
        .withMessage('وضعیت تأیید ایمیل باید بولین باشد'),

      body('isPhoneVerified')
        .optional()
        .isBoolean()
        .withMessage('وضعیت تأیید تلفن باید بولین باشد'),
    ];
  }

  // Customer update validation (admin only)
  static updateCustomer(): ValidationChain[] {
    return [
      body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام باید بین ۲ تا ۵۰ کاراکتر باشد')
        .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
        .withMessage('نام فقط می‌تواند شامل حروف فارسی و انگلیسی باشد'),

      body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('نام خانوادگی باید بین ۲ تا ۵۰ کاراکتر باشد')
        .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
        .withMessage('نام خانوادگی فقط می‌تواند شامل حروف فارسی و انگلیسی باشد'),

      body('phone')
        .optional()
        .matches(/^(\+98|0)?9\d{9}$/)
        .withMessage('شماره تلفن همراه صحیح نیست (مثال: 09123456789)'),

      body('avatar')
        .optional()
        .isURL()
        .withMessage('آدرس تصویر نامعتبر است'),

      body('status')
        .optional()
        .isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED'])
        .withMessage('وضعیت کاربر نامعتبر است'),

      body('isEmailVerified')
        .optional()
        .isBoolean()
        .withMessage('وضعیت تأیید ایمیل باید بولین باشد'),

      body('isPhoneVerified')
        .optional()
        .isBoolean()
        .withMessage('وضعیت تأیید تلفن باید بولین باشد'),
    ];
  }

  // Bulk customer status update validation
  static bulkUpdateCustomerStatus(): ValidationChain[] {
    return [
      body('customerIds')
        .isArray({ min: 1 })
        .withMessage('لیست شناسه مشتریان الزامی است'),

      body('customerIds.*')
        .isString()
        .withMessage('شناسه مشتری نامعتبر است'),

      body('status')
        .isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'BANNED'])
        .withMessage('وضعیت نامعتبر است'),

      body('reason')
        .optional()
        .trim()
        .isLength({ min: 5, max: 500 })
        .withMessage('دلیل تغییر وضعیت باید بین ۵ تا ۵۰۰ کاراکتر باشد'),
    ];
  }

  // Customer search validation
  static searchCustomers(): ValidationChain[] {
    return [
      body('q')
        .notEmpty()
        .withMessage('پارامتر جستجو الزامی است')
        .isLength({ min: 2, max: 100 })
        .withMessage('عبارت جستجو باید بین ۲ تا ۱۰۰ کاراکتر باشد'),

      body('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('حد نتایج باید بین ۱ تا ۱۰۰ باشد'),

      body('includeInactive')
        .optional()
        .isBoolean()
        .withMessage('شامل کاربران غیرفعال باید بولین باشد'),
    ];
  }

  // Create loyalty account validation (admin only)
  static createLoyaltyAccount(): ValidationChain[] {
    return [
      body('userId')
        .notEmpty()
        .withMessage('شناسه کاربر الزامی است')
        .isString()
        .withMessage('شناسه کاربر نامعتبر است'),

      body('initialPoints')
        .optional()
        .isInt({ min: 0, max: 1000000 })
        .withMessage('امتیاز اولیه باید بین ۰ تا ۱۰۰۰۰۰۰ باشد'),

      body('tier')
        .optional()
        .isIn(['BRONZE', 'SILVER', 'GOLD', 'PLATINUM'])
        .withMessage('سطح وفاداری نامعتبر است'),
    ];
  }

  // Earn points validation
  static earnPoints(): ValidationChain[] {
    return [
      body('userId')
        .notEmpty()
        .withMessage('شناسه کاربر الزامی است')
        .isString()
        .withMessage('شناسه کاربر نامعتبر است'),

      body('points')
        .isInt({ min: 1, max: 100000 })
        .withMessage('امتیاز باید بین ۱ تا ۱۰۰۰۰۰ باشد'),

      body('description')
        .notEmpty()
        .withMessage('توضیحات الزامی است')
        .isLength({ min: 5, max: 200 })
        .withMessage('توضیحات باید بین ۵ تا ۲۰۰ کاراکتر باشد'),

      body('orderId')
        .optional()
        .isString()
        .withMessage('شناسه سفارش نامعتبر است'),

      body('expiresAt')
        .optional()
        .isISO8601()
        .withMessage('تاریخ انقضا نامعتبر است'),
    ];
  }

  // Redeem points validation
  static redeemPoints(): ValidationChain[] {
    return [
      body('userId')
        .optional()
        .isString()
        .withMessage('شناسه کاربر نامعتبر است'),

      body('points')
        .isInt({ min: 1, max: 100000 })
        .withMessage('امتیاز باید بین ۱ تا ۱۰۰۰۰۰ باشد'),

      body('description')
        .notEmpty()
        .withMessage('توضیحات الزامی است')
        .isLength({ min: 5, max: 200 })
        .withMessage('توضیحات باید بین ۵ تا ۲۰۰ کاراکتر باشد'),

      body('orderId')
        .optional()
        .isString()
        .withMessage('شناسه سفارش نامعتبر است'),
    ];
  }

  // Adjust points validation (admin only)
  static adjustPoints(): ValidationChain[] {
    return [
      body('userId')
        .notEmpty()
        .withMessage('شناسه کاربر الزامی است')
        .isString()
        .withMessage('شناسه کاربر نامعتبر است'),

      body('points')
        .isInt({ min: -100000, max: 100000 })
        .withMessage('امتیاز باید بین -۱۰۰۰۰۰ تا ۱۰۰۰۰۰ باشد'),

      body('description')
        .notEmpty()
        .withMessage('توضیحات الزامی است')
        .isLength({ min: 5, max: 200 })
        .withMessage('توضیحات باید بین ۵ تا ۲۰۰ کاراکتر باشد'),
    ];
  }

  // Redeem reward validation
  static redeemReward(): ValidationChain[] {
    return [
      body('userId')
        .optional()
        .isString()
        .withMessage('شناسه کاربر نامعتبر است'),

      body('rewardId')
        .notEmpty()
        .withMessage('شناسه جایزه الزامی است')
        .isString()
        .withMessage('شناسه جایزه نامعتبر است'),

      body('quantity')
        .optional()
        .isInt({ min: 1, max: 10 })
        .withMessage('تعداد باید بین ۱ تا ۱۰ باشد'),
    ];
  }

  // Create review validation
  static createReview(): ValidationChain[] {
    return [
      body('productId')
        .notEmpty()
        .withMessage('شناسه محصول الزامی است')
        .isString()
        .withMessage('شناسه محصول نامعتبر است'),

      ValidationRules.reviewRating(),
      ValidationRules.reviewTitle(),
      ValidationRules.reviewContent(),
      ValidationRules.reviewProsOrCons('pros'),
      ValidationRules.reviewProsOrCons('cons'),

      body('isRecommended')
        .optional()
        .isBoolean()
        .withMessage('توصیه محصول باید true یا false باشد'),

      body('skinType')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('نوع پوست باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('ageRange')
        .optional()
        .isLength({ min: 2, max: 20 })
        .withMessage('بازه سنی باید بین ۲ تا ۲۰ کاراکتر باشد'),

      body('usageDuration')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('مدت استفاده باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('images')
        .optional()
        .isArray({ max: 5 })
        .withMessage('حداکثر ۵ تصویر مجاز است'),

      body('images.*')
        .optional()
        .isURL()
        .withMessage('آدرس تصویر نامعتبر است'),
    ];
  }

  // Update review validation
  static updateReview(): ValidationChain[] {
    return [
      body('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('امتیاز باید عددی بین ۱ تا ۵ باشد'),

      body('title')
        .optional()
        .isLength({ min: 3, max: 100 })
        .withMessage('عنوان نظر باید بین ۳ تا ۱۰۰ کاراکتر باشد'),

      body('content')
        .optional()
        .isLength({ min: 10, max: 2000 })
        .withMessage('متن نظر باید بین ۱۰ تا ۲۰۰۰ کاراکتر باشد'),

      ValidationRules.reviewProsOrCons('pros'),
      ValidationRules.reviewProsOrCons('cons'),

      body('isRecommended')
        .optional()
        .isBoolean()
        .withMessage('توصیه محصول باید true یا false باشد'),

      body('skinType')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('نوع پوست باید بین ۲ تا ۵۰ کاراکتر باشد'),

      body('ageRange')
        .optional()
        .isLength({ min: 2, max: 20 })
        .withMessage('بازه سنی باید بین ۲ تا ۲۰ کاراکتر باشد'),

      body('usageDuration')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('مدت استفاده باید بین ۲ تا ۵۰ کاراکتر باشد'),
    ];
  }

  // Vote review validation
  static voteReview(): ValidationChain[] {
    return [
      ValidationRules.reviewVote(),
    ];
  }

  // Add review response validation
  static addReviewResponse(): ValidationChain[] {
    return [
      body('content')
        .notEmpty()
        .withMessage('متن پاسخ الزامی است')
        .isLength({ min: 10, max: 1000 })
        .withMessage('متن پاسخ باید بین ۱۰ تا ۱۰۰۰ کاراکتر باشد'),

      body('isOfficial')
        .optional()
        .isBoolean()
        .withMessage('نوع پاسخ رسمی باید true یا false باشد'),
    ];
  }

  // Moderate review validation
  static moderateReview(): ValidationChain[] {
    return [
      ValidationRules.moderationStatus(),

      body('moderationNotes')
        .optional()
        .isLength({ min: 5, max: 500 })
        .withMessage('یادداشت تعدیل باید بین ۵ تا ۵۰۰ کاراکتر باشد'),
    ];
  }
}

export default ValidationSets;
